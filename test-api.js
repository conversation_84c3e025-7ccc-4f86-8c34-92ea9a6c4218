// Node.js API测试脚本
const https = require('https');
const http = require('http');

const API_BASE_URL = 'http://localhost:8080';

// 测试登录API
async function testLogin() {
    const loginData = JSON.stringify({
        username: 'admin',
        password: 'admin123'
    });

    const options = {
        hostname: 'localhost',
        port: 8080,
        path: '/api/auth/login',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(loginData)
        }
    };

    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    console.log('✅ 登录测试成功:');
                    console.log('   状态码:', res.statusCode);
                    console.log('   响应:', response);
                    resolve(response);
                } catch (error) {
                    console.log('❌ 登录测试失败 - JSON解析错误:', error.message);
                    console.log('   原始响应:', data);
                    reject(error);
                }
            });
        });

        req.on('error', (error) => {
            console.log('❌ 登录测试失败 - 网络错误:', error.message);
            reject(error);
        });

        req.write(loginData);
        req.end();
    });
}

// 测试获取猫咪列表API
async function testGetCats(token) {
    const options = {
        hostname: 'localhost',
        port: 8080,
        path: '/api/cats',
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    };

    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    console.log('✅ 获取猫咪列表成功:');
                    console.log('   状态码:', res.statusCode);
                    console.log('   数据条数:', response.data ? response.data.length : 0);
                    resolve(response);
                } catch (error) {
                    console.log('❌ 获取猫咪列表失败 - JSON解析错误:', error.message);
                    console.log('   原始响应:', data);
                    reject(error);
                }
            });
        });

        req.on('error', (error) => {
            console.log('❌ 获取猫咪列表失败 - 网络错误:', error.message);
            reject(error);
        });

        req.end();
    });
}

// 测试Swagger UI访问
async function testSwaggerUI() {
    const options = {
        hostname: 'localhost',
        port: 8080,
        path: '/swagger-ui.html',
        method: 'GET'
    };

    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            console.log('✅ Swagger UI访问测试:');
            console.log('   状态码:', res.statusCode);
            console.log('   Content-Type:', res.headers['content-type']);
            resolve(res.statusCode === 200);
        });

        req.on('error', (error) => {
            console.log('❌ Swagger UI访问失败:', error.message);
            reject(error);
        });

        req.end();
    });
}

// 主测试函数
async function runTests() {
    console.log('🔍 开始API深度连通性测试...\n');
    
    try {
        // 1. 测试Swagger UI
        console.log('1. 测试Swagger UI访问...');
        await testSwaggerUI();
        console.log('');
        
        // 2. 测试登录
        console.log('2. 测试登录API...');
        const loginResponse = await testLogin();
        console.log('');
        
        if (loginResponse.success && loginResponse.data && loginResponse.data.token) {
            const token = loginResponse.data.token;
            console.log('   获取到Token:', token.substring(0, 20) + '...');
            
            // 3. 测试需要认证的API
            console.log('3. 测试猫咪列表API...');
            await testGetCats(token);
            console.log('');
            
            console.log('🎉 所有测试完成！系统连通性正常。');
        } else {
            console.log('❌ 登录失败，无法继续测试需要认证的API');
        }
        
    } catch (error) {
        console.log('❌ 测试过程中发生错误:', error.message);
    }
}

// 运行测试
runTests();
