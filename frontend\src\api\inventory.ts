import { http } from '@/utils/http'

export const inventoryApi = {
  // 获取库存统计
  getInventoryStats() {
    return http.get('/api/inventory-management/statistics')
  },

  // 获取库存预警
  getStockAlerts(params?: any) {
    return http.get('/api/inventory-management/stock-alerts', { params })
  },

  // 获取最近库存记录
  getRecentRecords(params?: any) {
    return http.get('/api/inventory-management/recent-records', { params })
  },

  // 获取分类分布
  getCategoryDistribution() {
    return http.get('/api/inventory-management/category-distribution')
  },

  // 获取状态分布
  getStatusDistribution() {
    return http.get('/api/inventory-management/status-distribution')
  },

  // 获取库存趋势
  getInventoryTrend(params?: any) {
    return http.get('/api/inventory-management/inventory-trend', { params })
  },

  // 获取库存物品列表
  getInventoryItems(params?: any) {
    return http.get('/api/inventory-items', { params })
  },

  // 创建库存物品
  createInventoryItem(data: any) {
    return http.post('/api/inventory-items', data)
  },

  // 更新库存物品
  updateInventoryItem(id: number, data: any) {
    return http.put(`/api/inventory-items/${id}`, data)
  },

  // 删除库存物品
  deleteInventoryItem(id: number) {
    return http.delete(`/api/inventory-items/${id}`)
  },

  // 获取库存分类列表
  getInventoryCategories(params?: any) {
    return http.get('/api/inventory-categories', { params })
  },

  // 创建库存分类
  createInventoryCategory(data: any) {
    return http.post('/api/inventory-categories', data)
  },

  // 更新库存分类
  updateInventoryCategory(id: number, data: any) {
    return http.put(`/api/inventory-categories/${id}`, data)
  },

  // 删除库存分类
  deleteInventoryCategory(id: number) {
    return http.delete(`/api/inventory-categories/${id}`)
  },

  // 获取库存记录列表
  getInventoryRecords(params?: any) {
    return http.get('/api/inventory-records', { params })
  },

  // 创建库存记录
  createInventoryRecord(data: any) {
    return http.post('/api/inventory-records', data)
  },

  // 更新库存记录
  updateInventoryRecord(id: number, data: any) {
    return http.put(`/api/inventory-records/${id}`, data)
  },

  // 删除库存记录
  deleteInventoryRecord(id: number) {
    return http.delete(`/api/inventory-records/${id}`)
  },

  // 获取喂养计划列表
  getFeedingPlans(params?: any) {
    return http.get('/api/feeding-plans', { params })
  },

  // 创建喂养计划
  createFeedingPlan(data: any) {
    return http.post('/api/feeding-plans', data)
  },

  // 更新喂养计划
  updateFeedingPlan(id: number, data: any) {
    return http.put(`/api/feeding-plans/${id}`, data)
  },

  // 删除喂养计划
  deleteFeedingPlan(id: number) {
    return http.delete(`/api/feeding-plans/${id}`)
  },

  // 获取喂养记录列表
  getFeedingRecords(params?: any) {
    return http.get('/api/feeding-records', { params })
  },

  // 创建喂养记录
  createFeedingRecord(data: any) {
    return http.post('/api/feeding-records', data)
  },

  // 更新喂养记录
  updateFeedingRecord(id: number, data: any) {
    return http.put(`/api/feeding-records/${id}`, data)
  },

  // 删除喂养记录
  deleteFeedingRecord(id: number) {
    return http.delete(`/api/feeding-records/${id}`)
  },

  // 库存盘点
  performStockTaking(data: any) {
    return http.post('/api/inventory-management/stock-taking', data)
  },

  // 批量更新库存
  batchUpdateStock(data: any) {
    return http.post('/api/inventory-management/batch-update', data)
  },

  // 获取库存报告
  getInventoryReport(params?: any) {
    return http.get('/api/inventory-management/report', { params })
  },

  // 导出库存数据
  exportInventoryData(params?: any) {
    return http.get('/api/inventory-management/export', { 
      params,
      responseType: 'blob'
    })
  },

  // 获取供应商列表
  getSuppliers(params?: any) {
    return http.get('/api/suppliers', { params })
  },

  // 创建供应商
  createSupplier(data: any) {
    return http.post('/api/suppliers', data)
  },

  // 更新供应商
  updateSupplier(id: number, data: any) {
    return http.put(`/api/suppliers/${id}`, data)
  },

  // 删除供应商
  deleteSupplier(id: number) {
    return http.delete(`/api/suppliers/${id}`)
  }
}
