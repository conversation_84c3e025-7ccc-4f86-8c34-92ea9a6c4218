# ===================================================================
# H2内存数据库配置 - 用于快速演示
# ===================================================================

# 数据库配置 - H2内存数据库
spring.datasource.url=jdbc:h2:mem:cattery_db;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=MySQL
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect

# H2控制台配置
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console
spring.h2.console.settings.web-allow-others=true
spring.h2.console.settings.trace=false

# JPA/Hibernate配置
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.defer-datasource-initialization=true

# 初始化数据 - 使用DataInitializer而不是SQL脚本
spring.sql.init.mode=never

# 日志配置
logging.level.com.cattery=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.hibernate.SQL=DEBUG

# 服务器配置
server.port=8080

# JWT配置 - 密钥必须至少512位(64字节)用于HS512算法
jwt.secret=mySecretKey123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890
jwt.expiration=86400000

# CORS配置
cors.allowed-origins=http://localhost:3000,http://localhost:5173,http://localhost:8080

# Swagger配置
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.enabled=true
