import { http } from './config'
import type { ApiResponse } from './config'

// 媒体文件类型定义
export interface MediaFile {
  id: number
  catId: number
  fileName: string
  originalName: string
  filePath: string
  fileSize: number
  contentType: string
  width?: number
  height?: number
  thumbnailPath?: string
  isPrimary: boolean
  sortOrder: number
  description?: string
  takenDate?: string
  createdAt: string
  updatedAt: string
}

export interface MediaUploadResponse {
  id: number
  fileName: string
  filePath: string
  fileSize: number
  contentType: string
}

/**
 * 媒体文件管理API
 */
export const mediaApi = {
  /**
   * 获取猫咪的所有媒体文件
   */
  getCatMediaFiles(catId: number): Promise<ApiResponse<MediaFile[]>> {
    return http.get(`/cats/${catId}/media`)
  },

  /**
   * 获取猫咪的照片
   */
  getCatPhotos(catId: number): Promise<ApiResponse<MediaFile[]>> {
    return http.get(`/cats/${catId}/media/photos`)
  },

  /**
   * 获取猫咪的视频
   */
  getCatVideos(catId: number): Promise<ApiResponse<MediaFile[]>> {
    return http.get(`/cats/${catId}/media/videos`)
  },

  /**
   * 获取猫咪的主要照片
   */
  getPrimaryPhoto(catId: number): Promise<ApiResponse<MediaFile>> {
    return http.get(`/cats/${catId}/media/primary-photo`)
  },

  /**
   * 上传照片
   */
  uploadPhoto(catId: number, formData: FormData): Promise<ApiResponse<MediaUploadResponse>> {
    return http.upload(`/cats/${catId}/media/photos`, formData)
  },

  /**
   * 上传视频
   */
  uploadVideo(catId: number, formData: FormData): Promise<ApiResponse<MediaUploadResponse>> {
    return http.upload(`/cats/${catId}/media/videos`, formData)
  },

  /**
   * 设置主要照片
   */
  setPrimaryPhoto(catId: number, mediaId: number): Promise<ApiResponse<void>> {
    return http.put(`/cats/${catId}/media/${mediaId}/set-primary`)
  },

  /**
   * 更新媒体文件排序
   */
  updateMediaOrder(catId: number, mediaIds: number[]): Promise<ApiResponse<void>> {
    return http.put(`/cats/${catId}/media/order`, mediaIds)
  },

  /**
   * 删除媒体文件
   */
  deleteMedia(catId: number, mediaId: number): Promise<ApiResponse<void>> {
    return http.delete(`/cats/${catId}/media/${mediaId}`)
  },

  /**
   * 获取媒体文件详情
   */
  getMediaById(catId: number, mediaId: number): Promise<ApiResponse<MediaFile>> {
    return http.get(`/cats/${catId}/media/${mediaId}`)
  },

  /**
   * 更新媒体文件信息
   */
  updateMediaInfo(
    catId: number, 
    mediaId: number, 
    data: { title?: string; description?: string }
  ): Promise<ApiResponse<MediaFile>> {
    return http.put(`/cats/${catId}/media/${mediaId}`, data)
  }
}

/**
 * 文件上传API
 */
export const fileApi = {
  /**
   * 上传单个文件
   */
  uploadFile(formData: FormData): Promise<ApiResponse<MediaUploadResponse>> {
    return http.upload('/files/upload', formData)
  },

  /**
   * 上传多个文件
   */
  uploadMultipleFiles(formData: FormData): Promise<ApiResponse<MediaUploadResponse[]>> {
    return http.upload('/files/upload-multiple', formData)
  },

  /**
   * 下载文件
   */
  downloadFile(filePath: string): Promise<Blob> {
    return http.download(`/files/${filePath}`)
  },

  /**
   * 删除文件
   */
  deleteFile(filePath: string): Promise<ApiResponse<void>> {
    return http.delete(`/files/${filePath}`)
  },

  /**
   * 检查文件是否存在
   */
  checkFileExists(filePath: string): Promise<ApiResponse<{ exists: boolean }>> {
    return http.get(`/files/check/${filePath}`)
  }
}

export default {
  mediaApi,
  fileApi
}
