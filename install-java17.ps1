# Install Java 17 and Build Project
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Install Java 17 and Build Project" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check current Java version
Write-Host "Current Java version:" -ForegroundColor Yellow
try {
    $javaVersion = java -version 2>&1
    Write-Host $javaVersion -ForegroundColor Gray
    
    if ($javaVersion -like "*17.*") {
        Write-Host "Java 17 is already installed!" -ForegroundColor Green
        $java17Installed = $true
    } else {
        Write-Host "Java 17 is not installed (current version is Java 8)" -ForegroundColor Yellow
        $java17Installed = $false
    }
} catch {
    Write-Host "Java not found" -ForegroundColor Red
    $java17Installed = $false
}

if (-not $java17Installed) {
    Write-Host ""
    Write-Host "Installing Java 17..." -ForegroundColor Cyan
    
    # Create tools directory if not exists
    $toolsDir = "C:\tools"
    if (-not (Test-Path $toolsDir)) {
        New-Item -ItemType Directory -Path $toolsDir -Force
        Write-Host "Created tools directory: $toolsDir" -ForegroundColor Green
    }
    
    # Download OpenJDK 17
    $jdkVersion = "17.0.2"
    $jdkUrl = "https://download.java.net/java/GA/jdk17.0.2/dfd4a8d0985749f896bed50d7138ee7f/8/GPL/openjdk-17.0.2_windows-x64_bin.zip"
    $jdkZip = "$toolsDir\openjdk-17.0.2_windows-x64_bin.zip"
    $jdkDir = "$toolsDir\jdk-17.0.2"
    
    try {
        Write-Host "Downloading OpenJDK 17 from: $jdkUrl" -ForegroundColor Yellow
        Write-Host "This may take a few minutes..." -ForegroundColor Yellow
        
        Invoke-WebRequest -Uri $jdkUrl -OutFile $jdkZip
        Write-Host "Java 17 downloaded successfully" -ForegroundColor Green
        
        # Extract JDK
        Write-Host "Extracting Java 17..." -ForegroundColor Yellow
        Expand-Archive -Path $jdkZip -DestinationPath $toolsDir -Force
        Write-Host "Java 17 extracted to: $jdkDir" -ForegroundColor Green
        
        # Set JAVA_HOME for current session
        $env:JAVA_HOME = $jdkDir
        $env:PATH = "$jdkDir\bin;" + $env:PATH
        Write-Host "JAVA_HOME set to: $env:JAVA_HOME" -ForegroundColor Green
        
        # Test Java 17 installation
        Write-Host ""
        Write-Host "Testing Java 17 installation..." -ForegroundColor Cyan
        $java17Test = & "$jdkDir\bin\java.exe" -version 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Java 17 installation successful!" -ForegroundColor Green
            Write-Host $java17Test -ForegroundColor Gray
            $java17Installed = $true
        } else {
            Write-Host "Java 17 installation failed" -ForegroundColor Red
            Write-Host $java17Test -ForegroundColor Red
            return
        }
        
        # Clean up
        Remove-Item $jdkZip -Force
        Write-Host "Cleanup completed" -ForegroundColor Green
        
    } catch {
        Write-Host "Failed to install Java 17: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host ""
        Write-Host "Manual installation steps:" -ForegroundColor Yellow
        Write-Host "1. Download Java 17 from: https://jdk.java.net/17/" -ForegroundColor Cyan
        Write-Host "2. Extract to C:\tools\jdk-17.0.2" -ForegroundColor Cyan
        Write-Host "3. Set JAVA_HOME to C:\tools\jdk-17.0.2" -ForegroundColor Cyan
        Write-Host "4. Add C:\tools\jdk-17.0.2\bin to PATH" -ForegroundColor Cyan
        return
    }
}

if ($java17Installed) {
    Write-Host ""
    Write-Host "Building Spring Boot project with Java 17..." -ForegroundColor Cyan
    
    # Set location to backend directory
    Set-Location "D:\噔噔\backend"
    Write-Host "Current directory: $(Get-Location)" -ForegroundColor Green
    
    # Display current Java version being used
    Write-Host ""
    Write-Host "Java version for build:" -ForegroundColor Yellow
    & "$env:JAVA_HOME\bin\java.exe" -version
    
    # Clean and compile
    Write-Host ""
    Write-Host "Step 1: Clean project..." -ForegroundColor Yellow
    mvn clean
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Clean successful!" -ForegroundColor Green
        
        Write-Host ""
        Write-Host "Step 2: Compile project..." -ForegroundColor Yellow
        mvn compile
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Compile successful!" -ForegroundColor Green
            
            Write-Host ""
            Write-Host "Step 3: Package project..." -ForegroundColor Yellow
            mvn package -DskipTests
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "Package successful!" -ForegroundColor Green
                
                # Find the generated jar
                $jarFiles = Get-ChildItem "target" -Filter "*.jar" | Where-Object { $_.Name -notlike "*sources*" -and $_.Name -notlike "*javadoc*" }
                
                if ($jarFiles) {
                    $jarFile = $jarFiles[0]
                    Write-Host ""
                    Write-Host "Generated JAR: $($jarFile.Name)" -ForegroundColor Green
                    Write-Host "Size: $([math]::Round($jarFile.Length / 1MB, 2)) MB" -ForegroundColor Green
                    
                    Write-Host ""
                    Write-Host "========================================" -ForegroundColor Green
                    Write-Host "SUCCESS! Ready to start application" -ForegroundColor Green
                    Write-Host "========================================" -ForegroundColor Green
                    Write-Host ""
                    
                    Write-Host "Starting Spring Boot application..." -ForegroundColor Cyan
                    Write-Host "This may take a few minutes for first startup..." -ForegroundColor Yellow
                    Write-Host "Look for 'Started CatteryManagementApplication' message" -ForegroundColor Yellow
                    Write-Host ""
                    
                    # Run the jar file
                    & "$env:JAVA_HOME\bin\java.exe" -jar "target\$($jarFile.Name)"
                    
                } else {
                    Write-Host "No JAR file found in target directory" -ForegroundColor Red
                    
                    Write-Host ""
                    Write-Host "Trying to run with Maven..." -ForegroundColor Yellow
                    mvn spring-boot:run
                }
            } else {
                Write-Host "Package failed" -ForegroundColor Red
                Write-Host "Check the error messages above" -ForegroundColor Yellow
            }
        } else {
            Write-Host "Compile failed" -ForegroundColor Red
            Write-Host "Check the error messages above" -ForegroundColor Yellow
        }
    } else {
        Write-Host "Clean failed" -ForegroundColor Red
        Write-Host "Check the error messages above" -ForegroundColor Yellow
    }
}

Write-Host ""
Read-Host "Press Enter to exit"
