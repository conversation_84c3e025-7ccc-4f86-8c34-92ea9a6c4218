package com.cattery.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 财务记录实体
 */
@Data
@Entity
@Table(name = "finance_records")
@EntityListeners(AuditingEntityListener.class)
@EqualsAndHashCode(callSuper = false)
public class FinanceRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 记录类型：收入/支出
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false)
    private RecordType type;

    /**
     * 分类
     */
    @Column(name = "category", nullable = false, length = 100)
    private String category;

    /**
     * 金额
     */
    @Column(name = "amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal amount;

    /**
     * 描述
     */
    @Column(name = "description", nullable = false, length = 500)
    private String description;

    /**
     * 交易日期
     */
    @Column(name = "transaction_date", nullable = false)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate transactionDate;

    /**
     * 支付方式
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "payment_method", nullable = false)
    private PaymentMethod paymentMethod;

    /**
     * 关联猫咪ID
     */
    @Column(name = "related_cat_id")
    private Long relatedCatId;

    /**
     * 关联客户ID
     */
    @Column(name = "related_customer_id")
    private Long relatedCustomerId;

    /**
     * 关联繁育记录ID
     */
    @Column(name = "related_breeding_record_id")
    private Long relatedBreedingRecordId;

    /**
     * 关联健康记录ID
     */
    @Column(name = "related_health_record_id")
    private Long relatedHealthRecordId;

    /**
     * 发票号码
     */
    @Column(name = "invoice_number", length = 100)
    private String invoiceNumber;

    /**
     * 收据路径
     */
    @Column(name = "receipt_path", length = 500)
    private String receiptPath;

    /**
     * 备注
     */
    @Column(name = "notes", columnDefinition = "TEXT")
    private String notes;

    /**
     * 状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private RecordStatus status = RecordStatus.COMPLETED;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "updated_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 创建人ID
     */
    @Column(name = "created_by")
    private Long createdBy;

    /**
     * 更新人ID
     */
    @Column(name = "updated_by")
    private Long updatedBy;

    /**
     * 关联猫咪信息（关联查询时使用）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "related_cat_id", insertable = false, updatable = false)
    private Cat relatedCat;

    /**
     * 关联客户信息（关联查询时使用）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "related_customer_id", insertable = false, updatable = false)
    private Customer relatedCustomer;

    /**
     * 记录类型枚举
     */
    public enum RecordType {
        INCOME("收入"),
        EXPENSE("支出");

        private final String description;

        RecordType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 支付方式枚举
     */
    public enum PaymentMethod {
        CASH("现金"),
        BANK_TRANSFER("银行转账"),
        ALIPAY("支付宝"),
        WECHAT("微信支付"),
        CREDIT_CARD("信用卡"),
        OTHER("其他");

        private final String description;

        PaymentMethod(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 记录状态枚举
     */
    public enum RecordStatus {
        PENDING("待确认"),
        COMPLETED("已完成"),
        CANCELLED("已取消");

        private final String description;

        RecordStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
