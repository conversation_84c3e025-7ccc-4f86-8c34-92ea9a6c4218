<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客户管理 - 猫舍管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            line-height: 1.6;
        }

        .header {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 0 20px;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-links a {
            color: #666;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .nav-links a:hover, .nav-links a.active {
            background: #667eea;
            color: white;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logout-btn {
            background: #f56565;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .page-title h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 8px;
        }

        .page-title p {
            color: #666;
            font-size: 16px;
        }

        .header-actions {
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-secondary {
            background: white;
            color: #666;
            border: 1px solid #ddd;
        }

        .btn-secondary:hover {
            background: #f8f9fa;
        }

        .filters {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }

        .filters-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-group label {
            color: #333;
            font-weight: 500;
            font-size: 14px;
        }

        .filter-group input, .filter-group select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }

        .customers-table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }

        .table-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #eee;
        }

        .table-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .table-container {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
            font-size: 14px;
        }

        td {
            color: #666;
            font-size: 14px;
        }

        .customer-name {
            font-weight: 500;
            color: #333;
        }

        .customer-type {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .type-potential {
            background: #e0e7ff;
            color: #3730a3;
        }

        .type-active {
            background: #d1fae5;
            color: #065f46;
        }

        .type-adopted {
            background: #fef3c7;
            color: #92400e;
        }

        .type-vip {
            background: #fce7f3;
            color: #be185d;
        }

        .actions {
            display: flex;
            gap: 8px;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            font-weight: 500;
        }

        .btn-edit {
            background: #f59e0b;
            color: white;
        }

        .btn-view {
            background: #10b981;
            color: white;
        }

        .btn-delete {
            background: #ef4444;
            color: white;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #333;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 12px;
            padding: 30px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .form-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        @media (max-width: 768px) {
            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .header-actions {
                width: 100%;
                justify-content: flex-start;
            }

            .filters-row {
                grid-template-columns: 1fr;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .table-container {
                font-size: 12px;
            }

            th, td {
                padding: 10px 8px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">🐱 猫舍管理系统</div>
            <div class="nav-links">
                <a href="/dashboard.html">仪表盘</a>
                <a href="/cats-management.html">猫咪管理</a>
                <a href="/customers-management.html" class="active">客户管理</a>
                <a href="/health-management.html">健康记录</a>
                <a href="/breeding-management.html">繁育管理</a>
            </div>
            <div class="user-info">
                <span id="userName">加载中...</span>
                <button class="logout-btn" onclick="logout()">退出</button>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="page-header">
            <div class="page-title">
                <h1>客户管理</h1>
                <p>管理所有客户信息和购买记录</p>
            </div>
            <div class="header-actions">
                <button class="btn btn-secondary" onclick="refreshData()">刷新数据</button>
                <button class="btn btn-primary" onclick="showAddCustomerModal()">添加客户</button>
            </div>
        </div>

        <div class="filters">
            <div class="filters-row">
                <div class="filter-group">
                    <label for="searchName">搜索客户</label>
                    <input type="text" id="searchName" placeholder="输入客户姓名或邮箱">
                </div>
                <div class="filter-group">
                    <label for="filterType">客户类型</label>
                    <select id="filterType">
                        <option value="">全部类型</option>
                        <option value="POTENTIAL">潜在客户</option>
                        <option value="ACTIVE">活跃客户</option>
                        <option value="ADOPTED">已购买</option>
                        <option value="VIP">VIP客户</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="filterStatus">状态筛选</label>
                    <select id="filterStatus">
                        <option value="">全部状态</option>
                        <option value="ACTIVE">活跃</option>
                        <option value="INACTIVE">非活跃</option>
                    </select>
                </div>
                <div class="filter-group">
                    <button class="btn btn-primary" onclick="applyFilters()">应用筛选</button>
                </div>
            </div>
        </div>

        <div id="loadingMessage" class="loading">正在加载客户数据...</div>
        
        <div id="customersTable" class="customers-table" style="display: none;">
            <div class="table-header">
                <div class="table-title">客户列表</div>
            </div>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>客户姓名</th>
                            <th>邮箱</th>
                            <th>电话</th>
                            <th>客户类型</th>
                            <th>注册时间</th>
                            <th>最后联系</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="customersTableBody">
                    </tbody>
                </table>
            </div>
        </div>
        
        <div id="emptyState" class="empty-state" style="display: none;">
            <h3>暂无客户数据</h3>
            <p>点击"添加客户"按钮开始添加第一个客户</p>
        </div>
    </div>

    <!-- 添加/编辑客户模态框 -->
    <div id="customerModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">添加客户</h3>
                <button class="close-btn" onclick="closeCustomerModal()">&times;</button>
            </div>
            <form id="customerForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="customerName">客户姓名 *</label>
                        <input type="text" id="customerName" required>
                    </div>
                    <div class="form-group">
                        <label for="customerEmail">邮箱 *</label>
                        <input type="email" id="customerEmail" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="customerPhone">电话</label>
                        <input type="tel" id="customerPhone">
                    </div>
                    <div class="form-group">
                        <label for="customerGender">性别</label>
                        <select id="customerGender">
                            <option value="">请选择</option>
                            <option value="MALE">男</option>
                            <option value="FEMALE">女</option>
                            <option value="OTHER">其他</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="customerAge">年龄</label>
                        <input type="number" id="customerAge" min="0" max="120">
                    </div>
                    <div class="form-group">
                        <label for="customerType">客户类型</label>
                        <select id="customerType">
                            <option value="POTENTIAL">潜在客户</option>
                            <option value="ACTIVE">活跃客户</option>
                            <option value="ADOPTED">已购买</option>
                            <option value="VIP">VIP客户</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label for="customerAddress">地址</label>
                    <textarea id="customerAddress" placeholder="客户详细地址..."></textarea>
                </div>
                <div class="form-group">
                    <label for="customerNotes">备注</label>
                    <textarea id="customerNotes" placeholder="客户相关备注信息..."></textarea>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeCustomerModal()">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080/api';
        let currentCustomers = [];
        let editingCustomerId = null;
        
        // 检查登录状态
        function checkAuth() {
            const token = localStorage.getItem('token');
            const userInfo = localStorage.getItem('userInfo');
            
            if (!token || !userInfo) {
                window.location.href = '/login-test.html';
                return false;
            }
            
            try {
                const user = JSON.parse(userInfo);
                document.getElementById('userName').textContent = user.realName || user.username;
                return true;
            } catch (error) {
                logout();
                return false;
            }
        }
        
        // 退出登录
        function logout() {
            localStorage.removeItem('token');
            localStorage.removeItem('userInfo');
            window.location.href = '/login-test.html';
        }
        
        // 加载客户数据
        async function loadCustomers() {
            const token = localStorage.getItem('token');
            
            try {
                document.getElementById('loadingMessage').style.display = 'block';
                document.getElementById('customersTable').style.display = 'none';
                document.getElementById('emptyState').style.display = 'none';
                
                const response = await fetch(`${API_BASE_URL}/customers`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    currentCustomers = Array.isArray(data.data) ? data.data : [];
                    displayCustomers(currentCustomers);
                } else {
                    throw new Error(data.message || '获取客户数据失败');
                }
            } catch (error) {
                console.error('加载客户数据失败:', error);
                alert('加载客户数据失败: ' + error.message);
            } finally {
                document.getElementById('loadingMessage').style.display = 'none';
            }
        }
        
        // 显示客户列表
        function displayCustomers(customers) {
            const tableBody = document.getElementById('customersTableBody');
            const table = document.getElementById('customersTable');
            const emptyState = document.getElementById('emptyState');
            
            if (customers.length === 0) {
                table.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }
            
            tableBody.innerHTML = customers.map(customer => `
                <tr>
                    <td class="customer-name">${customer.name}</td>
                    <td>${customer.email}</td>
                    <td>${customer.phone || '-'}</td>
                    <td><span class="customer-type ${getTypeClass(customer.customerType)}">${getTypeText(customer.customerType)}</span></td>
                    <td>${formatDate(customer.createdAt)}</td>
                    <td>${formatDate(customer.lastContactDate) || '-'}</td>
                    <td>
                        <div class="actions">
                            <button class="btn-small btn-view" onclick="viewCustomer(${customer.id})">查看</button>
                            <button class="btn-small btn-edit" onclick="editCustomer(${customer.id})">编辑</button>
                            <button class="btn-small btn-delete" onclick="deleteCustomer(${customer.id})">删除</button>
                        </div>
                    </td>
                </tr>
            `).join('');
            
            table.style.display = 'block';
            emptyState.style.display = 'none';
        }
        
        // 获取客户类型样式类
        function getTypeClass(type) {
            switch (type) {
                case 'POTENTIAL': return 'type-potential';
                case 'ACTIVE': return 'type-active';
                case 'ADOPTED': return 'type-adopted';
                case 'VIP': return 'type-vip';
                default: return 'type-potential';
            }
        }
        
        // 获取客户类型文本
        function getTypeText(type) {
            switch (type) {
                case 'POTENTIAL': return '潜在客户';
                case 'ACTIVE': return '活跃客户';
                case 'ADOPTED': return '已购买';
                case 'VIP': return 'VIP客户';
                default: return '未知';
            }
        }
        
        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '';
            try {
                return new Date(dateStr).toLocaleDateString('zh-CN');
            } catch (error) {
                return '';
            }
        }
        
        // 应用筛选
        function applyFilters() {
            const searchName = document.getElementById('searchName').value.toLowerCase();
            const filterType = document.getElementById('filterType').value;
            const filterStatus = document.getElementById('filterStatus').value;
            
            const filteredCustomers = currentCustomers.filter(customer => {
                const matchName = !searchName || 
                    customer.name.toLowerCase().includes(searchName) ||
                    customer.email.toLowerCase().includes(searchName);
                const matchType = !filterType || customer.customerType === filterType;
                const matchStatus = !filterStatus || customer.status === filterStatus;
                
                return matchName && matchType && matchStatus;
            });
            
            displayCustomers(filteredCustomers);
        }
        
        // 刷新数据
        function refreshData() {
            loadCustomers();
        }
        
        // 显示添加客户模态框
        function showAddCustomerModal() {
            editingCustomerId = null;
            document.getElementById('modalTitle').textContent = '添加客户';
            document.getElementById('customerForm').reset();
            document.getElementById('customerModal').style.display = 'block';
        }
        
        // 关闭模态框
        function closeCustomerModal() {
            document.getElementById('customerModal').style.display = 'none';
        }
        
        // 查看客户详情
        function viewCustomer(customerId) {
            const customer = currentCustomers.find(c => c.id === customerId);
            if (customer) {
                alert(`客户详情:\n\n姓名: ${customer.name}\n邮箱: ${customer.email}\n电话: ${customer.phone || '未填写'}\n性别: ${customer.gender || '未填写'}\n年龄: ${customer.age || '未填写'}\n类型: ${getTypeText(customer.customerType)}\n地址: ${customer.address || '未填写'}\n备注: ${customer.notes || '无'}`);
            }
        }
        
        // 编辑客户
        function editCustomer(customerId) {
            const customer = currentCustomers.find(c => c.id === customerId);
            if (customer) {
                editingCustomerId = customerId;
                document.getElementById('modalTitle').textContent = '编辑客户';
                
                // 填充表单
                document.getElementById('customerName').value = customer.name || '';
                document.getElementById('customerEmail').value = customer.email || '';
                document.getElementById('customerPhone').value = customer.phone || '';
                document.getElementById('customerGender').value = customer.gender || '';
                document.getElementById('customerAge').value = customer.age || '';
                document.getElementById('customerType').value = customer.customerType || 'POTENTIAL';
                document.getElementById('customerAddress').value = customer.address || '';
                document.getElementById('customerNotes').value = customer.notes || '';
                
                document.getElementById('customerModal').style.display = 'block';
            }
        }
        
        // 删除客户
        function deleteCustomer(customerId) {
            const customer = currentCustomers.find(c => c.id === customerId);
            if (customer && confirm(`确定要删除客户"${customer.name}"吗？此操作不可恢复。`)) {
                // 这里应该调用删除API
                alert('删除功能暂未实现，请联系开发人员');
            }
        }
        
        // 表单提交处理
        document.getElementById('customerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = {
                name: document.getElementById('customerName').value,
                email: document.getElementById('customerEmail').value,
                phone: document.getElementById('customerPhone').value,
                gender: document.getElementById('customerGender').value,
                age: parseInt(document.getElementById('customerAge').value) || null,
                customerType: document.getElementById('customerType').value,
                address: document.getElementById('customerAddress').value,
                notes: document.getElementById('customerNotes').value,
                status: 'ACTIVE'
            };
            
            try {
                const token = localStorage.getItem('token');
                const url = editingCustomerId ? `${API_BASE_URL}/customers/${editingCustomerId}` : `${API_BASE_URL}/customers`;
                const method = editingCustomerId ? 'PUT' : 'POST';
                
                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert(editingCustomerId ? '客户信息更新成功！' : '客户添加成功！');
                    closeCustomerModal();
                    loadCustomers(); // 重新加载数据
                } else {
                    throw new Error(data.message || '操作失败');
                }
            } catch (error) {
                console.error('保存客户信息失败:', error);
                alert('保存失败: ' + error.message);
            }
        });
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            if (checkAuth()) {
                loadCustomers();
            }
            
            // 绑定筛选事件
            document.getElementById('searchName').addEventListener('input', applyFilters);
            document.getElementById('filterType').addEventListener('change', applyFilters);
            document.getElementById('filterStatus').addEventListener('change', applyFilters);
        });
        
        // 点击模态框外部关闭
        document.getElementById('customerModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeCustomerModal();
            }
        });
    </script>
</body>
</html>
