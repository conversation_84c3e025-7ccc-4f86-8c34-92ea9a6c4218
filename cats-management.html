<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>猫咪管理 - 猫舍管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            line-height: 1.6;
        }

        .header {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 0 20px;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-links a {
            color: #666;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .nav-links a:hover, .nav-links a.active {
            background: #667eea;
            color: white;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logout-btn {
            background: #f56565;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .page-title h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 8px;
        }

        .page-title p {
            color: #666;
            font-size: 16px;
        }

        .header-actions {
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-secondary {
            background: white;
            color: #666;
            border: 1px solid #ddd;
        }

        .btn-secondary:hover {
            background: #f8f9fa;
        }

        .filters {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }

        .filters-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-group label {
            color: #333;
            font-weight: 500;
            font-size: 14px;
        }

        .filter-group input, .filter-group select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }

        .cats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .cat-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }

        .cat-card:hover {
            transform: translateY(-2px);
        }

        .cat-image {
            width: 100%;
            height: 200px;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 48px;
        }

        .cat-info {
            padding: 20px;
        }

        .cat-name {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .cat-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin-bottom: 15px;
        }

        .cat-detail {
            font-size: 14px;
            color: #666;
        }

        .cat-detail strong {
            color: #333;
        }

        .cat-status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 15px;
        }

        .status-available {
            background: #d1fae5;
            color: #065f46;
        }

        .status-sold {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-breeding {
            background: #dbeafe;
            color: #1e40af;
        }

        .cat-actions {
            display: flex;
            gap: 10px;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            font-weight: 500;
        }

        .btn-edit {
            background: #f59e0b;
            color: white;
        }

        .btn-view {
            background: #10b981;
            color: white;
        }

        .btn-delete {
            background: #ef4444;
            color: white;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #333;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 12px;
            padding: 30px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .form-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        @media (max-width: 768px) {
            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .header-actions {
                width: 100%;
                justify-content: flex-start;
            }

            .filters-row {
                grid-template-columns: 1fr;
            }

            .cats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">🐱 猫舍管理系统</div>
            <div class="nav-links">
                <a href="/dashboard.html">仪表盘</a>
                <a href="/cats-management.html" class="active">猫咪管理</a>
                <a href="/customers-management.html">客户管理</a>
                <a href="/health-management.html">健康记录</a>
                <a href="/breeding-management.html">繁育管理</a>
            </div>
            <div class="user-info">
                <span id="userName">加载中...</span>
                <button class="logout-btn" onclick="logout()">退出</button>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="page-header">
            <div class="page-title">
                <h1>猫咪管理</h1>
                <p>管理猫舍中的所有猫咪信息</p>
            </div>
            <div class="header-actions">
                <button class="btn btn-secondary" onclick="refreshData()">刷新数据</button>
                <button class="btn btn-primary" onclick="showAddCatModal()">添加猫咪</button>
            </div>
        </div>

        <div class="filters">
            <div class="filters-row">
                <div class="filter-group">
                    <label for="searchName">搜索名称</label>
                    <input type="text" id="searchName" placeholder="输入猫咪名称">
                </div>
                <div class="filter-group">
                    <label for="filterBreed">品种筛选</label>
                    <select id="filterBreed">
                        <option value="">全部品种</option>
                        <option value="英国短毛猫">英国短毛猫</option>
                        <option value="美国短毛猫">美国短毛猫</option>
                        <option value="布偶猫">布偶猫</option>
                        <option value="波斯猫">波斯猫</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="filterStatus">状态筛选</label>
                    <select id="filterStatus">
                        <option value="">全部状态</option>
                        <option value="AVAILABLE">可售</option>
                        <option value="SOLD">已售</option>
                        <option value="BREEDING">繁育中</option>
                        <option value="MEDICAL">医疗中</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="filterGender">性别筛选</label>
                    <select id="filterGender">
                        <option value="">全部性别</option>
                        <option value="MALE">雄性</option>
                        <option value="FEMALE">雌性</option>
                    </select>
                </div>
                <div class="filter-group">
                    <button class="btn btn-primary" onclick="applyFilters()">应用筛选</button>
                </div>
            </div>
        </div>

        <div id="loadingMessage" class="loading">正在加载猫咪数据...</div>
        
        <div id="catsContainer" class="cats-grid" style="display: none;"></div>
        
        <div id="emptyState" class="empty-state" style="display: none;">
            <h3>暂无猫咪数据</h3>
            <p>点击"添加猫咪"按钮开始添加第一只猫咪</p>
        </div>
    </div>

    <!-- 添加/编辑猫咪模态框 -->
    <div id="catModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">添加猫咪</h3>
                <button class="close-btn" onclick="closeCatModal()">&times;</button>
            </div>
            <form id="catForm">
                <div class="form-group">
                    <label for="catName">猫咪名称 *</label>
                    <input type="text" id="catName" required>
                </div>
                <div class="form-group">
                    <label for="catBreed">品种 *</label>
                    <select id="catBreed" required>
                        <option value="">请选择品种</option>
                        <option value="英国短毛猫">英国短毛猫</option>
                        <option value="美国短毛猫">美国短毛猫</option>
                        <option value="布偶猫">布偶猫</option>
                        <option value="波斯猫">波斯猫</option>
                        <option value="其他">其他</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="catGender">性别 *</label>
                    <select id="catGender" required>
                        <option value="">请选择性别</option>
                        <option value="MALE">雄性</option>
                        <option value="FEMALE">雌性</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="catBirthDate">出生日期</label>
                    <input type="date" id="catBirthDate">
                </div>
                <div class="form-group">
                    <label for="catColor">毛色</label>
                    <input type="text" id="catColor" placeholder="如：银渐层、蓝白等">
                </div>
                <div class="form-group">
                    <label for="catWeight">体重 (kg)</label>
                    <input type="number" id="catWeight" step="0.1" min="0">
                </div>
                <div class="form-group">
                    <label for="catPrice">价格 (元)</label>
                    <input type="number" id="catPrice" step="0.01" min="0">
                </div>
                <div class="form-group">
                    <label for="catStatus">状态</label>
                    <select id="catStatus">
                        <option value="AVAILABLE">可售</option>
                        <option value="RESERVED">预定</option>
                        <option value="SOLD">已售</option>
                        <option value="BREEDING">繁育中</option>
                        <option value="MEDICAL">医疗中</option>
                        <option value="RETIRED">退役</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="catDescription">描述</label>
                    <textarea id="catDescription" placeholder="猫咪的详细描述..."></textarea>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeCatModal()">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080/api';
        let currentCats = [];
        let editingCatId = null;
        
        // 检查登录状态
        function checkAuth() {
            const token = localStorage.getItem('token');
            const userInfo = localStorage.getItem('userInfo');
            
            if (!token || !userInfo) {
                window.location.href = '/login-test.html';
                return false;
            }
            
            try {
                const user = JSON.parse(userInfo);
                document.getElementById('userName').textContent = user.realName || user.username;
                return true;
            } catch (error) {
                logout();
                return false;
            }
        }
        
        // 退出登录
        function logout() {
            localStorage.removeItem('token');
            localStorage.removeItem('userInfo');
            window.location.href = '/login-test.html';
        }
        
        // 加载猫咪数据
        async function loadCats() {
            const token = localStorage.getItem('token');
            
            try {
                document.getElementById('loadingMessage').style.display = 'block';
                document.getElementById('catsContainer').style.display = 'none';
                document.getElementById('emptyState').style.display = 'none';
                
                const response = await fetch(`${API_BASE_URL}/cats`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    currentCats = Array.isArray(data.data) ? data.data : [];
                    displayCats(currentCats);
                } else {
                    throw new Error(data.message || '获取猫咪数据失败');
                }
            } catch (error) {
                console.error('加载猫咪数据失败:', error);
                alert('加载猫咪数据失败: ' + error.message);
            } finally {
                document.getElementById('loadingMessage').style.display = 'none';
            }
        }
        
        // 显示猫咪列表
        function displayCats(cats) {
            const container = document.getElementById('catsContainer');
            const emptyState = document.getElementById('emptyState');
            
            if (cats.length === 0) {
                container.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }
            
            container.innerHTML = cats.map(cat => `
                <div class="cat-card">
                    <div class="cat-image">🐱</div>
                    <div class="cat-info">
                        <div class="cat-name">${cat.name}</div>
                        <div class="cat-details">
                            <div class="cat-detail"><strong>品种:</strong> ${cat.breed || '未知'}</div>
                            <div class="cat-detail"><strong>性别:</strong> ${cat.gender === 'MALE' ? '雄性' : '雌性'}</div>
                            <div class="cat-detail"><strong>年龄:</strong> ${calculateAge(cat.birthDate)}</div>
                            <div class="cat-detail"><strong>体重:</strong> ${cat.weight || '未知'}kg</div>
                        </div>
                        <div class="cat-status ${getStatusClass(cat.status)}">${getStatusText(cat.status)}</div>
                        <div class="cat-actions">
                            <button class="btn-small btn-view" onclick="viewCat(${cat.id})">查看</button>
                            <button class="btn-small btn-edit" onclick="editCat(${cat.id})">编辑</button>
                            <button class="btn-small btn-delete" onclick="deleteCat(${cat.id})">删除</button>
                        </div>
                    </div>
                </div>
            `).join('');
            
            container.style.display = 'grid';
            emptyState.style.display = 'none';
        }
        
        // 计算年龄
        function calculateAge(birthDate) {
            if (!birthDate) return '未知';
            
            const birth = new Date(birthDate);
            const now = new Date();
            const diffTime = Math.abs(now - birth);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            
            if (diffDays < 30) {
                return `${diffDays}天`;
            } else if (diffDays < 365) {
                return `${Math.floor(diffDays / 30)}个月`;
            } else {
                return `${Math.floor(diffDays / 365)}岁`;
            }
        }
        
        // 获取状态样式类
        function getStatusClass(status) {
            switch (status) {
                case 'AVAILABLE': return 'status-available';
                case 'SOLD': return 'status-sold';
                case 'BREEDING': return 'status-breeding';
                default: return 'status-available';
            }
        }
        
        // 获取状态文本
        function getStatusText(status) {
            switch (status) {
                case 'AVAILABLE': return '可售';
                case 'RESERVED': return '预定';
                case 'SOLD': return '已售';
                case 'BREEDING': return '繁育中';
                case 'MEDICAL': return '医疗中';
                case 'RETIRED': return '退役';
                default: return '未知';
            }
        }
        
        // 应用筛选
        function applyFilters() {
            const searchName = document.getElementById('searchName').value.toLowerCase();
            const filterBreed = document.getElementById('filterBreed').value;
            const filterStatus = document.getElementById('filterStatus').value;
            const filterGender = document.getElementById('filterGender').value;
            
            const filteredCats = currentCats.filter(cat => {
                const matchName = !searchName || cat.name.toLowerCase().includes(searchName);
                const matchBreed = !filterBreed || cat.breed === filterBreed;
                const matchStatus = !filterStatus || cat.status === filterStatus;
                const matchGender = !filterGender || cat.gender === filterGender;
                
                return matchName && matchBreed && matchStatus && matchGender;
            });
            
            displayCats(filteredCats);
        }
        
        // 刷新数据
        function refreshData() {
            loadCats();
        }
        
        // 显示添加猫咪模态框
        function showAddCatModal() {
            editingCatId = null;
            document.getElementById('modalTitle').textContent = '添加猫咪';
            document.getElementById('catForm').reset();
            document.getElementById('catModal').style.display = 'block';
        }
        
        // 关闭模态框
        function closeCatModal() {
            document.getElementById('catModal').style.display = 'none';
        }
        
        // 查看猫咪详情
        function viewCat(catId) {
            const cat = currentCats.find(c => c.id === catId);
            if (cat) {
                alert(`猫咪详情:\n\n名称: ${cat.name}\n品种: ${cat.breed || '未知'}\n性别: ${cat.gender === 'MALE' ? '雄性' : '雌性'}\n出生日期: ${cat.birthDate || '未知'}\n体重: ${cat.weight || '未知'}kg\n价格: ¥${cat.price || '未定价'}\n状态: ${getStatusText(cat.status)}\n描述: ${cat.description || '无'}`);
            }
        }
        
        // 编辑猫咪
        function editCat(catId) {
            const cat = currentCats.find(c => c.id === catId);
            if (cat) {
                editingCatId = catId;
                document.getElementById('modalTitle').textContent = '编辑猫咪';
                
                // 填充表单
                document.getElementById('catName').value = cat.name || '';
                document.getElementById('catBreed').value = cat.breed || '';
                document.getElementById('catGender').value = cat.gender || '';
                document.getElementById('catBirthDate').value = cat.birthDate || '';
                document.getElementById('catColor').value = cat.color || '';
                document.getElementById('catWeight').value = cat.weight || '';
                document.getElementById('catPrice').value = cat.price || '';
                document.getElementById('catStatus').value = cat.status || 'AVAILABLE';
                document.getElementById('catDescription').value = cat.description || '';
                
                document.getElementById('catModal').style.display = 'block';
            }
        }
        
        // 删除猫咪
        function deleteCat(catId) {
            const cat = currentCats.find(c => c.id === catId);
            if (cat && confirm(`确定要删除猫咪"${cat.name}"吗？此操作不可恢复。`)) {
                // 这里应该调用删除API
                alert('删除功能暂未实现，请联系开发人员');
            }
        }
        
        // 表单提交处理
        document.getElementById('catForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = {
                name: document.getElementById('catName').value,
                breed: document.getElementById('catBreed').value,
                gender: document.getElementById('catGender').value,
                birthDate: document.getElementById('catBirthDate').value,
                color: document.getElementById('catColor').value,
                weight: parseFloat(document.getElementById('catWeight').value) || null,
                price: parseFloat(document.getElementById('catPrice').value) || null,
                status: document.getElementById('catStatus').value,
                description: document.getElementById('catDescription').value
            };
            
            try {
                const token = localStorage.getItem('token');
                const url = editingCatId ? `${API_BASE_URL}/cats/${editingCatId}` : `${API_BASE_URL}/cats`;
                const method = editingCatId ? 'PUT' : 'POST';
                
                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert(editingCatId ? '猫咪信息更新成功！' : '猫咪添加成功！');
                    closeCatModal();
                    loadCats(); // 重新加载数据
                } else {
                    throw new Error(data.message || '操作失败');
                }
            } catch (error) {
                console.error('保存猫咪信息失败:', error);
                alert('保存失败: ' + error.message);
            }
        });
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            if (checkAuth()) {
                loadCats();
            }
            
            // 绑定筛选事件
            document.getElementById('searchName').addEventListener('input', applyFilters);
            document.getElementById('filterBreed').addEventListener('change', applyFilters);
            document.getElementById('filterStatus').addEventListener('change', applyFilters);
            document.getElementById('filterGender').addEventListener('change', applyFilters);
        });
        
        // 点击模态框外部关闭
        document.getElementById('catModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeCatModal();
            }
        });
    </script>
</body>
</html>
