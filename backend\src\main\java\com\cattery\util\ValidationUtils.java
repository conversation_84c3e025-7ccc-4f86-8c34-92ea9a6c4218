package com.cattery.util;

import com.cattery.exception.BusinessException;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.regex.Pattern;

/**
 * 验证工具类
 */
public class ValidationUtils {

    private static final Pattern SAFE_FILENAME_PATTERN = Pattern.compile("^[a-zA-Z0-9._\\-\\s()\\[\\]]+$");
    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[A-Za-z0-9+_.-]+@([A-Za-z0-9.-]+\\.[A-Za-z]{2,})$");
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");
    private static final Pattern USERNAME_PATTERN = Pattern.compile("^[a-zA-Z0-9_]{3,50}$");

    /**
     * 验证文件是否为空
     */
    public static void validateFileNotEmpty(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException("文件不能为空");
        }
    }

    /**
     * 验证文件大小
     */
    public static void validateFileSize(MultipartFile file, long maxSize) {
        if (file.getSize() > maxSize) {
            throw new BusinessException("文件大小超过限制，最大允许: " + formatFileSize(maxSize));
        }
    }

    /**
     * 验证文件类型
     */
    public static void validateFileType(MultipartFile file, String[] allowedTypes) {
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            throw new BusinessException("文件名不能为空");
        }

        String extension = getFileExtension(originalFilename).toLowerCase();
        if (!Arrays.asList(allowedTypes).contains(extension)) {
            throw new BusinessException("不支持的文件类型: " + extension + 
                    "，支持的类型: " + String.join(", ", allowedTypes));
        }
    }

    /**
     * 验证图片文件
     */
    public static void validateImageFile(MultipartFile file) {
        validateFileNotEmpty(file);
        validateFileSize(file, 10 * 1024 * 1024); // 10MB
        validateFileType(file, new String[]{"jpg", "jpeg", "png", "gif", "bmp", "webp"});
    }

    /**
     * 验证文档文件
     */
    public static void validateDocumentFile(MultipartFile file) {
        validateFileNotEmpty(file);
        validateFileSize(file, 50 * 1024 * 1024); // 50MB
        validateFileType(file, new String[]{"pdf", "doc", "docx", "xls", "xlsx", "txt"});
    }

    /**
     * 验证邮箱格式
     */
    public static void validateEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            throw new BusinessException("邮箱不能为空");
        }
        if (!EMAIL_PATTERN.matcher(email).matches()) {
            throw new BusinessException("邮箱格式不正确");
        }
    }

    /**
     * 验证手机号格式
     */
    public static void validatePhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return; // 手机号可以为空
        }
        if (!PHONE_PATTERN.matcher(phone).matches()) {
            throw new BusinessException("手机号格式不正确");
        }
    }

    /**
     * 验证用户名格式
     */
    public static void validateUsername(String username) {
        if (username == null || username.trim().isEmpty()) {
            throw new BusinessException("用户名不能为空");
        }
        if (!USERNAME_PATTERN.matcher(username).matches()) {
            throw new BusinessException("用户名只能包含字母、数字和下划线，长度3-50个字符");
        }
    }

    /**
     * 验证密码强度
     */
    public static void validatePassword(String password) {
        if (password == null || password.trim().isEmpty()) {
            throw new BusinessException("密码不能为空");
        }
        if (password.length() < 6) {
            throw new BusinessException("密码长度不能少于6个字符");
        }
        if (password.length() > 100) {
            throw new BusinessException("密码长度不能超过100个字符");
        }
    }

    /**
     * 验证字符串长度
     */
    public static void validateStringLength(String value, String fieldName, int minLength, int maxLength) {
        if (value == null) {
            throw new BusinessException(fieldName + "不能为空");
        }
        if (value.length() < minLength) {
            throw new BusinessException(fieldName + "长度不能少于" + minLength + "个字符");
        }
        if (value.length() > maxLength) {
            throw new BusinessException(fieldName + "长度不能超过" + maxLength + "个字符");
        }
    }

    /**
     * 验证数值范围
     */
    public static void validateNumberRange(Number value, String fieldName, Number min, Number max) {
        if (value == null) {
            throw new BusinessException(fieldName + "不能为空");
        }
        if (value.doubleValue() < min.doubleValue()) {
            throw new BusinessException(fieldName + "不能小于" + min);
        }
        if (value.doubleValue() > max.doubleValue()) {
            throw new BusinessException(fieldName + "不能大于" + max);
        }
    }

    /**
     * 验证文件名安全性
     */
    public static void validateFilename(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            throw new BusinessException("文件名不能为空");
        }
        
        // 检查危险字符
        if (filename.contains("..") || filename.contains("/") || filename.contains("\\")) {
            throw new BusinessException("文件名包含非法字符");
        }
        
        if (!SAFE_FILENAME_PATTERN.matcher(filename).matches()) {
            throw new BusinessException("文件名格式不正确");
        }
    }

    /**
     * 获取文件扩展名
     */
    public static String getFileExtension(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == filename.length() - 1) {
            return "";
        }
        return filename.substring(lastDotIndex + 1);
    }

    /**
     * 格式化文件大小
     */
    public static String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", size / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 清理文件名
     */
    public static String sanitizeFilename(String filename) {
        if (filename == null) {
            return "";
        }
        
        // 移除路径分隔符和其他危险字符
        String cleaned = filename.replaceAll("[/\\\\:*?\"<>|]", "_");
        
        // 移除连续的点
        cleaned = cleaned.replaceAll("\\.{2,}", ".");
        
        // 确保不以点开头或结尾
        cleaned = cleaned.replaceAll("^\\.|\\.$", "");
        
        return cleaned.trim();
    }

    /**
     * 验证ID是否有效
     */
    public static void validateId(Long id, String entityName) {
        if (id == null || id <= 0) {
            throw new BusinessException(entityName + "ID无效");
        }
    }

    /**
     * 验证分页参数
     */
    public static void validatePageParams(int page, int size) {
        if (page < 0) {
            throw new BusinessException("页码不能小于0");
        }
        if (size <= 0) {
            throw new BusinessException("页面大小必须大于0");
        }
        if (size > 1000) {
            throw new BusinessException("页面大小不能超过1000");
        }
    }
}
