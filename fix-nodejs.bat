@echo off
echo ========================================
echo Node.js 环境修复脚本
echo ========================================
echo.

echo 1. 检查当前Node.js安装状态...
echo.

echo 当前Node.js版本:
node --version 2>nul
if %errorlevel% neq 0 (
    echo [错误] Node.js 未正确安装或PATH配置有问题
) else (
    echo [成功] Node.js 已安装
)
echo.

echo 当前npm版本:
npm --version 2>nul
if %errorlevel% neq 0 (
    echo [错误] npm 未正确安装或已损坏
) else (
    echo [成功] npm 已安装
)
echo.

echo 2. 检查Node.js安装路径...
where node 2>nul
if %errorlevel% neq 0 (
    echo [错误] 无法找到node.exe路径
) else (
    echo [信息] 找到Node.js安装路径
)
echo.

echo 3. 检查npm安装路径...
where npm 2>nul
if %errorlevel% neq 0 (
    echo [错误] 无法找到npm路径
) else (
    echo [信息] 找到npm路径
)
echo.

echo ========================================
echo 修复建议:
echo ========================================
echo.
echo 如果上述检查发现问题，请按以下步骤修复：
echo.
echo 方案1: 重新安装Node.js (推荐)
echo   1. 访问 https://nodejs.org/
echo   2. 下载最新LTS版本 (推荐v18.x或v20.x)
echo   3. 卸载当前Node.js版本
echo   4. 安装新版本到默认路径 (如: C:\Program Files\nodejs\)
echo   5. 重启命令行窗口
echo.
echo 方案2: 修复当前安装
echo   1. 以管理员身份运行命令行
echo   2. 执行: npm install -g npm@latest
echo   3. 如果失败，尝试: npm cache clean --force
echo.
echo 方案3: 使用yarn替代npm
echo   1. 安装yarn: npm install -g yarn
echo   2. 在项目中使用yarn代替npm命令
echo.

echo ========================================
echo 环境变量检查:
echo ========================================
echo.
echo PATH环境变量中的Node.js相关路径:
echo %PATH% | findstr /i node
echo.

echo ========================================
echo 项目依赖安装指南:
echo ========================================
echo.
echo 修复Node.js后，在frontend目录执行以下命令:
echo.
echo   cd frontend
echo   npm install          # 安装依赖
echo   npm run dev          # 启动开发服务器
echo.
echo 或者使用yarn:
echo   cd frontend  
echo   yarn install         # 安装依赖
echo   yarn dev             # 启动开发服务器
echo.

echo ========================================
echo 常见问题解决:
echo ========================================
echo.
echo 问题1: 权限错误
echo   解决: 以管理员身份运行命令行
echo.
echo 问题2: 网络问题
echo   解决: 设置npm镜像源
echo   npm config set registry https://registry.npmmirror.com/
echo.
echo 问题3: 缓存问题  
echo   解决: 清理npm缓存
echo   npm cache clean --force
echo.
echo 问题4: 版本冲突
echo   解决: 删除node_modules文件夹后重新安装
echo   rmdir /s node_modules
echo   npm install
echo.

pause
