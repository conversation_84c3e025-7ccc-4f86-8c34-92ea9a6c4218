package com.cattery.repository;

import com.cattery.entity.AdoptionRecord;
import com.cattery.entity.Cat;
import com.cattery.entity.Customer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 领养记录仓库接口
 */
@Repository
public interface AdoptionRecordRepository extends JpaRepository<AdoptionRecord, Long>, JpaSpecificationExecutor<AdoptionRecord> {

    /**
     * 根据猫咪查找领养记录
     */
    Optional<AdoptionRecord> findByCat(Cat cat);

    /**
     * 根据客户查找领养记录
     */
    List<AdoptionRecord> findByCustomer(Customer customer);

    /**
     * 根据领养日期范围查找记录
     */
    List<AdoptionRecord> findByAdoptionDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 根据状态查找记录
     */
    List<AdoptionRecord> findByAdoptionStatus(AdoptionRecord.Status status);

    /**
     * 查找成功的领养记录
     */
    @Query("SELECT ar FROM AdoptionRecord ar WHERE ar.adoptionStatus = 'COMPLETED'")
    List<AdoptionRecord> findSuccessfulAdoptions();

    /**
     * 查找待处理的领养申请
     */
    @Query("SELECT ar FROM AdoptionRecord ar WHERE ar.adoptionStatus = 'PENDING'")
    List<AdoptionRecord> findPendingAdoptions();

    /**
     * 统计指定时间范围内的领养数量
     */
    long countByAdoptionDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 统计成功领养数量
     */
    @Query("SELECT COUNT(ar) FROM AdoptionRecord ar WHERE ar.adoptionStatus = 'COMPLETED'")
    long countSuccessfulAdoptions();

    /**
     * 按状态统计数量
     */
    @Query("SELECT ar.adoptionStatus, COUNT(ar) FROM AdoptionRecord ar GROUP BY ar.adoptionStatus")
    List<Object[]> countByStatusGrouped();

    /**
     * 根据客户ID查找领养记录
     */
    List<AdoptionRecord> findByCustomerId(Long customerId);

    /**
     * 根据客户ID按日期排序查找领养记录
     */
    List<AdoptionRecord> findByCustomerIdOrderByAdoptionDateDesc(Long customerId);


}
