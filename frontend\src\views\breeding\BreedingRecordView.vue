<template>
  <div class="breeding-record-view">
    <div class="page-header">
      <h1>繁育记录</h1>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        新增繁育记录
      </el-button>
    </div>

    <div class="search-bar">
      <el-form :model="searchForm" inline>
        <el-form-item label="母猫">
          <el-select v-model="searchForm.motherId" placeholder="选择母猫" clearable>
            <el-option
              v-for="cat in femaleCats"
              :key="cat.id"
              :label="cat.name"
              :value="cat.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="公猫">
          <el-select v-model="searchForm.fatherId" placeholder="选择公猫" clearable>
            <el-option
              v-for="cat in maleCats"
              :key="cat.id"
              :label="cat.name"
              :value="cat.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
            <el-option label="计划中" value="PLANNED" />
            <el-option label="进行中" value="IN_PROGRESS" />
            <el-option label="成功" value="SUCCESS" />
            <el-option label="失败" value="FAILED" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchRecords">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table :data="breedingRecords" v-loading="loading">
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column label="母猫">
        <template #default="{ row }">
          <div class="cat-info">
            <img v-if="row.mother?.primaryPhoto" :src="row.mother.primaryPhoto" class="cat-avatar" />
            <span>{{ row.mother?.name }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="公猫">
        <template #default="{ row }">
          <div class="cat-info">
            <img v-if="row.father?.primaryPhoto" :src="row.father.primaryPhoto" class="cat-avatar" />
            <span>{{ row.father?.name }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="matingDate" label="配种日期" />
      <el-table-column prop="expectedBirthDate" label="预产期" />
      <el-table-column label="状态">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="litterSize" label="产仔数" />
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button size="small" @click="viewRecord(row)">查看</el-button>
          <el-button size="small" type="primary" @click="editRecord(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="deleteRecord(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingRecord ? '编辑繁育记录' : '新增繁育记录'"
      width="600px"
    >
      <el-form :model="recordForm" :rules="formRules" ref="formRef" label-width="100px">
        <el-form-item label="母猫" prop="motherId">
          <el-select v-model="recordForm.motherId" placeholder="选择母猫">
            <el-option
              v-for="cat in femaleCats"
              :key="cat.id"
              :label="cat.name"
              :value="cat.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="公猫" prop="fatherId">
          <el-select v-model="recordForm.fatherId" placeholder="选择公猫">
            <el-option
              v-for="cat in maleCats"
              :key="cat.id"
              :label="cat.name"
              :value="cat.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="配种日期" prop="matingDate">
          <el-date-picker
            v-model="recordForm.matingDate"
            type="date"
            placeholder="选择配种日期"
          />
        </el-form-item>
        <el-form-item label="预产期" prop="expectedBirthDate">
          <el-date-picker
            v-model="recordForm.expectedBirthDate"
            type="date"
            placeholder="选择预产期"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="recordForm.status" placeholder="选择状态">
            <el-option label="计划中" value="PLANNED" />
            <el-option label="进行中" value="IN_PROGRESS" />
            <el-option label="成功" value="SUCCESS" />
            <el-option label="失败" value="FAILED" />
          </el-select>
        </el-form-item>
        <el-form-item label="产仔数" prop="litterSize">
          <el-input-number v-model="recordForm.litterSize" :min="0" :max="20" />
        </el-form-item>
        <el-form-item label="备注" prop="notes">
          <el-input
            v-model="recordForm.notes"
            type="textarea"
            :rows="3"
            placeholder="输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveRecord">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const showCreateDialog = ref(false)
const editingRecord = ref(null)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

const breedingRecords = ref([])
const femaleCats = ref([])
const maleCats = ref([])

const searchForm = reactive({
  motherId: '',
  fatherId: '',
  status: ''
})

const recordForm = reactive({
  motherId: '',
  fatherId: '',
  matingDate: '',
  expectedBirthDate: '',
  status: 'PLANNED',
  litterSize: 0,
  notes: ''
})

const formRules = {
  motherId: [{ required: true, message: '请选择母猫', trigger: 'change' }],
  fatherId: [{ required: true, message: '请选择公猫', trigger: 'change' }],
  matingDate: [{ required: true, message: '请选择配种日期', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

// 方法
const loadBreedingRecords = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    breedingRecords.value = [
      {
        id: 1,
        mother: { id: 1, name: '美美', primaryPhoto: '/images/cat1.jpg' },
        father: { id: 2, name: '帅帅', primaryPhoto: '/images/cat2.jpg' },
        matingDate: '2024-01-15',
        expectedBirthDate: '2024-03-20',
        status: 'SUCCESS',
        litterSize: 4,
        notes: '配种成功，母猫状态良好'
      }
    ]
    total.value = 1
  } catch (error) {
    ElMessage.error('加载繁育记录失败')
  } finally {
    loading.value = false
  }
}

const loadCats = async () => {
  try {
    // 模拟API调用
    femaleCats.value = [
      { id: 1, name: '美美' },
      { id: 3, name: '花花' }
    ]
    maleCats.value = [
      { id: 2, name: '帅帅' },
      { id: 4, name: '强强' }
    ]
  } catch (error) {
    ElMessage.error('加载猫咪列表失败')
  }
}

const searchRecords = () => {
  currentPage.value = 1
  loadBreedingRecords()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    motherId: '',
    fatherId: '',
    status: ''
  })
  searchRecords()
}

const viewRecord = (record: any) => {
  ElMessage.info('查看繁育记录功能开发中')
}

const editRecord = (record: any) => {
  editingRecord.value = record
  Object.assign(recordForm, record)
  showCreateDialog.value = true
}

const deleteRecord = async (record: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这条繁育记录吗？', '确认删除', {
      type: 'warning'
    })
    ElMessage.success('删除成功')
    loadBreedingRecords()
  } catch {
    // 用户取消删除
  }
}

const saveRecord = () => {
  ElMessage.success('保存成功')
  showCreateDialog.value = false
  loadBreedingRecords()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  loadBreedingRecords()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadBreedingRecords()
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    PLANNED: '',
    IN_PROGRESS: 'warning',
    SUCCESS: 'success',
    FAILED: 'danger'
  }
  return types[status] || ''
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    PLANNED: '计划中',
    IN_PROGRESS: '进行中',
    SUCCESS: '成功',
    FAILED: '失败'
  }
  return texts[status] || status
}

onMounted(() => {
  loadBreedingRecords()
  loadCats()
})
</script>

<style scoped>
.breeding-record-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-bar {
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 4px;
}

.cat-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.cat-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
