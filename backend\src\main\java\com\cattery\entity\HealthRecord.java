package com.cattery.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "health_records")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public class HealthRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_id", nullable = false)
    private Cat cat;
    
    @Column(name = "record_date", nullable = false)
    private LocalDate recordDate;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "record_type", nullable = false)
    private RecordType recordType;
    
    @Column(length = 200)
    private String title;
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    @Column(length = 100)
    private String veterinarian;
    
    @Column(length = 200)
    private String clinic;
    
    @Column(precision = 10, scale = 2)
    private BigDecimal cost;
    
    @Column(name = "next_appointment")
    private LocalDate nextAppointment;
    
    @Column(columnDefinition = "TEXT")
    private String attachments;
    
    @CreatedDate
    @Column(name = "created_time", nullable = false, updatable = false)
    private LocalDateTime createdTime;
    
    public enum RecordType {
        VACCINATION,  // 疫苗接种
        CHECKUP,      // 健康检查
        TREATMENT,    // 治疗
        SURGERY,      // 手术
        EMERGENCY,    // 急诊
        ROUTINE       // 常规护理
    }
}

