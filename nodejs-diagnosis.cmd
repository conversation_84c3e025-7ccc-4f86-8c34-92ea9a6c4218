@echo off
chcp 65001 >nul
echo ========================================
echo Node.js 环境详细诊断报告
echo ========================================
echo.

echo 📋 基本信息检查
echo ----------------------------------------
echo.

echo 1. 检查Node.js版本:
node --version 2>nul
if %errorlevel% equ 0 (
    echo ✅ Node.js 可以执行
) else (
    echo ❌ Node.js 无法执行，错误代码: %errorlevel%
)
echo.

echo 2. 检查npm版本:
npm --version 2>nul
if %errorlevel% equ 0 (
    echo ✅ npm 可以执行
) else (
    echo ❌ npm 无法执行，错误代码: %errorlevel%
)
echo.

echo 📂 安装路径检查
echo ----------------------------------------
echo.

echo 3. Node.js 安装路径:
where node 2>nul
if %errorlevel% equ 0 (
    echo ✅ 找到Node.js路径
) else (
    echo ❌ 未找到Node.js路径
)
echo.

echo 4. npm 安装路径:
where npm 2>nul
if %errorlevel% equ 0 (
    echo ✅ 找到npm路径
) else (
    echo ❌ 未找到npm路径
)
echo.

echo 🔧 环境变量检查
echo ----------------------------------------
echo.

echo 5. PATH环境变量中的Node.js路径:
echo %PATH% | findstr /i node
if %errorlevel% equ 0 (
    echo ✅ PATH中包含Node.js路径
) else (
    echo ❌ PATH中未找到Node.js路径
)
echo.

echo 6. NODE_PATH环境变量:
if defined NODE_PATH (
    echo NODE_PATH = %NODE_PATH%
) else (
    echo ℹ️ NODE_PATH 未设置 (这是正常的)
)
echo.

echo 📁 目录结构检查
echo ----------------------------------------
echo.

echo 7. 检查常见Node.js安装目录:
echo.

if exist "C:\Program Files\nodejs\" (
    echo ✅ 找到: C:\Program Files\nodejs\
    dir "C:\Program Files\nodejs\" /b | findstr /i "node npm"
) else (
    echo ❌ 未找到: C:\Program Files\nodejs\
)
echo.

if exist "C:\Program Files (x86)\nodejs\" (
    echo ✅ 找到: C:\Program Files (x86)\nodejs\
    dir "C:\Program Files (x86)\nodejs\" /b | findstr /i "node npm"
) else (
    echo ❌ 未找到: C:\Program Files (x86)\nodejs\
)
echo.

if exist "D:\软件\nodejs\" (
    echo ✅ 找到: D:\软件\nodejs\
    dir "D:\软件\nodejs\" /b | findstr /i "node npm"
) else (
    echo ❌ 未找到: D:\软件\nodejs\
)
echo.

if exist "D:\软件\node_modules\" (
    echo ⚠️ 找到异常目录: D:\软件\node_modules\
    echo 这可能是问题的根源！
) else (
    echo ✅ 未找到异常目录: D:\软件\node_modules\
)
echo.

echo 🔍 npm配置检查
echo ----------------------------------------
echo.

echo 8. npm配置信息:
npm config list 2>nul
if %errorlevel% equ 0 (
    echo ✅ npm配置正常
) else (
    echo ❌ npm配置有问题
)
echo.

echo 9. npm全局安装路径:
npm config get prefix 2>nul
if %errorlevel% equ 0 (
    echo ✅ 获取到npm全局路径
) else (
    echo ❌ 无法获取npm全局路径
)
echo.

echo 🧪 功能测试
echo ----------------------------------------
echo.

echo 10. 测试npm基本功能:
npm help 2>nul >nul
if %errorlevel% equ 0 (
    echo ✅ npm help 命令正常
) else (
    echo ❌ npm help 命令失败
)
echo.

echo 11. 测试npm缓存:
npm cache verify 2>nul >nul
if %errorlevel% equ 0 (
    echo ✅ npm缓存正常
) else (
    echo ❌ npm缓存有问题
)
echo.

echo 📊 诊断总结
echo ========================================
echo.

echo 根据以上检查结果，可能的问题包括:
echo.
echo 1. 如果Node.js版本正常但npm失败:
echo    - npm核心文件损坏或缺失
echo    - 建议重新安装npm: npm install -g npm@latest
echo.
echo 2. 如果发现D:\软件\node_modules\目录:
echo    - 这是异常的安装位置
echo    - 建议删除此目录并重新安装Node.js
echo.
echo 3. 如果PATH环境变量有问题:
echo    - 需要手动添加Node.js路径到PATH
echo    - 或者重新安装Node.js并勾选"Add to PATH"
echo.
echo 4. 如果完全无法执行:
echo    - 建议完全卸载后重新安装Node.js
echo    - 安装到标准路径: C:\Program Files\nodejs\
echo.

echo ========================================
echo 诊断完成！请根据上述结果进行修复。
echo ========================================
echo.

pause
