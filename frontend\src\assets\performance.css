/* 性能优化CSS - 避免布局抖动和重绘 */

/* ✅ 推荐动画属性（只触发合成） */
.performance-friendly {
  transform: translateX(0); /* 位移 */
  opacity: 1; /* 透明度 */
  filter: blur(0); /* 滤镜 */

  will-change: transform, opacity; /* 提前告知浏览器 */
  transition: transform 0.3s ease, opacity 0.3s ease;
}

/* 高性能动画基类 */
.performance-optimized {
  /* 启用GPU加速 */
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* ❌ 避免动画这些属性 */
.avoid-animating {
  /* 触发布局：left, top, width, height, margin, padding */
  /* 触发绘制：color, background, border, box-shadow */
}

/* 高性能动画 - 避免使用触发布局的属性 */

/* ❌ 性能差 - 触发布局
@keyframes slideInBad {
  from { left: -100px; }
  to { left: 0; }
}
*/

/* ✅ 性能好 - 只触发合成 */
@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes scaleOut {
  from {
    transform: scale(1);
    opacity: 1;
  }
  to {
    transform: scale(0.8);
    opacity: 0;
  }
}

/* 高性能动画类 */
.slide-in-left {
  animation: slideInLeft 0.3s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

.slide-in-up {
  animation: slideInUp 0.3s ease-out;
}

.slide-in-down {
  animation: slideInDown 0.3s ease-out;
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.fade-out {
  animation: fadeOut 0.3s ease-in;
}

.scale-in {
  animation: scaleIn 0.3s ease-out;
}

.scale-out {
  animation: scaleOut 0.3s ease-in;
}

/* 过渡效果优化 */
.smooth-transition {
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.fast-transition {
  transition: transform 0.15s ease, opacity 0.15s ease;
}

/* 避免重绘的hover效果 */
.hover-scale {
  transition: transform 0.2s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-lift {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 滚动性能优化 */
.scroll-container {
  /* 启用硬件加速滚动 */
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
  
  /* 优化滚动性能 */
  scroll-behavior: smooth;
  
  /* 避免滚动时的重绘 */
  will-change: scroll-position;
}

/* 图片加载优化 */
.lazy-image {
  /* 避免布局抖动 */
  width: 100%;
  height: auto;
  
  /* 平滑加载 */
  transition: opacity 0.3s ease;
  opacity: 0;
}

.lazy-image.loaded {
  opacity: 1;
}

/* 表格性能优化 */
.performance-table {
  /* 固定表格布局，提高渲染性能 */
  table-layout: fixed;
  
  /* 避免重绘 */
  will-change: auto;
}

.performance-table td,
.performance-table th {
  /* 避免文本重排 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 长列表优化 */
.virtual-list {
  /* 启用GPU加速 */
  transform: translateZ(0);
  
  /* 优化滚动 */
  -webkit-overflow-scrolling: touch;
  
  /* 避免重绘 */
  will-change: transform;
}

.virtual-list-item {
  /* 避免布局抖动 */
  contain: layout style paint;
  
  /* 优化渲染 */
  will-change: transform;
}

/* 模态框性能优化 */
.modal-backdrop {
  /* 使用transform而不是改变位置 */
  transform: translateZ(0);
  
  /* 优化透明度变化 */
  will-change: opacity;
  transition: opacity 0.3s ease;
}

.modal-content {
  /* 避免重绘 */
  will-change: transform, opacity;
  transform: translateZ(0);
  
  /* 优化动画 */
  transition: transform 0.3s ease, opacity 0.3s ease;
}

/* 响应式性能优化 */
@media (max-width: 768px) {
  /* 移动设备上减少动画复杂度 */
  .performance-optimized {
    will-change: auto;
  }
  
  /* 简化过渡效果 */
  .smooth-transition,
  .fast-transition {
    transition: opacity 0.2s ease;
  }
}

/* 减少动画偏好设置 */
@media (prefers-reduced-motion: reduce) {
  /* 禁用所有动画 */
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  /* 移除will-change */
  .performance-optimized {
    will-change: auto;
  }
}

/* 打印时移除所有动画 */
@media print {
  *,
  *::before,
  *::after {
    animation: none !important;
    transition: none !important;
    will-change: auto !important;
  }
}
