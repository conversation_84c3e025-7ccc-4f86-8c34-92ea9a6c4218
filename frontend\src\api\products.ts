import { http } from '@/utils/http'
import type { Product } from '@/types'

export const productApi = {
  // 获取所有商品
  getAll(): Promise<Product[]> {
    return http.get<Product[]>('/products')
  },

  // 根据ID获取商品
  getById(id: number): Promise<Product> {
    return http.get<Product>(`/products/${id}`)
  },

  // 创建商品
  create(product: Omit<Product, 'id'>): Promise<Product> {
    return http.post<Product>('/products', product)
  },

  // 更新商品
  update(id: number, product: Partial<Product>): Promise<Product> {
    return http.put<Product>(`/products/${id}`, product)
  },

  // 删除商品
  delete(id: number): Promise<void> {
    return http.delete<void>(`/products/${id}`)
  }
}
