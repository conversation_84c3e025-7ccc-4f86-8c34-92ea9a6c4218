import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/LoginView.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    name: 'Dashboard',
    component: () => import('@/views/DashboardView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/cats',
    name: 'Cats',
    component: () => import('@/views/CatListView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/cats/create',
    name: 'CatCreate',
    component: () => import('@/views/CatCreateView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/cats/:id',
    name: 'CatDetail',
    component: () => import('@/views/CatDetailView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/customers',
    name: 'Customers',
    component: () => import('@/views/CustomerView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/health',
    name: 'Health',
    component: () => import('@/views/HealthRecordView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/health/vaccines',
    name: 'HealthVaccines',
    component: () => import('@/views/health/VaccineView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/health/reminders',
    name: 'HealthReminders',
    component: () => import('@/views/health/HealthReminderView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/breeding',
    name: 'Breeding',
    component: () => import('@/views/breeding/BreedingDashboardView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/breeding/plans',
    name: 'BreedingPlans',
    component: () => import('@/views/breeding/BreedingPlanView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/finance',
    name: 'Finance',
    component: () => import('@/views/finance/FinanceDashboardView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/finance/budget',
    name: 'FinanceBudget',
    component: () => import('@/views/finance/BudgetView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/reports',
    name: 'Reports',
    component: () => import('@/views/reports/ReportsView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/api-test',
    name: 'ApiTest',
    component: () => import('@/views/ApiTestView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/diagnostic',
    name: 'Diagnostic',
    component: () => import('@/views/DiagnosticView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/test',
    name: 'Test',
    component: () => import('@/views/TestView.vue'),
    meta: { requiresAuth: true }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()

  // 初始化认证状态
  authStore.initializeAuth()

  const requiresAuth = to.meta.requiresAuth !== false
  const isAuthenticated = authStore.isAuthenticated

  if (requiresAuth && !isAuthenticated) {
    // 需要认证但未登录，跳转到登录页
    next('/login')
  } else if (to.path === '/login' && isAuthenticated) {
    // 已登录用户访问登录页，跳转到首页
    next('/')
  } else {
    next()
  }
})

export default router


