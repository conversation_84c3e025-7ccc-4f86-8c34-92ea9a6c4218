package com.cattery.dto.ai;

import lombok.Data;

/**
 * 面部特征DTO
 */
@Data
public class FacialFeatureDTO {
    
    /**
     * 特征类型 (eyes, ears, nose, mouth)
     */
    private String featureType;
    
    /**
     * 颜色
     */
    private String color;
    
    /**
     * 形状
     */
    private String shape;
    
    /**
     * 大小
     */
    private String size;
    
    /**
     * 位置坐标 (JSON格式)
     */
    private String coordinates;
    
    /**
     * 置信度
     */
    private Double confidence;
    
    /**
     * 特征描述
     */
    private String description;
}
