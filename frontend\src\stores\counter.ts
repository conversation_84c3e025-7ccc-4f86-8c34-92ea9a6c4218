import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { catApi } from '@/api'
import type { Cat } from '@/types'

export const useCatStore = defineStore('cat', () => {
  // 状态
  const cats = ref<Cat[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const totalCats = computed(() => cats.value.length)
  const availableCats = computed(() =>
    cats.value.filter(cat => cat.status === 'PENDING_ADOPTION')
  )

  // 操作
  async function fetchCats() {
    loading.value = true
    error.value = null
    try {
      cats.value = await catApi.getAll()
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取猫咪列表失败'
    } finally {
      loading.value = false
    }
  }

  async function addCat(cat: Omit<Cat, 'id'>) {
    try {
      const newCat = await catApi.create(cat)
      cats.value.push(newCat)
      return newCat
    } catch (err) {
      error.value = err instanceof Error ? err.message : '添加猫咪失败'
      throw err
    }
  }

  async function updateCat(id: number, cat: Partial<Cat>) {
    try {
      const updatedCat = await catApi.update(id, cat)
      const index = cats.value.findIndex(c => c.id === id)
      if (index !== -1) {
        cats.value[index] = updatedCat
      }
      return updatedCat
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新猫咪失败'
      throw err
    }
  }

  async function deleteCat(id: number) {
    try {
      await catApi.delete(id)
      const index = cats.value.findIndex(c => c.id === id)
      if (index !== -1) {
        cats.value.splice(index, 1)
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除猫咪失败'
      throw err
    }
  }

  return {
    // 状态
    cats,
    loading,
    error,
    // 计算属性
    totalCats,
    availableCats,
    // 操作
    fetchCats,
    addCat,
    updateCat,
    deleteCat
  }
})
