package com.cattery.controller;

import com.cattery.dto.ApiResponse;
import com.cattery.entity.InventoryItem;
import com.cattery.service.InventoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 库存管理控制器
 */
@RestController
@RequestMapping("/api/inventory")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "库存管理", description = "库存物品管理和库存操作")
public class InventoryController {
    
    private final InventoryService inventoryService;
    
    /**
     * 获取库存统计
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取库存统计", description = "获取库存相关的统计数据")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getInventoryStatistics() {
        log.info("获取库存统计");
        
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalValue", inventoryService.getTotalInventoryValue());
        statistics.put("lowStockItems", inventoryService.getLowStockItems().size());
        statistics.put("expiringItems", inventoryService.getExpiringItems(30).size()); // 30天内过期
        statistics.put("categoryStats", inventoryService.getCategoryStatistics());
        
        return ResponseEntity.ok(ApiResponse.success("获取库存统计成功", statistics));
    }
    
    /**
     * 获取所有库存物品
     */
    @GetMapping("/items")
    @Operation(summary = "获取库存物品列表", description = "分页获取库存物品列表，支持筛选和搜索")
    public ResponseEntity<ApiResponse<Page<InventoryItem>>> getAllInventoryItems(
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "分类") @RequestParam(required = false) String category,
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword) {
        
        log.info("获取库存物品列表: page={}, size={}, category={}, keyword={}", page, size, category, keyword);
        
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.ASC, "name"));
        Page<InventoryItem> items;
        
        if (keyword != null && !keyword.trim().isEmpty()) {
            items = inventoryService.searchInventoryItems(keyword, pageable);
        } else if (category != null) {
            items = inventoryService.getInventoryItemsByCategory(category, pageable);
        } else {
            items = inventoryService.getAllInventoryItems(pageable);
        }
        
        return ResponseEntity.ok(ApiResponse.success("获取库存物品列表成功", items));
    }
    
    /**
     * 根据ID获取库存物品
     */
    @GetMapping("/items/{id}")
    @Operation(summary = "获取库存物品详情", description = "根据ID获取库存物品详细信息")
    public ResponseEntity<ApiResponse<InventoryItem>> getInventoryItemById(
            @Parameter(description = "物品ID") @PathVariable Long id) {
        
        log.info("获取库存物品详情: id={}", id);
        
        return inventoryService.getInventoryItemById(id)
            .map(item -> ResponseEntity.ok(ApiResponse.success("获取库存物品详情成功", item)))
            .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * 创建库存物品
     */
    @PostMapping("/items")
    @Operation(summary = "创建库存物品", description = "创建新的库存物品")
    public ResponseEntity<ApiResponse<InventoryItem>> createInventoryItem(
            @Parameter(description = "库存物品信息") @Valid @RequestBody InventoryItem item) {
        
        log.info("创建库存物品: name={}, category={}", item.getItemName(), item.getCategory());
        
        InventoryItem createdItem = inventoryService.addInventoryItem(item);
        return ResponseEntity.ok(ApiResponse.success("创建库存物品成功", createdItem));
    }
    
    /**
     * 更新库存物品
     */
    @PutMapping("/items/{id}")
    @Operation(summary = "更新库存物品", description = "更新库存物品信息")
    public ResponseEntity<ApiResponse<InventoryItem>> updateInventoryItem(
            @Parameter(description = "物品ID") @PathVariable Long id,
            @Parameter(description = "更新的库存物品信息") @Valid @RequestBody InventoryItem item) {
        
        log.info("更新库存物品: id={}", id);
        
        InventoryItem updatedItem = inventoryService.updateInventoryItem(id, item);
        return ResponseEntity.ok(ApiResponse.success("更新库存物品成功", updatedItem));
    }
    
    /**
     * 更新库存物品状态
     */
    @PatchMapping("/items/{id}/status")
    @Operation(summary = "更新库存物品状态", description = "更新库存物品的状态")
    public ResponseEntity<ApiResponse<InventoryItem>> updateItemStatus(
            @Parameter(description = "物品ID") @PathVariable Long id,
            @Parameter(description = "新状态") @RequestParam InventoryItem.Status status) {
        
        log.info("更新库存物品状态: id={}, status={}", id, status);
        
        InventoryItem updatedItem = inventoryService.updateItemStatus(id, status);
        return ResponseEntity.ok(ApiResponse.success("更新库存物品状态成功", updatedItem));
    }
    
    /**
     * 删除库存物品
     */
    @DeleteMapping("/items/{id}")
    @Operation(summary = "删除库存物品", description = "删除库存物品")
    public ResponseEntity<ApiResponse<Void>> deleteInventoryItem(
            @Parameter(description = "物品ID") @PathVariable Long id) {
        
        log.info("删除库存物品: id={}", id);
        
        inventoryService.deleteInventoryItem(id);
        return ResponseEntity.ok(ApiResponse.success("删除库存物品成功", null));
    }
    
    /**
     * 入库操作
     */
    @PostMapping("/items/{id}/stock-in")
    @Operation(summary = "入库操作", description = "增加库存数量")
    public ResponseEntity<ApiResponse<InventoryItem>> stockIn(
            @Parameter(description = "物品ID") @PathVariable Long id,
            @Parameter(description = "入库数量") @RequestParam Integer quantity,
            @Parameter(description = "备注") @RequestParam(required = false) String notes) {
        
        log.info("入库操作: id={}, quantity={}", id, quantity);
        
        InventoryItem updatedItem = inventoryService.stockIn(id, quantity, notes);
        return ResponseEntity.ok(ApiResponse.success("入库操作成功", updatedItem));
    }
    
    /**
     * 出库操作
     */
    @PostMapping("/items/{id}/stock-out")
    @Operation(summary = "出库操作", description = "减少库存数量")
    public ResponseEntity<ApiResponse<InventoryItem>> stockOut(
            @Parameter(description = "物品ID") @PathVariable Long id,
            @Parameter(description = "出库数量") @RequestParam Integer quantity,
            @Parameter(description = "备注") @RequestParam(required = false) String notes) {
        
        log.info("出库操作: id={}, quantity={}", id, quantity);
        
        InventoryItem updatedItem = inventoryService.stockOut(id, quantity, notes);
        return ResponseEntity.ok(ApiResponse.success("出库操作成功", updatedItem));
    }
    
    /**
     * 获取库存不足的物品
     */
    @GetMapping("/low-stock")
    @Operation(summary = "获取库存不足的物品", description = "获取库存数量低于最小库存的物品")
    public ResponseEntity<ApiResponse<List<InventoryItem>>> getLowStockItems() {
        log.info("获取库存不足的物品");
        
        List<InventoryItem> lowStockItems = inventoryService.getLowStockItems();
        return ResponseEntity.ok(ApiResponse.success("获取库存不足物品成功", lowStockItems));
    }
    
    /**
     * 获取即将过期的物品
     */
    @GetMapping("/expiring")
    @Operation(summary = "获取即将过期的物品", description = "获取即将过期的库存物品")
    public ResponseEntity<ApiResponse<List<InventoryItem>>> getExpiringItems(
            @Parameter(description = "提前天数") @RequestParam(defaultValue = "30") int daysAhead) {
        
        log.info("获取即将过期的物品: daysAhead={}", daysAhead);
        
        List<InventoryItem> expiringItems = inventoryService.getExpiringItems(daysAhead);
        return ResponseEntity.ok(ApiResponse.success("获取即将过期物品成功", expiringItems));
    }
    
    /**
     * 获取分类统计
     */
    @GetMapping("/category-statistics")
    @Operation(summary = "获取分类统计", description = "获取各分类的库存统计")
    public ResponseEntity<ApiResponse<List<Object[]>>> getCategoryStatistics() {
        log.info("获取分类统计");
        
        List<Object[]> categoryStats = inventoryService.getCategoryStatistics();
        return ResponseEntity.ok(ApiResponse.success("获取分类统计成功", categoryStats));
    }
}
