# English message file

# Common messages
common.success=Operation successful
common.error=Operation failed
common.notFound=Not found
common.unauthorized=Unauthorized
common.forbidden=Forbidden
common.badRequest=Bad request
common.internalError=Internal server error
common.serviceUnavailable=Service unavailable

# User authentication
auth.login.success=Login successful
auth.login.failed=Login failed
auth.logout.success=Logout successful
auth.token.invalid=Invalid token
auth.token.expired=Token expired
auth.password.incorrect=Incorrect password
auth.user.notFound=User not found
auth.user.locked=User is locked
auth.user.disabled=User is disabled

# Cat management
cat.created=Cat created successfully
cat.updated=Cat information updated successfully
cat.deleted=Cat deleted successfully
cat.notFound=Cat not found
cat.name.required=Cat name is required
cat.breed.required=Breed is required
cat.gender.required=Gender is required
cat.dateOfBirth.required=Date of birth is required
cat.microchip.duplicate=Microchip ID already exists
cat.registration.duplicate=Registration number already exists

# Health management
health.record.created=Health record created successfully
health.record.updated=Health record updated successfully
health.record.deleted=Health record deleted successfully
health.record.notFound=Health record not found
health.vaccine.due=Vaccine due soon
health.vaccine.overdue=Vaccine overdue
health.checkup.due=Checkup due
health.checkup.overdue=Checkup overdue

# Breeding management
breeding.record.created=Breeding record created successfully
breeding.record.updated=Breeding record updated successfully
breeding.record.deleted=Breeding record deleted successfully
breeding.record.notFound=Breeding record not found
breeding.mating.scheduled=Mating scheduled
breeding.pregnancy.confirmed=Pregnancy confirmed
breeding.birth.recorded=Birth recorded
breeding.kitten.registered=Kitten registered

# Customer management
customer.created=Customer created successfully
customer.updated=Customer information updated successfully
customer.deleted=Customer deleted successfully
customer.notFound=Customer not found
customer.email.duplicate=Email already exists
customer.phone.duplicate=Phone number already exists
customer.inquiry.created=Inquiry record created successfully
customer.followup.created=Follow-up record created successfully

# Inventory management
inventory.item.created=Inventory item created successfully
inventory.item.updated=Inventory item updated successfully
inventory.item.deleted=Inventory item deleted successfully
inventory.item.notFound=Inventory item not found
inventory.stock.insufficient=Insufficient stock
inventory.stock.updated=Stock updated successfully
inventory.lowStock.alert=Low stock alert
inventory.outOfStock.alert=Out of stock alert

# Finance management
finance.transaction.created=Transaction record created successfully
finance.transaction.updated=Transaction record updated successfully
finance.transaction.deleted=Transaction record deleted successfully
finance.transaction.notFound=Transaction record not found
finance.amount.invalid=Invalid amount
finance.category.required=Category is required
finance.budget.exceeded=Budget exceeded

# AI features
ai.recognition.success=Recognition successful
ai.recognition.failed=Recognition failed
ai.prediction.success=Prediction successful
ai.prediction.failed=Prediction failed
ai.service.unavailable=AI service unavailable
ai.image.invalid=Invalid image format
ai.image.tooLarge=Image file too large
ai.confidence.low=Low recognition confidence

# File management
file.upload.success=File uploaded successfully
file.upload.failed=File upload failed
file.delete.success=File deleted successfully
file.delete.failed=File deletion failed
file.notFound=File not found
file.type.invalid=Invalid file type
file.size.exceeded=File size exceeded

# Reports
report.generated=Report generated successfully
report.generation.failed=Report generation failed
report.exported=Report exported successfully
report.export.failed=Report export failed
report.type.invalid=Invalid report type
report.period.invalid=Invalid report period

# System settings
settings.updated=Settings updated successfully
settings.reset=Settings reset successfully
settings.backup.created=Backup created successfully
settings.backup.restored=Backup restored successfully
settings.backup.failed=Backup operation failed

# Data validation
validation.required=This field is required
validation.email.invalid=Invalid email format
validation.phone.invalid=Invalid phone number format
validation.date.invalid=Invalid date format
validation.number.invalid=Invalid number format
validation.length.min=Length cannot be less than {0} characters
validation.length.max=Length cannot exceed {0} characters
validation.value.min=Value cannot be less than {0}
validation.value.max=Value cannot exceed {0}

# Business rules
business.cat.age.invalid=Invalid cat age
business.breeding.age.tooYoung=Cat is too young for breeding
business.breeding.age.tooOld=Cat is too old for breeding
business.breeding.inbreeding.detected=Inbreeding risk detected
business.health.vaccine.required=Vaccination required
business.health.checkup.required=Health checkup required
business.adoption.age.tooYoung=Kitten is too young for adoption
business.adoption.health.required=Health check required before adoption

# Status text
status.cat.available=Available
status.cat.adopted=Adopted
status.cat.reserved=Reserved
status.cat.breeding=Breeding
status.cat.medical=Medical
status.cat.quarantine=Quarantine

status.health.normal=Normal
status.health.abnormal=Abnormal
status.health.pending=Pending

status.breeding.planned=Planned
status.breeding.mated=Mated
status.breeding.pregnant=Pregnant
status.breeding.born=Born
status.breeding.weaned=Weaned

status.customer.potential=Potential
status.customer.active=Active
status.customer.adopted=Adopted
status.customer.breeder=Breeder

status.transaction.income=Income
status.transaction.expense=Expense
status.transaction.transfer=Transfer

# Gender text
gender.male=Male
gender.female=Female
gender.unknown=Unknown

# Record types
recordType.vaccination=Vaccination
recordType.checkup=Checkup
recordType.treatment=Treatment
recordType.surgery=Surgery
recordType.dental=Dental
recordType.grooming=Grooming
recordType.geneticTest=Genetic Test
recordType.weightCheck=Weight Check

# Time formats
time.format.date=MMM dd, yyyy
time.format.datetime=MMM dd, yyyy HH:mm:ss
time.format.time=HH:mm:ss

# Units
unit.kg=kg
unit.g=g
unit.cm=cm
unit.year=years old
unit.month=months old
unit.day=days old

# Currency
currency.cny=CNY
currency.symbol=$

# Pagination
page.first=First
page.previous=Previous
page.next=Next
page.last=Last
page.total=Total {0} records
page.size={0} per page

# Operation logs
log.created=Created {0}
log.updated=Updated {0}
log.deleted=Deleted {0}
log.viewed=Viewed {0}
log.exported=Exported {0}
log.imported=Imported {0}

# Notification messages
notification.vaccine.due=Cat {0}''s vaccine is due soon
notification.checkup.due=Cat {0} needs a health checkup
notification.birth.due=Cat {0} is due to give birth soon
notification.stock.low=Item {0} is low in stock
notification.customer.inquiry=New inquiry from customer {0}

# Email templates
email.subject.vaccine.reminder=Vaccine Reminder
email.subject.checkup.reminder=Checkup Reminder
email.subject.birth.notification=Birth Notification
email.subject.adoption.confirmation=Adoption Confirmation
email.subject.inquiry.response=Inquiry Response

# SMS templates
sms.vaccine.reminder=Your cat {0} needs vaccination. Please schedule an appointment.
sms.checkup.reminder=Your cat {0} needs a health checkup. Please schedule an appointment.
sms.birth.notification=Your cat {0} has successfully given birth. Please check the details.
sms.adoption.confirmation=Congratulations on successfully adopting cat {0}!
