<template>
  <div class="user-view">
    <div class="page-header">
      <h1>用户管理</h1>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        新增用户
      </el-button>
    </div>

    <el-table :data="users" v-loading="loading">
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="username" label="用户名" />
      <el-table-column prop="realName" label="真实姓名" />
      <el-table-column prop="email" label="邮箱" />
      <el-table-column prop="phone" label="电话" />
      <el-table-column label="状态">
        <template #default="{ row }">
          <el-tag :type="row.enabled ? 'success' : 'danger'">
            {{ row.enabled ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="lastLoginTime" label="最后登录" />
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button size="small" type="primary" @click="editUser(row)">编辑</el-button>
          <el-button size="small" type="warning" @click="resetPassword(row)">重置密码</el-button>
          <el-button size="small" type="danger" @click="deleteUser(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog v-model="showCreateDialog" title="用户管理" width="600px">
      <el-form :model="userForm" label-width="100px">
        <el-form-item label="用户名">
          <el-input v-model="userForm.username" placeholder="输入用户名" />
        </el-form-item>
        <el-form-item label="真实姓名">
          <el-input v-model="userForm.realName" placeholder="输入真实姓名" />
        </el-form-item>
        <el-form-item label="邮箱">
          <el-input v-model="userForm.email" placeholder="输入邮箱" />
        </el-form-item>
        <el-form-item label="电话">
          <el-input v-model="userForm.phone" placeholder="输入电话" />
        </el-form-item>
        <el-form-item label="密码" v-if="!editingUser">
          <el-input v-model="userForm.password" type="password" placeholder="输入密码" />
        </el-form-item>
        <el-form-item label="状态">
          <el-switch v-model="userForm.enabled" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveUser">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

const loading = ref(false)
const showCreateDialog = ref(false)
const editingUser = ref(null)

const users = ref([
  {
    id: 1,
    username: 'admin',
    realName: '管理员',
    email: '<EMAIL>',
    phone: '13800138000',
    enabled: true,
    lastLoginTime: '2024-01-15 14:30:25'
  },
  {
    id: 2,
    username: 'user',
    realName: '普通用户',
    email: '<EMAIL>',
    phone: '13800138001',
    enabled: true,
    lastLoginTime: '2024-01-15 13:45:12'
  }
])

const userForm = reactive({
  username: '',
  realName: '',
  email: '',
  phone: '',
  password: '',
  enabled: true
})

const editUser = (user: any) => {
  editingUser.value = user
  Object.assign(userForm, user)
  showCreateDialog.value = true
}

const resetPassword = async (user: any) => {
  try {
    await ElMessageBox.confirm('确定要重置该用户的密码吗？', '确认重置', {
      type: 'warning'
    })
    ElMessage.success('密码重置成功，新密码已发送到用户邮箱')
  } catch {
    // 用户取消
  }
}

const deleteUser = async (user: any) => {
  try {
    await ElMessageBox.confirm('确定要删除该用户吗？', '确认删除', {
      type: 'warning'
    })
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

const saveUser = () => {
  ElMessage.success('保存成功')
  showCreateDialog.value = false
  editingUser.value = null
}

onMounted(() => {
  loading.value = false
})
</script>

<style scoped>
.user-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
</style>
