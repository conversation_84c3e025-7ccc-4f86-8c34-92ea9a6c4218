# 通用消息
common.success=操作成功
common.error=操作失败
common.notFound=资源未找到
common.unauthorized=未授权访问
common.forbidden=权限不足
common.badRequest=请求参数错误
common.internalError=服务器内部错误

# 验证消息
validation.required=此字段为必填项
validation.email=请输入有效的邮箱地址
validation.phone=请输入有效的手机号码
validation.length.min=长度不能少于{0}个字符
validation.length.max=长度不能超过{0}个字符
validation.pattern=格式不正确

# 认证消息
auth.login.success=登录成功
auth.login.failed=用户名或密码错误
auth.logout.success=登出成功
auth.register.success=注册成功
auth.register.failed=注册失败
auth.token.invalid=令牌无效
auth.token.expired=令牌已过期
auth.username.exists=用户名已存在
auth.email.exists=邮箱已存在
auth.phone.exists=手机号已存在

# 猫咪管理
cat.created=猫咪信息创建成功
cat.updated=猫咪信息更新成功
cat.deleted=猫咪信息删除成功
cat.notFound=猫咪不存在
cat.name.required=猫咪名称不能为空
cat.breed.required=品种不能为空
cat.gender.required=性别不能为空
cat.dateOfBirth.required=出生日期不能为空

# 健康管理
health.record.created=健康记录创建成功
health.record.updated=健康记录更新成功
health.record.deleted=健康记录删除成功
health.record.notFound=健康记录不存在

# 客户管理
customer.created=客户信息创建成功
customer.updated=客户信息更新成功
customer.deleted=客户信息删除成功
customer.notFound=客户不存在

# 领养管理
adoption.created=领养申请创建成功
adoption.approved=领养申请已批准
adoption.rejected=领养申请已拒绝
adoption.completed=领养已完成
adoption.cancelled=领养已取消

# 财务管理
finance.transaction.created=财务记录创建成功
finance.transaction.updated=财务记录更新成功
finance.transaction.deleted=财务记录删除成功

# AI功能
ai.recognition.success=识别成功
ai.recognition.failed=识别失败
ai.prediction.success=预测完成
ai.prediction.failed=预测失败
ai.analysis.success=分析完成
ai.analysis.failed=分析失败

# 报表
report.generated=报表生成成功
report.export.success=报表导出成功
report.export.failed=报表导出失败

# 文件上传
file.upload.success=文件上传成功
file.upload.failed=文件上传失败
file.size.exceeded=文件大小超出限制
file.type.invalid=文件类型不支持
