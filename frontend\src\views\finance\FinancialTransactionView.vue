<template>
  <div class="financial-transaction-view">
    <div class="page-header">
      <h1>财务交易</h1>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        新增交易
      </el-button>
    </div>

    <div class="content-area">
      <el-card>
        <p>这是财务交易页面，功能开发中...</p>
      </el-card>
    </div>

    <el-dialog v-model="showCreateDialog" title="新增财务交易" width="600px">
      <el-form :model="form" label-width="100px">
        <el-form-item label="交易类型">
          <el-select v-model="form.type" placeholder="选择交易类型">
            <el-option label="收入" value="INCOME" />
            <el-option label="支出" value="EXPENSE" />
          </el-select>
        </el-form-item>
        <el-form-item label="金额">
          <el-input-number v-model="form.amount" :min="0" :precision="2" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="form.description" type="textarea" :rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="save">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

const showCreateDialog = ref(false)
const form = reactive({
  type: '',
  amount: 0,
  description: ''
})

const save = () => {
  ElMessage.success('保存成功')
  showCreateDialog.value = false
}
</script>

<style scoped>
.financial-transaction-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
</style>
