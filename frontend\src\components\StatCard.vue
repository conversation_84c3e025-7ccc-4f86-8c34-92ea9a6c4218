<template>
  <el-card class="stat-card" :class="cardClass">
    <div class="stat-content">
      <div class="stat-icon" :style="iconStyle">
        <slot name="icon">
          <el-icon><component :is="icon" /></el-icon>
        </slot>
      </div>
      <div class="stat-info">
        <div class="stat-number">{{ formattedValue }}</div>
        <div class="stat-label">{{ label }}</div>
        <div v-if="trend" class="stat-trend" :class="trendClass">
          <el-icon>
            <component :is="trendIcon" />
          </el-icon>
          <span>{{ trendText }}</span>
        </div>
      </div>
      <div v-if="$slots.extra" class="stat-extra">
        <slot name="extra"></slot>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ArrowUp, ArrowDown, Minus } from '@element-plus/icons-vue'

interface Props {
  value: number | string
  label: string
  icon?: string
  color?: string
  gradient?: string[]
  trend?: {
    value: number
    type: 'up' | 'down' | 'flat'
    text?: string
  }
  format?: 'number' | 'currency' | 'percentage'
  precision?: number
  size?: 'small' | 'medium' | 'large'
}

const props = withDefaults(defineProps<Props>(), {
  format: 'number',
  precision: 0,
  size: 'medium'
})

// 计算属性
const cardClass = computed(() => {
  return {
    [`stat-card--${props.size}`]: true
  }
})

const iconStyle = computed(() => {
  if (props.gradient && props.gradient.length >= 2) {
    return {
      background: `linear-gradient(135deg, ${props.gradient[0]} 0%, ${props.gradient[1]} 100%)`
    }
  }
  if (props.color) {
    return {
      backgroundColor: props.color
    }
  }
  return {}
})

const formattedValue = computed(() => {
  const value = typeof props.value === 'string' ? parseFloat(props.value) : props.value
  
  if (isNaN(value)) {
    return props.value
  }
  
  switch (props.format) {
    case 'currency':
      return new Intl.NumberFormat('zh-CN', {
        style: 'currency',
        currency: 'CNY',
        minimumFractionDigits: props.precision,
        maximumFractionDigits: props.precision
      }).format(value)
    
    case 'percentage':
      return new Intl.NumberFormat('zh-CN', {
        style: 'percent',
        minimumFractionDigits: props.precision,
        maximumFractionDigits: props.precision
      }).format(value / 100)
    
    default:
      return new Intl.NumberFormat('zh-CN', {
        minimumFractionDigits: props.precision,
        maximumFractionDigits: props.precision
      }).format(value)
  }
})

const trendClass = computed(() => {
  if (!props.trend) return ''
  
  return {
    'stat-trend--up': props.trend.type === 'up',
    'stat-trend--down': props.trend.type === 'down',
    'stat-trend--flat': props.trend.type === 'flat'
  }
})

const trendIcon = computed(() => {
  if (!props.trend) return null
  
  switch (props.trend.type) {
    case 'up':
      return ArrowUp
    case 'down':
      return ArrowDown
    default:
      return Minus
  }
})

const trendText = computed(() => {
  if (!props.trend) return ''
  
  if (props.trend.text) {
    return props.trend.text
  }
  
  const value = Math.abs(props.trend.value)
  const sign = props.trend.type === 'up' ? '+' : props.trend.type === 'down' ? '-' : ''
  
  return `${sign}${value}%`
})
</script>

<style scoped lang="scss">
.stat-card {
  margin-bottom: 20px;
  
  &--small {
    .stat-content {
      .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 18px;
        margin-right: 12px;
      }
      
      .stat-info {
        .stat-number {
          font-size: 20px;
        }
        
        .stat-label {
          font-size: 12px;
        }
      }
    }
  }
  
  &--medium {
    .stat-content {
      .stat-icon {
        width: 60px;
        height: 60px;
        font-size: 24px;
        margin-right: 16px;
      }
      
      .stat-info {
        .stat-number {
          font-size: 28px;
        }
        
        .stat-label {
          font-size: 14px;
        }
      }
    }
  }
  
  &--large {
    .stat-content {
      .stat-icon {
        width: 80px;
        height: 80px;
        font-size: 32px;
        margin-right: 20px;
      }
      
      .stat-info {
        .stat-number {
          font-size: 36px;
        }
        
        .stat-label {
          font-size: 16px;
        }
      }
    }
  }
  
  .stat-content {
    display: flex;
    align-items: center;
    
    .stat-icon {
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      flex-shrink: 0;
    }
    
    .stat-info {
      flex: 1;
      min-width: 0;
      
      .stat-number {
        font-weight: 600;
        color: #303133;
        line-height: 1;
        margin-bottom: 4px;
        word-break: break-all;
      }
      
      .stat-label {
        color: #909399;
        margin-bottom: 4px;
      }
      
      .stat-trend {
        display: flex;
        align-items: center;
        font-size: 12px;
        
        .el-icon {
          margin-right: 2px;
        }
        
        &--up {
          color: #67c23a;
        }
        
        &--down {
          color: #f56c6c;
        }
        
        &--flat {
          color: #909399;
        }
      }
    }
    
    .stat-extra {
      margin-left: 16px;
      flex-shrink: 0;
    }
  }
}

@media (max-width: 768px) {
  .stat-card {
    &--medium {
      .stat-content {
        .stat-icon {
          width: 50px;
          height: 50px;
          font-size: 20px;
          margin-right: 12px;
        }
        
        .stat-info {
          .stat-number {
            font-size: 24px;
          }
        }
      }
    }
    
    &--large {
      .stat-content {
        .stat-icon {
          width: 60px;
          height: 60px;
          font-size: 24px;
          margin-right: 16px;
        }
        
        .stat-info {
          .stat-number {
            font-size: 28px;
          }
        }
      }
    }
  }
}
</style>
