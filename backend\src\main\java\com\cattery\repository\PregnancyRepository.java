package com.cattery.repository;

import com.cattery.entity.Pregnancy;
import com.cattery.entity.Cat;
import com.cattery.entity.Mating;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 怀孕记录仓库接口
 */
@Repository
public interface PregnancyRepository extends JpaRepository<Pregnancy, Long>, JpaSpecificationExecutor<Pregnancy> {

    /**
     * 根据母猫查找怀孕记录
     */
    List<Pregnancy> findByMother(Cat mother);

    /**
     * 根据配种记录查找怀孕记录
     */
    Optional<Pregnancy> findByMating(Mating mating);

    /**
     * 根据怀孕日期范围查找记录
     */
    List<Pregnancy> findByPregnancyDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 根据状态查找怀孕记录
     */
    List<Pregnancy> findByStatus(Pregnancy.Status status);

    /**
     * 查找当前怀孕的猫咪
     */
    @Query("SELECT p FROM Pregnancy p WHERE p.status = 'PREGNANT'")
    List<Pregnancy> findCurrentPregnancies();

    /**
     * 查找已完成的怀孕记录
     */
    @Query("SELECT p FROM Pregnancy p WHERE p.status = 'COMPLETED'")
    List<Pregnancy> findCompletedPregnancies();

    /**
     * 统计指定时间范围内的怀孕数量
     */
    long countByPregnancyDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 统计成功分娩数量
     */
    @Query("SELECT COUNT(p) FROM Pregnancy p WHERE p.status = 'COMPLETED' AND p.birthDate IS NOT NULL")
    long countSuccessfulBirths();

    /**
     * 按状态统计数量
     */
    @Query("SELECT p.status, COUNT(p) FROM Pregnancy p GROUP BY p.status")
    List<Object[]> countByStatusGrouped();

    /**
     * 查找即将分娩的怀孕记录
     */
    @Query("SELECT p FROM Pregnancy p WHERE p.status = 'PREGNANT' " +
           "AND p.expectedBirthDate BETWEEN CURRENT_DATE AND :endDate")
    List<Pregnancy> findUpcomingBirths(@Param("endDate") LocalDateTime endDate);

    /**
     * 根据状态按预期分娩日期排序查找
     */
    List<Pregnancy> findByStatusOrderByExpectedBirthDateAsc(Pregnancy.Status status);

    /**
     * 查找即将分娩的记录（修正方法名）
     */
    @Query("SELECT p FROM Pregnancy p WHERE p.status = 'PREGNANT' " +
           "AND p.expectedBirthDate <= :cutoffDate")
    List<Pregnancy> findUpcomingDeliveries(@Param("cutoffDate") LocalDateTime cutoffDate);

    /**
     * 统计实际分娩数量
     */
    long countByBirthDateBetween(LocalDateTime startDate, LocalDateTime endDate);
}
