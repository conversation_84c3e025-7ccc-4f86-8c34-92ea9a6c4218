package com.cattery.dto.ai;

import lombok.Data;
import java.util.List;

/**
 * 猫咪识别结果DTO
 */
@Data
public class CatRecognitionResultDTO {
    
    /**
     * 识别置信度 (0.0 - 1.0)
     */
    private Double confidence;
    
    /**
     * 识别的品种名称
     */
    private String breedName;
    
    /**
     * 识别的颜色
     */
    private String color;
    
    /**
     * 识别的性别
     */
    private String gender;
    
    /**
     * 估计年龄
     */
    private Integer estimatedAge;
    
    /**
     * 面部特征列表
     */
    private List<FacialFeatureDTO> facialFeatures;
    
    /**
     * 身体大小
     */
    private String bodySize;
    
    /**
     * 毛发长度
     */
    private String coatLength;

    /**
     * 毛发图案
     */
    private String coatPattern;
    
    /**
     * 匹配的猫咪列表
     */
    private List<CatSummaryDTO> matchedCats;
    
    /**
     * 相似度匹配列表
     */
    private List<SimilarityMatch> similarityMatches;
    
    /**
     * 相似度匹配内部类
     */
    @Data
    public static class SimilarityMatch {
        private Long catId;
        private String catName;
        private Double similarity;
        private String reason;
        private List<String> matchedFeatures;
    }
}
