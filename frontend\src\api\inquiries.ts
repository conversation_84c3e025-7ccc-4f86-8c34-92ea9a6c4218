import api from './index'
import type { ApiResponse, PageResponse, CustomerInquiry, PaginationParams } from '@/types/api'

/**
 * 客户咨询API
 */
export const inquiriesApi = {
  /**
   * 获取咨询列表
   */
  getInquiries: (params: PaginationParams & {
    customerId?: number
    inquiryType?: string
    status?: string
    priority?: string
    assignedTo?: number
    startDate?: string
    endDate?: string
  }): Promise<ApiResponse<PageResponse<CustomerInquiry>>> => {
    return api.get('/api/inquiries', { params }).then(response => response.data)
  },

  /**
   * 根据ID获取咨询
   */
  getInquiryById: (id: number): Promise<ApiResponse<CustomerInquiry>> => {
    return api.get(`/api/inquiries/${id}`).then(response => response.data)
  },

  /**
   * 创建咨询
   */
  createInquiry: (data: Partial<CustomerInquiry>): Promise<ApiResponse<CustomerInquiry>> => {
    return api.post('/api/inquiries', data).then(response => response.data)
  },

  /**
   * 更新咨询
   */
  updateInquiry: (id: number, data: Partial<CustomerInquiry>): Promise<ApiResponse<CustomerInquiry>> => {
    return api.put(`/api/inquiries/${id}`, data).then(response => response.data)
  },

  /**
   * 删除咨询
   */
  deleteInquiry: (id: number): Promise<ApiResponse<void>> => {
    return api.delete(`/api/inquiries/${id}`).then(response => response.data)
  },

  /**
   * 回复咨询
   */
  replyInquiry: (id: number, reply: string): Promise<ApiResponse<CustomerInquiry>> => {
    return api.post(`/api/inquiries/${id}/reply`, { reply }).then(response => response.data)
  },

  /**
   * 分配咨询
   */
  assignInquiry: (id: number, assignedTo: number): Promise<ApiResponse<CustomerInquiry>> => {
    return api.put(`/api/inquiries/${id}/assign`, { assignedTo }).then(response => response.data)
  },

  /**
   * 更新咨询状态
   */
  updateInquiryStatus: (id: number, status: string): Promise<ApiResponse<CustomerInquiry>> => {
    return api.put(`/api/inquiries/${id}/status`, { status }).then(response => response.data)
  },

  /**
   * 更新咨询优先级
   */
  updateInquiryPriority: (id: number, priority: string): Promise<ApiResponse<CustomerInquiry>> => {
    return api.put(`/api/inquiries/${id}/priority`, { priority }).then(response => response.data)
  },

  /**
   * 获取待处理的咨询
   */
  getPendingInquiries: (): Promise<ApiResponse<CustomerInquiry[]>> => {
    return api.get('/api/inquiries/pending').then(response => response.data)
  },

  /**
   * 获取高优先级咨询
   */
  getHighPriorityInquiries: (): Promise<ApiResponse<CustomerInquiry[]>> => {
    return api.get('/api/inquiries/high-priority').then(response => response.data)
  },

  /**
   * 获取我的咨询（分配给当前用户的）
   */
  getMyInquiries: (): Promise<ApiResponse<CustomerInquiry[]>> => {
    return api.get('/api/inquiries/my').then(response => response.data)
  },

  /**
   * 获取客户的咨询历史
   */
  getCustomerInquiryHistory: (customerId: number): Promise<ApiResponse<CustomerInquiry[]>> => {
    return api.get(`/api/inquiries/customer/${customerId}/history`).then(response => response.data)
  },

  /**
   * 获取咨询统计
   */
  getInquiryStats: (): Promise<ApiResponse<{
    totalInquiries: number
    pendingInquiries: number
    repliedInquiries: number
    resolvedInquiries: number
    closedInquiries: number
    highPriorityInquiries: number
    statusStats: Record<string, number>
    typeStats: Record<string, number>
    priorityStats: Record<string, number>
    monthlyTrend: Array<{
      month: string
      count: number
    }>
  }>> => {
    return api.get('/api/inquiries/stats').then(response => response.data)
  },

  /**
   * 批量删除咨询
   */
  batchDeleteInquiries: (ids: number[]): Promise<ApiResponse<void>> => {
    return api.delete('/api/inquiries/batch', { data: { ids } }).then(response => response.data)
  },

  /**
   * 批量分配咨询
   */
  batchAssignInquiries: (ids: number[], assignedTo: number): Promise<ApiResponse<void>> => {
    return api.put('/api/inquiries/batch/assign', { ids, assignedTo }).then(response => response.data)
  },

  /**
   * 批量更新状态
   */
  batchUpdateStatus: (ids: number[], status: string): Promise<ApiResponse<void>> => {
    return api.put('/api/inquiries/batch/status', { ids, status }).then(response => response.data)
  },

  /**
   * 导出咨询数据
   */
  exportInquiries: (params: {
    format?: 'excel' | 'csv'
    startDate?: string
    endDate?: string
    status?: string
    type?: string
  } = {}): Promise<Blob> => {
    return api.get('/api/inquiries/export', { 
      params,
      responseType: 'blob'
    }).then(response => response.data)
  }
}

// 导出单个方法以便直接使用
export const {
  getInquiries,
  getInquiryById,
  createInquiry,
  updateInquiry,
  deleteInquiry,
  replyInquiry,
  assignInquiry,
  updateInquiryStatus,
  updateInquiryPriority,
  getPendingInquiries,
  getHighPriorityInquiries,
  getMyInquiries,
  getCustomerInquiryHistory,
  getInquiryStats,
  batchDeleteInquiries,
  batchAssignInquiries,
  batchUpdateStatus,
  exportInquiries
} = inquiriesApi
