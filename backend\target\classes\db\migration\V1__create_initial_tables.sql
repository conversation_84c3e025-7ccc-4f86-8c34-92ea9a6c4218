-- 猫舍管理系统初始化脚本
-- 版本: V1
-- 描述: 创建基础表结构

-- ================================
-- 1. 用户相关表
-- ================================

-- 权限表
CREATE TABLE IF NOT EXISTS permissions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE COMMENT '权限名称',
    display_name VARCHAR(100) COMMENT '显示名称',
    description TEXT COMMENT '权限描述',
    permission_group VARCHAR(50) COMMENT '权限分组',
    enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建者ID',
    updated_by BIGINT COMMENT '更新者ID'
) COMMENT '权限表';

-- 角色表
CREATE TABLE IF NOT EXISTS roles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE COMMENT '角色名称',
    display_name VARCHAR(100) COMMENT '显示名称',
    description TEXT COMMENT '角色描述',
    enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建者ID',
    updated_by BIGINT COMMENT '更新者ID'
) COMMENT '角色表';

-- 角色权限关联表
CREATE TABLE IF NOT EXISTS role_permissions (
    role_id BIGINT NOT NULL,
    permission_id BIGINT NOT NULL,
    PRIMARY KEY (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
) COMMENT '角色权限关联表';

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    real_name VARCHAR(100) COMMENT '真实姓名',
    avatar VARCHAR(500) COMMENT '头像URL',
    enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    account_non_expired BOOLEAN NOT NULL DEFAULT TRUE COMMENT '账户是否未过期',
    account_non_locked BOOLEAN NOT NULL DEFAULT TRUE COMMENT '账户是否未锁定',
    credentials_non_expired BOOLEAN NOT NULL DEFAULT TRUE COMMENT '凭证是否未过期',
    last_login_time TIMESTAMP COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) COMMENT '最后登录IP',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建者ID',
    updated_by BIGINT COMMENT '更新者ID'
) COMMENT '用户表';

-- 用户角色关联表
CREATE TABLE IF NOT EXISTS user_roles (
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
) COMMENT '用户角色关联表';

-- ================================
-- 2. 猫咪相关表
-- ================================

-- 猫咪表
CREATE TABLE IF NOT EXISTS cats (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '猫咪姓名',
    breed_name VARCHAR(100) NOT NULL COMMENT '品种名称',
    gender ENUM('MALE', 'FEMALE', 'UNKNOWN') NOT NULL COMMENT '性别',
    date_of_birth TIMESTAMP NOT NULL COMMENT '出生日期',
    color VARCHAR(100) COMMENT '颜色',
    pattern VARCHAR(100) COMMENT '花纹',
    current_weight DECIMAL(8,2) COMMENT '当前体重（克）',
    microchip_id VARCHAR(50) UNIQUE COMMENT '芯片号',
    registration_number VARCHAR(50) UNIQUE COMMENT '注册号',
    status ENUM('AVAILABLE', 'ADOPTED', 'RESERVED', 'BREEDING', 'MEDICAL', 'QUARANTINE') NOT NULL DEFAULT 'AVAILABLE' COMMENT '状态',
    description TEXT COMMENT '描述',
    notes TEXT COMMENT '备注',
    primary_photo VARCHAR(500) COMMENT '主照片URL',
    is_neutered BOOLEAN DEFAULT FALSE COMMENT '是否绝育',
    father_id BIGINT COMMENT '父亲ID',
    mother_id BIGINT COMMENT '母亲ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建者ID',
    updated_by BIGINT COMMENT '更新者ID',
    FOREIGN KEY (father_id) REFERENCES cats(id) ON DELETE SET NULL,
    FOREIGN KEY (mother_id) REFERENCES cats(id) ON DELETE SET NULL
) COMMENT '猫咪表';

-- 猫咪照片表
CREATE TABLE IF NOT EXISTS cat_photos (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    cat_id BIGINT NOT NULL COMMENT '猫咪ID',
    file_name VARCHAR(255) NOT NULL COMMENT '文件名',
    original_name VARCHAR(255) COMMENT '原始文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_size BIGINT COMMENT '文件大小',
    content_type VARCHAR(100) COMMENT '文件类型',
    width INT COMMENT '图片宽度',
    height INT COMMENT '图片高度',
    thumbnail_path VARCHAR(500) COMMENT '缩略图路径',
    is_primary BOOLEAN DEFAULT FALSE COMMENT '是否为主要照片',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    description TEXT COMMENT '照片描述',
    taken_date TIMESTAMP COMMENT '拍摄日期',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建者ID',
    updated_by BIGINT COMMENT '更新者ID',
    FOREIGN KEY (cat_id) REFERENCES cats(id) ON DELETE CASCADE
) COMMENT '猫咪照片表';

-- 猫咪健康记录表
CREATE TABLE IF NOT EXISTS cat_health_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    cat_id BIGINT NOT NULL COMMENT '猫咪ID',
    record_type ENUM('VACCINATION', 'CHECKUP', 'TREATMENT', 'SURGERY', 'DENTAL', 'GROOMING', 'GENETIC_TEST', 'WEIGHT_CHECK', 'EMERGENCY', 'FOLLOW_UP') NOT NULL COMMENT '记录类型',
    record_date TIMESTAMP NOT NULL COMMENT '记录日期',
    veterinarian VARCHAR(100) COMMENT '兽医姓名',
    clinic VARCHAR(200) COMMENT '诊所名称',
    weight DECIMAL(8,2) COMMENT '体重（克）',
    temperature DECIMAL(4,1) COMMENT '体温（摄氏度）',
    vaccine_name VARCHAR(100) COMMENT '疫苗名称',
    vaccine_batch VARCHAR(50) COMMENT '疫苗批次号',
    next_vaccine_date TIMESTAMP COMMENT '下次疫苗日期',
    diagnosis TEXT COMMENT '诊断结果',
    treatment TEXT COMMENT '治疗方案',
    medication TEXT COMMENT '用药信息',
    notes TEXT COMMENT '备注',
    next_appointment TIMESTAMP COMMENT '下次预约日期',
    cost DECIMAL(10,2) COMMENT '费用',
    attachment_path VARCHAR(500) COMMENT '附件文件路径',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建者ID',
    updated_by BIGINT COMMENT '更新者ID',
    FOREIGN KEY (cat_id) REFERENCES cats(id) ON DELETE CASCADE
) COMMENT '猫咪健康记录表';

-- ================================
-- 3. 客户相关表
-- ================================

-- 客户表
CREATE TABLE IF NOT EXISTS customers (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '客户姓名',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '电话',
    address TEXT COMMENT '地址',
    customer_type ENUM('POTENTIAL', 'ACTIVE', 'ADOPTED', 'BREEDER', 'VIP') NOT NULL DEFAULT 'POTENTIAL' COMMENT '客户类型',
    id_number VARCHAR(50) COMMENT '身份证号',
    occupation VARCHAR(100) COMMENT '职业',
    age INT COMMENT '年龄',
    gender ENUM('MALE', 'FEMALE', 'OTHER') COMMENT '性别',
    marital_status ENUM('SINGLE', 'MARRIED', 'DIVORCED', 'WIDOWED') COMMENT '婚姻状况',
    family_size INT COMMENT '家庭成员数量',
    has_pet_experience BOOLEAN DEFAULT FALSE COMMENT '是否有养宠经验',
    housing_type ENUM('APARTMENT', 'HOUSE', 'TOWNHOUSE', 'CONDO', 'OTHER') COMMENT '住房类型',
    has_yard BOOLEAN DEFAULT FALSE COMMENT '是否有院子',
    preferred_breed VARCHAR(100) COMMENT '偏好的猫咪品种',
    preferred_gender ENUM('MALE', 'FEMALE', 'UNKNOWN') COMMENT '偏好的猫咪性别',
    preferred_age_range VARCHAR(50) COMMENT '偏好的猫咪年龄范围',
    budget_range VARCHAR(50) COMMENT '预算范围',
    notes TEXT COMMENT '备注',
    customer_score DECIMAL(3,1) COMMENT '客户评分',
    last_contact_date TIMESTAMP COMMENT '最后联系时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建者ID',
    updated_by BIGINT COMMENT '更新者ID'
) COMMENT '客户表';

-- ================================
-- 4. 基础索引
-- ================================

-- 用户表索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_users_enabled ON users(enabled);

-- 猫咪表索引
CREATE INDEX idx_cats_name ON cats(name);
CREATE INDEX idx_cats_breed_name ON cats(breed_name);
CREATE INDEX idx_cats_status ON cats(status);
CREATE INDEX idx_cats_gender ON cats(gender);
CREATE INDEX idx_cats_microchip_id ON cats(microchip_id);

-- 健康记录表索引
CREATE INDEX idx_health_records_cat_id ON cat_health_records(cat_id);
CREATE INDEX idx_health_records_type ON cat_health_records(record_type);
CREATE INDEX idx_health_records_date ON cat_health_records(record_date);

-- 客户表索引
CREATE INDEX idx_customers_email ON customers(email);
CREATE INDEX idx_customers_phone ON customers(phone);
CREATE INDEX idx_customers_type ON customers(customer_type);
