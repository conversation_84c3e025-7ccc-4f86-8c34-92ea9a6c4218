<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1>仪表盘</h1>
      <p>猫舍管理系统数据概览</p>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">刷新数据</el-button>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleDateRangeChange"
        />
      </div>
    </div>

    <!-- 待办提醒 -->
    <el-row :gutter="20" class="alerts-section">
      <el-col :span="24">
        <el-card class="alerts-card">
          <template #header>
            <div class="card-header">
              <span>待办提醒</span>
              <el-badge :value="totalAlerts" class="alert-badge" />
            </div>
          </template>

          <el-row :gutter="15">
            <el-col :span="6">
              <div class="alert-item health-alert" @click="$router.push('/health/reminders')">
                <el-icon class="alert-icon"><FirstAidKit /></el-icon>
                <div class="alert-content">
                  <div class="alert-count">{{ alerts.healthAlerts }}</div>
                  <div class="alert-label">健康提醒</div>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="alert-item vaccine-alert" @click="$router.push('/health/vaccines')">
                <el-icon class="alert-icon"><Medicine /></el-icon>
                <div class="alert-content">
                  <div class="alert-count">{{ alerts.vaccineAlerts }}</div>
                  <div class="alert-label">疫苗提醒</div>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="alert-item breeding-alert" @click="$router.push('/breeding')">
                <el-icon class="alert-icon"><Management /></el-icon>
                <div class="alert-content">
                  <div class="alert-count">{{ alerts.breedingAlerts }}</div>
                  <div class="alert-label">繁育提醒</div>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="alert-item inventory-alert" @click="$router.push('/inventory')">
                <el-icon class="alert-icon"><Warning /></el-icon>
                <div class="alert-content">
                  <div class="alert-count">{{ alerts.inventoryAlerts }}</div>
                  <div class="alert-label">库存预警</div>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-section">
      <el-col :span="6">
        <StatCard
          title="总猫咪数量"
          :value="dashboardData.totalCats"
          icon="Avatar"
          color="#409EFF"
          :trend="dashboardData.catsTrend"
          @click="$router.push('/cats')"
        />
      </el-col>
      <el-col :span="6">
        <StatCard
          title="客户数量"
          :value="dashboardData.totalCustomers"
          icon="User"
          color="#67C23A"
          :trend="dashboardData.customersTrend"
          @click="$router.push('/customers')"
        />
      </el-col>
      <el-col :span="6">
        <StatCard
          title="本月收入"
          :value="dashboardData.monthlyRevenue"
          icon="Money"
          color="#E6A23C"
          :trend="dashboardData.revenueTrend"
          format="currency"
          @click="$router.push('/finance')"
        />
      </el-col>
      <el-col :span="6">
        <StatCard
          title="库存物品"
          :value="dashboardData.totalInventory"
          icon="Box"
          color="#F56C6C"
          :trend="dashboardData.inventoryTrend"
          @click="$router.push('/inventory')"
        />
      </el-col>
    </el-row>
          <div class="stat-info">
            <h3>¥{{ formatCurrency(dashboardData.totalRevenue) }}</h3>
            <p>总收入</p>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 图表和数据分析 -->
    <el-row :gutter="20" class="charts-section">
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>收入趋势</span>
          </template>
          <div class="chart-container">
            <ChartCard
              type="line"
              :data="revenueChartData"
              :options="revenueChartOptions"
              height="300px"
            />
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>猫咪品种分布</span>
          </template>
          <div class="chart-container">
            <ChartCard
              type="pie"
              :data="breedChartData"
              :options="breedChartOptions"
              height="300px"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 活动和日历 -->
    <el-row :gutter="20" class="activity-section">
      <el-col :span="12">
        <el-card class="activity-card">
          <template #header>
            <span>最近活动</span>
          </template>
          <div class="activity-list">
            <div
              v-for="activity in recentActivities"
              :key="activity.id"
              class="activity-item"
            >
              <div class="activity-icon" :class="activity.type">
                <el-icon><component :is="activity.icon" /></el-icon>
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-description">{{ activity.description }}</div>
                <div class="activity-time">{{ formatTime(activity.time) }}</div>
              </div>
            </div>
          </div>
          <div class="activity-more">
            <el-button text @click="$router.push('/activities')">查看更多</el-button>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="calendar-card">
          <template #header>
            <span>日程安排</span>
          </template>
          <div class="calendar-container">
            <el-calendar v-model="calendarDate" class="dashboard-calendar">
              <template #date-cell="{ data }">
                <div class="calendar-cell">
                  <div class="cell-date">{{ data.day.split('-').slice(2).join('-') }}</div>
                  <div v-if="getEventsForDate(data.day).length > 0" class="cell-events">
                    <div
                      v-for="event in getEventsForDate(data.day).slice(0, 2)"
                      :key="event.id"
                      class="cell-event"
                      :class="event.type"
                    >
                      {{ event.title }}
                    </div>
                    <div
                      v-if="getEventsForDate(data.day).length > 2"
                      class="cell-more"
                    >
                      +{{ getEventsForDate(data.day).length - 2 }}
                    </div>
                  </div>
                </div>
              </template>
            </el-calendar>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作 -->
    <el-row :gutter="20" class="quick-actions-section">
      <el-col :span="24">
        <el-card class="quick-actions-card">
          <template #header>
            <span>快速操作</span>
          </template>
          <div class="quick-actions">
            <el-button type="primary" @click="$router.push('/cats/create')">
              <el-icon><Plus /></el-icon>
              新增猫咪
            </el-button>
            <el-button @click="$router.push('/health-records/create')">
              <el-icon><FirstAidKit /></el-icon>
              添加健康记录
            </el-button>
            <el-button @click="$router.push('/customers/create')">
              <el-icon><User /></el-icon>
              新增客户
            </el-button>
            <el-button @click="$router.push('/inventory/items/create')">
              <el-icon><Box /></el-icon>
              添加库存
            </el-button>
            <el-button @click="$router.push('/finance/transactions/create')">
              <el-icon><Money /></el-icon>
              记录交易
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Avatar, User, Box, Money, Plus, Edit, Check, FirstAidKit,
  Medicine, Management, Warning
} from '@element-plus/icons-vue'
import { getCats, getCatStats } from '@/api/cats'
import { getCustomers, getCustomerStats } from '@/api/customers'
import { getFinanceStats } from '@/api/finance'
import { getHealthStats, getVaccineReminders, getCheckupReminders } from '@/api/health'
import { getBreedingStats } from '@/api/breeding'
import StatCard from '@/components/StatCard.vue'
import ChartCard from '@/components/ChartCard.vue'

const loading = ref(false)
const dateRange = ref<[Date, Date] | null>(null)
const calendarDate = ref(new Date())

const dashboardData = ref({
  totalCats: 0,
  totalCustomers: 0,
  monthlyRevenue: 0,
  totalInventory: 0,
  catsTrend: { value: 5, type: 'up' },
  customersTrend: { value: 2, type: 'up' },
  revenueTrend: { value: 12, type: 'up' },
  inventoryTrend: { value: 3, type: 'down' }
})

const alerts = ref({
  healthAlerts: 0,
  vaccineAlerts: 0,
  breedingAlerts: 0,
  inventoryAlerts: 0
})

const recentActivities = ref<Activity[]>([])
const calendarEvents = ref<CalendarEvent[]>([])

// 图表数据
const revenueChartData = ref({
  labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
  datasets: [{
    label: '收入',
    data: [12000, 15000, 18000, 16000, 22000, 25000],
    borderColor: '#409EFF',
    backgroundColor: 'rgba(64, 158, 255, 0.1)',
    tension: 0.4
  }]
})

const revenueChartOptions = ref({
  responsive: true,
  plugins: {
    legend: {
      display: false
    }
  },
  scales: {
    y: {
      beginAtZero: true,
      ticks: {
        callback: (value: number) => `¥${value.toLocaleString()}`
      }
    }
  }
})

const breedChartData = ref({
  labels: ['英国短毛猫', '美国短毛猫', '布偶猫', '波斯猫', '其他'],
  datasets: [{
    data: [30, 25, 20, 15, 10],
    backgroundColor: [
      '#409EFF',
      '#67C23A',
      '#E6A23C',
      '#F56C6C',
      '#909399'
    ]
  }]
})

const breedChartOptions = ref({
  responsive: true,
  plugins: {
    legend: {
      position: 'bottom'
    }
  }
})

// 计算属性
const totalAlerts = computed(() => {
  return alerts.value.healthAlerts +
         alerts.value.vaccineAlerts +
         alerts.value.breedingAlerts +
         alerts.value.inventoryAlerts
})

// 方法
async function fetchDashboardData() {
  try {
    loading.value = true

    // 并行获取各种统计数据
    const [
      catsResponse,
      customersResponse,
      financeStatsResponse,
      healthStatsResponse,
      breedingStatsResponse,
      vaccineRemindersResponse,
      checkupRemindersResponse
    ] = await Promise.allSettled([
      getCats({ page: 1, size: 1 }), // 只获取总数
      getCustomers({ page: 1, size: 1 }), // 只获取总数
      getFinanceStats(),
      getHealthStats(),
      getBreedingStats(),
      getVaccineReminders(),
      getCheckupReminders()
    ])

    // 处理猫咪数据
    if (catsResponse.status === 'fulfilled' && catsResponse.value.success) {
      const catsData = catsResponse.value.data
      dashboardData.value.totalCats = Array.isArray(catsData) ? catsData.length : (catsData.total || 0)
    }

    // 处理客户数据
    if (customersResponse.status === 'fulfilled' && customersResponse.value.success) {
      const customersData = customersResponse.value.data
      dashboardData.value.totalCustomers = Array.isArray(customersData) ? customersData.length : (customersData.total || 0)
    }

    // 处理财务数据
    if (financeStatsResponse.status === 'fulfilled' && financeStatsResponse.value.success) {
      const financeData = financeStatsResponse.value.data
      dashboardData.value.monthlyRevenue = financeData.monthlyIncome || 0
    }

    // 处理健康提醒
    if (vaccineRemindersResponse.status === 'fulfilled' && vaccineRemindersResponse.value.success) {
      const vaccineData = vaccineRemindersResponse.value.data
      alerts.value.vaccineAlerts = Array.isArray(vaccineData) ? vaccineData.length : 0
    }

    if (checkupRemindersResponse.status === 'fulfilled' && checkupRemindersResponse.value.success) {
      const checkupData = checkupRemindersResponse.value.data
      alerts.value.healthAlerts = Array.isArray(checkupData) ? checkupData.length : 0
    }

    // 处理繁育数据
    if (breedingStatsResponse.status === 'fulfilled' && breedingStatsResponse.value.success) {
      const breedingData = breedingStatsResponse.value.data
      alerts.value.breedingAlerts = breedingData.activeBreeding || 0
    }

    console.log('仪表盘数据加载完成:', {
      totalCats: dashboardData.value.totalCats,
      totalCustomers: dashboardData.value.totalCustomers,
      monthlyRevenue: dashboardData.value.monthlyRevenue,
      alerts: alerts.value
    })

  } catch (error) {
    console.error('获取仪表盘数据失败:', error)
    ElMessage.error('获取仪表盘数据失败')
  } finally {
    loading.value = false
  }
}

async function refreshData() {
  await fetchDashboardData()
  ElMessage.success('数据已刷新')
}

function handleDateRangeChange(dates: [Date, Date] | null) {
  if (dates) {
    // 根据日期范围重新获取数据
    fetchDashboardData()
  }
}

function formatTime(time: string | Date) {
  const date = new Date(time)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else {
    return `${days}天前`
  }
}

function getEventsForDate(date: string): CalendarEvent[] {
  return calendarEvents.value.filter(event =>
    event.date === date
  )
}

onMounted(() => {
  fetchDashboardData()
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.dashboard-header h1 {
  margin: 0;
  color: #303133;
}

.dashboard-header p {
  margin: 5px 0 0 0;
  color: #909399;
}

.header-actions {
  display: flex;
  gap: 15px;
  align-items: center;
}

/* 提醒区域 */
.alerts-section {
  margin-bottom: 20px;
}

.alerts-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.alert-badge {
  margin-left: 10px;
}

.alert-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.alert-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.alert-item.health-alert {
  border-left: 4px solid #67C23A;
}

.alert-item.vaccine-alert {
  border-left: 4px solid #409EFF;
}

.alert-item.breeding-alert {
  border-left: 4px solid #E6A23C;
}

.alert-item.inventory-alert {
  border-left: 4px solid #F56C6C;
}

.alert-icon {
  font-size: 24px;
  margin-right: 15px;
  color: #409EFF;
}

.alert-content {
  text-align: center;
}

.alert-count {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.alert-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

/* 统计区域 */
.stats-section {
  margin-bottom: 20px;
}

/* 图表区域 */
.charts-section {
  margin-bottom: 20px;
}

.chart-card {
  height: 400px;
}

.chart-container {
  height: 300px;
}

/* 活动区域 */
.activity-section {
  margin-bottom: 20px;
}

.activity-card,
.calendar-card {
  height: 500px;
}

.activity-list {
  max-height: 400px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 16px;
  color: white;
}

.activity-icon.cat {
  background: #409EFF;
}

.activity-icon.health {
  background: #67C23A;
}

.activity-icon.customer {
  background: #E6A23C;
}

.activity-icon.adoption {
  background: #F56C6C;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 5px;
}

.activity-description {
  color: #606266;
  font-size: 14px;
  margin-bottom: 5px;
}

.activity-time {
  color: #909399;
  font-size: 12px;
}

.activity-more {
  text-align: center;
  padding: 15px 0;
  border-top: 1px solid #f0f0f0;
}

/* 日历 */
.calendar-container {
  height: 400px;
  overflow: hidden;
}

.dashboard-calendar {
  height: 100%;
}

.calendar-cell {
  height: 100%;
  padding: 2px;
}

.cell-date {
  font-size: 12px;
  color: #303133;
  margin-bottom: 2px;
}

.cell-events {
  font-size: 10px;
}

.cell-event {
  background: #409EFF;
  color: white;
  padding: 1px 3px;
  border-radius: 2px;
  margin-bottom: 1px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.cell-event.health {
  background: #67C23A;
}

.cell-event.breeding {
  background: #E6A23C;
}

.cell-event.appointment {
  background: #F56C6C;
}

.cell-more {
  color: #909399;
  font-size: 10px;
}

/* 快速操作 */
.quick-actions-section {
  margin-bottom: 20px;
}

.quick-actions {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.quick-actions .el-button {
  flex: 1;
  min-width: 150px;
}

/* 响应式 */
@media (max-width: 768px) {
  .dashboard {
    padding: 15px;
  }

  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }

  .quick-actions {
    flex-direction: column;
  }

  .quick-actions .el-button {
    min-width: auto;
  }
}
</style>
  totalCustomers: 0,
  totalProducts: 0,
  totalRevenue: 0,
  catsByBreed: []
})

const loading = ref(true)

const maxBreedCount = computed(() => {
  return Math.max(...dashboardData.value.catsByBreed.map(breed => breed.value), 1)
})

const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('zh-CN').format(amount)
}

const fetchDashboardData = async () => {
  loading.value = true
  try {
    const data = await dashboardApi.getStats()
    dashboardData.value = data
  } catch (error) {
    ElMessage.error('获取统计数据失败')
    console.error('Dashboard data fetch error:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchDashboardData()
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.dashboard-header {
  margin-bottom: 30px;
}

.dashboard-header h1 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 28px;
  font-weight: 600;
}

.dashboard-header p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.cats { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.stat-icon.customers { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.stat-icon.products { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.stat-icon.revenue { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

.stat-info h3 {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.stat-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.charts-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.chart-card {
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.breed-chart {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.breed-item {
  display: grid;
  grid-template-columns: 80px 1fr 40px;
  align-items: center;
  gap: 12px;
}

.breed-name {
  font-size: 14px;
  color: #333;
}

.breed-bar {
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.breed-progress {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  transition: width 0.3s ease;
}

.breed-count {
  font-size: 14px;
  color: #666;
  text-align: right;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  background: #f8f9fa;
}

.activity-icon {
  color: #667eea;
  font-size: 16px;
  margin-top: 2px;
}

.activity-content p {
  margin: 0 0 4px 0;
  color: #333;
  font-size: 14px;
}

.activity-time {
  color: #999;
  font-size: 12px;
}

.no-data {
  color: #999;
  text-align: center;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .charts-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>
