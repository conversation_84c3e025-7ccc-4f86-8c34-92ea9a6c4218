package com.cattery.dto.report;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 待办事项DTO
 */
@Data
public class TodoItemDTO {
    
    /**
     * 待办事项ID
     */
    private Long id;
    
    /**
     * 标题
     */
    private String title;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 优先级 (HIGH, MEDIUM, LOW)
     */
    private String priority;
    
    /**
     * 类型 (HEALTH_CHECK, VACCINATION, FEEDING, CLEANING, APPOINTMENT)
     */
    private String type;
    
    /**
     * 截止时间
     */
    private LocalDateTime dueDate;
    
    /**
     * 相关猫咪ID
     */
    private Long catId;
    
    /**
     * 相关猫咪名称
     */
    private String catName;
    
    /**
     * 状态 (PENDING, IN_PROGRESS, COMPLETED, OVERDUE)
     */
    private String status;
    
    /**
     * 负责人
     */
    private String assignedTo;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
}
