package com.cattery.util;

import lombok.RequiredArgsConstructor;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;


import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;

/**
 * 国际化消息工具类
 */
@Component
@RequiredArgsConstructor
public class MessageUtils {

    private final MessageSource messageSource;

    /**
     * 获取国际化消息
     *
     * @param code 消息代码
     * @return 国际化消息
     */
    public static String getMessage(String code) {
        return getInstance().getMessageInternal(code, null, LocaleContextHolder.getLocale());
    }

    /**
     * 获取国际化消息
     *
     * @param code 消息代码
     * @param args 参数
     * @return 国际化消息
     */
    public static String getMessage(String code, Object... args) {
        return getInstance().getMessageInternal(code, args, LocaleContextHolder.getLocale());
    }

    /**
     * 获取国际化消息
     *
     * @param code   消息代码
     * @param args   参数
     * @param locale 区域
     * @return 国际化消息
     */
    public static String getMessage(String code, Object[] args, Locale locale) {
        return getInstance().getMessageInternal(code, args, locale);
    }

    /**
     * 获取国际化消息（带默认值）
     *
     * @param code         消息代码
     * @param defaultMessage 默认消息
     * @return 国际化消息
     */
    public static String getMessage(String code, String defaultMessage) {
        return getInstance().getMessageInternal(code, null, defaultMessage, LocaleContextHolder.getLocale());
    }

    /**
     * 获取国际化消息（带默认值）
     *
     * @param code         消息代码
     * @param args         参数
     * @param defaultMessage 默认消息
     * @return 国际化消息
     */
    public static String getMessage(String code, Object[] args, String defaultMessage) {
        return getInstance().getMessageInternal(code, args, defaultMessage, LocaleContextHolder.getLocale());
    }

    /**
     * 格式化日期
     *
     * @param date 日期
     * @return 格式化后的日期字符串
     */
    public static String formatDate(LocalDate date) {
        if (date == null) {
            return "";
        }
        
        Locale locale = LocaleContextHolder.getLocale();
        String pattern = getInstance().getMessageInternal("time.format.date", null, "yyyy-MM-dd", locale);
        
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern, locale);
            return date.format(formatter);
        } catch (Exception e) {
            // 如果格式化失败，使用默认格式
            return date.format(DateTimeFormatter.ISO_LOCAL_DATE);
        }
    }

    /**
     * 格式化日期时间
     *
     * @param dateTime 日期时间
     * @return 格式化后的日期时间字符串
     */
    public static String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }
        
        Locale locale = LocaleContextHolder.getLocale();
        String pattern = getInstance().getMessageInternal("time.format.datetime", null, "yyyy-MM-dd HH:mm:ss", locale);
        
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern, locale);
            return dateTime.format(formatter);
        } catch (Exception e) {
            // 如果格式化失败，使用默认格式
            return dateTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        }
    }

    /**
     * 格式化货币
     *
     * @param amount 金额
     * @return 格式化后的货币字符串
     */
    public static String formatCurrency(Double amount) {
        if (amount == null) {
            return "";
        }
        
        Locale locale = LocaleContextHolder.getLocale();
        String symbol = getInstance().getMessageInternal("currency.symbol", null, "¥", locale);
        
        return symbol + String.format("%.2f", amount);
    }

    /**
     * 获取性别文本
     *
     * @param gender 性别枚举值
     * @return 性别文本
     */
    public static String getGenderText(String gender) {
        if (gender == null) {
            return getMessage("gender.unknown");
        }
        
        switch (gender.toUpperCase()) {
            case "MALE":
                return getMessage("gender.male");
            case "FEMALE":
                return getMessage("gender.female");
            default:
                return getMessage("gender.unknown");
        }
    }

    /**
     * 获取状态文本
     *
     * @param status 状态值
     * @param type   状态类型（cat, health, breeding等）
     * @return 状态文本
     */
    public static String getStatusText(String status, String type) {
        if (status == null) {
            return "";
        }
        
        String code = String.format("status.%s.%s", type, status.toLowerCase());
        return getMessage(code, status);
    }

    /**
     * 获取记录类型文本
     *
     * @param recordType 记录类型
     * @return 记录类型文本
     */
    public static String getRecordTypeText(String recordType) {
        if (recordType == null) {
            return "";
        }
        
        String code = "recordType." + recordType.toLowerCase();
        return getMessage(code, recordType);
    }

    /**
     * 获取单位文本
     *
     * @param unit 单位
     * @return 单位文本
     */
    public static String getUnitText(String unit) {
        if (unit == null) {
            return "";
        }
        
        String code = "unit." + unit.toLowerCase();
        return getMessage(code, unit);
    }

    /**
     * 格式化年龄
     *
     * @param ageInMonths 年龄（月）
     * @return 格式化后的年龄字符串
     */
    public static String formatAge(Integer ageInMonths) {
        if (ageInMonths == null || ageInMonths < 0) {
            return "";
        }
        
        if (ageInMonths < 12) {
            return ageInMonths + " " + getUnitText("month");
        } else {
            int years = ageInMonths / 12;
            int months = ageInMonths % 12;
            
            if (months == 0) {
                return years + " " + getUnitText("year");
            } else {
                return years + " " + getUnitText("year") + " " + months + " " + getUnitText("month");
            }
        }
    }

    /**
     * 格式化重量
     *
     * @param weight 重量（克）
     * @return 格式化后的重量字符串
     */
    public static String formatWeight(Double weight) {
        if (weight == null) {
            return "";
        }
        
        if (weight >= 1000) {
            return String.format("%.2f %s", weight / 1000, getUnitText("kg"));
        } else {
            return String.format("%.0f %s", weight, getUnitText("g"));
        }
    }

    /**
     * 获取分页信息文本
     *
     * @param current 当前页
     * @param size    每页大小
     * @param total   总记录数
     * @return 分页信息文本
     */
    public static String getPageInfo(int current, int size, long total) {
        int start = (current - 1) * size + 1;
        int end = Math.min(current * size, (int) total);
        
        return getMessage("page.info", new Object[]{start, end, total});
    }

    /**
     * 生成操作日志消息
     *
     * @param operation 操作类型
     * @param target    操作目标
     * @return 日志消息
     */
    public static String getLogMessage(String operation, String target) {
        String code = "log." + operation.toLowerCase();
        return getMessage(code, target);
    }

    /**
     * 生成通知消息
     *
     * @param type   通知类型
     * @param params 参数
     * @return 通知消息
     */
    public static String getNotificationMessage(String type, Object... params) {
        String code = "notification." + type;
        return getMessage(code, params);
    }

    /**
     * 获取邮件主题
     *
     * @param type 邮件类型
     * @return 邮件主题
     */
    public static String getEmailSubject(String type) {
        String code = "email.subject." + type;
        return getMessage(code);
    }

    /**
     * 获取短信模板
     *
     * @param type   短信类型
     * @param params 参数
     * @return 短信内容
     */
    public static String getSmsTemplate(String type, Object... params) {
        String code = "sms." + type;
        return getMessage(code, params);
    }

    /**
     * 检查是否支持指定区域
     *
     * @param locale 区域
     * @return 是否支持
     */
    public static boolean isSupportedLocale(Locale locale) {
        if (locale == null) {
            return false;
        }
        
        String language = locale.getLanguage();
        String country = locale.getCountry();
        
        // 支持的语言列表
        return ("zh".equals(language) && "CN".equals(country)) ||
               ("en".equals(language) && "US".equals(country)) ||
               ("ja".equals(language) && "JP".equals(country)) ||
               ("ko".equals(language) && "KR".equals(country));
    }

    /**
     * 获取默认区域
     *
     * @return 默认区域
     */
    public static Locale getDefaultLocale() {
        return Locale.SIMPLIFIED_CHINESE;
    }

    // 私有方法

    private String getMessageInternal(String code, Object[] args, Locale locale) {
        try {
            return messageSource.getMessage(code, args, locale);
        } catch (Exception e) {
            return code;
        }
    }

    private String getMessageInternal(String code, Object[] args, String defaultMessage, Locale locale) {
        try {
            return messageSource.getMessage(code, args, defaultMessage, locale);
        } catch (Exception e) {
            return defaultMessage != null ? defaultMessage : code;
        }
    }

    // 单例获取
    private static MessageUtils instance;

    public static MessageUtils getInstance() {
        return instance;
    }


}
