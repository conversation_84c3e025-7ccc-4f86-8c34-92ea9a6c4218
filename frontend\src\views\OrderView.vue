<template>
  <div>
    <el-card>
      <template #header>
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <span>订单管理</span>
          <el-button type="primary" @click="handleOpenDialog()">创建新订单</el-button>
        </div>
      </template>
      
      <el-table :data="orders" v-loading="loading" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="customerName" label="客户" width="120" />
        <el-table-column prop="totalPrice" label="总价" width="100">
          <template #default="scope">
            ¥{{ scope.row.totalPrice.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="orderDate" label="订单日期" width="180" />
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button size="small" @click="handleViewOrder(scope.row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 创建订单对话框 -->
    <el-dialog v-model="dialogVisible" title="创建新订单" width="800px">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="客户" prop="customerId">
          <el-select v-model="form.customerId" placeholder="请选择客户" style="width: 100%">
            <el-option v-for="customer in customers" :key="customer.id" :label="customer.name" :value="customer.id" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="订单项目">
          <el-button @click="addOrderItem" type="primary" size="small">添加商品</el-button>
          <el-table :data="form.orderItems" style="width: 100%; margin-top: 10px;">
            <el-table-column label="商品" width="200">
              <template #default="scope">
                <el-select v-model="scope.row.productId" placeholder="选择商品" style="width: 100%">
                  <el-option v-for="product in products" :key="product.id" :label="product.name" :value="product.id" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="数量" width="120">
              <template #default="scope">
                <el-input-number v-model="scope.row.quantity" :min="1" size="small" />
              </template>
            </el-table-column>
            <el-table-column label="单价" width="120">
              <template #default="scope">
                <span>¥{{ getProductPrice(scope.row.productId) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="小计" width="120">
              <template #default="scope">
                <span>¥{{ (scope.row.quantity * getProductPrice(scope.row.productId)).toFixed(2) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template #default="scope">
                <el-button size="small" type="danger" @click="removeOrderItem(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        
        <el-form-item label="总计">
          <span style="font-size: 18px; font-weight: bold;">¥{{ totalAmount.toFixed(2) }}</span>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">创建订单</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

const orders = ref([]);
const customers = ref([]);
const products = ref([]);
const loading = ref(true);
const dialogVisible = ref(false);
const form = ref({
  customerId: null,
  orderItems: []
});
const formRef = ref(null);

const rules = {
  customerId: [{ required: true, message: '请选择客户', trigger: 'change' }]
};

const totalAmount = computed(() => {
  return form.value.orderItems.reduce((total, item) => {
    return total + (item.quantity * getProductPrice(item.productId));
  }, 0);
});

async function fetchData() {
  loading.value = true;
  try {
    const response = await fetch('/api/orders');
    if (!response.ok) throw new Error('Failed to fetch orders');
    const result = await response.json();
    const ordersData = result.data || [];
    orders.value = ordersData.map((o: any) => ({...o, orderDate: new Date(o.orderDate).toLocaleString()}));
  } catch (error) {
    ElMessage.error('获取订单列表失败');
  } finally {
    loading.value = false;
  }
}

async function fetchDependencies() {
    try {
        const [custRes, prodRes] = await Promise.all([
            fetch('/api/customers'),
            fetch('/api/products')
        ]);
        const custResult = await custRes.json();
        const prodResult = await prodRes.json();
        customers.value = custResult.data || [];
        products.value = prodResult.data || [];
    } catch(e) {
        ElMessage.error('获取客户或商品列表失败');
    }
}

function getProductPrice(productId) {
  const product = products.value.find(p => p.id === productId);
  return product ? product.price : 0;
}

function getStatusType(status) {
  const statusMap = {
    'PENDING': 'warning',
    'COMPLETED': 'success',
    'CANCELLED': 'danger'
  };
  return statusMap[status] || '';
}

function handleOpenDialog() {
  dialogVisible.value = true;
  form.value = {
    customerId: null,
    orderItems: []
  };
}

function addOrderItem() {
  form.value.orderItems.push({
    productId: null,
    quantity: 1
  });
}

function removeOrderItem(index) {
  form.value.orderItems.splice(index, 1);
}

async function handleSubmit() {
  await formRef.value.validate();
  
  if (form.value.orderItems.length === 0) {
    ElMessage.error('请至少添加一个商品');
    return;
  }

  try {
    const response = await fetch('/api/orders', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(form.value)
    });
    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '创建订单失败');
    }
    ElMessage.success('订单创建成功');
    dialogVisible.value = false;
    fetchData();
  } catch (error) {
    ElMessage.error(error instanceof Error ? error.message : '创建订单失败');
  }
}

function handleViewOrder(order) {
  ElMessage.info('查看订单详情功能待实现');
}

onMounted(() => {
  fetchData();
  fetchDependencies();
});
</script>
