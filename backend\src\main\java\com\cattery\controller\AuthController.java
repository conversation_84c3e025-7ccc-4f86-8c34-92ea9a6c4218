package com.cattery.controller;

import com.cattery.dto.auth.LoginRequest;
import com.cattery.dto.auth.LoginResponse;
import com.cattery.dto.auth.RegisterRequest;
import com.cattery.dto.common.ApiResponse;
import com.cattery.security.JwtTokenProvider;
import com.cattery.service.AuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

/**
 * 认证控制器
 */
@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "认证管理", description = "用户登录、注册、登出等认证相关接口")
public class AuthController {

    private final AuthenticationManager authenticationManager;
    private final JwtTokenProvider tokenProvider;
    private final AuthService authService;

    /**
     * 用户登录
     */
    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "用户使用用户名和密码登录系统")
    public ResponseEntity<ApiResponse<LoginResponse>> login(@Valid @RequestBody LoginRequest loginRequest) {
        log.info("用户登录请求: {}", loginRequest.getUsername());
        
        try {
            // 认证用户
            Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                    loginRequest.getUsername(),
                    loginRequest.getPassword()
                )
            );
            
            SecurityContextHolder.getContext().setAuthentication(authentication);
            
            // 生成JWT令牌
            String jwt = tokenProvider.generateToken(authentication);
            
            // 构建响应
            LoginResponse response = authService.buildLoginResponse(authentication, jwt);
            
            log.info("用户登录成功: {}", loginRequest.getUsername());
            
            return ResponseEntity.ok(ApiResponse.success("登录成功", response));
            
        } catch (Exception e) {
            log.error("用户登录失败: {}", loginRequest.getUsername(), e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("用户名或密码错误"));
        }
    }

    /**
     * 用户注册
     */
    @PostMapping("/register")
    @Operation(summary = "用户注册", description = "新用户注册账户")
    public ResponseEntity<ApiResponse<String>> register(@Valid @RequestBody RegisterRequest registerRequest) {
        log.info("用户注册请求: {}", registerRequest.getUsername());
        
        try {
            authService.register(registerRequest);
            log.info("用户注册成功: {}", registerRequest.getUsername());
            
            return ResponseEntity.ok(ApiResponse.success("注册成功"));
            
        } catch (Exception e) {
            log.error("用户注册失败: {}", registerRequest.getUsername(), e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 刷新令牌
     */
    @PostMapping("/refresh")
    @Operation(summary = "刷新令牌", description = "使用有效的JWT令牌获取新的令牌")
    public ResponseEntity<ApiResponse<LoginResponse>> refreshToken(
            @RequestHeader("Authorization") String authHeader) {
        
        try {
            if (authHeader != null && authHeader.startsWith("Bearer ")) {
                String token = authHeader.substring(7);
                
                if (tokenProvider.validateToken(token)) {
                    String newToken = tokenProvider.refreshToken(token);
                    String username = tokenProvider.getUsernameFromToken(token);
                    
                    LoginResponse response = authService.buildRefreshResponse(username, newToken);
                    
                    log.info("令牌刷新成功: {}", username);
                    
                    return ResponseEntity.ok(ApiResponse.success("令牌刷新成功", response));
                }
            }
            
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("无效的令牌"));
            
        } catch (Exception e) {
            log.error("令牌刷新失败", e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("令牌刷新失败"));
        }
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    @Operation(summary = "用户登出", description = "用户登出系统")
    public ResponseEntity<ApiResponse<String>> logout() {
        try {
            SecurityContextHolder.clearContext();
            log.info("用户登出成功");
            
            return ResponseEntity.ok(ApiResponse.success("登出成功"));
            
        } catch (Exception e) {
            log.error("用户登出失败", e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("登出失败"));
        }
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/me")
    @Operation(summary = "获取当前用户信息", description = "获取当前登录用户的详细信息")
    public ResponseEntity<ApiResponse<LoginResponse>> getCurrentUser() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            
            if (authentication != null && authentication.isAuthenticated()) {
                LoginResponse response = authService.buildCurrentUserResponse(authentication);
                
                return ResponseEntity.ok(ApiResponse.success("获取用户信息成功", response));
            }
            
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("用户未登录"));
            
        } catch (Exception e) {
            log.error("获取当前用户信息失败", e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("获取用户信息失败"));
        }
    }

    /**
     * 检查用户名是否可用
     */
    @GetMapping("/check-username")
    @Operation(summary = "检查用户名", description = "检查用户名是否已被使用")
    public ResponseEntity<ApiResponse<Boolean>> checkUsername(@RequestParam String username) {
        try {
            boolean available = authService.isUsernameAvailable(username);
            
            return ResponseEntity.ok(ApiResponse.success("检查完成", available));
            
        } catch (Exception e) {
            log.error("检查用户名失败", e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("检查失败"));
        }
    }

    /**
     * 检查邮箱是否可用
     */
    @GetMapping("/check-email")
    @Operation(summary = "检查邮箱", description = "检查邮箱是否已被使用")
    public ResponseEntity<ApiResponse<Boolean>> checkEmail(@RequestParam String email) {
        try {
            boolean available = authService.isEmailAvailable(email);
            
            return ResponseEntity.ok(ApiResponse.success("检查完成", available));
            
        } catch (Exception e) {
            log.error("检查邮箱失败", e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("检查失败"));
        }
    }
}
