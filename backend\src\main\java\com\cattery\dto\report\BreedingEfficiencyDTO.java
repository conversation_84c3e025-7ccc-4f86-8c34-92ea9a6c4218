package com.cattery.dto.report;

import lombok.Data;

/**
 * 繁育效率DTO
 */
@Data
public class BreedingEfficiencyDTO {
    
    /**
     * 受孕率
     */
    private Double conceptionRate;
    
    /**
     * 分娩成功率
     */
    private Double birthSuccessRate;
    
    /**
     * 小猫存活率
     */
    private Double kittenSurvivalRate;
    
    /**
     * 平均怀孕周期（天）
     */
    private Double averageGestationPeriod;
    
    /**
     * 平均断奶时间（天）
     */
    private Double averageWeaningTime;
    
    /**
     * 母猫健康评分
     */
    private Double motherHealthScore;
    
    /**
     * 小猫健康评分
     */
    private Double kittenHealthScore;
    
    /**
     * 繁育周期效率
     */
    private Double breedingCycleEfficiency;
    
    /**
     * 遗传多样性指数
     */
    private Double geneticDiversityIndex;
}
