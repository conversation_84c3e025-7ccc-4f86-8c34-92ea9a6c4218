@echo off
echo ========================================
echo Simple npm Fix
echo ========================================
echo.

echo Current Status:
echo   Node.js: Working (v22.17.1)
echo   npm: Broken
echo   Goal: Fix npm only
echo.

echo Method 1: Try to reinstall npm globally
echo.

echo Step 1: Download npm installer script
curl -L https://www.npmjs.com/install.sh -o npm-install.sh 2>nul
if exist npm-install.sh (
    echo Downloaded npm installer
    del npm-install.sh
) else (
    echo Download failed, trying alternative...
)

echo.
echo Step 2: Try manual npm installation
echo Opening npm releases page...
start https://github.com/npm/cli/releases/latest

echo.
echo Step 3: Alternative - Install yarn
echo Opening yarn installation page...
start https://yarnpkg.com/getting-started/install

echo.
echo ========================================
echo Manual Fix Instructions
echo ========================================
echo.

echo Option A: Download and install npm manually
echo 1. Go to: https://github.com/npm/cli/releases/latest
echo 2. Download: npm-X.X.X.tgz
echo 3. Extract to Node.js directory
echo.

echo Option B: Install yarn as npm replacement
echo 1. Go to: https://yarnpkg.com/getting-started/install
echo 2. Download and install yarn
echo 3. Use yarn instead of npm commands
echo.

echo Option C: Try PowerShell npm fix
echo 1. Open PowerShell as Administrator
echo 2. Run: Set-ExecutionPolicy RemoteSigned
echo 3. Run: iwr https://get.pnpm.io/install.ps1 -useb | iex
echo.

echo ========================================
echo Test npm after fix
echo ========================================
echo.

echo Testing npm...
npm --version 2>nul
if %errorlevel% equ 0 (
    echo SUCCESS: npm is working!
    echo npm version:
    npm --version
    echo.
    echo Now installing project dependencies...
    cd frontend
    npm install
    if %errorlevel% equ 0 (
        echo.
        echo SUCCESS: Dependencies installed!
        echo Starting development server...
        npm run dev
    )
) else (
    echo npm still not working
    echo Please try one of the manual methods above
)

echo.
pause
