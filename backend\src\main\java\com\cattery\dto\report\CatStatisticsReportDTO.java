package com.cattery.dto.report;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 猫咪统计报表DTO
 */
@Data
public class CatStatisticsReportDTO {
    
    /**
     * 报表周期
     */
    private String reportPeriod;
    
    /**
     * 生成时间
     */
    private LocalDateTime generatedAt;
    
    /**
     * 总猫咪数
     */
    private Long totalCats;
    
    /**
     * 报表期间新增猫咪数
     */
    private Long newCatsInPeriod;
    
    /**
     * 按性别分布
     * Key: 性别 (MALE, FEMALE)
     * Value: 数量
     */
    private Map<String, Long> genderDistribution;
    
    /**
     * 按品种分布
     * Key: 品种名称
     * Value: 数量
     */
    private Map<String, Long> breedDistribution;
    
    /**
     * 按状态分布
     * Key: 状态 (AVAILABLE, ADOPTED, RESERVED, BREEDING, MEDICAL, QUARANTINE)
     * Value: 数量
     */
    private Map<String, Long> statusDistribution;
    
    /**
     * 按年龄分布
     * Key: 年龄组 (幼猫, 青年猫, 成年猫, 老年猫)
     * Value: 数量
     */
    private Map<String, Long> ageDistribution;
    
    /**
     * 健康统计
     */
    private HealthStatisticsDTO healthStatistics;
    
    /**
     * 健康统计内部类
     */
    @Data
    public static class HealthStatisticsDTO {
        /**
         * 健康猫咪数
         */
        private Long healthyCats;
        
        /**
         * 需要关注的猫咪数
         */
        private Long catsNeedingAttention;
        
        /**
         * 疫苗接种率
         */
        private Double vaccinationRate;
        
        /**
         * 体检覆盖率
         */
        private Double checkupCoverage;
        
        /**
         * 平均健康评分
         */
        private Double averageHealthScore;
        
        /**
         * 常见健康问题
         * Key: 健康问题
         * Value: 发生次数
         */
        private Map<String, Long> commonHealthIssues;
    }
}
