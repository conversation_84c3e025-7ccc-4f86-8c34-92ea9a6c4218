<template>
  <div class="accessibility-example">
    <!-- 跳转链接 -->
    <a href="#main-content" class="skip-link">跳转到主要内容</a>
    
    <h1>可访问性表单示例</h1>
    
    <main id="main-content">
      <!-- 正确的表单标签使用 -->
      <form @submit.prevent="handleSubmit" class="accessible-form">
        
        <!-- 1. 正确的label for属性使用 -->
        <div class="form-field">
          <label for="username">用户名 <span class="required">*</span></label>
          <input 
            id="username"
            name="username"
            type="text"
            v-model="form.username"
            required
            aria-describedby="username-help"
            :aria-invalid="errors.username ? 'true' : 'false'"
            class="form-input"
          />
          <div id="username-help" class="help-text">
            请输入您的用户名，3-20个字符
          </div>
          <div v-if="errors.username" class="error-message" role="alert">
            {{ errors.username }}
          </div>
        </div>

        <!-- 2. Select元素必须有可访问的名称 -->
        <div class="form-field">
          <label for="cat-breed">猫咪品种</label>
          <select 
            id="cat-breed"
            name="catBreed"
            v-model="form.catBreed"
            aria-describedby="breed-help"
            class="form-select"
          >
            <option value="">请选择品种</option>
            <option value="persian">波斯猫</option>
            <option value="siamese">暹罗猫</option>
            <option value="british">英国短毛猫</option>
            <option value="maine-coon">缅因猫</option>
          </select>
          <div id="breed-help" class="help-text">
            选择您感兴趣的猫咪品种
          </div>
        </div>

        <!-- 3. 带有title和placeholder的输入框 -->
        <div class="form-field">
          <label for="email">邮箱地址</label>
          <input 
            id="email"
            name="email"
            type="email"
            v-model="form.email"
            title="请输入有效的邮箱地址"
            placeholder="<EMAIL>"
            aria-describedby="email-help"
            class="form-input"
          />
          <div id="email-help" class="help-text">
            我们将通过邮箱与您联系
          </div>
        </div>

        <!-- 4. 带有aria-label的输入框 -->
        <div class="form-field">
          <input 
            id="search"
            name="search"
            type="search"
            v-model="form.search"
            aria-label="搜索猫咪"
            placeholder="搜索猫咪名称或品种"
            class="form-input"
          />
        </div>

        <!-- 5. 单选按钮组 -->
        <fieldset class="form-fieldset">
          <legend>性别偏好</legend>
          <div class="radio-group">
            <div class="radio-item">
              <input 
                id="gender-male"
                name="genderPreference"
                type="radio"
                value="male"
                v-model="form.genderPreference"
              />
              <label for="gender-male">公猫</label>
            </div>
            <div class="radio-item">
              <input 
                id="gender-female"
                name="genderPreference"
                type="radio"
                value="female"
                v-model="form.genderPreference"
              />
              <label for="gender-female">母猫</label>
            </div>
            <div class="radio-item">
              <input 
                id="gender-any"
                name="genderPreference"
                type="radio"
                value="any"
                v-model="form.genderPreference"
              />
              <label for="gender-any">无偏好</label>
            </div>
          </div>
        </fieldset>

        <!-- 6. 复选框 -->
        <div class="form-field">
          <div class="checkbox-item">
            <input 
              id="newsletter"
              name="newsletter"
              type="checkbox"
              v-model="form.newsletter"
            />
            <label for="newsletter">订阅猫舍新闻</label>
          </div>
        </div>

        <!-- 7. 文本域 -->
        <div class="form-field">
          <label for="message">留言</label>
          <textarea 
            id="message"
            name="message"
            v-model="form.message"
            rows="4"
            aria-describedby="message-help"
            class="form-textarea"
          ></textarea>
          <div id="message-help" class="help-text">
            请告诉我们您的需求或问题
          </div>
        </div>

        <!-- 8. 避免嵌套交互控件的按钮 -->
        <div class="form-actions">
          <button 
            type="submit" 
            class="button-primary"
            :disabled="isSubmitting"
            :aria-busy="isSubmitting"
          >
            <span v-if="isSubmitting">提交中...</span>
            <span v-else>提交表单</span>
          </button>
          
          <button 
            type="button" 
            @click="resetForm"
            class="button-secondary"
          >
            重置
          </button>
        </div>
      </form>

      <!-- 9. 可访问的组合框示例 -->
      <div class="combobox-example">
        <h2>智能搜索示例</h2>
        <div class="combobox" role="combobox" :aria-expanded="showDropdown">
          <label for="cat-search">搜索猫咪</label>
          <input 
            id="cat-search"
            ref="comboboxInput"
            v-model="searchQuery"
            @input="handleSearch"
            @keydown="handleKeydown"
            @focus="showDropdown = true"
            aria-autocomplete="list"
            :aria-activedescendant="activeOptionId"
            class="combobox-input"
            placeholder="输入猫咪名称"
          />
          
          <ul 
            v-if="showDropdown && filteredCats.length"
            class="combobox-dropdown"
            role="listbox"
          >
            <li 
              v-for="(cat, index) in filteredCats"
              :key="cat.id"
              :id="`option-${index}`"
              role="option"
              :aria-selected="index === activeOptionIndex"
              @click="selectCat(cat)"
              @mouseenter="activeOptionIndex = index"
              class="combobox-option"
              :class="{ active: index === activeOptionIndex }"
            >
              {{ cat.name }} - {{ cat.breed }}
            </li>
          </ul>
        </div>
      </div>

      <!-- 10. 状态消息 -->
      <div 
        v-if="statusMessage"
        :class="['status-message', statusType]"
        role="status"
        aria-live="polite"
      >
        {{ statusMessage }}
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'

// 表单数据
const form = ref({
  username: '',
  catBreed: '',
  email: '',
  search: '',
  genderPreference: '',
  newsletter: false,
  message: ''
})

// 错误状态
const errors = ref({
  username: ''
})

// 提交状态
const isSubmitting = ref(false)

// 状态消息
const statusMessage = ref('')
const statusType = ref('info')

// 组合框相关
const searchQuery = ref('')
const showDropdown = ref(false)
const activeOptionIndex = ref(-1)
const comboboxInput = ref<HTMLInputElement>()

// 模拟猫咪数据
const cats = ref([
  { id: 1, name: '小白', breed: '波斯猫' },
  { id: 2, name: '小黑', breed: '暹罗猫' },
  { id: 3, name: '橘子', breed: '英国短毛猫' },
  { id: 4, name: '灰灰', breed: '缅因猫' },
  { id: 5, name: '花花', breed: '布偶猫' }
])

// 过滤的猫咪列表
const filteredCats = computed(() => {
  if (!searchQuery.value) return cats.value
  return cats.value.filter(cat => 
    cat.name.includes(searchQuery.value) || 
    cat.breed.includes(searchQuery.value)
  )
})

// 当前活跃选项的ID
const activeOptionId = computed(() => {
  return activeOptionIndex.value >= 0 ? `option-${activeOptionIndex.value}` : ''
})

// 处理表单提交
const handleSubmit = async () => {
  // 验证表单
  errors.value = {}
  
  if (!form.value.username) {
    errors.value.username = '用户名不能为空'
    return
  }
  
  if (form.value.username.length < 3) {
    errors.value.username = '用户名至少3个字符'
    return
  }

  isSubmitting.value = true
  statusMessage.value = '正在提交表单...'
  statusType.value = 'info'

  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    statusMessage.value = '表单提交成功！'
    statusType.value = 'success'
    
    // 重置表单
    resetForm()
  } catch (error) {
    statusMessage.value = '提交失败，请重试'
    statusType.value = 'error'
  } finally {
    isSubmitting.value = false
    
    // 3秒后清除状态消息
    setTimeout(() => {
      statusMessage.value = ''
    }, 3000)
  }
}

// 重置表单
const resetForm = () => {
  form.value = {
    username: '',
    catBreed: '',
    email: '',
    search: '',
    genderPreference: '',
    newsletter: false,
    message: ''
  }
  errors.value = {}
}

// 处理搜索
const handleSearch = () => {
  activeOptionIndex.value = -1
  showDropdown.value = true
}

// 处理键盘导航
const handleKeydown = (event: KeyboardEvent) => {
  if (!showDropdown.value) return

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      activeOptionIndex.value = Math.min(
        activeOptionIndex.value + 1,
        filteredCats.value.length - 1
      )
      break
    case 'ArrowUp':
      event.preventDefault()
      activeOptionIndex.value = Math.max(activeOptionIndex.value - 1, -1)
      break
    case 'Enter':
      event.preventDefault()
      if (activeOptionIndex.value >= 0) {
        selectCat(filteredCats.value[activeOptionIndex.value])
      }
      break
    case 'Escape':
      showDropdown.value = false
      activeOptionIndex.value = -1
      break
  }
}

// 选择猫咪
const selectCat = (cat: any) => {
  searchQuery.value = `${cat.name} - ${cat.breed}`
  showDropdown.value = false
  activeOptionIndex.value = -1
  
  nextTick(() => {
    comboboxInput.value?.focus()
  })
}
</script>

<style scoped>
/* 使用导入的可访问性样式 */
@import '../assets/accessibility-fixes.css';

.accessibility-example {
  max-width: 600px;
  margin: 0 auto;
  padding: 2rem;
}

.accessible-form {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-fieldset {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.form-fieldset legend {
  font-weight: 500;
  padding: 0 0.5rem;
}

.radio-group {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.radio-item,
.checkbox-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

.button-primary {
  background: #409eff;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.button-primary:hover:not(:disabled) {
  background: #337ecc;
}

.button-primary:disabled {
  background: #c0c4cc;
  cursor: not-allowed;
}

.button-secondary {
  background: #f5f7fa;
  color: #606266;
  border: 1px solid #dcdfe6;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.button-secondary:hover {
  background: #ecf5ff;
  border-color: #409eff;
  color: #409eff;
}

.combobox-example {
  margin-top: 2rem;
  padding: 2rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.combobox-option.active {
  background: #409eff;
  color: white;
}

.status-message {
  margin-top: 1rem;
  padding: 0.75rem;
  border-radius: 4px;
  font-size: 14px;
}

.status-message.info {
  background: #ecf5ff;
  color: #409eff;
  border: 1px solid #b3d8ff;
}

.status-message.success {
  background: #f0f9ff;
  color: #67c23a;
  border: 1px solid #c2e7b0;
}

.status-message.error {
  background: #fef0f0;
  color: #f56c6c;
  border: 1px solid #fbc4c4;
}

.required {
  color: #f56c6c;
}
</style>
