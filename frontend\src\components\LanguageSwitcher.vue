<template>
  <div class="language-switcher">
    <!-- 下拉菜单模式 -->
    <el-dropdown 
      v-if="mode === 'dropdown'"
      @command="handleLanguageChange"
      trigger="click"
      placement="bottom-end"
    >
      <el-button 
        :type="buttonType" 
        :size="size"
        :text="text"
        :bg="bg"
      >
        <span class="language-flag">{{ currentLocaleInfo?.flag }}</span>
        <span v-if="showText" class="language-name">{{ currentLocaleInfo?.name }}</span>
        <el-icon class="el-icon--right">
          <arrow-down />
        </el-icon>
      </el-button>
      
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item
            v-for="locale in SUPPORT_LOCALES"
            :key="locale.code"
            :command="locale.code"
            :disabled="locale.code === currentLocale"
            class="language-item"
          >
            <span class="language-flag">{{ locale.flag }}</span>
            <span class="language-name">{{ locale.name }}</span>
            <el-icon v-if="locale.code === currentLocale" class="check-icon">
              <check />
            </el-icon>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>

    <!-- 选择器模式 -->
    <el-select
      v-else-if="mode === 'select'"
      :model-value="currentLocale"
      @change="handleLanguageChange"
      :size="size"
      :placeholder="$t('common.selectPlaceholder')"
      style="width: 150px"
    >
      <el-option
        v-for="locale in SUPPORT_LOCALES"
        :key="locale.code"
        :label="locale.name"
        :value="locale.code"
      >
        <div class="language-option">
          <span class="language-flag">{{ locale.flag }}</span>
          <span class="language-name">{{ locale.name }}</span>
        </div>
      </el-option>
    </el-select>

    <!-- 按钮组模式 -->
    <el-button-group v-else-if="mode === 'buttons'">
      <el-button
        v-for="locale in SUPPORT_LOCALES"
        :key="locale.code"
        :type="locale.code === currentLocale ? 'primary' : 'default'"
        :size="size"
        @click="handleLanguageChange(locale.code)"
      >
        <span class="language-flag">{{ locale.flag }}</span>
        <span v-if="showText" class="language-name">{{ locale.name }}</span>
      </el-button>
    </el-button-group>

    <!-- 标签页模式 -->
    <el-tabs
      v-else-if="mode === 'tabs'"
      :model-value="currentLocale"
      @tab-change="handleLanguageChange"
      :size="size"
      type="card"
    >
      <el-tab-pane
        v-for="locale in SUPPORT_LOCALES"
        :key="locale.code"
        :label="locale.name"
        :name="locale.code"
      >
        <template #label>
          <span class="language-flag">{{ locale.flag }}</span>
          <span v-if="showText" class="language-name">{{ locale.name }}</span>
        </template>
      </el-tab-pane>
    </el-tabs>

    <!-- 弹出框模式 -->
    <div v-else-if="mode === 'popover'">
      <el-popover
        placement="bottom"
        :width="200"
        trigger="click"
        :title="$t('common.language')"
      >
        <template #reference>
          <el-button 
            :type="buttonType" 
            :size="size"
            :text="text"
            :bg="bg"
          >
            <span class="language-flag">{{ currentLocaleInfo?.flag }}</span>
            <span v-if="showText" class="language-name">{{ currentLocaleInfo?.name }}</span>
          </el-button>
        </template>
        
        <div class="language-list">
          <div
            v-for="locale in SUPPORT_LOCALES"
            :key="locale.code"
            class="language-list-item"
            :class="{ active: locale.code === currentLocale }"
            @click="handleLanguageChange(locale.code)"
          >
            <span class="language-flag">{{ locale.flag }}</span>
            <span class="language-name">{{ locale.name }}</span>
            <el-icon v-if="locale.code === currentLocale" class="check-icon">
              <check />
            </el-icon>
          </div>
        </div>
      </el-popover>
    </div>

    <!-- 简单模式 -->
    <el-button
      v-else
      :type="buttonType"
      :size="size"
      :text="text"
      :bg="bg"
      @click="toggleLanguage"
    >
      <span class="language-flag">{{ currentLocaleInfo?.flag }}</span>
      <span v-if="showText" class="language-name">{{ currentLocaleInfo?.name }}</span>
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ArrowDown, Check } from '@element-plus/icons-vue'
import { 
  SUPPORT_LOCALES, 
  getCurrentLocale, 
  getCurrentLocaleInfo, 
  setLocale 
} from '@/i18n'

// 组件属性
interface Props {
  mode?: 'dropdown' | 'select' | 'buttons' | 'tabs' | 'popover' | 'simple'
  size?: 'large' | 'default' | 'small'
  buttonType?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text' | 'default'
  showText?: boolean
  text?: boolean
  bg?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'dropdown',
  size: 'default',
  buttonType: 'default',
  showText: true,
  text: false,
  bg: false
})

// 发射事件
const emit = defineEmits<{
  change: [locale: string]
  beforeChange: [locale: string]
  afterChange: [locale: string]
}>()

// 计算属性
const currentLocale = computed(() => getCurrentLocale())
const currentLocaleInfo = computed(() => getCurrentLocaleInfo())

// 处理语言切换
const handleLanguageChange = async (locale: string) => {
  if (locale === currentLocale.value) {
    return
  }

  try {
    // 发射切换前事件
    emit('beforeChange', locale)
    
    // 切换语言
    await setLocale(locale)
    
    // 发射切换事件
    emit('change', locale)
    emit('afterChange', locale)
    
  } catch (error) {
    console.error('Language change failed:', error)
  }
}

// 简单模式的语言切换（在支持的语言间循环）
const toggleLanguage = () => {
  const currentIndex = SUPPORT_LOCALES.findIndex(
    locale => locale.code === currentLocale.value
  )
  const nextIndex = (currentIndex + 1) % SUPPORT_LOCALES.length
  const nextLocale = SUPPORT_LOCALES[nextIndex]
  
  handleLanguageChange(nextLocale.code)
}
</script>

<style scoped>
.language-switcher {
  display: inline-block;
}

.language-flag {
  font-size: 16px;
  margin-right: 6px;
}

.language-name {
  font-size: 14px;
}

.language-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 120px;
}

.language-item .language-flag {
  margin-right: 8px;
}

.language-item .check-icon {
  color: var(--el-color-primary);
  margin-left: 8px;
}

.language-option {
  display: flex;
  align-items: center;
}

.language-option .language-flag {
  margin-right: 8px;
}

.language-list {
  max-height: 200px;
  overflow-y: auto;
}

.language-list-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.language-list-item:hover {
  background-color: var(--el-fill-color-light);
}

.language-list-item.active {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

.language-list-item .language-flag {
  margin-right: 8px;
}

.language-list-item .check-icon {
  color: var(--el-color-primary);
  margin-left: 8px;
}

/* 按钮组模式样式 */
.el-button-group .el-button .language-flag {
  margin-right: 4px;
}

.el-button-group .el-button .language-name {
  font-size: 12px;
}

/* 标签页模式样式 */
.el-tabs .language-flag {
  margin-right: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .language-name {
    display: none;
  }
  
  .language-switcher .el-button {
    padding: 8px 12px;
  }
  
  .language-flag {
    font-size: 18px;
    margin-right: 0;
  }
}

/* 深色主题适配 */
.dark .language-list-item:hover {
  background-color: var(--el-fill-color-dark);
}

.dark .language-list-item.active {
  background-color: var(--el-color-primary-dark-2);
}

/* 动画效果 */
.language-switcher {
  transition: all 0.3s ease;
}

.language-list-item {
  transition: all 0.2s ease;
}

/* 无障碍支持 */
.language-list-item:focus {
  outline: 2px solid var(--el-color-primary);
  outline-offset: 2px;
}

.language-switcher .el-button:focus {
  outline: 2px solid var(--el-color-primary);
  outline-offset: 2px;
}
</style>
