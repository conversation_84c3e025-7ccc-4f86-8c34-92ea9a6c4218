@echo off
echo Checking Node.js environment...

echo Node.js version:
node --version
set NODE_STATUS=%errorlevel%

echo npm version:
npm --version
set NPM_STATUS=%errorlevel%

echo Node.js path:
where node

echo npm path:
where npm

if %NODE_STATUS% equ 0 (
    echo Node.js: OK
) else (
    echo Node.js: ERROR
)

if %NPM_STATUS% equ 0 (
    echo npm: OK
) else (
    echo npm: ERROR - This is the problem!
    echo Attempting to fix npm...
    
    REM Try to reinstall npm
    echo Method 1: Reinstall npm globally
    node -e "console.log('Using Node.js to fix npm...')"
    
    REM Download npm using Node.js
    node -e "
    const https = require('https');
    const fs = require('fs');
    const { execSync } = require('child_process');
    
    console.log('Downloading npm...');
    
    try {
        execSync('curl -o npm.zip https://registry.npmjs.org/npm/-/npm-10.2.4.tgz', {stdio: 'inherit'});
        console.log('npm downloaded successfully');
    } catch (error) {
        console.log('Download failed, trying alternative...');
        
        // Alternative: Install using PowerShell
        try {
            execSync('powershell -Command \"Invoke-WebRequest -Uri https://registry.npmjs.org/npm/-/npm-10.2.4.tgz -OutFile npm.tgz\"', {stdio: 'inherit'});
            console.log('npm downloaded via PowerShell');
        } catch (error2) {
            console.log('All download methods failed');
        }
    }
    "
    
    REM Test npm again
    echo Testing npm after fix attempt...
    npm --version
    if %errorlevel% equ 0 (
        echo npm fixed successfully!
        goto install_deps
    ) else (
        echo npm fix failed, installing yarn as alternative...
        goto install_yarn
    )
)

:install_deps
echo Installing project dependencies...
cd frontend
if exist package.json (
    echo Found package.json, installing dependencies...
    npm install
    if %errorlevel% equ 0 (
        echo Dependencies installed successfully!
        echo Starting development server...
        npm run dev
    ) else (
        echo npm install failed, trying yarn...
        goto install_yarn
    )
) else (
    echo package.json not found
)
goto end

:install_yarn
echo Installing yarn as npm alternative...
echo Please install yarn manually from: https://yarnpkg.com/getting-started/install
start https://yarnpkg.com/getting-started/install
echo After installing yarn, run: yarn install && yarn dev
goto end

:end
pause
