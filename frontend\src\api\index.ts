import axios from 'axios'

const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    console.log('发送请求:', config.method?.toUpperCase(), config.url)
    return config
  },
  (error) => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log('收到响应:', response.status, response.config.url)
    return response
  },
  (error) => {
    console.error('响应错误:', error.response?.status, error.config?.url, error.message)
    return Promise.reject(error)
  }
)

export default api

// API方法
export const catApi = {
  // 获取所有猫咪
  getAll: (params?: any) => api.get('/api/cats', { params }),
  
  // 获取单个猫咪
  getById: (id: number) => api.get(`/api/cats/${id}`),
  
  // 获取统计信息
  getStatistics: () => api.get('/api/cats/statistics'),
  
  // 创建猫咪
  create: (data: any) => api.post('/api/cats', data),
  
  // 更新猫咪
  update: (id: number, data: any) => api.put(`/api/cats/${id}`, data),
  
  // 删除猫咪
  delete: (id: number) => api.delete(`/api/cats/${id}`)
}

export const testApi = {
  // 健康检查
  health: () => api.get('/api/test/health'),
  
  // Hello接口
  hello: () => api.get('/api/test/hello')
}


