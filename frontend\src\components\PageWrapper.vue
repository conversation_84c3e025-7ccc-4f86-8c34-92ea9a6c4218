<template>
  <div class="page-wrapper">
    <div v-if="loading" class="page-loading">
      <el-skeleton :rows="5" animated />
      <p class="loading-text">{{ loadingText }}</p>
    </div>
    
    <div v-else-if="error" class="page-error">
      <el-result
        icon="error"
        :title="errorTitle"
        :sub-title="error"
      >
        <template #extra>
          <el-button type="primary" @click="handleRetry">
            重试
          </el-button>
          <el-button @click="$router.push('/')">
            返回首页
          </el-button>
        </template>
      </el-result>
    </div>
    
    <div v-else class="page-content">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  loading?: boolean
  error?: string
  loadingText?: string
  errorTitle?: string
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  loadingText: '正在加载...',
  errorTitle: '页面加载失败'
})

const emit = defineEmits<{
  retry: []
}>()

const handleRetry = () => {
  emit('retry')
}
</script>

<style scoped>
.page-wrapper {
  min-height: 400px;
}

.page-loading {
  padding: 40px;
  text-align: center;
}

.loading-text {
  margin-top: 20px;
  color: #666;
  font-size: 14px;
}

.page-error {
  padding: 40px;
}

.page-content {
  min-height: 100%;
}
</style>
