<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统监控 - 猫舍管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            padding: 0 20px;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 70px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo {
            font-size: 24px;
            font-weight: 700;
            color: white;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo::before {
            content: '🐱';
            font-size: 28px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
            color: white;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-1px);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .page-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .page-title {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .page-title::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
        }

        .page-title h1 {
            color: #333;
            font-size: 36px;
            margin-bottom: 15px;
            font-weight: 700;
        }

        .page-title p {
            color: #666;
            font-size: 18px;
            max-width: 600px;
            margin: 0 auto;
        }

        .system-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .status-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .status-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--status-color, #10b981);
        }

        .status-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
        }

        .status-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .status-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
            background: var(--status-color, #10b981);
        }

        .status-title {
            font-size: 16px;
            font-weight: 600;
            color: #666;
        }

        .status-value {
            font-size: 24px;
            font-weight: 700;
            color: #333;
            margin-bottom: 10px;
        }

        .status-description {
            font-size: 14px;
            color: #666;
        }

        .status-online { --status-color: #10b981; }
        .status-performance { --status-color: #3b82f6; }
        .status-memory { --status-color: #f59e0b; }
        .status-database { --status-color: #8b5cf6; }

        .api-tests {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            margin-bottom: 40px;
        }

        .api-tests h3 {
            color: #333;
            margin-bottom: 25px;
            font-size: 24px;
            font-weight: 600;
            text-align: center;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .test-item {
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 15px;
            transition: all 0.3s ease;
        }

        .test-item:hover {
            border-color: #667eea;
            box-shadow: 0 5px 20px rgba(102, 126, 234, 0.1);
        }

        .test-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .test-name {
            font-weight: 600;
            color: #333;
        }

        .test-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-success {
            background: #d1fae5;
            color: #065f46;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        .test-details {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
        }

        .test-actions {
            display: flex;
            gap: 10px;
        }

        .btn-small {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-test {
            background: #667eea;
            color: white;
        }

        .btn-test:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }

        .btn-view {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #ddd;
        }

        .btn-view:hover {
            background: #e9ecef;
        }

        .performance-metrics {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            margin-bottom: 40px;
        }

        .metrics-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .metrics-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .metric-item {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
        }

        .metric-value {
            font-size: 32px;
            font-weight: 700;
            color: #333;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 14px;
            color: #666;
        }

        .system-logs {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        }

        .logs-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .logs-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }

        .refresh-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .refresh-btn:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }

        .logs-container {
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            line-height: 1.4;
        }

        .log-entry {
            margin-bottom: 5px;
        }

        .log-timestamp {
            color: #888;
        }

        .log-level-info {
            color: #00ff00;
        }

        .log-level-warn {
            color: #ffff00;
        }

        .log-level-error {
            color: #ff0000;
        }

        @media (max-width: 768px) {
            .page-title {
                padding: 30px 20px;
            }

            .page-title h1 {
                font-size: 28px;
            }

            .test-grid {
                grid-template-columns: 1fr;
            }

            .metrics-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">猫舍管理系统</div>
            <div class="user-info">
                <div class="user-avatar" id="userAvatar">A</div>
                <span id="userName">加载中...</span>
                <button class="logout-btn" onclick="logout()">退出</button>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="page-header">
            <div class="page-title">
                <h1>系统监控</h1>
                <p>实时监控系统状态、API性能和数据库连接，确保系统稳定运行</p>
            </div>
        </div>

        <div class="system-status">
            <div class="status-card status-online">
                <div class="status-header">
                    <div class="status-icon">🟢</div>
                    <div class="status-title">系统状态</div>
                </div>
                <div class="status-value" id="systemStatus">在线</div>
                <div class="status-description">运行时间: <span id="uptime">2天 15小时</span></div>
            </div>

            <div class="status-card status-performance">
                <div class="status-header">
                    <div class="status-icon">⚡</div>
                    <div class="status-title">响应性能</div>
                </div>
                <div class="status-value" id="responseTime">45ms</div>
                <div class="status-description">平均响应时间</div>
            </div>

            <div class="status-card status-memory">
                <div class="status-header">
                    <div class="status-icon">💾</div>
                    <div class="status-title">内存使用</div>
                </div>
                <div class="status-value" id="memoryUsage">68%</div>
                <div class="status-description">512MB / 1GB</div>
            </div>

            <div class="status-card status-database">
                <div class="status-header">
                    <div class="status-icon">🗄️</div>
                    <div class="status-title">数据库</div>
                </div>
                <div class="status-value" id="dbStatus">正常</div>
                <div class="status-description">连接池: 8/20</div>
            </div>
        </div>

        <div class="api-tests">
            <h3>API 接口测试</h3>
            <div class="test-grid">
                <div class="test-item">
                    <div class="test-header">
                        <div class="test-name">用户认证</div>
                        <div class="test-status status-success" id="authStatus">正常</div>
                    </div>
                    <div class="test-details">测试登录和JWT验证功能</div>
                    <div class="test-actions">
                        <button class="btn-small btn-test" onclick="testAPI('/auth/login', 'authStatus')">测试</button>
                        <button class="btn-small btn-view" onclick="viewTestDetails('auth')">详情</button>
                    </div>
                </div>

                <div class="test-item">
                    <div class="test-header">
                        <div class="test-name">猫咪管理</div>
                        <div class="test-status status-success" id="catsStatus">正常</div>
                    </div>
                    <div class="test-details">测试猫咪数据的增删改查</div>
                    <div class="test-actions">
                        <button class="btn-small btn-test" onclick="testAPI('/cats', 'catsStatus')">测试</button>
                        <button class="btn-small btn-view" onclick="viewTestDetails('cats')">详情</button>
                    </div>
                </div>

                <div class="test-item">
                    <div class="test-header">
                        <div class="test-name">客户管理</div>
                        <div class="test-status status-success" id="customersStatus">正常</div>
                    </div>
                    <div class="test-details">测试客户信息管理功能</div>
                    <div class="test-actions">
                        <button class="btn-small btn-test" onclick="testAPI('/customers', 'customersStatus')">测试</button>
                        <button class="btn-small btn-view" onclick="viewTestDetails('customers')">详情</button>
                    </div>
                </div>

                <div class="test-item">
                    <div class="test-header">
                        <div class="test-name">健康记录</div>
                        <div class="test-status status-success" id="healthStatus">正常</div>
                    </div>
                    <div class="test-details">测试健康记录管理功能</div>
                    <div class="test-actions">
                        <button class="btn-small btn-test" onclick="testAPI('/health', 'healthStatus')">测试</button>
                        <button class="btn-small btn-view" onclick="viewTestDetails('health')">详情</button>
                    </div>
                </div>

                <div class="test-item">
                    <div class="test-header">
                        <div class="test-name">财务管理</div>
                        <div class="test-status status-success" id="financialStatus">正常</div>
                    </div>
                    <div class="test-details">测试财务数据和统计功能</div>
                    <div class="test-actions">
                        <button class="btn-small btn-test" onclick="testAPI('/financial/stats', 'financialStatus')">测试</button>
                        <button class="btn-small btn-view" onclick="viewTestDetails('financial')">详情</button>
                    </div>
                </div>

                <div class="test-item">
                    <div class="test-header">
                        <div class="test-name">繁育管理</div>
                        <div class="test-status status-success" id="breedingStatus">正常</div>
                    </div>
                    <div class="test-details">测试繁育记录管理功能</div>
                    <div class="test-actions">
                        <button class="btn-small btn-test" onclick="testAPI('/breeding', 'breedingStatus')">测试</button>
                        <button class="btn-small btn-view" onclick="viewTestDetails('breeding')">详情</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="performance-metrics">
            <div class="metrics-header">
                <div class="metrics-title">性能指标</div>
            </div>
            <div class="metrics-grid">
                <div class="metric-item">
                    <div class="metric-value" id="requestsPerSecond">125</div>
                    <div class="metric-label">请求/秒</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="errorRate">0.2%</div>
                    <div class="metric-label">错误率</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="activeUsers">23</div>
                    <div class="metric-label">活跃用户</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="dbConnections">8</div>
                    <div class="metric-label">数据库连接</div>
                </div>
            </div>
        </div>

        <div class="system-logs">
            <div class="logs-header">
                <div class="logs-title">系统日志</div>
                <button class="refresh-btn" onclick="refreshLogs()">刷新</button>
            </div>
            <div class="logs-container" id="logsContainer">
                <div class="log-entry">
                    <span class="log-timestamp">[2024-01-25 14:30:15]</span>
                    <span class="log-level-info">[INFO]</span>
                    系统启动完成，所有服务正常运行
                </div>
                <div class="log-entry">
                    <span class="log-timestamp">[2024-01-25 14:29:45]</span>
                    <span class="log-level-info">[INFO]</span>
                    数据库连接池初始化成功，连接数: 20
                </div>
                <div class="log-entry">
                    <span class="log-timestamp">[2024-01-25 14:29:30]</span>
                    <span class="log-level-info">[INFO]</span>
                    JWT认证模块加载完成
                </div>
                <div class="log-entry">
                    <span class="log-timestamp">[2024-01-25 14:29:15]</span>
                    <span class="log-level-info">[INFO]</span>
                    Spring Boot应用启动中...
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080/api';
        
        // 检查登录状态
        function checkAuth() {
            const token = localStorage.getItem('token');
            const userInfo = localStorage.getItem('userInfo');
            
            if (!token || !userInfo) {
                window.location.href = '/login-test.html';
                return false;
            }
            
            try {
                const user = JSON.parse(userInfo);
                const userName = user.realName || user.username;
                document.getElementById('userName').textContent = userName;
                document.getElementById('userAvatar').textContent = userName.charAt(0).toUpperCase();
                return true;
            } catch (error) {
                logout();
                return false;
            }
        }
        
        // 退出登录
        function logout() {
            localStorage.removeItem('token');
            localStorage.removeItem('userInfo');
            window.location.href = '/login-test.html';
        }
        
        // 测试API接口
        async function testAPI(endpoint, statusElementId) {
            const token = localStorage.getItem('token');
            const statusElement = document.getElementById(statusElementId);
            
            // 设置测试中状态
            statusElement.className = 'test-status status-pending';
            statusElement.textContent = '测试中...';
            
            try {
                const startTime = Date.now();
                const response = await fetch(`${API_BASE_URL}${endpoint}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        statusElement.className = 'test-status status-success';
                        statusElement.textContent = `正常 (${responseTime}ms)`;
                        addLogEntry('INFO', `API测试成功: ${endpoint} - 响应时间: ${responseTime}ms`);
                    } else {
                        throw new Error(data.message || '接口返回错误');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                statusElement.className = 'test-status status-error';
                statusElement.textContent = '错误';
                addLogEntry('ERROR', `API测试失败: ${endpoint} - ${error.message}`);
            }
        }
        
        // 查看测试详情
        function viewTestDetails(module) {
            const details = {
                auth: '认证模块测试详情:\n• 登录接口响应时间\n• JWT令牌验证\n• 权限检查\n• 会话管理',
                cats: '猫咪管理测试详情:\n• 数据查询性能\n• 增删改操作\n• 数据验证\n• 分页功能',
                customers: '客户管理测试详情:\n• 客户信息查询\n• 数据完整性\n• 搜索功能\n• 关联数据',
                health: '健康记录测试详情:\n• 记录查询\n• 数据关联\n• 统计功能\n• 时间范围查询',
                financial: '财务管理测试详情:\n• 统计数据准确性\n• 计算逻辑\n• 报表生成\n• 数据聚合',
                breeding: '繁育管理测试详情:\n• 繁育记录查询\n• 数据关联\n• 时间线功能\n• 统计分析'
            };
            
            alert(details[module] || '测试详情暂无');
        }
        
        // 添加日志条目
        function addLogEntry(level, message) {
            const logsContainer = document.getElementById('logsContainer');
            const timestamp = new Date().toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `
                <span class="log-timestamp">[${timestamp}]</span>
                <span class="log-level-${level.toLowerCase()}">[${level}]</span>
                ${message}
            `;
            
            logsContainer.insertBefore(logEntry, logsContainer.firstChild);
            
            // 限制日志条数
            const logEntries = logsContainer.querySelectorAll('.log-entry');
            if (logEntries.length > 50) {
                logsContainer.removeChild(logEntries[logEntries.length - 1]);
            }
        }
        
        // 刷新日志
        function refreshLogs() {
            addLogEntry('INFO', '手动刷新系统日志');
            
            // 模拟新的日志条目
            setTimeout(() => {
                addLogEntry('INFO', '系统状态检查完成，所有服务正常');
            }, 500);
            
            setTimeout(() => {
                addLogEntry('INFO', '内存使用率: 68%, 数据库连接: 8/20');
            }, 1000);
        }
        
        // 更新系统指标
        function updateMetrics() {
            // 模拟实时数据更新
            const requestsPerSecond = Math.floor(Math.random() * 50) + 100;
            const errorRate = (Math.random() * 0.5).toFixed(1);
            const activeUsers = Math.floor(Math.random() * 10) + 20;
            const dbConnections = Math.floor(Math.random() * 5) + 6;
            
            document.getElementById('requestsPerSecond').textContent = requestsPerSecond;
            document.getElementById('errorRate').textContent = errorRate + '%';
            document.getElementById('activeUsers').textContent = activeUsers;
            document.getElementById('dbConnections').textContent = dbConnections;
            
            // 更新响应时间
            const responseTime = Math.floor(Math.random() * 20) + 35;
            document.getElementById('responseTime').textContent = responseTime + 'ms';
            
            // 更新内存使用率
            const memoryUsage = Math.floor(Math.random() * 10) + 65;
            document.getElementById('memoryUsage').textContent = memoryUsage + '%';
        }
        
        // 运行全部API测试
        function runAllTests() {
            const tests = [
                { endpoint: '/auth/login', statusId: 'authStatus' },
                { endpoint: '/cats', statusId: 'catsStatus' },
                { endpoint: '/customers', statusId: 'customersStatus' },
                { endpoint: '/health', statusId: 'healthStatus' },
                { endpoint: '/financial/stats', statusId: 'financialStatus' },
                { endpoint: '/breeding', statusId: 'breedingStatus' }
            ];
            
            tests.forEach((test, index) => {
                setTimeout(() => {
                    testAPI(test.endpoint, test.statusId);
                }, index * 500);
            });
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            if (checkAuth()) {
                // 初始化页面
                addLogEntry('INFO', '系统监控页面加载完成');
                
                // 定期更新指标
                setInterval(updateMetrics, 5000);
                
                // 5秒后自动运行一次全部测试
                setTimeout(runAllTests, 5000);
            }
        });
    </script>
</body>
</html>
