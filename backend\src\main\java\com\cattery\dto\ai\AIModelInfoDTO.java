package com.cattery.dto.ai;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * AI模型信息DTO
 */
@Data
public class AIModelInfoDTO {
    
    /**
     * 猫咪识别模型
     */
    private String catRecognitionModel;
    
    /**
     * 品种分类模型
     */
    private String breedClassificationModel;
    
    /**
     * 健康预测模型
     */
    private String healthPredictionModel;
    
    /**
     * 行为分析模型
     */
    private String behaviorAnalysisModel;
    
    /**
     * 模型版本信息
     */
    private Map<String, String> modelVersions;
    
    /**
     * 模型训练日期
     */
    private Map<String, LocalDateTime> trainingDates;
    
    /**
     * 模型准确率
     */
    private Map<String, Double> modelAccuracy;
    
    /**
     * 模型大小（MB）
     */
    private Map<String, Long> modelSizes;
    
    /**
     * 支持的功能列表
     */
    private Map<String, Boolean> supportedFeatures;
    
    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;
    
    /**
     * 模型状态
     */
    private Map<String, String> modelStatus;

    /**
     * 最后更新时间（字符串格式）
     */
    private String lastUpdated;

    /**
     * 整体准确率
     */
    private Double accuracy;
}
