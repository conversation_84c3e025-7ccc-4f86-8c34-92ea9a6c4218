<template>
  <div class="vaccine-view">
    <div class="page-header">
      <h1>疫苗管理</h1>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        新增疫苗记录
      </el-button>
    </div>

    <div class="search-bar">
      <el-form :model="searchForm" inline>
        <el-form-item label="猫咪">
          <el-select v-model="searchForm.catId" placeholder="选择猫咪" clearable>
            <el-option
              v-for="cat in cats"
              :key="cat.id"
              :label="cat.name"
              :value="cat.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="疫苗类型">
          <el-select v-model="searchForm.vaccineType" placeholder="选择疫苗类型" clearable>
            <el-option label="狂犬疫苗" value="RABIES" />
            <el-option label="三联疫苗" value="FVRCP" />
            <el-option label="猫瘟疫苗" value="PANLEUKOPENIA" />
            <el-option label="其他" value="OTHER" />
          </el-select>
        </el-form-item>
        <el-form-item label="接种日期">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchVaccines">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table :data="vaccines" v-loading="loading">
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column label="猫咪">
        <template #default="{ row }">
          <div class="cat-info">
            <img v-if="row.cat?.primaryPhoto" :src="row.cat.primaryPhoto" class="cat-avatar" />
            <span>{{ row.cat?.name }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="vaccineName" label="疫苗名称" />
      <el-table-column label="疫苗类型">
        <template #default="{ row }">
          <el-tag :type="getVaccineTypeColor(row.vaccineType)">
            {{ getVaccineTypeText(row.vaccineType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="vaccinationDate" label="接种日期" />
      <el-table-column prop="nextDueDate" label="下次接种日期" />
      <el-table-column prop="veterinarian" label="兽医" />
      <el-table-column prop="clinic" label="诊所" />
      <el-table-column label="状态">
        <template #default="{ row }">
          <el-tag :type="getStatusColor(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button size="small" @click="viewVaccine(row)">查看</el-button>
          <el-button size="small" type="primary" @click="editVaccine(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="deleteVaccine(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingVaccine ? '编辑疫苗记录' : '新增疫苗记录'"
      width="600px"
    >
      <el-form :model="vaccineForm" :rules="formRules" ref="formRef" label-width="100px">
        <el-form-item label="猫咪" prop="catId">
          <el-select v-model="vaccineForm.catId" placeholder="选择猫咪">
            <el-option
              v-for="cat in cats"
              :key="cat.id"
              :label="cat.name"
              :value="cat.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="疫苗名称" prop="vaccineName">
          <el-input v-model="vaccineForm.vaccineName" placeholder="输入疫苗名称" />
        </el-form-item>
        <el-form-item label="疫苗类型" prop="vaccineType">
          <el-select v-model="vaccineForm.vaccineType" placeholder="选择疫苗类型">
            <el-option label="狂犬疫苗" value="RABIES" />
            <el-option label="三联疫苗" value="FVRCP" />
            <el-option label="猫瘟疫苗" value="PANLEUKOPENIA" />
            <el-option label="其他" value="OTHER" />
          </el-select>
        </el-form-item>
        <el-form-item label="接种日期" prop="vaccinationDate">
          <el-date-picker
            v-model="vaccineForm.vaccinationDate"
            type="date"
            placeholder="选择接种日期"
          />
        </el-form-item>
        <el-form-item label="下次接种" prop="nextDueDate">
          <el-date-picker
            v-model="vaccineForm.nextDueDate"
            type="date"
            placeholder="选择下次接种日期"
          />
        </el-form-item>
        <el-form-item label="批次号" prop="batchNumber">
          <el-input v-model="vaccineForm.batchNumber" placeholder="输入批次号" />
        </el-form-item>
        <el-form-item label="兽医" prop="veterinarian">
          <el-input v-model="vaccineForm.veterinarian" placeholder="输入兽医姓名" />
        </el-form-item>
        <el-form-item label="诊所" prop="clinic">
          <el-input v-model="vaccineForm.clinic" placeholder="输入诊所名称" />
        </el-form-item>
        <el-form-item label="费用" prop="cost">
          <el-input-number v-model="vaccineForm.cost" :min="0" :precision="2" />
        </el-form-item>
        <el-form-item label="备注" prop="notes">
          <el-input
            v-model="vaccineForm.notes"
            type="textarea"
            :rows="3"
            placeholder="输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveVaccine">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const showCreateDialog = ref(false)
const editingVaccine = ref(null)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

const vaccines = ref([])
const cats = ref([])

const searchForm = reactive({
  catId: '',
  vaccineType: '',
  dateRange: []
})

const vaccineForm = reactive({
  catId: '',
  vaccineName: '',
  vaccineType: '',
  vaccinationDate: '',
  nextDueDate: '',
  batchNumber: '',
  veterinarian: '',
  clinic: '',
  cost: 0,
  notes: ''
})

const formRules = {
  catId: [{ required: true, message: '请选择猫咪', trigger: 'change' }],
  vaccineName: [{ required: true, message: '请输入疫苗名称', trigger: 'blur' }],
  vaccineType: [{ required: true, message: '请选择疫苗类型', trigger: 'change' }],
  vaccinationDate: [{ required: true, message: '请选择接种日期', trigger: 'change' }]
}

// 方法
const loadVaccines = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    vaccines.value = [
      {
        id: 1,
        cat: { id: 1, name: '美美', primaryPhoto: '/images/cat1.jpg' },
        vaccineName: '狂犬病疫苗',
        vaccineType: 'RABIES',
        vaccinationDate: '2024-01-15',
        nextDueDate: '2025-01-15',
        batchNumber: 'RB2024001',
        veterinarian: '张医生',
        clinic: '宠物医院',
        cost: 120.00,
        status: 'COMPLETED',
        notes: '接种正常，无不良反应'
      }
    ]
    total.value = 1
  } catch (error) {
    ElMessage.error('加载疫苗记录失败')
  } finally {
    loading.value = false
  }
}

const loadCats = async () => {
  try {
    // 模拟API调用
    cats.value = [
      { id: 1, name: '美美' },
      { id: 2, name: '帅帅' },
      { id: 3, name: '花花' }
    ]
  } catch (error) {
    ElMessage.error('加载猫咪列表失败')
  }
}

const searchVaccines = () => {
  currentPage.value = 1
  loadVaccines()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    catId: '',
    vaccineType: '',
    dateRange: []
  })
  searchVaccines()
}

const viewVaccine = (vaccine: any) => {
  ElMessage.info('查看疫苗记录功能开发中')
}

const editVaccine = (vaccine: any) => {
  editingVaccine.value = vaccine
  Object.assign(vaccineForm, vaccine)
  showCreateDialog.value = true
}

const deleteVaccine = async (vaccine: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这条疫苗记录吗？', '确认删除', {
      type: 'warning'
    })
    ElMessage.success('删除成功')
    loadVaccines()
  } catch {
    // 用户取消删除
  }
}

const saveVaccine = () => {
  ElMessage.success('保存成功')
  showCreateDialog.value = false
  loadVaccines()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  loadVaccines()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadVaccines()
}

const getVaccineTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    RABIES: 'danger',
    FVRCP: 'success',
    PANLEUKOPENIA: 'warning',
    OTHER: 'info'
  }
  return colors[type] || 'info'
}

const getVaccineTypeText = (type: string) => {
  const texts: Record<string, string> = {
    RABIES: '狂犬疫苗',
    FVRCP: '三联疫苗',
    PANLEUKOPENIA: '猫瘟疫苗',
    OTHER: '其他'
  }
  return texts[type] || type
}

const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    COMPLETED: 'success',
    PENDING: 'warning',
    OVERDUE: 'danger'
  }
  return colors[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    COMPLETED: '已完成',
    PENDING: '待接种',
    OVERDUE: '已过期'
  }
  return texts[status] || status
}

onMounted(() => {
  loadVaccines()
  loadCats()
})
</script>

<style scoped>
.vaccine-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-bar {
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 4px;
}

.cat-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.cat-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
