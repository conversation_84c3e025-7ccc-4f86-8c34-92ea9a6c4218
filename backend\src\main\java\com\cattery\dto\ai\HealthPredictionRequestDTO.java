package com.cattery.dto.ai;

import lombok.Data;
import java.util.List;

/**
 * 健康预测请求DTO
 */
@Data
public class HealthPredictionRequestDTO {
    
    /**
     * 当前症状列表
     */
    private List<String> currentSymptoms;
    
    /**
     * 行为变化
     */
    private List<String> behaviorChanges;
    
    /**
     * 食欲变化
     */
    private String appetiteChange;
    
    /**
     * 活动水平变化
     */
    private String activityChange;
    
    /**
     * 体重变化
     */
    private String weightChange;
    
    /**
     * 睡眠模式变化
     */
    private String sleepPatternChange;
    
    /**
     * 排便习惯变化
     */
    private String litterBoxChange;
    
    /**
     * 其他观察到的变化
     */
    private String otherObservations;
    
    /**
     * 环境变化
     */
    private List<String> environmentalChanges;
    
    /**
     * 最近用药情况
     */
    private List<String> recentMedications;
    
    /**
     * 预测类型 (GENERAL, SPECIFIC_DISEASE, EMERGENCY)
     */
    private String predictionType;
}
