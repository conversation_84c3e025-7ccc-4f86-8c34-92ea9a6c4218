import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { ElCard, ElTable, ElCalendar, ElAlert } from 'element-plus'
import VaccineTrackingView from '../VaccineTrackingView.vue'

// Mock API modules
vi.mock('@/api', () => ({
  healthApi: {
    getVaccineRecords: vi.fn(() => Promise.resolve({
      data: [
        {
          id: 1,
          catId: 1,
          catName: '小花',
          catPhoto: 'http://example.com/cat1.jpg',
          vaccineName: '猫三联疫苗',
          vaccineType: 'fvrcp',
          scheduledDate: '2024-01-15',
          actualDate: '2024-01-15',
          status: 'completed',
          veterinarian: '张医生',
          nextDueDate: '2025-01-15'
        },
        {
          id: 2,
          catId: 2,
          catName: '小黑',
          catPhoto: 'http://example.com/cat2.jpg',
          vaccineName: '狂犬疫苗',
          vaccineType: 'rabies',
          scheduledDate: '2024-01-20',
          actualDate: null,
          status: 'pending',
          veterinarian: '李医生',
          nextDueDate: null
        }
      ],
      total: 2
    })),
    getVaccineStats: vi.fn(() => Promise.resolve({
      completed: 15,
      pending: 3,
      overdue: 1,
      vaccinationRate: 85,
      completedTrend: { value: 5, type: 'up' },
      pendingTrend: { value: 1, type: 'down' },
      overdueTrend: { value: 0, type: 'flat' },
      rateTrend: { value: 3, type: 'up' }
    })),
    getUrgentVaccines: vi.fn(() => Promise.resolve([
      {
        id: 1,
        catId: 2,
        catName: '小黑',
        catPhoto: 'http://example.com/cat2.jpg',
        vaccineName: '狂犬疫苗',
        dueDate: '2024-01-20',
        daysOverdue: 0,
        urgency: 'warning',
        title: '疫苗即将到期',
        description: '小黑的狂犬疫苗将在3天后到期'
      }
    ])),
    createVaccineRecord: vi.fn(() => Promise.resolve({ id: 3 })),
    createVaccinePlan: vi.fn(() => Promise.resolve({ id: 1 }))
  },
  catApi: {
    getAll: vi.fn(() => Promise.resolve([
      { id: 1, name: '小花', breedName: '英国短毛猫' },
      { id: 2, name: '小黑', breedName: '美国短毛猫' }
    ]))
  }
}))

// Mock components
vi.mock('@/components/StatCard.vue', () => ({
  default: {
    name: 'StatCard',
    template: '<div class="stat-card-mock">{{ title }}: {{ value }}</div>',
    props: ['title', 'value', 'icon', 'color', 'trend', 'format']
  }
}))

vi.mock('@/components/health/VaccineRecordForm.vue', () => ({
  default: {
    name: 'VaccineRecordForm',
    template: '<div class="vaccine-record-form-mock">Vaccine Record Form</div>',
    emits: ['submit', 'cancel']
  }
}))

vi.mock('@/components/health/VaccinePlanForm.vue', () => ({
  default: {
    name: 'VaccinePlanForm',
    template: '<div class="vaccine-plan-form-mock">Vaccine Plan Form</div>',
    emits: ['submit', 'cancel']
  }
}))

vi.mock('@/components/health/VaccineRecordDetail.vue', () => ({
  default: {
    name: 'VaccineRecordDetail',
    template: '<div class="vaccine-record-detail-mock">Vaccine Record Detail</div>',
    props: ['record'],
    emits: ['edit', 'record-vaccination']
  }
}))

describe('VaccineTrackingView', () => {
  let wrapper: any

  beforeEach(() => {
    wrapper = mount(VaccineTrackingView, {
      global: {
        components: {
          ElCard,
          ElTable,
          ElCalendar,
          ElAlert
        },
        stubs: {
          'el-button': true,
          'el-button-group': true,
          'el-select': true,
          'el-option': true,
          'el-date-picker': true,
          'el-pagination': true,
          'el-dialog': true,
          'el-badge': true,
          'el-tag': true,
          'el-avatar': true,
          'el-icon': true,
          'StatCard': true,
          'VaccineRecordForm': true,
          'VaccinePlanForm': true,
          'VaccineRecordDetail': true
        }
      }
    })
  })

  it('renders properly', () => {
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.vaccine-tracking').exists()).toBe(true)
  })

  it('displays page header correctly', () => {
    const header = wrapper.find('.page-header')
    expect(header.exists()).toBe(true)
    expect(header.find('h1').text()).toBe('疫苗跟踪')
    expect(header.find('p').text()).toBe('猫咪疫苗接种计划和记录管理')
  })

  it('displays statistics section', () => {
    const statsSection = wrapper.find('.stats-section')
    expect(statsSection.exists()).toBe(true)
    
    // 检查是否有4个统计卡片
    const statCards = wrapper.findAllComponents({ name: 'StatCard' })
    expect(statCards.length).toBe(4)
  })

  it('displays vaccine alerts section', () => {
    const alertsSection = wrapper.find('.alerts-section')
    expect(alertsSection.exists()).toBe(true)
    
    const alertsCard = wrapper.find('.alerts-card')
    expect(alertsCard.exists()).toBe(true)
  })

  it('displays vaccine calendar', () => {
    const calendarSection = wrapper.find('.calendar-section')
    expect(calendarSection.exists()).toBe(true)
    
    const calendar = wrapper.findComponent(ElCalendar)
    expect(calendar.exists()).toBe(true)
  })

  it('displays vaccine records table', () => {
    const recordsSection = wrapper.find('.records-section')
    expect(recordsSection.exists()).toBe(true)
    
    const table = wrapper.findComponent(ElTable)
    expect(table.exists()).toBe(true)
  })

  it('loads vaccine data on mount', async () => {
    await wrapper.vm.$nextTick()
    
    expect(wrapper.vm.vaccineRecords.length).toBeGreaterThan(0)
    expect(wrapper.vm.vaccineStats.completed).toBeGreaterThanOrEqual(0)
    expect(wrapper.vm.urgentVaccines.length).toBeGreaterThanOrEqual(0)
  })

  it('handles create vaccine record dialog', async () => {
    const createButton = wrapper.find('.header-actions .el-button[type="primary"]')
    await createButton.trigger('click')
    
    expect(wrapper.vm.showCreateDialog).toBe(true)
  })

  it('handles create vaccine plan dialog', async () => {
    const planButton = wrapper.find('.header-actions .el-button:not([type="primary"])')
    await planButton.trigger('click')
    
    expect(wrapper.vm.showPlanDialog).toBe(true)
  })

  it('filters vaccine records correctly', async () => {
    // 设置测试数据
    wrapper.vm.vaccineRecords = [
      { id: 1, catId: 1, status: 'completed', vaccineType: 'fvrcp' },
      { id: 2, catId: 2, status: 'pending', vaccineType: 'rabies' }
    ]
    
    // 设置筛选条件
    wrapper.vm.filters.status = 'completed'
    
    // 触发查询
    await wrapper.vm.fetchVaccineRecords()
    
    // 验证筛选逻辑
    expect(wrapper.vm.filters.status).toBe('completed')
  })

  it('formats dates correctly', () => {
    const testDate = '2024-01-15'
    const formatted = wrapper.vm.formatDate(testDate)
    
    expect(formatted).toBe('2024/1/15')
  })

  it('gets correct status type', () => {
    expect(wrapper.vm.getStatusType('completed')).toBe('success')
    expect(wrapper.vm.getStatusType('pending')).toBe('warning')
    expect(wrapper.vm.getStatusType('overdue')).toBe('danger')
  })

  it('gets correct status text', () => {
    expect(wrapper.vm.getStatusText('completed')).toBe('已完成')
    expect(wrapper.vm.getStatusText('pending')).toBe('待接种')
    expect(wrapper.vm.getStatusText('overdue')).toBe('已过期')
  })

  it('handles vaccine record submission', async () => {
    const mockRecordData = {
      catId: 1,
      vaccineName: '猫三联疫苗',
      scheduledDate: '2024-01-15'
    }
    
    await wrapper.vm.handleSubmit(mockRecordData)
    
    expect(wrapper.vm.showCreateDialog).toBe(false)
  })

  it('handles vaccine plan submission', async () => {
    const mockPlanData = {
      catId: 1,
      vaccineType: 'fvrcp',
      startDate: '2024-01-01'
    }
    
    await wrapper.vm.handlePlanSubmit(mockPlanData)
    
    expect(wrapper.vm.showPlanDialog).toBe(false)
  })

  it('handles alert click correctly', async () => {
    const mockAlert = {
      id: 1,
      catId: 1,
      catName: '小花',
      vaccineName: '猫三联疫苗'
    }
    
    const spy = vi.spyOn(wrapper.vm, 'recordVaccination')
    wrapper.vm.handleAlertClick(mockAlert)
    
    expect(spy).toHaveBeenCalledWith(mockAlert)
  })

  it('gets vaccines for specific date', () => {
    wrapper.vm.vaccineRecords = [
      { id: 1, scheduledDate: '2024-01-15', actualDate: null },
      { id: 2, scheduledDate: '2024-01-16', actualDate: '2024-01-16' },
      { id: 3, scheduledDate: '2024-01-15', actualDate: null }
    ]
    
    const vaccinesFor15th = wrapper.vm.getVaccinesForDate('2024-01-15')
    expect(vaccinesFor15th.length).toBe(2)
    
    const vaccinesFor16th = wrapper.vm.getVaccinesForDate('2024-01-16')
    expect(vaccinesFor16th.length).toBe(1)
  })

  it('handles view vaccine detail', () => {
    const mockVaccine = { id: 1, catName: '小花', vaccineName: '猫三联疫苗' }
    
    wrapper.vm.viewVaccineDetail(mockVaccine)
    
    expect(wrapper.vm.selectedRecord).toEqual(mockVaccine)
    expect(wrapper.vm.showDetailDialog).toBe(true)
  })

  it('handles edit record', () => {
    const mockRecord = { id: 1, catName: '小花', vaccineName: '猫三联疫苗' }
    
    wrapper.vm.editRecord(mockRecord)
    
    expect(wrapper.vm.selectedRecord).toEqual(mockRecord)
    expect(wrapper.vm.showCreateDialog).toBe(true)
  })

  it('handles pagination correctly', async () => {
    wrapper.vm.pagination.page = 2
    wrapper.vm.pagination.size = 10
    
    const spy = vi.spyOn(wrapper.vm, 'fetchVaccineRecords')
    
    // 模拟分页变化
    await wrapper.vm.fetchVaccineRecords()
    
    expect(spy).toHaveBeenCalled()
  })

  it('displays loading state correctly', async () => {
    wrapper.vm.loading = true
    await wrapper.vm.$nextTick()
    
    const table = wrapper.findComponent(ElTable)
    expect(table.attributes('v-loading')).toBeDefined()
  })

  it('handles API errors gracefully', async () => {
    // 模拟API错误
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    
    // 这里可以添加错误处理的测试逻辑
    
    consoleSpy.mockRestore()
  })

  it('toggles calendar view correctly', async () => {
    const initialView = wrapper.vm.calendarView
    
    // 模拟切换视图
    wrapper.vm.calendarView = initialView === 'month' ? 'week' : 'month'
    await wrapper.vm.$nextTick()
    
    expect(wrapper.vm.calendarView).not.toBe(initialView)
  })

  it('validates vaccine types correctly', () => {
    const vaccineTypes = wrapper.vm.vaccineTypes
    
    expect(vaccineTypes).toBeInstanceOf(Array)
    expect(vaccineTypes.length).toBeGreaterThan(0)
    expect(vaccineTypes[0]).toHaveProperty('label')
    expect(vaccineTypes[0]).toHaveProperty('value')
  })
})
