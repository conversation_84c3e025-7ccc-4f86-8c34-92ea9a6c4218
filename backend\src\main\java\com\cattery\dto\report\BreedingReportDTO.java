package com.cattery.dto.report;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 繁育报表DTO
 */
@Data
public class BreedingReportDTO {
    
    /**
     * 报表期间
     */
    private String reportPeriod;
    
    /**
     * 生成时间
     */
    private LocalDateTime generatedAt;
    
    /**
     * 总配种次数
     */
    private Integer totalMatings;
    
    /**
     * 配种成功率
     */
    private Double matingSuccessRate;
    
    /**
     * 总怀孕数
     */
    private Integer totalPregnancies;
    
    /**
     * 总分娩数
     */
    private Integer totalBirths;
    
    /**
     * 总新生小猫数
     */
    private Integer totalKittens;
    
    /**
     * 平均窝产仔数
     */
    private Double averageLitterSize;
    
    /**
     * 存活率
     */
    private Double survivalRate;
    
    /**
     * 按品种的繁育统计
     */
    private Map<String, Long> breedingByBreed;
    
    /**
     * 月度繁育趋势
     */
    private List<MonthlyBreedingDataDTO> monthlyTrend;
    
    /**
     * 繁育效率统计
     */
    private BreedingEfficiencyDTO breedingEfficiency;
    
    /**
     * 遗传健康指标
     */
    private Map<String, Double> geneticHealthMetrics;

    /**
     * 怀孕成功率
     */
    private Double pregnancySuccessRate;
}
