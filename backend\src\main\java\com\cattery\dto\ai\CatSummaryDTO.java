package com.cattery.dto.ai;

import lombok.Data;
import java.time.LocalDate;

/**
 * 猫咪摘要DTO
 */
@Data
public class CatSummaryDTO {
    
    /**
     * 猫咪ID
     */
    private Long id;
    
    /**
     * 猫咪名称
     */
    private String name;
    
    /**
     * 品种名称
     */
    private String breedName;
    
    /**
     * 颜色
     */
    private String color;
    
    /**
     * 性别
     */
    private String gender;
    
    /**
     * 出生日期
     */
    private LocalDate dateOfBirth;
    
    /**
     * 年龄（月）
     */
    private Integer ageInMonths;
    
    /**
     * 照片URL
     */
    private String photoUrl;
    
    /**
     * 健康状态
     */
    private String healthStatus;
    
    /**
     * 是否可领养
     */
    private Boolean isAvailableForAdoption;

    /**
     * 主要照片URL
     */
    private String primaryPhoto;
}
