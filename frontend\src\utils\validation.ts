/**
 * 表单验证工具函数
 */

export interface ValidationRule {
  required?: boolean
  message?: string
  validator?: (rule: any, value: any, callback: any) => void
  trigger?: string | string[]
  min?: number
  max?: number
  pattern?: RegExp
  type?: 'string' | 'number' | 'boolean' | 'method' | 'regexp' | 'integer' | 'float' | 'array' | 'object' | 'enum' | 'date' | 'url' | 'hex' | 'email'
}

/**
 * 必填验证
 */
export const required = (message = '此字段为必填项'): ValidationRule => ({
  required: true,
  message,
  trigger: 'blur'
})

/**
 * 邮箱验证
 */
export const email = (message = '请输入正确的邮箱地址'): ValidationRule => ({
  type: 'email',
  message,
  trigger: 'blur'
})

/**
 * 手机号验证
 */
export const phone = (message = '请输入正确的手机号码'): ValidationRule => ({
  pattern: /^1[3-9]\d{9}$/,
  message,
  trigger: 'blur'
})

/**
 * 身份证号验证
 */
export const idCard = (message = '请输入正确的身份证号码'): ValidationRule => ({
  pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
  message,
  trigger: 'blur'
})

/**
 * 密码验证
 */
export const password = (minLength = 6, message = `密码长度不能少于${minLength}位`): ValidationRule => ({
  min: minLength,
  message,
  trigger: 'blur'
})

/**
 * 强密码验证
 */
export const strongPassword = (message = '密码必须包含大小写字母、数字和特殊字符，长度8-20位'): ValidationRule => ({
  pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,20}$/,
  message,
  trigger: 'blur'
})

/**
 * 确认密码验证
 */
export const confirmPassword = (passwordField: string, message = '两次输入的密码不一致'): ValidationRule => ({
  validator: (rule: any, value: any, callback: any) => {
    const form = rule.form
    if (value && value !== form[passwordField]) {
      callback(new Error(message))
    } else {
      callback()
    }
  },
  trigger: 'blur'
})

/**
 * 数字验证
 */
export const number = (message = '请输入数字'): ValidationRule => ({
  type: 'number',
  message,
  trigger: 'blur'
})

/**
 * 整数验证
 */
export const integer = (message = '请输入整数'): ValidationRule => ({
  type: 'integer',
  message,
  trigger: 'blur'
})

/**
 * 正数验证
 */
export const positiveNumber = (message = '请输入正数'): ValidationRule => ({
  validator: (rule: any, value: any, callback: any) => {
    if (value !== undefined && value !== null && value !== '') {
      const num = Number(value)
      if (isNaN(num) || num <= 0) {
        callback(new Error(message))
      } else {
        callback()
      }
    } else {
      callback()
    }
  },
  trigger: 'blur'
})

/**
 * 非负数验证
 */
export const nonNegativeNumber = (message = '请输入非负数'): ValidationRule => ({
  validator: (rule: any, value: any, callback: any) => {
    if (value !== undefined && value !== null && value !== '') {
      const num = Number(value)
      if (isNaN(num) || num < 0) {
        callback(new Error(message))
      } else {
        callback()
      }
    } else {
      callback()
    }
  },
  trigger: 'blur'
})

/**
 * 数字范围验证
 */
export const numberRange = (min: number, max: number, message?: string): ValidationRule => ({
  validator: (rule: any, value: any, callback: any) => {
    if (value !== undefined && value !== null && value !== '') {
      const num = Number(value)
      if (isNaN(num)) {
        callback(new Error('请输入数字'))
      } else if (num < min || num > max) {
        callback(new Error(message || `请输入${min}到${max}之间的数字`))
      } else {
        callback()
      }
    } else {
      callback()
    }
  },
  trigger: 'blur'
})

/**
 * 字符串长度验证
 */
export const stringLength = (min: number, max?: number, message?: string): ValidationRule => {
  if (max) {
    return {
      min,
      max,
      message: message || `长度应在${min}到${max}个字符之间`,
      trigger: 'blur'
    }
  } else {
    return {
      min,
      message: message || `长度不能少于${min}个字符`,
      trigger: 'blur'
    }
  }
}

/**
 * URL验证
 */
export const url = (message = '请输入正确的URL地址'): ValidationRule => ({
  type: 'url',
  message,
  trigger: 'blur'
})

/**
 * 日期验证
 */
export const date = (message = '请选择日期'): ValidationRule => ({
  type: 'date',
  message,
  trigger: 'change'
})

/**
 * 日期范围验证
 */
export const dateRange = (startDate?: Date, endDate?: Date, message?: string): ValidationRule => ({
  validator: (rule: any, value: any, callback: any) => {
    if (value) {
      const date = new Date(value)
      if (startDate && date < startDate) {
        callback(new Error(message || `日期不能早于${startDate.toLocaleDateString()}`))
      } else if (endDate && date > endDate) {
        callback(new Error(message || `日期不能晚于${endDate.toLocaleDateString()}`))
      } else {
        callback()
      }
    } else {
      callback()
    }
  },
  trigger: 'change'
})

/**
 * 数组验证
 */
export const array = (message = '请至少选择一项'): ValidationRule => ({
  type: 'array',
  min: 1,
  message,
  trigger: 'change'
})

/**
 * 自定义正则验证
 */
export const pattern = (regex: RegExp, message: string): ValidationRule => ({
  pattern: regex,
  message,
  trigger: 'blur'
})

/**
 * 自定义验证函数
 */
export const custom = (validator: (rule: any, value: any, callback: any) => void, trigger = 'blur'): ValidationRule => ({
  validator,
  trigger
})

/**
 * 猫咪名称验证
 */
export const catName = (message = '猫咪名称长度应在2-20个字符之间'): ValidationRule => ({
  min: 2,
  max: 20,
  message,
  trigger: 'blur'
})

/**
 * 价格验证
 */
export const price = (message = '请输入正确的价格'): ValidationRule => ({
  validator: (rule: any, value: any, callback: any) => {
    if (value !== undefined && value !== null && value !== '') {
      const num = Number(value)
      if (isNaN(num) || num < 0) {
        callback(new Error(message))
      } else if (num > 999999) {
        callback(new Error('价格不能超过999999'))
      } else {
        callback()
      }
    } else {
      callback()
    }
  },
  trigger: 'blur'
})

/**
 * 体重验证
 */
export const weight = (message = '请输入正确的体重（0.1-50kg）'): ValidationRule => ({
  validator: (rule: any, value: any, callback: any) => {
    if (value !== undefined && value !== null && value !== '') {
      const num = Number(value)
      if (isNaN(num) || num <= 0 || num > 50) {
        callback(new Error(message))
      } else {
        callback()
      }
    } else {
      callback()
    }
  },
  trigger: 'blur'
})

/**
 * 芯片号验证
 */
export const microchipId = (message = '芯片号格式不正确'): ValidationRule => ({
  pattern: /^[0-9A-Fa-f]{15}$/,
  message,
  trigger: 'blur'
})

/**
 * 客户评分验证
 */
export const customerScore = (message = '客户评分应在0-10之间'): ValidationRule => ({
  validator: (rule: any, value: any, callback: any) => {
    if (value !== undefined && value !== null && value !== '') {
      const num = Number(value)
      if (isNaN(num) || num < 0 || num > 10) {
        callback(new Error(message))
      } else {
        callback()
      }
    } else {
      callback()
    }
  },
  trigger: 'blur'
})

/**
 * 库存数量验证
 */
export const stockQuantity = (message = '库存数量必须为非负整数'): ValidationRule => ({
  validator: (rule: any, value: any, callback: any) => {
    if (value !== undefined && value !== null && value !== '') {
      const num = Number(value)
      if (isNaN(num) || num < 0 || !Number.isInteger(num)) {
        callback(new Error(message))
      } else {
        callback()
      }
    } else {
      callback()
    }
  },
  trigger: 'blur'
})

/**
 * 组合验证规则
 */
export const combine = (...rules: ValidationRule[]): ValidationRule[] => {
  return rules
}

/**
 * 条件验证
 */
export const conditional = (
  condition: (form: any) => boolean,
  rule: ValidationRule,
  message?: string
): ValidationRule => ({
  validator: (ruleObj: any, value: any, callback: any) => {
    const form = ruleObj.form
    if (condition(form)) {
      if (rule.required && (!value || value === '')) {
        callback(new Error(message || rule.message || '此字段为必填项'))
      } else if (rule.validator) {
        rule.validator(ruleObj, value, callback)
      } else if (rule.pattern && !rule.pattern.test(value)) {
        callback(new Error(message || rule.message || '格式不正确'))
      } else {
        callback()
      }
    } else {
      callback()
    }
  },
  trigger: rule.trigger || 'blur'
})
