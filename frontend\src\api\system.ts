import api from './index'
import type { ApiResponse, PageResponse, User, Role, Permission, PaginationParams } from '@/types/api'

export interface SystemConfig {
  siteName: string
  siteDescription: string
  siteLogo: string
  contactEmail: string
  contactPhone: string
  address: string
  businessHours: string
  timezone: string
  language: string
  currency: string
  dateFormat: string
  timeFormat: string
  enableRegistration: boolean
  enableEmailVerification: boolean
  enableSmsVerification: boolean
  maxFileSize: number
  allowedFileTypes: string[]
  backupEnabled: boolean
  backupFrequency: string
  maintenanceMode: boolean
  maintenanceMessage: string
}

export interface SystemInfo {
  version: string
  buildTime: string
  javaVersion: string
  osName: string
  osVersion: string
  totalMemory: string
  freeMemory: string
  usedMemory: string
  cpuCores: number
  diskSpace: {
    total: string
    free: string
    used: string
  }
  databaseInfo: {
    type: string
    version: string
    url: string
  }
  uptime: string
}

export interface AuditLog {
  id: number
  userId: number
  username: string
  action: string
  resource: string
  resourceId?: number
  details: string
  ipAddress: string
  userAgent: string
  timestamp: string
  result: 'SUCCESS' | 'FAILURE'
}

/**
 * 系统管理API
 */
export const systemApi = {
  /**
   * 获取系统配置
   */
  getSystemConfig: (): Promise<ApiResponse<SystemConfig>> => {
    return api.get('/api/system/config').then(response => response.data)
  },

  /**
   * 更新系统配置
   */
  updateSystemConfig: (config: Partial<SystemConfig>): Promise<ApiResponse<SystemConfig>> => {
    return api.put('/api/system/config', config).then(response => response.data)
  },

  /**
   * 获取系统信息
   */
  getSystemInfo: (): Promise<ApiResponse<SystemInfo>> => {
    return api.get('/api/system/info').then(response => response.data)
  },

  /**
   * 获取系统健康状态
   */
  getSystemHealth: (): Promise<ApiResponse<{
    status: 'UP' | 'DOWN' | 'DEGRADED'
    components: Record<string, {
      status: 'UP' | 'DOWN'
      details?: any
    }>
  }>> => {
    return api.get('/api/system/health').then(response => response.data)
  },

  /**
   * 获取用户列表
   */
  getUsers: (params: PaginationParams & {
    username?: string
    email?: string
    status?: string
    roleId?: number
  }): Promise<ApiResponse<PageResponse<User>>> => {
    return api.get('/api/system/users', { params }).then(response => response.data)
  },

  /**
   * 创建用户
   */
  createUser: (data: {
    username: string
    email: string
    password: string
    fullName?: string
    phone?: string
    roleIds: number[]
  }): Promise<ApiResponse<User>> => {
    return api.post('/api/system/users', data).then(response => response.data)
  },

  /**
   * 更新用户
   */
  updateUser: (id: number, data: Partial<User>): Promise<ApiResponse<User>> => {
    return api.put(`/api/system/users/${id}`, data).then(response => response.data)
  },

  /**
   * 删除用户
   */
  deleteUser: (id: number): Promise<ApiResponse<void>> => {
    return api.delete(`/api/system/users/${id}`).then(response => response.data)
  },

  /**
   * 重置用户密码
   */
  resetUserPassword: (id: number, newPassword: string): Promise<ApiResponse<void>> => {
    return api.put(`/api/system/users/${id}/reset-password`, { newPassword }).then(response => response.data)
  },

  /**
   * 锁定/解锁用户
   */
  toggleUserLock: (id: number, locked: boolean): Promise<ApiResponse<User>> => {
    return api.put(`/api/system/users/${id}/lock`, { locked }).then(response => response.data)
  },

  /**
   * 获取角色列表
   */
  getRoles: (params?: PaginationParams): Promise<ApiResponse<PageResponse<Role>>> => {
    return api.get('/api/system/roles', { params }).then(response => response.data)
  },

  /**
   * 创建角色
   */
  createRole: (data: {
    name: string
    description?: string
    permissionIds: number[]
  }): Promise<ApiResponse<Role>> => {
    return api.post('/api/system/roles', data).then(response => response.data)
  },

  /**
   * 更新角色
   */
  updateRole: (id: number, data: Partial<Role>): Promise<ApiResponse<Role>> => {
    return api.put(`/api/system/roles/${id}`, data).then(response => response.data)
  },

  /**
   * 删除角色
   */
  deleteRole: (id: number): Promise<ApiResponse<void>> => {
    return api.delete(`/api/system/roles/${id}`).then(response => response.data)
  },

  /**
   * 获取权限列表
   */
  getPermissions: (): Promise<ApiResponse<Permission[]>> => {
    return api.get('/api/system/permissions').then(response => response.data)
  },

  /**
   * 获取审计日志
   */
  getAuditLogs: (params: PaginationParams & {
    userId?: number
    action?: string
    resource?: string
    startDate?: string
    endDate?: string
    result?: string
  }): Promise<ApiResponse<PageResponse<AuditLog>>> => {
    return api.get('/api/system/audit-logs', { params }).then(response => response.data)
  },

  /**
   * 清理审计日志
   */
  cleanupAuditLogs: (beforeDate: string): Promise<ApiResponse<{ deletedCount: number }>> => {
    return api.delete('/api/system/audit-logs/cleanup', { data: { beforeDate } }).then(response => response.data)
  },

  /**
   * 创建数据库备份
   */
  createBackup: (): Promise<ApiResponse<{ backupFile: string, size: number }>> => {
    return api.post('/api/system/backup').then(response => response.data)
  },

  /**
   * 获取备份列表
   */
  getBackups: (): Promise<ApiResponse<Array<{
    filename: string
    size: number
    createdAt: string
  }>>> => {
    return api.get('/api/system/backups').then(response => response.data)
  },

  /**
   * 下载备份文件
   */
  downloadBackup: (filename: string): Promise<Blob> => {
    return api.get(`/api/system/backups/${filename}/download`, {
      responseType: 'blob'
    }).then(response => response.data)
  },

  /**
   * 删除备份文件
   */
  deleteBackup: (filename: string): Promise<ApiResponse<void>> => {
    return api.delete(`/api/system/backups/${filename}`).then(response => response.data)
  },

  /**
   * 恢复数据库
   */
  restoreBackup: (filename: string): Promise<ApiResponse<void>> => {
    return api.post(`/api/system/backups/${filename}/restore`).then(response => response.data)
  },

  /**
   * 清理系统缓存
   */
  clearCache: (cacheNames?: string[]): Promise<ApiResponse<void>> => {
    return api.post('/api/system/cache/clear', { cacheNames }).then(response => response.data)
  },

  /**
   * 获取缓存统计
   */
  getCacheStats: (): Promise<ApiResponse<Record<string, {
    size: number
    hitCount: number
    missCount: number
    hitRate: number
  }>>> => {
    return api.get('/api/system/cache/stats').then(response => response.data)
  },

  /**
   * 发送系统通知
   */
  sendSystemNotification: (data: {
    title: string
    content: string
    type: 'INFO' | 'WARNING' | 'ERROR' | 'SUCCESS'
    targetUsers?: number[]
    targetRoles?: number[]
  }): Promise<ApiResponse<void>> => {
    return api.post('/api/system/notifications/send', data).then(response => response.data)
  },

  /**
   * 获取系统日志
   */
  getSystemLogs: (params: {
    level?: 'ERROR' | 'WARN' | 'INFO' | 'DEBUG'
    startDate?: string
    endDate?: string
    keyword?: string
    page?: number
    size?: number
  }): Promise<ApiResponse<PageResponse<{
    timestamp: string
    level: string
    logger: string
    message: string
    exception?: string
  }>>> => {
    return api.get('/api/system/logs', { params }).then(response => response.data)
  },

  /**
   * 下载系统日志
   */
  downloadSystemLogs: (params: {
    level?: string
    startDate?: string
    endDate?: string
  }): Promise<Blob> => {
    return api.get('/api/system/logs/download', {
      params,
      responseType: 'blob'
    }).then(response => response.data)
  }
}

// 导出单个方法以便直接使用
export const {
  getSystemConfig,
  updateSystemConfig,
  getSystemInfo,
  getSystemHealth,
  getUsers,
  createUser,
  updateUser,
  deleteUser,
  resetUserPassword,
  toggleUserLock,
  getRoles,
  createRole,
  updateRole,
  deleteRole,
  getPermissions,
  getAuditLogs,
  cleanupAuditLogs,
  createBackup,
  getBackups,
  downloadBackup,
  deleteBackup,
  restoreBackup,
  clearCache,
  getCacheStats,
  sendSystemNotification,
  getSystemLogs,
  downloadSystemLogs
} = systemApi
