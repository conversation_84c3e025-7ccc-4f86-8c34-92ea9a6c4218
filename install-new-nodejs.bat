@echo off
chcp 65001 >nul
echo ========================================
echo Node.js 全新安装指导
echo ========================================
echo.

echo 📥 步骤1: 下载Node.js
echo ----------------------------------------
echo.
echo 推荐版本: Node.js LTS (长期支持版)
echo 当前推荐: v20.x 或 v18.x
echo.
echo 正在为您打开Node.js官方下载页面...
start https://nodejs.org/zh-cn/
echo.
echo 下载要求:
echo ✓ 选择 "LTS" 版本 (推荐)
echo ✓ 选择 "Windows Installer (.msi)"
echo ✓ 选择 "64-bit" (如果您的系统是64位)
echo.
echo 请下载完成后按任意键继续...
pause >nul

echo.
echo 🔧 步骤2: 安装配置
echo ----------------------------------------
echo.
echo 安装时的重要设置:
echo.
echo 1. 安装路径设置:
echo    ✅ 推荐: C:\Program Files\nodejs\
echo    ❌ 避免: D:\软件\ 或其他中文路径
echo.
echo 2. 必须勾选的选项:
echo    ✅ "Add to PATH environment variable"
echo    ✅ "Install additional tools for Node.js" (可选，但推荐)
echo.
echo 3. 安装过程:
echo    - 双击下载的 .msi 文件
echo    - 按照向导进行安装
echo    - 确保勾选上述选项
echo    - 等待安装完成
echo.

echo 请完成安装后按任意键继续验证...
pause >nul

echo.
echo ✅ 步骤3: 安装验证
echo ----------------------------------------
echo.

echo 请打开新的命令行窗口并测试:
echo.

echo 测试命令:
echo   node --version
echo   npm --version
echo.

echo 预期结果:
echo   v20.x.x (或v18.x.x)
echo   10.x.x (或9.x.x)
echo.

echo 正在当前窗口测试...
echo.

echo Node.js版本:
node --version 2>nul
if %errorlevel% equ 0 (
    echo ✅ Node.js安装成功！
) else (
    echo ❌ Node.js未正确安装
    echo 请检查:
    echo 1. 是否勾选了"Add to PATH"
    echo 2. 是否需要重启命令行
    echo 3. 环境变量是否正确设置
)
echo.

echo npm版本:
npm --version 2>nul
if %errorlevel% equ 0 (
    echo ✅ npm安装成功！
) else (
    echo ❌ npm未正确安装
    echo 这通常随Node.js一起安装
)
echo.

echo 📦 步骤4: 配置npm (可选但推荐)
echo ----------------------------------------
echo.

echo 设置npm国内镜像源 (提高下载速度):
npm config set registry https://registry.npmmirror.com/ 2>nul
if %errorlevel% equ 0 (
    echo ✅ npm镜像源设置成功
) else (
    echo ❌ npm镜像源设置失败，可能npm未正确安装
)
echo.

echo 验证镜像源设置:
npm config get registry 2>nul
echo.

echo 🚀 步骤5: 安装项目依赖
echo ----------------------------------------
echo.

echo 现在可以安装项目依赖了:
echo.

cd /d "%~dp0frontend"
if exist package.json (
    echo 找到项目配置文件: package.json
    echo.
    
    echo 清理旧的依赖文件...
    if exist node_modules (
        echo 删除旧的 node_modules...
        rmdir /s /q node_modules 2>nul
    )
    if exist package-lock.json (
        echo 删除旧的 package-lock.json...
        del package-lock.json 2>nul
    )
    
    echo.
    echo 安装项目依赖...
    echo 这可能需要几分钟时间，请耐心等待...
    echo.
    
    npm install
    if %errorlevel% equ 0 (
        echo.
        echo ✅ 项目依赖安装成功！
        echo.
        echo 🎉 准备启动开发服务器...
        echo 访问地址: http://localhost:3000
        echo 按 Ctrl+C 可以停止服务器
        echo.
        echo 正在启动...
        timeout /t 3 /nobreak >nul
        npm run dev
    ) else (
        echo.
        echo ❌ 项目依赖安装失败
        echo.
        echo 可能的解决方案:
        echo 1. 检查网络连接
        echo 2. 尝试清理npm缓存: npm cache clean --force
        echo 3. 尝试使用yarn: npm install -g yarn
        echo 4. 检查防火墙设置
        echo.
    )
) else (
    echo ❌ 未找到 package.json 文件
    echo 请确保在正确的项目目录中运行此脚本
)

echo.
echo ========================================
echo 安装过程完成！
echo ========================================
echo.

if %errorlevel% equ 0 (
    echo 🎉 恭喜！Node.js环境已完全修复
    echo.
    echo 现在您可以:
    echo ✓ 使用完整的开发环境
    echo ✓ 运行 npm run dev 启动开发服务器
    echo ✓ 使用所有npm命令
    echo ✓ 安装新的包和依赖
) else (
    echo ⚠️ 安装过程中遇到一些问题
    echo.
    echo 备用方案:
    echo 1. 继续使用静态版本: start-simple.bat
    echo 2. 尝试使用yarn替代npm
    echo 3. 检查网络和防火墙设置
)

echo.
pause
