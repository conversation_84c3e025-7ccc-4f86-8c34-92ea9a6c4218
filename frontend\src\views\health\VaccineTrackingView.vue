<template>
  <div class="vaccine-tracking">
    <div class="page-header">
      <h1>疫苗跟踪</h1>
      <p>猫咪疫苗接种计划和记录管理</p>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          添加疫苗记录
        </el-button>
        <el-button @click="showPlanDialog = true">
          <el-icon><Calendar /></el-icon>
          制定疫苗计划
        </el-button>
      </div>
    </div>

    <!-- 疫苗统计 -->
    <el-row :gutter="20" class="stats-section">
      <el-col :span="6">
        <StatCard
          title="已接种"
          :value="vaccineStats.completed"
          icon="SuccessFilled"
          color="#67C23A"
          :trend="vaccineStats.completedTrend"
        />
      </el-col>
      <el-col :span="6">
        <StatCard
          title="待接种"
          :value="vaccineStats.pending"
          icon="Clock"
          color="#E6A23C"
          :trend="vaccineStats.pendingTrend"
        />
      </el-col>
      <el-col :span="6">
        <StatCard
          title="过期未种"
          :value="vaccineStats.overdue"
          icon="Warning"
          color="#F56C6C"
          :trend="vaccineStats.overdueTrend"
        />
      </el-col>
      <el-col :span="6">
        <StatCard
          title="接种率"
          :value="vaccineStats.vaccinationRate"
          icon="TrendCharts"
          color="#409EFF"
          format="percentage"
          :trend="vaccineStats.rateTrend"
        />
      </el-col>
    </el-row>

    <!-- 疫苗提醒 -->
    <el-row :gutter="20" class="alerts-section">
      <el-col :span="24">
        <el-card class="alerts-card">
          <template #header>
            <div class="card-header">
              <span>疫苗提醒</span>
              <el-badge :value="urgentVaccines.length" class="alert-badge" />
            </div>
          </template>
          
          <div class="vaccine-alerts">
            <el-alert
              v-for="alert in urgentVaccines"
              :key="alert.id"
              :title="alert.title"
              :description="alert.description"
              :type="alert.urgency"
              :closable="false"
              class="vaccine-alert"
              @click="handleAlertClick(alert)"
            >
              <template #default>
                <div class="alert-content">
                  <div class="cat-info">
                    <el-avatar :src="alert.catPhoto" :size="40">
                      {{ alert.catName.charAt(0) }}
                    </el-avatar>
                    <div class="cat-details">
                      <h4>{{ alert.catName }}</h4>
                      <p>{{ alert.vaccineName }}</p>
                    </div>
                  </div>
                  <div class="alert-info">
                    <div class="due-date">{{ alert.dueDate }}</div>
                    <div class="days-overdue" v-if="alert.daysOverdue > 0">
                      逾期 {{ alert.daysOverdue }} 天
                    </div>
                  </div>
                  <div class="alert-actions">
                    <el-button size="small" type="primary" @click.stop="recordVaccination(alert)">
                      记录接种
                    </el-button>
                  </div>
                </div>
              </template>
            </el-alert>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 疫苗日历 -->
    <el-row :gutter="20" class="calendar-section">
      <el-col :span="24">
        <el-card class="calendar-card">
          <template #header>
            <div class="card-header">
              <span>疫苗日历</span>
              <el-button-group>
                <el-button size="small" :type="calendarView === 'month' ? 'primary' : ''" @click="calendarView = 'month'">月视图</el-button>
                <el-button size="small" :type="calendarView === 'week' ? 'primary' : ''" @click="calendarView = 'week'">周视图</el-button>
              </el-button-group>
            </div>
          </template>
          
          <el-calendar v-model="calendarDate" class="vaccine-calendar">
            <template #date-cell="{ data }">
              <div class="calendar-cell">
                <div class="cell-date">{{ data.day.split('-').slice(2).join('-') }}</div>
                <div v-if="getVaccinesForDate(data.day).length > 0" class="cell-vaccines">
                  <div
                    v-for="vaccine in getVaccinesForDate(data.day).slice(0, 2)"
                    :key="vaccine.id"
                    class="cell-vaccine"
                    :class="vaccine.status"
                    @click="viewVaccineDetail(vaccine)"
                  >
                    {{ vaccine.catName }} - {{ vaccine.vaccineName }}
                  </div>
                  <div
                    v-if="getVaccinesForDate(data.day).length > 2"
                    class="cell-more"
                  >
                    +{{ getVaccinesForDate(data.day).length - 2 }}
                  </div>
                </div>
              </div>
            </template>
          </el-calendar>
        </el-card>
      </el-col>
    </el-row>

    <!-- 疫苗记录列表 -->
    <el-row :gutter="20" class="records-section">
      <el-col :span="24">
        <el-card class="records-card">
          <template #header>
            <div class="card-header">
              <span>疫苗记录</span>
              <div class="filter-controls">
                <el-select v-model="filters.catId" placeholder="选择猫咪" clearable style="width: 150px">
                  <el-option
                    v-for="cat in cats"
                    :key="cat.id"
                    :label="cat.name"
                    :value="cat.id"
                  />
                </el-select>
                <el-select v-model="filters.vaccineType" placeholder="疫苗类型" clearable style="width: 150px">
                  <el-option
                    v-for="type in vaccineTypes"
                    :key="type.value"
                    :label="type.label"
                    :value="type.value"
                  />
                </el-select>
                <el-select v-model="filters.status" placeholder="状态" clearable style="width: 120px">
                  <el-option label="已完成" value="completed" />
                  <el-option label="待接种" value="pending" />
                  <el-option label="已过期" value="overdue" />
                </el-select>
                <el-button @click="fetchVaccineRecords">查询</el-button>
              </div>
            </div>
          </template>
          
          <el-table :data="vaccineRecords" v-loading="loading" stripe>
            <el-table-column label="猫咪" width="150">
              <template #default="scope">
                <div class="cat-info">
                  <el-avatar :src="scope.row.catPhoto" :size="30">
                    {{ scope.row.catName.charAt(0) }}
                  </el-avatar>
                  <span>{{ scope.row.catName }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="vaccineName" label="疫苗名称" width="150" />
            <el-table-column prop="vaccineType" label="疫苗类型" width="120" />
            <el-table-column prop="scheduledDate" label="计划日期" width="120">
              <template #default="scope">
                {{ formatDate(scope.row.scheduledDate) }}
              </template>
            </el-table-column>
            <el-table-column prop="actualDate" label="实际日期" width="120">
              <template #default="scope">
                {{ scope.row.actualDate ? formatDate(scope.row.actualDate) : '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="veterinarian" label="兽医" width="120" />
            <el-table-column prop="nextDueDate" label="下次接种" width="120">
              <template #default="scope">
                {{ scope.row.nextDueDate ? formatDate(scope.row.nextDueDate) : '-' }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button size="small" @click="viewDetail(scope.row)">详情</el-button>
                <el-button 
                  size="small" 
                  type="primary" 
                  v-if="scope.row.status === 'pending'"
                  @click="recordVaccination(scope.row)"
                >
                  记录接种
                </el-button>
                <el-button size="small" @click="editRecord(scope.row)">编辑</el-button>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="fetchVaccineRecords"
            @current-change="fetchVaccineRecords"
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 新增疫苗记录对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="添加疫苗记录"
      width="600px"
    >
      <VaccineRecordForm
        @submit="handleSubmit"
        @cancel="showCreateDialog = false"
      />
    </el-dialog>

    <!-- 疫苗计划对话框 -->
    <el-dialog
      v-model="showPlanDialog"
      title="制定疫苗计划"
      width="700px"
    >
      <VaccinePlanForm
        @submit="handlePlanSubmit"
        @cancel="showPlanDialog = false"
      />
    </el-dialog>

    <!-- 疫苗详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="疫苗记录详情"
      width="800px"
    >
      <VaccineRecordDetail
        v-if="selectedRecord"
        :record="selectedRecord"
        @edit="editRecord"
        @record-vaccination="recordVaccination"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Plus, Calendar, SuccessFilled, Clock, Warning, TrendCharts
} from '@element-plus/icons-vue'
import { healthApi, catApi } from '@/api'
import StatCard from '@/components/StatCard.vue'
import VaccineRecordForm from '@/components/health/VaccineRecordForm.vue'
import VaccinePlanForm from '@/components/health/VaccinePlanForm.vue'
import VaccineRecordDetail from '@/components/health/VaccineRecordDetail.vue'
import type { VaccineRecord, VaccineStats, Cat, VaccineAlert } from '@/types'

const loading = ref(false)
const showCreateDialog = ref(false)
const showPlanDialog = ref(false)
const showDetailDialog = ref(false)
const selectedRecord = ref<VaccineRecord | null>(null)
const calendarView = ref('month')
const calendarDate = ref(new Date())

const filters = ref({
  catId: null,
  vaccineType: null,
  status: null
})

const pagination = ref({
  page: 1,
  size: 20,
  total: 0
})

const vaccineStats = ref<VaccineStats>({
  completed: 0,
  pending: 0,
  overdue: 0,
  vaccinationRate: 0,
  completedTrend: { value: 0, type: 'flat' },
  pendingTrend: { value: 0, type: 'flat' },
  overdueTrend: { value: 0, type: 'flat' },
  rateTrend: { value: 0, type: 'flat' }
})

const vaccineRecords = ref<VaccineRecord[]>([])
const urgentVaccines = ref<VaccineAlert[]>([])
const cats = ref<Cat[]>([])

const vaccineTypes = [
  { label: '核心疫苗', value: 'core' },
  { label: '非核心疫苗', value: 'non_core' },
  { label: '狂犬疫苗', value: 'rabies' },
  { label: '猫三联', value: 'fvrcp' },
  { label: '猫五联', value: 'fvrcp_plus' }
]

async function fetchVaccineRecords() {
  try {
    loading.value = true
    const params = {
      page: pagination.value.page,
      size: pagination.value.size,
      ...filters.value
    }
    
    const response = await healthApi.getVaccineRecords(params)
    vaccineRecords.value = response.data
    pagination.value.total = response.total
  } catch (error) {
    ElMessage.error('获取疫苗记录失败')
  } finally {
    loading.value = false
  }
}

async function fetchDashboardData() {
  try {
    const [statsData, alertsData, catsData] = await Promise.all([
      healthApi.getVaccineStats(),
      healthApi.getUrgentVaccines(),
      catApi.getAll()
    ])
    
    vaccineStats.value = statsData
    urgentVaccines.value = alertsData
    cats.value = catsData
  } catch (error) {
    ElMessage.error('获取疫苗数据失败')
  }
}

function getVaccinesForDate(date: string): VaccineRecord[] {
  return vaccineRecords.value.filter(record => 
    record.scheduledDate === date || record.actualDate === date
  )
}

function viewVaccineDetail(vaccine: VaccineRecord) {
  selectedRecord.value = vaccine
  showDetailDialog.value = true
}

function viewDetail(record: VaccineRecord) {
  selectedRecord.value = record
  showDetailDialog.value = true
}

function editRecord(record: VaccineRecord) {
  // 编辑疫苗记录
  selectedRecord.value = record
  showCreateDialog.value = true
}

function recordVaccination(record: VaccineRecord | VaccineAlert) {
  // 记录疫苗接种
  // 这里可以打开一个快速记录对话框
  ElMessage.info('记录疫苗接种功能')
}

function handleAlertClick(alert: VaccineAlert) {
  // 处理疫苗提醒点击
  recordVaccination(alert)
}

async function handleSubmit(recordData: Partial<VaccineRecord>) {
  try {
    await healthApi.createVaccineRecord(recordData)
    ElMessage.success('疫苗记录添加成功')
    showCreateDialog.value = false
    fetchVaccineRecords()
    fetchDashboardData()
  } catch (error) {
    ElMessage.error('添加疫苗记录失败')
  }
}

async function handlePlanSubmit(planData: any) {
  try {
    await healthApi.createVaccinePlan(planData)
    ElMessage.success('疫苗计划制定成功')
    showPlanDialog.value = false
    fetchVaccineRecords()
    fetchDashboardData()
  } catch (error) {
    ElMessage.error('制定疫苗计划失败')
  }
}

function formatDate(date: string | Date) {
  return new Date(date).toLocaleDateString('zh-CN')
}

function getStatusType(status: string) {
  const typeMap: Record<string, string> = {
    'completed': 'success',
    'pending': 'warning',
    'overdue': 'danger',
    'cancelled': 'info'
  }
  return typeMap[status] || 'info'
}

function getStatusText(status: string) {
  const textMap: Record<string, string> = {
    'completed': '已完成',
    'pending': '待接种',
    'overdue': '已过期',
    'cancelled': '已取消'
  }
  return textMap[status] || status
}

onMounted(() => {
  fetchVaccineRecords()
  fetchDashboardData()
})
</script>

<style scoped>
.vaccine-tracking {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #303133;
}

.page-header p {
  margin: 5px 0 0 0;
  color: #909399;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.stats-section,
.alerts-section,
.calendar-section,
.records-section {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.vaccine-alerts {
  max-height: 300px;
  overflow-y: auto;
}

.vaccine-alert {
  margin-bottom: 10px;
  cursor: pointer;
}

.alert-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
}

.cat-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.cat-details h4 {
  margin: 0;
  color: #303133;
}

.cat-details p {
  margin: 0;
  color: #606266;
  font-size: 12px;
}

.alert-info {
  text-align: center;
}

.due-date {
  font-weight: bold;
  color: #303133;
}

.days-overdue {
  color: #F56C6C;
  font-size: 12px;
}

.vaccine-calendar {
  height: 600px;
}

.calendar-cell {
  height: 100%;
  padding: 2px;
}

.cell-date {
  font-size: 12px;
  color: #303133;
  margin-bottom: 2px;
}

.cell-vaccines {
  font-size: 10px;
}

.cell-vaccine {
  background: #409EFF;
  color: white;
  padding: 1px 3px;
  border-radius: 2px;
  margin-bottom: 1px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}

.cell-vaccine.completed {
  background: #67C23A;
}

.cell-vaccine.pending {
  background: #E6A23C;
}

.cell-vaccine.overdue {
  background: #F56C6C;
}

.cell-more {
  color: #909399;
  font-size: 10px;
}

.alert-badge {
  margin-left: 10px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .filter-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
}
</style>
