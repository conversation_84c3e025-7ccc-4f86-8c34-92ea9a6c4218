<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财务报表 - 猫舍管理系统</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            padding: 0 20px;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 70px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo {
            font-size: 24px;
            font-weight: 700;
            color: white;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo::before {
            content: '🐱';
            font-size: 28px;
        }

        .nav-links {
            display: flex;
            gap: 5px;
        }

        .nav-links a {
            color: rgba(255,255,255,0.9);
            text-decoration: none;
            padding: 10px 18px;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-links a:hover,
        .nav-links a.active {
            color: white;
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
            color: white;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-1px);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .page-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .page-title {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .page-title::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
        }

        .page-title h1 {
            color: #333;
            font-size: 36px;
            margin-bottom: 15px;
            font-weight: 700;
        }

        .page-title p {
            color: #666;
            font-size: 18px;
            max-width: 600px;
            margin: 0 auto;
        }

        .financial-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .summary-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .summary-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--card-color, #667eea);
        }

        .summary-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .card-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
            background: var(--card-color, #667eea);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #666;
        }

        .card-value {
            font-size: 28px;
            font-weight: 700;
            color: #333;
            margin-bottom: 10px;
        }

        .card-change {
            font-size: 14px;
            color: #10b981;
        }

        .revenue-card { --card-color: #10b981; }
        .expense-card { --card-color: #ef4444; }
        .profit-card { --card-color: #f59e0b; }
        .balance-card { --card-color: #8b5cf6; }

        .charts-section {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }

        .chart-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        }

        .chart-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .chart-canvas {
            position: relative;
            height: 300px;
        }

        .transactions-table {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        }

        .table-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #eee;
        }

        .table-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }

        .table-container {
            overflow-x: auto;
            max-height: 400px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
            font-size: 14px;
            position: sticky;
            top: 0;
        }

        td {
            color: #666;
            font-size: 14px;
        }

        .amount-positive {
            color: #10b981;
            font-weight: 600;
        }

        .amount-negative {
            color: #ef4444;
            font-weight: 600;
        }

        .transaction-type {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .type-income {
            background: #d1fae5;
            color: #065f46;
        }

        .type-expense {
            background: #fee2e2;
            color: #991b1b;
        }

        .loading {
            text-align: center;
            padding: 60px;
            color: #666;
        }

        .loading::before {
            content: '💰';
            font-size: 48px;
            display: block;
            margin-bottom: 20px;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        @media (max-width: 768px) {
            .charts-section {
                grid-template-columns: 1fr;
            }

            .financial-summary {
                grid-template-columns: 1fr;
            }

            .page-title {
                padding: 30px 20px;
            }

            .page-title h1 {
                font-size: 28px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">猫舍管理系统</div>
            <div class="nav-links">
                <a href="/dashboard.html">仪表盘</a>
                <a href="/cats-management.html">猫咪管理</a>
                <a href="/customers-management.html">客户管理</a>
                <a href="/health-management.html">健康记录</a>
                <a href="/breeding-management.html">繁育管理</a>
                <a href="/financial-reports.html" class="active">财务报表</a>
            </div>
            <div class="user-info">
                <div class="user-avatar" id="userAvatar">A</div>
                <span id="userName">加载中...</span>
                <button class="logout-btn" onclick="logout()">退出</button>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="page-header">
            <div class="page-title">
                <h1>财务报表</h1>
                <p>全面的财务数据分析和可视化报表，帮助您了解猫舍的经营状况</p>
            </div>
        </div>

        <div id="loadingMessage" class="loading">正在加载财务数据...</div>

        <div id="financialContent" style="display: none;">
            <div class="financial-summary">
                <div class="summary-card revenue-card">
                    <div class="card-header">
                        <div class="card-icon">💰</div>
                        <div class="card-title">总收入</div>
                    </div>
                    <div class="card-value" id="totalRevenue">¥0</div>
                    <div class="card-change">+15% 较上月</div>
                </div>

                <div class="summary-card expense-card">
                    <div class="card-header">
                        <div class="card-icon">💸</div>
                        <div class="card-title">总支出</div>
                    </div>
                    <div class="card-value" id="totalExpense">¥0</div>
                    <div class="card-change">+8% 较上月</div>
                </div>

                <div class="summary-card profit-card">
                    <div class="card-header">
                        <div class="card-icon">📈</div>
                        <div class="card-title">净利润</div>
                    </div>
                    <div class="card-value" id="netProfit">¥0</div>
                    <div class="card-change">+25% 较上月</div>
                </div>

                <div class="summary-card balance-card">
                    <div class="card-header">
                        <div class="card-icon">🏦</div>
                        <div class="card-title">账户余额</div>
                    </div>
                    <div class="card-value" id="accountBalance">¥0</div>
                    <div class="card-change">健康状态</div>
                </div>
            </div>

            <div class="charts-section">
                <div class="chart-container">
                    <div class="chart-title">月度收支趋势</div>
                    <div class="chart-canvas">
                        <canvas id="revenueChart"></canvas>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="chart-title">支出分类</div>
                    <div class="chart-canvas">
                        <canvas id="expenseChart"></canvas>
                    </div>
                </div>
            </div>

            <div class="transactions-table">
                <div class="table-header">
                    <div class="table-title">最近交易记录</div>
                </div>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>日期</th>
                                <th>类型</th>
                                <th>描述</th>
                                <th>分类</th>
                                <th>金额</th>
                            </tr>
                        </thead>
                        <tbody id="transactionsTableBody">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080/api';
        let revenueChart, expenseChart;
        
        // 检查登录状态
        function checkAuth() {
            const token = localStorage.getItem('token');
            const userInfo = localStorage.getItem('userInfo');
            
            if (!token || !userInfo) {
                window.location.href = '/login-test.html';
                return false;
            }
            
            try {
                const user = JSON.parse(userInfo);
                const userName = user.realName || user.username;
                document.getElementById('userName').textContent = userName;
                document.getElementById('userAvatar').textContent = userName.charAt(0).toUpperCase();
                return true;
            } catch (error) {
                logout();
                return false;
            }
        }
        
        // 退出登录
        function logout() {
            localStorage.removeItem('token');
            localStorage.removeItem('userInfo');
            window.location.href = '/login-test.html';
        }
        
        // 加载财务数据
        async function loadFinancialData() {
            const token = localStorage.getItem('token');
            
            try {
                // 并行请求财务统计和交易记录
                const [statsResponse, transactionsResponse] = await Promise.allSettled([
                    fetch(`${API_BASE_URL}/financial/stats`, {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    }),
                    fetch(`${API_BASE_URL}/financial/transactions`, {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    })
                ]);
                
                // 处理统计数据
                if (statsResponse.status === 'fulfilled') {
                    const statsData = await statsResponse.value.json();
                    if (statsData.success) {
                        updateFinancialSummary(statsData.data);
                    }
                }
                
                // 处理交易记录
                if (transactionsResponse.status === 'fulfilled') {
                    const transactionsData = await transactionsResponse.value.json();
                    if (transactionsData.success) {
                        updateTransactionsTable(transactionsData.data);
                    }
                }
                
                // 创建图表
                createCharts();
                
            } catch (error) {
                console.error('加载财务数据失败:', error);
            } finally {
                document.getElementById('loadingMessage').style.display = 'none';
                document.getElementById('financialContent').style.display = 'block';
            }
        }
        
        // 更新财务摘要
        function updateFinancialSummary(data) {
            // 使用模拟数据或API返回的数据
            const revenue = data.totalIncome || 125000;
            const expense = data.totalExpense || 85000;
            const profit = revenue - expense;
            const balance = data.accountBalance || 250000;
            
            document.getElementById('totalRevenue').textContent = `¥${revenue.toLocaleString()}`;
            document.getElementById('totalExpense').textContent = `¥${expense.toLocaleString()}`;
            document.getElementById('netProfit').textContent = `¥${profit.toLocaleString()}`;
            document.getElementById('accountBalance').textContent = `¥${balance.toLocaleString()}`;
        }
        
        // 更新交易记录表格
        function updateTransactionsTable(transactions) {
            const tableBody = document.getElementById('transactionsTableBody');
            
            // 使用模拟数据
            const mockTransactions = [
                { date: '2024-01-20', type: 'INCOME', description: '猫咪销售 - 英短小雪', category: '销售收入', amount: 8000 },
                { date: '2024-01-19', type: 'EXPENSE', description: '猫粮采购', category: '饲料费用', amount: -1200 },
                { date: '2024-01-18', type: 'INCOME', description: '繁育服务费', category: '服务收入', amount: 2000 },
                { date: '2024-01-17', type: 'EXPENSE', description: '疫苗接种费用', category: '医疗费用', amount: -800 },
                { date: '2024-01-16', type: 'INCOME', description: '猫咪销售 - 布偶美美', category: '销售收入', amount: 12000 },
                { date: '2024-01-15', type: 'EXPENSE', description: '设备维护', category: '设备费用', amount: -500 },
                { date: '2024-01-14', type: 'INCOME', description: '寄养服务费', category: '服务收入', amount: 600 },
                { date: '2024-01-13', type: 'EXPENSE', description: '清洁用品', category: '日常用品', amount: -300 }
            ];
            
            const dataToUse = Array.isArray(transactions) && transactions.length > 0 ? transactions : mockTransactions;
            
            tableBody.innerHTML = dataToUse.map(transaction => `
                <tr>
                    <td>${formatDate(transaction.date)}</td>
                    <td><span class="transaction-type ${transaction.type.toLowerCase() === 'income' ? 'type-income' : 'type-expense'}">${transaction.type === 'INCOME' ? '收入' : '支出'}</span></td>
                    <td>${transaction.description}</td>
                    <td>${transaction.category}</td>
                    <td class="${transaction.amount > 0 ? 'amount-positive' : 'amount-negative'}">¥${Math.abs(transaction.amount).toLocaleString()}</td>
                </tr>
            `).join('');
        }
        
        // 创建图表
        function createCharts() {
            createRevenueChart();
            createExpenseChart();
        }
        
        // 创建收支趋势图
        function createRevenueChart() {
            const ctx = document.getElementById('revenueChart').getContext('2d');
            
            revenueChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                    datasets: [{
                        label: '收入',
                        data: [15000, 18000, 22000, 19000, 25000, 28000, 32000, 29000, 35000, 38000, 42000, 45000],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: '支出',
                        data: [12000, 14000, 16000, 15000, 18000, 20000, 22000, 21000, 24000, 26000, 28000, 30000],
                        borderColor: '#ef4444',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '¥' + value.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });
        }
        
        // 创建支出分类饼图
        function createExpenseChart() {
            const ctx = document.getElementById('expenseChart').getContext('2d');
            
            expenseChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['饲料费用', '医疗费用', '设备费用', '人工费用', '其他费用'],
                    datasets: [{
                        data: [35, 25, 15, 20, 5],
                        backgroundColor: [
                            '#667eea',
                            '#f093fb',
                            '#43e97b',
                            '#f59e0b',
                            '#ef4444'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                        }
                    }
                }
            });
        }
        
        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '';
            try {
                return new Date(dateStr).toLocaleDateString('zh-CN');
            } catch (error) {
                return dateStr;
            }
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            if (checkAuth()) {
                loadFinancialData();
            }
        });
    </script>
</body>
</html>
