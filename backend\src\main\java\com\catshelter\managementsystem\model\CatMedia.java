package com.catshelter.managementsystem.model;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * 猫咪媒体文件实体类
 */
@Data
@Entity
@Table(name = "cat_media")
@EqualsAndHashCode(callSuper = false)
public class CatMedia {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_id", nullable = false)
    private Cat cat;

    @Enumerated(EnumType.STRING)
    @Column(name = "media_type", nullable = false)
    private MediaType mediaType;

    @Column(name = "file_path", nullable = false)
    private String filePath;

    @Column(name = "file_url")
    private String fileUrl;

    @Column(name = "original_file_name")
    private String originalFileName;

    @Column(name = "file_name")
    private String fileName;

    @Column(name = "file_extension")
    private String fileExtension;

    @Column(name = "mime_type")
    private String mimeType;

    @Column(name = "file_size")
    private Long fileSize;

    @Column(length = 200)
    private String title;

    @Column(columnDefinition = "TEXT")
    private String description;

    @Column(name = "is_primary")
    private Boolean isPrimary = false;

    @Column(name = "display_order")
    private Integer displayOrder = 0;

    @Column(name = "sort_order")
    private Integer sortOrder = 0;

    @Column
    private Integer width;

    @Column
    private Integer height;

    @Column
    private Integer duration; // 视频时长（秒）

    @Column(name = "thumbnail_path")
    private String thumbnailPath;

    @Column(name = "thumbnail_url")
    private String thumbnailUrl;

    @Column(name = "preview_image_path")
    private String previewImagePath;

    @Column(name = "preview_image_url")
    private String previewImageUrl;

    @Column(name = "file_hash")
    private String fileHash;

    @Column(length = 50)
    private String status = "ACTIVE";

    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    public enum MediaType {
        PHOTO("照片"),
        VIDEO("视频"),
        DOCUMENT("文档");

        private final String displayName;

        MediaType(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }
}