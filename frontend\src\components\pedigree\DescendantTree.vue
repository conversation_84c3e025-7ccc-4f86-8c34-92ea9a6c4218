<template>
  <div class="descendant-tree">
    <div class="tree-container">
      <div class="tree-content">
        <div class="node-container">
          <cat-node :node="node" :is-root="true" @node-click="handleNodeClick"></cat-node>
        </div>
        
        <div v-if="node.children && node.children.length > 0" class="children-container">
          <div v-for="child in node.children" :key="child.id" class="child-branch">
            <div class="node-container">
              <cat-node :node="child" @node-click="handleNodeClick"></cat-node>
            </div>
            
            <div v-if="child.children && child.children.length > 0" class="grandchildren-container">
              <div v-for="grandchild in child.children" :key="grandchild.id" class="grandchild-branch">
                <div class="node-container">
                  <cat-node :node="grandchild" @node-click="handleNodeClick"></cat-node>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div v-else class="no-descendants">
          <el-empty description="暂无后代记录"></el-empty>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CatNode from './CatNode.vue'

export default {
  name: 'DescendantTree',
  components: {
    CatNode
  },
  props: {
    node: {
      type: Object,
      required: true
    },
    generations: {
      type: Number,
      default: 2
    }
  },
  setup(props, { emit }) {
    const handleNodeClick = (node) => {
      emit('node-click', node)
    }
    
    return {
      handleNodeClick
    }
  }
}
</script>

<style scoped>
.descendant-tree {
  width: 100%;
  overflow: auto;
}

.tree-container {
  display: flex;
  flex-direction: column;
  min-width: 600px;
}

.tree-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
}

.node-container {
  margin: 10px;
}

.children-container {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 30px;
  position: relative;
}

.children-container::before {
  content: '';
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 1px;
  height: 15px;
  background-color: #ccc;
}

.child-branch {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 20px;
  position: relative;
}

.child-branch::before {
  content: '';
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 1px;
  height: 15px;
  background-color: #ccc;
}

.child-branch::after {
  content: '';
  position: absolute;
  top: -15px;
  left: 0;
  right: 0;
  height: 1px;
  background-color: #ccc;
}

.grandchildren-container {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 30px;
  position: relative;
}

.grandchildren-container::before {
  content: '';
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 1px;
  height: 15px;
  background-color: #ccc;
}

.grandchild-branch {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 10px;
  position: relative;
}

.grandchild-branch::before {
  content: '';
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 1px;
  height: 15px;
  background-color: #ccc;
}

.grandchild-branch::after {
  content: '';
  position: absolute;
  top: -15px;
  left: 0;
  right: 0;
  height: 1px;
  background-color: #ccc;
}

.no-descendants {
  margin-top: 50px;
}

/* 连接线的水平部分 */
.children-container .child-branch:first-child::after {
  left: 50%;
}

.children-container .child-branch:last-child::after {
  right: 50%;
}

.grandchildren-container .grandchild-branch:first-child::after {
  left: 50%;
}

.grandchildren-container .grandchild-branch:last-child::after {
  right: 50%;
}
</style>