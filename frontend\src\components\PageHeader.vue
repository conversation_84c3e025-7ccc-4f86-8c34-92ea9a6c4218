<template>
  <div class="page-header" :class="headerClass">
    <div class="header-content">
      <!-- 返回按钮 -->
      <div v-if="showBack" class="header-back">
        <el-button 
          type="text" 
          @click="handleBack"
          :icon="ArrowLeft"
        >
          返回
        </el-button>
      </div>
      
      <!-- 标题区域 -->
      <div class="header-title">
        <div class="title-main">
          <h1>{{ title }}</h1>
          <el-tag v-if="status" :type="statusType" size="small">
            {{ status }}
          </el-tag>
        </div>
        <p v-if="subtitle" class="title-subtitle">{{ subtitle }}</p>
      </div>
      
      <!-- 操作区域 -->
      <div v-if="$slots.actions" class="header-actions">
        <slot name="actions"></slot>
      </div>
    </div>
    
    <!-- 面包屑导航 -->
    <div v-if="breadcrumbs && breadcrumbs.length > 0" class="header-breadcrumb">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item 
          v-for="(item, index) in breadcrumbs" 
          :key="index"
          :to="item.path"
        >
          <el-icon v-if="item.icon">
            <component :is="item.icon" />
          </el-icon>
          {{ item.title }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    
    <!-- 标签页 -->
    <div v-if="tabs && tabs.length > 0" class="header-tabs">
      <el-tabs
        :model-value="activeTab"
        @update:model-value="handleTabChange"
        :stretch="tabStretch"
      >
        <el-tab-pane 
          v-for="tab in tabs" 
          :key="tab.name"
          :label="tab.label"
          :name="tab.name"
          :disabled="tab.disabled"
        >
          <template #label>
            <span class="tab-label">
              <el-icon v-if="tab.icon">
                <component :is="tab.icon" />
              </el-icon>
              {{ tab.label }}
              <el-badge 
                v-if="tab.badge" 
                :value="tab.badge" 
                :max="99"
                class="tab-badge"
              />
            </span>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>
    
    <!-- 额外内容 -->
    <div v-if="$slots.extra" class="header-extra">
      <slot name="extra"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowLeft } from '@element-plus/icons-vue'

interface BreadcrumbItem {
  title: string
  path?: string
  icon?: string
}

interface TabItem {
  name: string
  label: string
  icon?: string
  badge?: number | string
  disabled?: boolean
}

interface Props {
  title: string
  subtitle?: string
  status?: string
  statusType?: 'success' | 'info' | 'warning' | 'danger'
  showBack?: boolean
  backPath?: string
  breadcrumbs?: BreadcrumbItem[]
  tabs?: TabItem[]
  activeTab?: string
  tabStretch?: boolean
  size?: 'small' | 'medium' | 'large'
  background?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  statusType: 'info',
  showBack: false,
  tabStretch: false,
  size: 'medium',
  background: true
})

const emit = defineEmits<{
  back: []
  tabChange: [tabName: string]
}>()

const router = useRouter()

// 计算属性
const headerClass = computed(() => {
  return {
    [`page-header--${props.size}`]: true,
    'page-header--no-bg': !props.background
  }
})

// 方法
const handleBack = () => {
  if (props.backPath) {
    router.push(props.backPath)
  } else {
    router.back()
  }
  emit('back')
}

const handleTabChange = (tabName: string) => {
  emit('tabChange', tabName)
}
</script>

<style scoped lang="scss">
.page-header {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 20px;
  
  &--small {
    .header-content {
      padding: 12px 20px;
      
      .header-title {
        .title-main h1 {
          font-size: 18px;
        }
        
        .title-subtitle {
          font-size: 12px;
        }
      }
    }
    
    .header-breadcrumb {
      padding: 8px 20px 12px;
    }
  }
  
  &--medium {
    .header-content {
      padding: 16px 20px;
      
      .header-title {
        .title-main h1 {
          font-size: 24px;
        }
        
        .title-subtitle {
          font-size: 14px;
        }
      }
    }
    
    .header-breadcrumb {
      padding: 12px 20px 16px;
    }
  }
  
  &--large {
    .header-content {
      padding: 24px 20px;
      
      .header-title {
        .title-main h1 {
          font-size: 28px;
        }
        
        .title-subtitle {
          font-size: 16px;
        }
      }
    }
    
    .header-breadcrumb {
      padding: 16px 20px 20px;
    }
  }
  
  &--no-bg {
    background: transparent;
    border-bottom: none;
  }
  
  .header-content {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    
    .header-back {
      flex-shrink: 0;
      padding-top: 4px;
    }
    
    .header-title {
      flex: 1;
      min-width: 0;
      
      .title-main {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 4px;
        
        h1 {
          margin: 0;
          font-weight: 600;
          color: #303133;
          line-height: 1.2;
        }
      }
      
      .title-subtitle {
        margin: 0;
        color: #909399;
        line-height: 1.4;
      }
    }
    
    .header-actions {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
  
  .header-breadcrumb {
    border-top: 1px solid #f0f0f0;
  }
  
  .header-tabs {
    border-top: 1px solid #f0f0f0;
    
    :deep(.el-tabs__header) {
      margin: 0;
      
      .el-tabs__nav-wrap {
        padding: 0 20px;
      }
    }
    
    .tab-label {
      display: flex;
      align-items: center;
      gap: 4px;
      
      .tab-badge {
        margin-left: 4px;
      }
    }
  }
  
  .header-extra {
    padding: 0 20px 16px;
    border-top: 1px solid #f0f0f0;
  }
}

@media (max-width: 768px) {
  .page-header {
    margin-bottom: 16px;
    
    &--medium,
    &--large {
      .header-content {
        padding: 12px 16px;
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
        
        .header-title {
          .title-main h1 {
            font-size: 20px;
          }
          
          .title-subtitle {
            font-size: 13px;
          }
        }
        
        .header-actions {
          justify-content: flex-end;
        }
      }
      
      .header-breadcrumb {
        padding: 8px 16px 12px;
      }
    }
    
    .header-tabs {
      :deep(.el-tabs__header) {
        .el-tabs__nav-wrap {
          padding: 0 16px;
        }
      }
    }
    
    .header-extra {
      padding: 0 16px 12px;
    }
  }
}
</style>
