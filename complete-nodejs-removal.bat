@echo off
chcp 65001 >nul
echo ========================================
echo Node.js 完全彻底卸载工具
echo ========================================
echo.

echo 🔍 第一步：定位Node.js安装位置
echo ----------------------------------------
echo.

echo 检查Node.js当前路径...
where node 2>nul
if %errorlevel% equ 0 (
    echo ✓ 找到Node.js安装路径
    for /f "tokens=*" %%i in ('where node 2^>nul') do (
        echo Node.js位置: %%i
        set "NODE_PATH=%%i"
    )
) else (
    echo ❌ 无法通过where命令找到Node.js
)
echo.

echo 检查npm当前路径...
where npm 2>nul
if %errorlevel% equ 0 (
    echo ✓ 找到npm路径
    for /f "tokens=*" %%i in ('where npm 2^>nul') do (
        echo npm位置: %%i
    )
) else (
    echo ❌ 无法找到npm路径
)
echo.

echo 🗂️ 第二步：检查所有可能的安装目录
echo ----------------------------------------
echo.

echo 检查标准安装位置...

if exist "C:\Program Files\nodejs\" (
    echo ✓ 找到: C:\Program Files\nodejs\
    dir "C:\Program Files\nodejs\" /b
    set FOUND_STANDARD=1
) else (
    echo ❌ 未找到: C:\Program Files\nodejs\
)
echo.

if exist "C:\Program Files (x86)\nodejs\" (
    echo ✓ 找到: C:\Program Files (x86)\nodejs\
    dir "C:\Program Files (x86)\nodejs\" /b
    set FOUND_X86=1
) else (
    echo ❌ 未找到: C:\Program Files (x86)\nodejs\
)
echo.

echo 检查用户目录安装...
if exist "%USERPROFILE%\nodejs\" (
    echo ✓ 找到: %USERPROFILE%\nodejs\
    dir "%USERPROFILE%\nodejs\" /b
    set FOUND_USER=1
) else (
    echo ❌ 未找到: %USERPROFILE%\nodejs\
)
echo.

echo 检查AppData目录...
if exist "%APPDATA%\npm\" (
    echo ✓ 找到: %APPDATA%\npm\
    set FOUND_APPDATA_NPM=1
) else (
    echo ❌ 未找到: %APPDATA%\npm\
)

if exist "%APPDATA%\npm-cache\" (
    echo ✓ 找到: %APPDATA%\npm-cache\
    set FOUND_APPDATA_CACHE=1
) else (
    echo ❌ 未找到: %APPDATA%\npm-cache\
)
echo.

echo 检查LocalAppData目录...
if exist "%LOCALAPPDATA%\npm\" (
    echo ✓ 找到: %LOCALAPPDATA%\npm\
    set FOUND_LOCALAPPDATA_NPM=1
) else (
    echo ❌ 未找到: %LOCALAPPDATA%\npm\
)

if exist "%LOCALAPPDATA%\npm-cache\" (
    echo ✓ 找到: %LOCALAPPDATA%\npm-cache\
    set FOUND_LOCALAPPDATA_CACHE=1
) else (
    echo ❌ 未找到: %LOCALAPPDATA%\npm-cache\
)
echo.

echo 检查其他可能位置...
if exist "D:\nodejs\" (
    echo ✓ 找到: D:\nodejs\
    set FOUND_D_NODEJS=1
) else (
    echo ❌ 未找到: D:\nodejs\
)

if exist "D:\软件\nodejs\" (
    echo ✓ 找到: D:\软件\nodejs\
    set FOUND_D_SOFTWARE=1
) else (
    echo ❌ 未找到: D:\软件\nodejs\
)

if exist "D:\软件\node_modules\" (
    echo ⚠️ 找到问题目录: D:\软件\node_modules\
    set FOUND_PROBLEM_DIR=1
) else (
    echo ✅ 问题目录不存在: D:\软件\node_modules\
)
echo.

echo 🗑️ 第三步：开始删除所有Node.js文件
echo ----------------------------------------
echo.

echo ⚠️ 警告：即将删除所有找到的Node.js相关文件和目录
echo 按任意键继续，或按Ctrl+C取消...
pause >nul

echo 开始删除...
echo.

REM 删除标准安装目录
if defined FOUND_STANDARD (
    echo 删除: C:\Program Files\nodejs\
    rmdir /s /q "C:\Program Files\nodejs\" 2>nul
    if exist "C:\Program Files\nodejs\" (
        echo ❌ 删除失败，可能需要管理员权限
        echo 请手动删除此目录
    ) else (
        echo ✅ 删除成功
    )
)

REM 删除32位安装目录
if defined FOUND_X86 (
    echo 删除: C:\Program Files (x86)\nodejs\
    rmdir /s /q "C:\Program Files (x86)\nodejs\" 2>nul
    if exist "C:\Program Files (x86)\nodejs\" (
        echo ❌ 删除失败，可能需要管理员权限
    ) else (
        echo ✅ 删除成功
    )
)

REM 删除用户目录
if defined FOUND_USER (
    echo 删除: %USERPROFILE%\nodejs\
    rmdir /s /q "%USERPROFILE%\nodejs\" 2>nul
    if exist "%USERPROFILE%\nodejs\" (
        echo ❌ 删除失败
    ) else (
        echo ✅ 删除成功
    )
)

REM 删除AppData目录
if defined FOUND_APPDATA_NPM (
    echo 删除: %APPDATA%\npm\
    rmdir /s /q "%APPDATA%\npm\" 2>nul
    if exist "%APPDATA%\npm\" (
        echo ❌ 删除失败
    ) else (
        echo ✅ 删除成功
    )
)

if defined FOUND_APPDATA_CACHE (
    echo 删除: %APPDATA%\npm-cache\
    rmdir /s /q "%APPDATA%\npm-cache\" 2>nul
    if exist "%APPDATA%\npm-cache\" (
        echo ❌ 删除失败
    ) else (
        echo ✅ 删除成功
    )
)

REM 删除LocalAppData目录
if defined FOUND_LOCALAPPDATA_NPM (
    echo 删除: %LOCALAPPDATA%\npm\
    rmdir /s /q "%LOCALAPPDATA%\npm\" 2>nul
    if exist "%LOCALAPPDATA%\npm\" (
        echo ❌ 删除失败
    ) else (
        echo ✅ 删除成功
    )
)

if defined FOUND_LOCALAPPDATA_CACHE (
    echo 删除: %LOCALAPPDATA%\npm-cache\
    rmdir /s /q "%LOCALAPPDATA%\npm-cache\" 2>nul
    if exist "%LOCALAPPDATA%\npm-cache\" (
        echo ❌ 删除失败
    ) else (
        echo ✅ 删除成功
    )
)

REM 删除其他位置
if defined FOUND_D_NODEJS (
    echo 删除: D:\nodejs\
    rmdir /s /q "D:\nodejs\" 2>nul
    if exist "D:\nodejs\" (
        echo ❌ 删除失败
    ) else (
        echo ✅ 删除成功
    )
)

if defined FOUND_D_SOFTWARE (
    echo 删除: D:\软件\nodejs\
    rmdir /s /q "D:\软件\nodejs\" 2>nul
    if exist "D:\软件\nodejs\" (
        echo ❌ 删除失败
    ) else (
        echo ✅ 删除成功
    )
)

if defined FOUND_PROBLEM_DIR (
    echo 删除问题目录: D:\软件\node_modules\
    rmdir /s /q "D:\软件\node_modules\" 2>nul
    if exist "D:\软件\node_modules\" (
        echo ❌ 删除失败，这是关键目录！
        echo 请手动删除: D:\软件\node_modules\
    ) else (
        echo ✅ 问题目录删除成功！
    )
)

echo.
echo 🔧 第四步：清理环境变量
echo ----------------------------------------
echo.

echo 需要手动清理环境变量:
echo 1. 按Win+R，输入: sysdm.cpl
echo 2. 点击"环境变量"
echo 3. 编辑"Path"变量，删除所有nodejs相关路径
echo 4. 删除NODE_PATH变量(如果存在)
echo.

echo 正在打开系统属性...
start sysdm.cpl

echo 请完成环境变量清理后按任意键继续...
pause >nul

echo.
echo 🔧 第五步：清理注册表 (可选)
echo ----------------------------------------
echo.

echo 清理注册表中的Node.js相关项...
echo 注意：这一步是可选的，如果不确定请跳过
echo.

echo 按Y继续清理注册表，按N跳过:
set /p CLEAN_REGISTRY="清理注册表? (Y/N): "

if /i "%CLEAN_REGISTRY%"=="Y" (
    echo 清理注册表...
    reg delete "HKEY_LOCAL_MACHINE\SOFTWARE\Node.js" /f 2>nul
    reg delete "HKEY_CURRENT_USER\SOFTWARE\Node.js" /f 2>nul
    reg delete "HKEY_LOCAL_MACHINE\SOFTWARE\npm" /f 2>nul
    reg delete "HKEY_CURRENT_USER\SOFTWARE\npm" /f 2>nul
    echo 注册表清理完成
) else (
    echo 跳过注册表清理
)

echo.
echo ✅ 第六步：验证卸载结果
echo ----------------------------------------
echo.

echo 测试Node.js是否还存在...
node --version 2>nul
if %errorlevel% equ 0 (
    echo ❌ Node.js仍然可以执行！
    echo 当前版本:
    node --version
    echo 路径:
    where node
    echo.
    echo ⚠️ 卸载不完整，可能原因:
    echo 1. 还有其他安装位置未发现
    echo 2. 环境变量未清理干净
    echo 3. 需要重启计算机
) else (
    echo ✅ Node.js已完全卸载！
)

echo.
echo 测试npm是否还存在...
npm --version 2>nul
if %errorlevel% equ 0 (
    echo ❌ npm仍然可以执行！
) else (
    echo ✅ npm已完全卸载！
)

echo.
echo ========================================
echo 卸载完成！
echo ========================================
echo.

node --version 2>nul
npm --version 2>nul
if %errorlevel% neq 0 (
    echo 🎉 恭喜！Node.js已完全卸载干净！
    echo.
    echo 现在可以:
    echo 1. 重启计算机 (推荐)
    echo 2. 下载并安装新版本Node.js
    echo 3. 访问: https://nodejs.org
    echo.
    echo 安装新版本时请注意:
    echo ✓ 选择LTS版本
    echo ✓ 安装到标准路径: C:\Program Files\nodejs\
    echo ✓ 勾选"Add to PATH"选项
) else (
    echo ⚠️ 卸载可能不完整
    echo 建议:
    echo 1. 重启计算机
    echo 2. 再次运行此脚本
    echo 3. 手动检查剩余文件
)

echo.
pause
