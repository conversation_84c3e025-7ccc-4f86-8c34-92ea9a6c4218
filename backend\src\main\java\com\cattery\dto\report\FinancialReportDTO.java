package com.cattery.dto.report;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 财务报表DTO
 */
@Data
public class FinancialReportDTO {
    
    /**
     * 报表期间
     */
    private String reportPeriod;
    
    /**
     * 生成时间
     */
    private LocalDateTime generatedAt;
    
    /**
     * 总收入
     */
    private BigDecimal totalIncome;
    
    /**
     * 总支出
     */
    private BigDecimal totalExpense;
    
    /**
     * 净利润
     */
    private BigDecimal netProfit;
    
    /**
     * 利润率
     */
    private BigDecimal profitMargin;
    
    /**
     * 按类别的收入分布
     */
    private Map<String, BigDecimal> incomeByCategory;
    
    /**
     * 按类别的支出分布
     */
    private Map<String, BigDecimal> expenseByCategory;
    
    /**
     * 月度趋势
     */
    private List<MonthlyFinancialDataDTO> monthlyTrend;
    
    /**
     * 支付方式统计
     */
    private Map<String, BigDecimal> paymentMethodStats;
    
    /**
     * 现金流状况
     */
    private String cashFlowStatus;
    
    /**
     * 财务健康评分
     */
    private Integer financialHealthScore;

    /**
     * 净收入
     */
    private BigDecimal netIncome;

    /**
     * 支付方式统计
     */
    private Map<String, Long> paymentMethodStatistics;
}
