package com.cattery.service;

import com.cattery.entity.InventoryItem;
import com.cattery.repository.InventoryItemRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 库存管理服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class InventoryService {
    
    private final InventoryItemRepository inventoryItemRepository;
    
    /**
     * 添加库存物品
     */
    public InventoryItem addInventoryItem(InventoryItem item) {
        log.info("添加库存物品: name={}, category={}", item.getItemName(), item.getCategory());

        // 检查物品名称是否已存在
        if (inventoryItemRepository.existsByItemName(item.getItemName())) {
            throw new IllegalArgumentException("物品名称已存在: " + item.getItemName());
        }
        
        item.setStatus(InventoryItem.Status.ACTIVE);
        item.setCreatedAt(LocalDateTime.now());
        item.setUpdatedAt(LocalDateTime.now());
        
        return inventoryItemRepository.save(item);
    }
    
    /**
     * 更新库存物品
     */
    public InventoryItem updateInventoryItem(Long itemId, InventoryItem updatedItem) {
        log.info("更新库存物品: itemId={}", itemId);
        
        InventoryItem existingItem = inventoryItemRepository.findById(itemId)
            .orElseThrow(() -> new IllegalArgumentException("库存物品不存在"));
        
        // 检查名称唯一性（排除当前物品）
        if (!existingItem.getItemName().equals(updatedItem.getItemName()) &&
            inventoryItemRepository.existsByItemName(updatedItem.getItemName())) {
            throw new IllegalArgumentException("物品名称已存在: " + updatedItem.getItemName());
        }

        // 更新字段
        existingItem.setItemName(updatedItem.getItemName());
        existingItem.setCategory(updatedItem.getCategory());
        existingItem.setDescription(updatedItem.getDescription());
        existingItem.setBrand(updatedItem.getBrand());
        existingItem.setModel(updatedItem.getModel());
        existingItem.setUnit(updatedItem.getUnit());
        existingItem.setUnitPrice(updatedItem.getUnitPrice());
        existingItem.setCurrentStock(updatedItem.getCurrentStock());
        existingItem.setMinimumStock(updatedItem.getMinimumStock());
        existingItem.setMaximumStock(updatedItem.getMaximumStock());
        existingItem.setSupplier(updatedItem.getSupplier());
        existingItem.setSupplierContact(updatedItem.getSupplierContact());
        existingItem.setStorageLocation(updatedItem.getStorageLocation());
        existingItem.setExpiryDate(updatedItem.getExpiryDate());
        existingItem.setNotes(updatedItem.getNotes());
        existingItem.setUpdatedAt(LocalDateTime.now());
        
        return inventoryItemRepository.save(existingItem);
    }
    
    /**
     * 获取所有库存物品
     */
    @Transactional(readOnly = true)
    public Page<InventoryItem> getAllInventoryItems(Pageable pageable) {
        log.debug("获取所有库存物品: pageable={}", pageable);
        return inventoryItemRepository.findAll(pageable);
    }
    
    /**
     * 根据ID获取库存物品
     */
    @Transactional(readOnly = true)
    public Optional<InventoryItem> getInventoryItemById(Long itemId) {
        log.debug("根据ID获取库存物品: itemId={}", itemId);
        return inventoryItemRepository.findById(itemId);
    }
    
    /**
     * 根据分类获取库存物品
     */
    @Transactional(readOnly = true)
    public Page<InventoryItem> getInventoryItemsByCategory(String category, Pageable pageable) {
        log.debug("根据分类获取库存物品: category={}, pageable={}", category, pageable);
        return inventoryItemRepository.findByCategory(category, pageable);
    }
    
    /**
     * 搜索库存物品
     */
    @Transactional(readOnly = true)
    public Page<InventoryItem> searchInventoryItems(String keyword, Pageable pageable) {
        log.debug("搜索库存物品: keyword={}, pageable={}", keyword, pageable);
        return inventoryItemRepository.findByItemNameContainingIgnoreCaseOrDescriptionContainingIgnoreCase(
            keyword, keyword, pageable);
    }
    
    /**
     * 获取库存不足的物品
     */
    @Transactional(readOnly = true)
    public List<InventoryItem> getLowStockItems() {
        log.debug("获取库存不足的物品");
        return inventoryItemRepository.findLowStockItems();
    }
    
    /**
     * 获取即将过期的物品
     */
    @Transactional(readOnly = true)
    public List<InventoryItem> getExpiringItems(int daysAhead) {
        log.debug("获取即将过期的物品: daysAhead={}", daysAhead);
        LocalDateTime cutoffDate = LocalDateTime.now().plusDays(daysAhead);
        return inventoryItemRepository.findExpiringItems(cutoffDate);
    }
    
    /**
     * 入库操作
     */
    public InventoryItem stockIn(Long itemId, Integer quantity, String notes) {
        log.info("入库操作: itemId={}, quantity={}", itemId, quantity);
        
        InventoryItem item = inventoryItemRepository.findById(itemId)
            .orElseThrow(() -> new IllegalArgumentException("库存物品不存在"));
        
        if (quantity <= 0) {
            throw new IllegalArgumentException("入库数量必须大于0");
        }
        
        item.setCurrentStock(item.getCurrentStock() + quantity);
        item.setUpdatedAt(LocalDateTime.now());
        
        // 这里可以添加库存变动记录的逻辑
        log.info("入库完成: itemId={}, 新库存={}", itemId, item.getCurrentStock());
        
        return inventoryItemRepository.save(item);
    }
    
    /**
     * 出库操作
     */
    public InventoryItem stockOut(Long itemId, Integer quantity, String notes) {
        log.info("出库操作: itemId={}, quantity={}", itemId, quantity);
        
        InventoryItem item = inventoryItemRepository.findById(itemId)
            .orElseThrow(() -> new IllegalArgumentException("库存物品不存在"));
        
        if (quantity <= 0) {
            throw new IllegalArgumentException("出库数量必须大于0");
        }
        
        if (item.getCurrentStock() < quantity) {
            throw new IllegalArgumentException("库存不足，当前库存: " + item.getCurrentStock());
        }
        
        item.setCurrentStock(item.getCurrentStock() - quantity);
        item.setUpdatedAt(LocalDateTime.now());
        
        // 这里可以添加库存变动记录的逻辑
        log.info("出库完成: itemId={}, 新库存={}", itemId, item.getCurrentStock());
        
        return inventoryItemRepository.save(item);
    }
    
    /**
     * 更新库存状态
     */
    public InventoryItem updateItemStatus(Long itemId, InventoryItem.Status status) {
        log.info("更新库存状态: itemId={}, status={}", itemId, status);
        
        InventoryItem item = inventoryItemRepository.findById(itemId)
            .orElseThrow(() -> new IllegalArgumentException("库存物品不存在"));
        
        item.setStatus(status);
        item.setUpdatedAt(LocalDateTime.now());
        
        return inventoryItemRepository.save(item);
    }
    
    /**
     * 删除库存物品
     */
    public void deleteInventoryItem(Long itemId) {
        log.info("删除库存物品: itemId={}", itemId);
        
        InventoryItem item = inventoryItemRepository.findById(itemId)
            .orElseThrow(() -> new IllegalArgumentException("库存物品不存在"));
        
        // 检查是否还有库存
        if (item.getCurrentStock() > 0) {
            throw new IllegalStateException("无法删除有库存的物品，当前库存: " + item.getCurrentStock());
        }
        
        inventoryItemRepository.delete(item);
    }
    
    /**
     * 获取库存总价值
     */
    @Transactional(readOnly = true)
    public BigDecimal getTotalInventoryValue() {
        log.debug("获取库存总价值");
        return inventoryItemRepository.calculateTotalInventoryValue();
    }
    
    /**
     * 获取分类统计
     */
    @Transactional(readOnly = true)
    public List<Object[]> getCategoryStatistics() {
        log.debug("获取分类统计");
        return inventoryItemRepository.getCategoryStatistics();
    }
}
