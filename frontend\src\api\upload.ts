import api from './index'
import type { ApiResponse, FileUploadResponse } from '@/types/api'

/**
 * 文件上传API
 */
export const uploadApi = {
  /**
   * 上传单个文件
   */
  uploadFile: (file: File, category: string = 'general'): Promise<ApiResponse<FileUploadResponse>> => {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('category', category)
    
    return api.post('/api/upload/file', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }).then(response => response.data)
  },

  /**
   * 上传多个文件
   */
  uploadFiles: (files: File[], category: string = 'general'): Promise<ApiResponse<FileUploadResponse[]>> => {
    const formData = new FormData()
    files.forEach(file => {
      formData.append('files', file)
    })
    formData.append('category', category)
    
    return api.post('/api/upload/files', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }).then(response => response.data)
  },

  /**
   * 上传猫咪照片
   */
  uploadCatPhoto: (file: File, catId?: number): Promise<ApiResponse<FileUploadResponse>> => {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('category', 'cat-photos')
    if (catId) {
      formData.append('catId', catId.toString())
    }
    
    return api.post('/api/upload/cat-photo', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }).then(response => response.data)
  },

  /**
   * 上传健康记录附件
   */
  uploadHealthRecordAttachment: (file: File, recordId?: number): Promise<ApiResponse<FileUploadResponse>> => {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('category', 'health-records')
    if (recordId) {
      formData.append('recordId', recordId.toString())
    }
    
    return api.post('/api/upload/health-attachment', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }).then(response => response.data)
  },

  /**
   * 上传财务收据
   */
  uploadFinanceReceipt: (file: File, recordId?: number): Promise<ApiResponse<FileUploadResponse>> => {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('category', 'finance-receipts')
    if (recordId) {
      formData.append('recordId', recordId.toString())
    }
    
    return api.post('/api/upload/finance-receipt', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }).then(response => response.data)
  },

  /**
   * 上传用户头像
   */
  uploadAvatar: (file: File): Promise<ApiResponse<FileUploadResponse>> => {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('category', 'avatars')
    
    return api.post('/api/upload/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }).then(response => response.data)
  },

  /**
   * 删除文件
   */
  deleteFile: (url: string): Promise<ApiResponse<void>> => {
    return api.delete('/api/upload/file', { data: { url } }).then(response => response.data)
  },

  /**
   * 获取文件信息
   */
  getFileInfo: (url: string): Promise<ApiResponse<{
    url: string
    filename: string
    size: number
    contentType: string
    uploadTime: string
    category: string
  }>> => {
    return api.get('/api/upload/file-info', { params: { url } }).then(response => response.data)
  },

  /**
   * 获取上传配置
   */
  getUploadConfig: (): Promise<ApiResponse<{
    maxFileSize: number
    allowedTypes: string[]
    uploadPath: string
    categories: string[]
  }>> => {
    return api.get('/api/upload/config').then(response => response.data)
  }
}

// 导出单个方法以便直接使用
export const {
  uploadFile,
  uploadFiles,
  uploadCatPhoto,
  uploadHealthRecordAttachment,
  uploadFinanceReceipt,
  uploadAvatar,
  deleteFile,
  getFileInfo,
  getUploadConfig
} = uploadApi
