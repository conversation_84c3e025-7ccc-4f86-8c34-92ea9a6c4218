/* 现代浏览器兼容性样式 */

/* 响应式缩放样式 - 使用现代标准 */
html {
  font-size: 16px;
  text-size-adjust: 100%; /* 现代标准，支持所有主流浏览器 */
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

#app {
  min-height: 100vh;
  width: 100%;
}

/* 响应式断点 - 现代方法 */
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  html {
    font-size: 12px;
  }
}

@media (min-width: 1200px) {
  html {
    font-size: 18px;
  }
}

@media (min-width: 1600px) {
  html {
    font-size: 20px;
  }
}

/* 现代CSS特性 */
@supports (display: grid) {
  .grid-container {
    display: grid;
  }
}

@supports (display: flex) {
  .flex-container {
    display: flex;
  }
}

/* 可访问性改进 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --text-color: #000000;
    --background-color: #ffffff;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --text-color: #ffffff;
    --background-color: #1a1a1a;
  }
}
