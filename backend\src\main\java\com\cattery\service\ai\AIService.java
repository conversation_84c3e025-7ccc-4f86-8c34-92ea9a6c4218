package com.cattery.service.ai;

import com.cattery.dto.ai.*;
import com.cattery.entity.Cat;
import com.cattery.entity.CatHealthRecord;
import com.cattery.repository.CatRepository;
import com.cattery.repository.CatHealthRecordRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * AI服务类 - 提供猫咪识别、健康预测、行为分析等AI功能
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AIService {

    private final CatRepository catRepository;
    private final CatHealthRecordRepository healthRecordRepository;
    private final CatRecognitionService catRecognitionService;
    private final HealthPredictionService healthPredictionService;
    private final BehaviorAnalysisService behaviorAnalysisService;
    private final ImageProcessingService imageProcessingService;

    @Value("${ai.confidence.threshold:0.8}")
    private double confidenceThreshold;

    @Value("${ai.max.predictions:5}")
    private int maxPredictions;

    /**
     * 猫咪识别
     */
    public CatRecognitionResultDTO recognizeCat(MultipartFile image) {
        try {
            log.info("开始猫咪识别，图片大小: {} bytes", image.getSize());
            
            // 图片预处理
            byte[] processedImage = imageProcessingService.preprocessImage(image.getBytes());
            
            // 调用识别服务
            CatRecognitionResultDTO result = catRecognitionService.recognizeCat(processedImage);
            
            // 匹配数据库中的猫咪
            if (result.getConfidence() > confidenceThreshold) {
                List<Cat> matchedCats = findMatchingCats(result);
                result.setMatchedCats(matchedCats.stream()
                    .map(this::convertToCatSummary)
                    .collect(Collectors.toList()));
            }
            
            log.info("猫咪识别完成，置信度: {}", result.getConfidence());
            return result;
            
        } catch (IOException e) {
            log.error("图片处理失败", e);
            throw new RuntimeException("图片处理失败", e);
        } catch (Exception e) {
            log.error("猫咪识别失败", e);
            throw new RuntimeException("猫咪识别失败", e);
        }
    }

    /**
     * 品种识别
     */
    public BreedRecognitionResultDTO recognizeBreed(MultipartFile image) {
        try {
            log.info("开始品种识别，图片大小: {} bytes", image.getSize());
            
            byte[] processedImage = imageProcessingService.preprocessImage(image.getBytes());
            BreedRecognitionResultDTO result = catRecognitionService.recognizeBreed(processedImage);
            
            // 获取品种详细信息
            if (result.getConfidence() > confidenceThreshold) {
                BreedInfoDTO breedInfo = getBreedInformation(result.getBreedName());
                result.setBreedInfo(breedInfo);
            }
            
            log.info("品种识别完成，识别品种: {}, 置信度: {}", 
                result.getBreedName(), result.getConfidence());
            return result;
            
        } catch (IOException e) {
            log.error("图片处理失败", e);
            throw new RuntimeException("图片处理失败", e);
        } catch (Exception e) {
            log.error("品种识别失败", e);
            throw new RuntimeException("品种识别失败", e);
        }
    }

    /**
     * 健康预测
     */
    public HealthPredictionResultDTO predictHealth(Long catId, HealthPredictionRequestDTO request) {
        try {
            log.info("开始健康预测，猫咪ID: {}", catId);
            
            Cat cat = catRepository.findById(catId)
                .orElseThrow(() -> new RuntimeException("猫咪不存在"));
            
            // 获取历史健康记录
            List<CatHealthRecord> healthRecords = healthRecordRepository.findByCatIdOrderByRecordDateDesc(catId);
            
            // 构建预测输入数据
            HealthPredictionInputDTO inputData = buildHealthPredictionInput(cat, healthRecords, request);
            
            // 调用健康预测服务
            HealthPredictionResultDTO result = healthPredictionService.predictHealth(inputData);
            
            // 生成建议
            List<HealthRecommendationDTO> recommendations = generateHealthRecommendations(result);
            result.setRecommendations(recommendations);
            
            log.info("健康预测完成，风险等级: {}", result.getRiskLevel());
            return result;
            
        } catch (Exception e) {
            log.error("健康预测失败", e);
            throw new RuntimeException("健康预测失败", e);
        }
    }

    /**
     * 行为分析
     */
    public BehaviorAnalysisResultDTO analyzeBehavior(Long catId, BehaviorAnalysisRequestDTO request) {
        try {
            log.info("开始行为分析，猫咪ID: {}", catId);
            
            Cat cat = catRepository.findById(catId)
                .orElseThrow(() -> new RuntimeException("猫咪不存在"));
            
            // 构建行为分析输入数据
            BehaviorAnalysisInputDTO inputData = buildBehaviorAnalysisInput(cat, request);
            
            // 调用行为分析服务
            BehaviorAnalysisResultDTO result = behaviorAnalysisService.analyzeBehavior(inputData);
            
            // 生成行为建议
            List<BehaviorRecommendationDTO> recommendations = generateBehaviorRecommendations(result);
            result.setRecommendations(recommendations);
            
            log.info("行为分析完成，行为评分: {}", result.getBehaviorScore());
            return result;
            
        } catch (Exception e) {
            log.error("行为分析失败", e);
            throw new RuntimeException("行为分析失败", e);
        }
    }

    /**
     * 批量健康评估
     */
    public List<HealthAssessmentSummaryDTO> batchHealthAssessment(List<Long> catIds) {
        try {
            log.info("开始批量健康评估，猫咪数量: {}", catIds.size());
            
            List<HealthAssessmentSummaryDTO> results = new ArrayList<>();
            
            for (Long catId : catIds) {
                try {
                    Cat cat = catRepository.findById(catId).orElse(null);
                    if (cat == null) continue;
                    
                    List<CatHealthRecord> healthRecords = 
                        healthRecordRepository.findByCatIdOrderByRecordDateDesc(catId);
                    
                    HealthAssessmentSummaryDTO assessment = 
                        healthPredictionService.assessHealthSummary(cat, healthRecords);
                    
                    results.add(assessment);
                    
                } catch (Exception e) {
                    log.warn("猫咪 {} 健康评估失败: {}", catId, e.getMessage());
                }
            }
            
            log.info("批量健康评估完成，成功评估: {} 只猫咪", results.size());
            return results;
            
        } catch (Exception e) {
            log.error("批量健康评估失败", e);
            throw new RuntimeException("批量健康评估失败", e);
        }
    }

    /**
     * 获取AI服务状态
     */
    public AIServiceStatusDTO getServiceStatus() {
        AIServiceStatusDTO status = new AIServiceStatusDTO();
        
        try {
            // 检查各个AI服务的状态
            status.setCatRecognitionAvailable(catRecognitionService.isAvailable());
            status.setHealthPredictionAvailable(healthPredictionService.isAvailable());
            status.setBehaviorAnalysisAvailable(behaviorAnalysisService.isAvailable());
            status.setImageProcessingAvailable(true); // 假设图像处理服务可用
            
            // 获取服务版本信息
            status.setServiceVersion("v1.0.0");
            status.setLastUpdateTime(LocalDateTime.now());
            
            // 计算整体可用性
            boolean overallAvailable = status.getCatRecognitionAvailable() &&
                                     status.getHealthPredictionAvailable() &&
                                     status.getBehaviorAnalysisAvailable() &&
                                     status.getImageProcessingAvailable();
            status.setOverallAvailable(overallAvailable);
            
        } catch (Exception e) {
            log.error("获取AI服务状态失败", e);
            status.setOverallAvailable(false);
        }
        
        return status;
    }

    // 私有辅助方法

    private List<Cat> findMatchingCats(CatRecognitionResultDTO result) {
        // 根据识别结果的特征匹配数据库中的猫咪
        // 这里可以根据品种、颜色、性别等特征进行匹配
        return catRepository.findByBreedAndColorContaining(
            result.getBreedName(), result.getColor());
    }

    private CatSummaryDTO convertToCatSummary(Cat cat) {
        CatSummaryDTO summary = new CatSummaryDTO();
        summary.setId(cat.getId());
        summary.setName(cat.getName());
        summary.setBreedName(cat.getBreedName());
        summary.setGender(cat.getGender().toString());
        summary.setColor(cat.getColor());
        summary.setDateOfBirth(cat.getDateOfBirth());
        summary.setPhotoUrl(cat.getPrimaryPhoto());
        return summary;
    }

    private BreedInfoDTO getBreedInformation(String breedName) {
        // 获取品种详细信息
        BreedInfoDTO breedInfo = new BreedInfoDTO();
        breedInfo.setBreedName(breedName);
        
        // 这里可以从数据库或外部API获取品种信息
        switch (breedName.toLowerCase()) {
            case "british shorthair":
                breedInfo.setOrigin("英国");
                breedInfo.setCharacteristics(Arrays.asList("温和", "友善", "适应性强"));
                breedInfo.setCommonHealthIssues(Arrays.asList("肥胖", "心脏病", "肾病"));
                breedInfo.setLifeExpectancy("12-17年");
                break;
            case "persian":
                breedInfo.setOrigin("伊朗");
                breedInfo.setCharacteristics(Arrays.asList("安静", "优雅", "需要定期梳理"));
                breedInfo.setCommonHealthIssues(Arrays.asList("呼吸问题", "眼部疾病", "肾病"));
                breedInfo.setLifeExpectancy("10-17年");
                break;
            // 添加更多品种信息
        }
        
        return breedInfo;
    }

    private HealthPredictionInputDTO buildHealthPredictionInput(Cat cat, 
            List<CatHealthRecord> healthRecords, HealthPredictionRequestDTO request) {
        
        HealthPredictionInputDTO input = new HealthPredictionInputDTO();
        input.setCatId(cat.getId());
        input.setAge(calculateAge(cat.getDateOfBirth()));
        input.setBreed(cat.getBreedName());
        input.setGender(cat.getGender().toString());
        input.setWeight(cat.getCurrentWeight() != null ? cat.getCurrentWeight().doubleValue() : null);
        
        // 处理健康记录
        input.setHealthRecords(healthRecords.stream()
            .map(this::convertToHealthRecordSummary)
            .collect(Collectors.toList()));
        
        // 添加当前症状和体征
        if (request.getCurrentSymptoms() != null) {
            input.setCurrentSymptoms(request.getCurrentSymptoms());
        }
        
        return input;
    }

    private BehaviorAnalysisInputDTO buildBehaviorAnalysisInput(Cat cat, 
            BehaviorAnalysisRequestDTO request) {
        
        BehaviorAnalysisInputDTO input = new BehaviorAnalysisInputDTO();
        input.setCatId(cat.getId());
        input.setAge(calculateAge(cat.getDateOfBirth()));
        input.setBreed(cat.getBreedName());
        input.setGender(cat.getGender().toString());
        
        // 添加行为观察数据
        // 转换行为观察数据
        if (request.getBehaviorObservations() != null) {
            List<BehaviorAnalysisInputDTO.BehaviorObservationDTO> observations =
                request.getBehaviorObservations().stream()
                    .map(obs -> {
                        BehaviorAnalysisInputDTO.BehaviorObservationDTO dto =
                            new BehaviorAnalysisInputDTO.BehaviorObservationDTO();
                        dto.setBehaviorType(obs);
                        dto.setFrequency(1.0);
                        dto.setIntensity(1.0);
                        dto.setDuration(1.0);
                        return dto;
                    })
                    .collect(Collectors.toList());
            input.setBehaviorObservations(observations);
        }

        // 转换环境因素
        if (request.getEnvironmentFactors() != null) {
            Map<String, Object> envFactors = request.getEnvironmentFactors().stream()
                .collect(Collectors.toMap(
                    factor -> factor,
                    factor -> "present"
                ));
            input.setEnvironmentFactors(envFactors);
        }

        input.setObservationPeriod(request.getObservationPeriod() != null ?
            request.getObservationPeriod().toString() : "7");
        
        return input;
    }

    private List<HealthRecommendationDTO> generateHealthRecommendations(HealthPredictionResultDTO result) {
        List<HealthRecommendationDTO> recommendations = new ArrayList<>();
        
        // 根据预测结果生成建议
        if (result.getRiskLevel().equals("HIGH")) {
            recommendations.add(new HealthRecommendationDTO(
                "立即就医", "建议立即联系兽医进行详细检查", "URGENT"));
        } else if (result.getRiskLevel().equals("MEDIUM")) {
            recommendations.add(new HealthRecommendationDTO(
                "定期监测", "建议增加健康监测频率", "IMPORTANT"));
        }
        
        // 根据具体疾病风险生成建议
        for (DiseaseRiskDTO risk : result.getDiseaseRisks()) {
            if (risk.getRiskScore() > 0.7) {
                recommendations.add(new HealthRecommendationDTO(
                    risk.getDiseaseName() + "预防",
                    "建议采取预防措施，定期检查",
                    "PREVENTIVE"));
            }
        }
        
        return recommendations;
    }

    private List<BehaviorRecommendationDTO> generateBehaviorRecommendations(BehaviorAnalysisResultDTO result) {
        List<BehaviorRecommendationDTO> recommendations = new ArrayList<>();
        
        // 根据行为分析结果生成建议
        for (BehaviorPatternDTO pattern : result.getBehaviorPatterns()) {
            if (pattern.getAbnormalityScore() > 0.7) {
                recommendations.add(new BehaviorRecommendationDTO(
                    "行为干预", 
                    "检测到异常行为模式，建议咨询行为专家",
                    "BEHAVIORAL"));
            }
        }
        
        return recommendations;
    }

    private int calculateAge(LocalDate dateOfBirth) {
        return LocalDate.now().getYear() - dateOfBirth.getYear();
    }

    private HealthRecordSummaryDTO convertToHealthRecordSummary(CatHealthRecord record) {
        HealthRecordSummaryDTO summary = new HealthRecordSummaryDTO();
        summary.setRecordType(record.getRecordType().toString());
        summary.setRecordDate(record.getRecordDate());
        summary.setNotes(record.getNotes());
        return summary;
    }


}
