<template>
  <div>
    <el-card>
      <template #header>
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <span>健康记录管理</span>
          <el-button type="primary" @click="handleOpenDialog()" :disabled="!selectedCatId">添加健康记录</el-button>
        </div>
      </template>
      
      <div style="margin-bottom: 20px;">
        <el-select v-model="selectedCatId" placeholder="请选择猫咪" style="width: 200px;" @change="fetchHealthRecords">
          <el-option v-for="cat in cats" :key="cat.id" :label="cat.name" :value="cat.id" />
        </el-select>
      </div>
      
      <el-table :data="healthRecords" v-loading="loading" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="recordDate" label="记录日期" width="120" />
        <el-table-column prop="recordType" label="记录类型" width="120" />
        <el-table-column prop="notes" label="备注" />
        <el-table-column prop="createdAt" label="创建时间" width="180" />
        <el-table-column label="操作" width="180">
          <template #default="scope">
            <el-button size="small" @click="handleOpenDialog(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加/编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="猫咪" prop="catId">
          <el-select v-model="form.catId" placeholder="请选择猫咪" style="width: 100%">
            <el-option v-for="cat in cats" :key="cat.id" :label="cat.name" :value="cat.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="记录日期" prop="recordDate">
          <el-date-picker v-model="form.recordDate" type="date" placeholder="选择日期" style="width: 100%" />
        </el-form-item>
        <el-form-item label="记录类型" prop="recordType">
          <el-select v-model="form.recordType" placeholder="请选择记录类型" style="width: 100%">
            <el-option label="疫苗接种" value="疫苗接种" />
            <el-option label="驱虫" value="驱虫" />
            <el-option label="体检" value="体检" />
            <el-option label="治疗" value="治疗" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="notes">
          <el-input v-model="form.notes" type="textarea" :rows="4" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

const cats = ref([]);
const healthRecords = ref([]);
const selectedCatId = ref(null);
const loading = ref(false);
const dialogVisible = ref(false);
const form = ref({});
const formRef = ref(null);

const dialogTitle = computed(() => (form.value.id ? '编辑健康记录' : '添加健康记录'));

const rules = {
  catId: [{ required: true, message: '请选择猫咪', trigger: 'change' }],
  recordDate: [{ required: true, message: '请选择记录日期', trigger: 'change' }],
  recordType: [{ required: true, message: '请选择记录类型', trigger: 'change' }]
};

async function fetchCats() {
  try {
    const response = await fetch('/api/cats');
    if (!response.ok) throw new Error('Failed to fetch cats');
    const result = await response.json();
    cats.value = result.data || [];

    // 默认选择第一只猫咪
    if (cats.value.length > 0 && !selectedCatId.value) {
      selectedCatId.value = cats.value[0].id;
      // 获取该猫咪的健康记录
      fetchHealthRecords();
    }
  } catch (error) {
    ElMessage.error('获取猫咪列表失败');
  }
}

async function fetchHealthRecords() {
  if (!selectedCatId.value) {
    healthRecords.value = [];
    return;
  }
  loading.value = true;
  try {
    const response = await fetch(`/api/health-records?catId=${selectedCatId.value}`);
    if (!response.ok) throw new Error('Failed to fetch health records');
    const result = await response.json();
    healthRecords.value = result.data || [];
  } catch (error) {
    ElMessage.error('获取健康记录失败');
    healthRecords.value = [];
  } finally {
    loading.value = false;
  }
}

function handleOpenDialog(record = null) {
  dialogVisible.value = true;
  if (record) {
    form.value = { ...record };
  } else {
    form.value = {
      id: null,
      catId: selectedCatId.value,
      recordDate: '',
      recordType: '',
      notes: ''
    };
  }
}

async function handleSubmit() {
  if (!formRef.value) return;
  await formRef.value.validate();
  const isEdit = !!(form.value as any).id;
  const url = isEdit ? `/api/health-records/${(form.value as any).id}` : '/api/health-records';
  const method = isEdit ? 'PUT' : 'POST';

  try {
    const response = await fetch(url, {
      method,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(form.value)
    });
    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '操作失败');
    }
    ElMessage.success(isEdit ? '更新成功' : '添加成功');
    dialogVisible.value = false;
    fetchHealthRecords();
  } catch (error) {
    ElMessage.error(error instanceof Error ? error.message : '操作失败');
  }
}

async function handleDelete(id: number) {
  try {
    await ElMessageBox.confirm('确定要删除这条健康记录吗？', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });

    const response = await fetch(`/api/health-records/${id}`, {
      method: 'DELETE'
    });
    if (!response.ok) throw new Error('删除失败');

    ElMessage.success('删除成功');
    fetchHealthRecords();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
    }
  }
}

onMounted(() => {
  fetchCats();
});
</script>
