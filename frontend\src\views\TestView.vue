<template>
  <div class="test-view">
    <h1>测试页面</h1>
    <p>如果您能看到这个页面，说明Vue应用正常运行。</p>
    
    <el-card>
      <h2>系统状态</h2>
      <ul>
        <li>Vue 3: ✅ 正常</li>
        <li>Element Plus: ✅ 正常</li>
        <li>路由: ✅ 正常</li>
        <li>样式: ✅ 正常</li>
      </ul>
    </el-card>
    
    <el-card style="margin-top: 20px;">
      <h2>API连接状态</h2>
      <div class="api-status">
        <el-alert
          :title="apiStatusMessage"
          :type="apiStatusType"
          :description="apiStatusDescription"
          show-icon
          :closable="false"
        />
        <div style="margin-top: 10px;">
          <el-button @click="checkConnection" :loading="checking" size="small">
            检查后端连接
          </el-button>
          <el-button @click="switchToMockData" type="warning" size="small">
            切换到虚拟数据模式
          </el-button>
        </div>
      </div>
    </el-card>

    <el-card style="margin-top: 20px;">
      <h2>快速导航</h2>
      <el-space>
        <el-button type="primary" @click="$router.push('/login')">
          前往登录页
        </el-button>
        <el-button @click="$router.push('/about')">
          关于页面
        </el-button>
        <el-button @click="$router.push('/api-test')" type="success">
          API测试中心
        </el-button>
      </el-space>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { smartApi } from '@/utils/smartApi'

const checking = ref(false)
const apiStatus = ref({
  useMockData: false,
  connected: false,
  mode: '真实API模式'
})

const apiStatusType = computed(() => {
  if (apiStatus.value.useMockData) return 'warning'
  return apiStatus.value.connected ? 'success' : 'error'
})

const apiStatusMessage = computed(() => {
  if (apiStatus.value.useMockData) return '虚拟数据模式'
  return apiStatus.value.connected ? '后端连接正常' : '后端连接失败'
})

const apiStatusDescription = computed(() => {
  if (apiStatus.value.useMockData) {
    return '当前使用虚拟数据，所有功能可正常使用，但数据不会保存到真实数据库'
  }
  return apiStatus.value.connected
    ? '已成功连接到后端服务，可以使用完整功能'
    : '无法连接到后端服务，建议切换到虚拟数据模式继续使用'
})

const checkConnection = async () => {
  checking.value = true
  try {
    const connected = await smartApi.forceCheckConnection()
    const status = smartApi.getStatus()

    apiStatus.value = {
      useMockData: status.useMockData,
      connected,
      mode: status.mode
    }

    if (connected && !status.useMockData) {
      ElMessage.success('后端连接正常')
    } else if (status.useMockData) {
      ElMessage.warning('已切换到虚拟数据模式')
    } else {
      ElMessage.error('后端连接失败')
    }
  } catch (error) {
    ElMessage.error('连接检查失败')
  } finally {
    checking.value = false
  }
}

const switchToMockData = () => {
  smartApi.switchMode(true)
  const status = smartApi.getStatus()
  apiStatus.value = {
    useMockData: status.useMockData,
    connected: false,
    mode: status.mode
  }
  ElMessage.success('已切换到虚拟数据模式')
}

onMounted(() => {
  const status = smartApi.getStatus()
  apiStatus.value = {
    useMockData: status.useMockData,
    connected: !status.useMockData,
    mode: status.mode
  }
})
</script>

<style scoped>
.test-view {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

h1 {
  color: #409eff;
  text-align: center;
  margin-bottom: 30px;
}

h2 {
  color: #333;
  margin-bottom: 15px;
}

.api-status {
  margin: 10px 0;
}

ul {
  list-style: none;
  padding: 0;
}

li {
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

li:last-child {
  border-bottom: none;
}
</style>
