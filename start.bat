@echo off
chcp 65001 > nul
echo ========================================
echo 猫舍管理系统启动脚本
echo ========================================
echo.

echo 检查环境...
where java >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Java，请安装Java 17+
    pause
    exit /b 1
)

echo.
echo 1. 启动后端服务...
cd backend
echo 启动Spring Boot应用程序...
if exist mvnw.cmd (
    start "猫舍管理系统-后端" cmd /k "mvnw.cmd spring-boot:run"
) else (
    start "猫舍管理系统-后端" cmd /k "mvn spring-boot:run"
)

echo.
echo 2. 等待后端服务启动...
echo 请等待后端完全启动 (30-60秒)...
timeout /t 15 /nobreak > nul

echo.
echo 3. 打开前端页面...
cd ../frontend
start "" index.html

echo.
echo ========================================
echo 系统启动完成！
echo ========================================
echo.
echo 访问地址:
echo   前端页面: file:///%CD%/index.html
echo   测试页面: file:///%CD%/test.html
echo   后端API: http://localhost:8080
echo   API文档: http://localhost:8080/swagger-ui.html
echo   健康检查: http://localhost:8080/api/test/health
echo   H2数据库: http://localhost:8080/h2-console
echo.
echo 提示: 如果前端无法连接后端，系统会自动切换到模拟数据模式
echo.
pause