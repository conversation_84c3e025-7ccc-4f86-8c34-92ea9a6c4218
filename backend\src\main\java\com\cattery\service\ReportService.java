package com.cattery.service;

import com.cattery.dto.report.*;
import com.cattery.entity.*;
import com.cattery.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 报表服务类 - 提供各种数据分析和报表生成功能
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(readOnly = true)
public class ReportService {

    private final CatRepository catRepository;
    private final CatHealthRecordRepository healthRecordRepository;
    private final FinancialTransactionRepository financialTransactionRepository;
    private final CustomerRepository customerRepository;
    private final AdoptionRecordRepository adoptionRecordRepository;
    private final MatingRepository matingRepository;
    private final PregnancyRepository pregnancyRepository;
    @SuppressWarnings("unused")
    private final InventoryItemRepository inventoryItemRepository;

    /**
     * 生成猫咪统计报表
     */
    public CatStatisticsReportDTO generateCatStatisticsReport(LocalDate startDate, LocalDate endDate) {
        log.info("生成猫咪统计报表: {} - {}", startDate, endDate);
        
        CatStatisticsReportDTO report = new CatStatisticsReportDTO();
        report.setReportPeriod(formatPeriod(startDate, endDate));
        report.setGeneratedAt(LocalDateTime.now());
        
        // 基础统计
        long totalCats = catRepository.count();
        long newCatsInPeriod = catRepository.countByCreatedTimeBetween(
            startDate.atStartOfDay(), endDate.atTime(23, 59, 59));
        
        report.setTotalCats(totalCats);
        report.setNewCatsInPeriod(newCatsInPeriod);
        
        // 按性别统计
        Map<String, Long> genderDistribution = catRepository.findAll().stream()
            .collect(Collectors.groupingBy(
                cat -> cat.getGender().toString(),
                Collectors.counting()
            ));
        report.setGenderDistribution(genderDistribution);
        
        // 按品种统计
        Map<String, Long> breedDistribution = catRepository.findAll().stream()
            .collect(Collectors.groupingBy(
                Cat::getBreedName,
                Collectors.counting()
            ));
        report.setBreedDistribution(breedDistribution);
        
        // 按状态统计
        Map<String, Long> statusDistribution = catRepository.findAll().stream()
            .collect(Collectors.groupingBy(
                cat -> cat.getStatus().toString(),
                Collectors.counting()
            ));
        report.setStatusDistribution(statusDistribution);
        
        // 年龄分布
        Map<String, Long> ageDistribution = calculateAgeDistribution();
        report.setAgeDistribution(ageDistribution);
        
        // 健康状态统计
        CatStatisticsReportDTO.HealthStatisticsDTO healthStats = calculateCatHealthStatistics(startDate, endDate);
        report.setHealthStatistics(healthStats);
        
        log.info("猫咪统计报表生成完成，总猫咪数: {}", totalCats);
        return report;
    }

    /**
     * 生成财务报表
     */
    public FinancialReportDTO generateFinancialReport(LocalDate startDate, LocalDate endDate) {
        log.info("生成财务报表: {} - {}", startDate, endDate);
        
        FinancialReportDTO report = new FinancialReportDTO();
        report.setReportPeriod(formatPeriod(startDate, endDate));
        report.setGeneratedAt(LocalDateTime.now());
        
        LocalDateTime startDateTime = startDate.atStartOfDay();
        LocalDateTime endDateTime = endDate.atTime(23, 59, 59);
        
        // 收入统计
        List<FinancialTransaction> incomeTransactions = financialTransactionRepository
            .findByTransactionTypeAndTransactionDateBetween(
                FinancialTransaction.TransactionType.INCOME, startDateTime, endDateTime);
        
        BigDecimal totalIncome = incomeTransactions.stream()
            .map(FinancialTransaction::getAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        // 支出统计
        List<FinancialTransaction> expenseTransactions = financialTransactionRepository
            .findByTransactionTypeAndTransactionDateBetween(
                FinancialTransaction.TransactionType.EXPENSE, startDateTime, endDateTime);
        
        BigDecimal totalExpense = expenseTransactions.stream()
            .map(FinancialTransaction::getAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        report.setTotalIncome(totalIncome);
        report.setTotalExpense(totalExpense);
        report.setNetIncome(totalIncome.subtract(totalExpense));
        
        // 按分类统计收入
        Map<String, BigDecimal> incomeByCategory = incomeTransactions.stream()
            .collect(Collectors.groupingBy(
                transaction -> transaction.getCategory().getName(),
                Collectors.reducing(BigDecimal.ZERO, FinancialTransaction::getAmount, BigDecimal::add)
            ));
        report.setIncomeByCategory(incomeByCategory);
        
        // 按分类统计支出
        Map<String, BigDecimal> expenseByCategory = expenseTransactions.stream()
            .collect(Collectors.groupingBy(
                transaction -> transaction.getCategory().getName(),
                Collectors.reducing(BigDecimal.ZERO, FinancialTransaction::getAmount, BigDecimal::add)
            ));
        report.setExpenseByCategory(expenseByCategory);
        
        // 月度趋势
        List<MonthlyFinancialDataDTO> monthlyTrend = calculateMonthlyFinancialTrend(startDate, endDate);
        report.setMonthlyTrend(monthlyTrend);
        
        // 支付方式统计
        Map<String, Long> paymentMethodStats = incomeTransactions.stream()
            .collect(Collectors.groupingBy(
                transaction -> transaction.getPaymentMethod().toString(),
                Collectors.counting()
            ));
        report.setPaymentMethodStatistics(paymentMethodStats);
        
        log.info("财务报表生成完成，总收入: {}, 总支出: {}", totalIncome, totalExpense);
        return report;
    }

    /**
     * 生成健康报表
     */
    public HealthReportDTO generateHealthReport(LocalDate startDate, LocalDate endDate) {
        log.info("生成健康报表: {} - {}", startDate, endDate);
        
        HealthReportDTO report = new HealthReportDTO();
        report.setReportPeriod(formatPeriod(startDate, endDate));
        report.setGeneratedAt(LocalDateTime.now());
        
        LocalDateTime startDateTime = startDate.atStartOfDay();
        LocalDateTime endDateTime = endDate.atTime(23, 59, 59);
        
        // 健康记录统计
        List<CatHealthRecord> healthRecords = healthRecordRepository
            .findByRecordDateBetween(startDateTime, endDateTime);
        
        report.setTotalHealthRecords(healthRecords.size());
        
        // 按记录类型统计
        Map<String, Long> recordTypeDistribution = healthRecords.stream()
            .collect(Collectors.groupingBy(
                record -> record.getRecordType().toString(),
                Collectors.counting()
            ));
        report.setRecordTypeDistribution(recordTypeDistribution);
        
        // 疫苗接种统计
        VaccinationStatisticsDTO vaccinationStats = calculateVaccinationStatistics(startDate, endDate);
        report.setVaccinationStatistics(vaccinationStats);
        
        // 体检统计
        CheckupStatisticsDTO checkupStats = calculateCheckupStatistics(startDate, endDate);
        report.setCheckupStatistics(checkupStats);
        
        // 健康问题统计
        List<HealthIssueStatisticsDTO> healthIssueStats = calculateHealthIssueStatistics(startDate, endDate);
        report.setHealthIssueStatistics(healthIssueStats);
        
        // 兽医统计
        Map<String, Long> veterinarianStats = healthRecords.stream()
            .filter(record -> record.getVeterinarian() != null)
            .collect(Collectors.groupingBy(
                CatHealthRecord::getVeterinarian,
                Collectors.counting()
            ));
        report.setVeterinarianStatistics(veterinarianStats);
        
        log.info("健康报表生成完成，健康记录数: {}", healthRecords.size());
        return report;
    }

    /**
     * 生成繁育报表
     */
    public BreedingReportDTO generateBreedingReport(LocalDate startDate, LocalDate endDate) {
        log.info("生成繁育报表: {} - {}", startDate, endDate);
        
        BreedingReportDTO report = new BreedingReportDTO();
        report.setReportPeriod(formatPeriod(startDate, endDate));
        report.setGeneratedAt(LocalDateTime.now());
        
        LocalDateTime startDateTime = startDate.atStartOfDay();
        LocalDateTime endDateTime = endDate.atTime(23, 59, 59);
        
        // 配种统计
        List<Mating> matings = matingRepository.findByMatingDateBetween(startDateTime, endDateTime);
        report.setTotalMatings(matings.size());
        
        // 配种成功率
        long successfulMatings = matings.stream()
            .filter(mating -> mating.getResult() == Mating.MatingResult.SUCCESSFUL)
            .count();
        
        double matingSuccessRate = matings.isEmpty() ? 0.0 : 
            (double) successfulMatings / matings.size() * 100;
        report.setMatingSuccessRate(matingSuccessRate);
        
        // 怀孕统计
        List<Pregnancy> pregnancies = pregnancyRepository.findByPregnancyDateBetween(startDateTime, endDateTime);
        report.setTotalPregnancies(pregnancies.size());
        
        // 分娩统计
        long completedPregnancies = pregnancies.stream()
            .filter(pregnancy -> pregnancy.getStatus() == Pregnancy.Status.COMPLETED)
            .count();
        
        double pregnancySuccessRate = pregnancies.isEmpty() ? 0.0 :
            (double) completedPregnancies / pregnancies.size() * 100;
        report.setPregnancySuccessRate(pregnancySuccessRate);
        
        // 按品种统计繁育
        Map<String, Long> breedingByBreed = matings.stream()
            .collect(Collectors.groupingBy(
                mating -> mating.getFemaleParent().getBreedName(),
                Collectors.counting()
            ));
        report.setBreedingByBreed(breedingByBreed);
        
        // 月度繁育趋势
        List<MonthlyBreedingDataDTO> monthlyTrend = calculateMonthlyBreedingTrend(startDate, endDate);
        report.setMonthlyTrend(monthlyTrend);
        
        // 繁育效率统计
        BreedingEfficiencyDTO efficiency = calculateBreedingEfficiency(startDate, endDate);
        report.setBreedingEfficiency(efficiency);
        
        log.info("繁育报表生成完成，配种数: {}, 怀孕数: {}", matings.size(), pregnancies.size());
        return report;
    }

    /**
     * 生成客户报表
     */
    public CustomerReportDTO generateCustomerReport(LocalDate startDate, LocalDate endDate) {
        log.info("生成客户报表: {} - {}", startDate, endDate);
        
        CustomerReportDTO report = new CustomerReportDTO();
        report.setReportPeriod(formatPeriod(startDate, endDate));
        report.setGeneratedAt(LocalDateTime.now());
        
        LocalDateTime startDateTime = startDate.atStartOfDay();
        LocalDateTime endDateTime = endDate.atTime(23, 59, 59);
        
        // 客户统计
        long totalCustomers = customerRepository.count();
        long newCustomers = customerRepository.countByCreatedAtBetween(startDateTime, endDateTime);
        
        report.setTotalCustomers(totalCustomers);
        report.setNewCustomers(newCustomers);
        
        // 按类型统计客户
        Map<String, Long> customerTypeDistribution = customerRepository.findAll().stream()
            .collect(Collectors.groupingBy(
                customer -> customer.getCustomerType().toString(),
                Collectors.counting()
            ));
        report.setCustomerTypeDistribution(customerTypeDistribution);
        
        // 领养统计
        List<AdoptionRecord> adoptions = adoptionRecordRepository
            .findByAdoptionDateBetween(startDateTime, endDateTime);
        
        report.setTotalAdoptions(adoptions.size());
        
        // 领养成功率
        AdoptionStatisticsDTO adoptionStats = calculateAdoptionStatistics(startDate, endDate);
        report.setAdoptionStatistics(adoptionStats);
        
        // 客户满意度统计
        CustomerSatisfactionDTO satisfactionStats = calculateCustomerSatisfaction(startDate, endDate);
        report.setCustomerSatisfaction(satisfactionStats);
        
        // 地区分布
        Map<String, Long> regionDistribution = customerRepository.findAll().stream()
            .filter(customer -> customer.getAddress() != null)
            .collect(Collectors.groupingBy(
                customer -> extractRegion(customer.getAddress()),
                Collectors.counting()
            ));
        report.setRegionDistribution(regionDistribution);
        
        log.info("客户报表生成完成，总客户数: {}, 新客户数: {}", totalCustomers, newCustomers);
        return report;
    }

    /**
     * 生成综合仪表盘数据
     */
    public DashboardReportDTO generateDashboardReport() {
        log.info("生成综合仪表盘数据");
        
        DashboardReportDTO report = new DashboardReportDTO();
        report.setGeneratedAt(LocalDateTime.now());
        
        // 核心指标
        report.setTotalCats(catRepository.count());
        report.setTotalCustomers(customerRepository.count());
        report.setTotalHealthRecords(healthRecordRepository.count());
        
        // 本月财务数据
        LocalDate startOfMonth = LocalDate.now().withDayOfMonth(1);
        LocalDate endOfMonth = LocalDate.now();
        
        FinancialReportDTO monthlyFinancial = generateFinancialReport(startOfMonth, endOfMonth);
        report.setMonthlyIncome(monthlyFinancial.getTotalIncome());
        report.setMonthlyExpense(monthlyFinancial.getTotalExpense());
        
        // 待办事项
        List<TodoItemDTO> todoItems = generateTodoItems();
        report.setTodoItems(todoItems);
        
        // 最近活动
        List<RecentActivityDTO> recentActivities = generateRecentActivities();
        report.setRecentActivities(recentActivities);
        
        // 健康预警
        List<HealthAlertDTO> healthAlerts = generateHealthAlerts();
        report.setHealthAlerts(healthAlerts);
        
        log.info("综合仪表盘数据生成完成");
        return report;
    }

    // 私有辅助方法

    private Map<String, Long> calculateAgeDistribution() {
        List<Cat> cats = catRepository.findAll();
        Map<String, Long> ageDistribution = new HashMap<>();
        
        for (Cat cat : cats) {
            int ageInMonths = calculateAgeInMonths(cat.getDateOfBirth());
            String ageGroup = getAgeGroup(ageInMonths);
            ageDistribution.merge(ageGroup, 1L, Long::sum);
        }
        
        return ageDistribution;
    }

    private int calculateAgeInMonths(LocalDate dateOfBirth) {
        LocalDate now = LocalDate.now();
        return (int) ((now.getYear() - dateOfBirth.getYear()) * 12 + 
                     (now.getMonthValue() - dateOfBirth.getMonthValue()));
    }

    private String getAgeGroup(int ageInMonths) {
        if (ageInMonths < 6) return "幼猫 (0-6个月)";
        if (ageInMonths < 12) return "青年猫 (6-12个月)";
        if (ageInMonths < 84) return "成年猫 (1-7岁)";
        return "老年猫 (7岁以上)";
    }

    private CatStatisticsReportDTO.HealthStatisticsDTO calculateCatHealthStatistics(LocalDate startDate, LocalDate endDate) {
        // 实现健康统计计算逻辑
        CatStatisticsReportDTO.HealthStatisticsDTO stats = new CatStatisticsReportDTO.HealthStatisticsDTO();
        // ... 具体实现
        return stats;
    }

    private List<MonthlyFinancialDataDTO> calculateMonthlyFinancialTrend(LocalDate startDate, LocalDate endDate) {
        // 实现月度财务趋势计算
        List<MonthlyFinancialDataDTO> trend = new ArrayList<>();
        // ... 具体实现
        return trend;
    }

    private VaccinationStatisticsDTO calculateVaccinationStatistics(LocalDate startDate, LocalDate endDate) {
        // 实现疫苗统计计算
        VaccinationStatisticsDTO stats = new VaccinationStatisticsDTO();
        // ... 具体实现
        return stats;
    }

    private CheckupStatisticsDTO calculateCheckupStatistics(LocalDate startDate, LocalDate endDate) {
        // 实现体检统计计算
        CheckupStatisticsDTO stats = new CheckupStatisticsDTO();
        // ... 具体实现
        return stats;
    }

    private List<HealthIssueStatisticsDTO> calculateHealthIssueStatistics(LocalDate startDate, LocalDate endDate) {
        // 实现健康问题统计
        List<HealthIssueStatisticsDTO> stats = new ArrayList<>();
        // ... 具体实现
        return stats;
    }

    private List<MonthlyBreedingDataDTO> calculateMonthlyBreedingTrend(LocalDate startDate, LocalDate endDate) {
        // 实现月度繁育趋势计算
        List<MonthlyBreedingDataDTO> trend = new ArrayList<>();
        // ... 具体实现
        return trend;
    }

    private BreedingEfficiencyDTO calculateBreedingEfficiency(LocalDate startDate, LocalDate endDate) {
        // 实现繁育效率计算
        BreedingEfficiencyDTO efficiency = new BreedingEfficiencyDTO();
        // ... 具体实现
        return efficiency;
    }

    private AdoptionStatisticsDTO calculateAdoptionStatistics(LocalDate startDate, LocalDate endDate) {
        // 实现领养统计计算
        AdoptionStatisticsDTO stats = new AdoptionStatisticsDTO();
        // ... 具体实现
        return stats;
    }

    private CustomerSatisfactionDTO calculateCustomerSatisfaction(LocalDate startDate, LocalDate endDate) {
        // 实现客户满意度计算
        CustomerSatisfactionDTO satisfaction = new CustomerSatisfactionDTO();
        // ... 具体实现
        return satisfaction;
    }

    private String extractRegion(String address) {
        // 从地址中提取地区信息
        if (address == null || address.trim().isEmpty()) {
            return "未知";
        }
        
        // 简单的地区提取逻辑，可以根据实际需求优化
        String[] parts = address.split("省|市|区|县");
        if (parts.length > 0) {
            return parts[0].trim();
        }
        
        return "其他";
    }

    private List<TodoItemDTO> generateTodoItems() {
        // 生成待办事项
        List<TodoItemDTO> todoItems = new ArrayList<>();
        // ... 具体实现
        return todoItems;
    }

    private List<RecentActivityDTO> generateRecentActivities() {
        // 生成最近活动
        List<RecentActivityDTO> activities = new ArrayList<>();
        // ... 具体实现
        return activities;
    }

    private List<HealthAlertDTO> generateHealthAlerts() {
        // 生成健康预警
        List<HealthAlertDTO> alerts = new ArrayList<>();
        // ... 具体实现
        return alerts;
    }

    private String formatPeriod(LocalDate startDate, LocalDate endDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return startDate.format(formatter) + " 至 " + endDate.format(formatter);
    }
}
