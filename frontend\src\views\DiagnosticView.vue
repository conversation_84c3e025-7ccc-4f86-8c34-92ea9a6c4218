<template>
  <div class="diagnostic-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>🔧 系统诊断</span>
          <el-button type="primary" @click="runDiagnostic">
            <el-icon><Tools /></el-icon>
            运行诊断
          </el-button>
        </div>
      </template>
      
      <el-space direction="vertical" style="width: 100%" size="large">
        <!-- 系统状态概览 -->
        <el-card>
          <template #header>
            <span>系统状态概览</span>
          </template>
          
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="status-item">
                <div class="status-icon" :class="{ 'status-success': backendStatus === 'online' }">
                  <el-icon><Connection /></el-icon>
                </div>
                <div class="status-text">
                  <div class="status-title">后端服务</div>
                  <div class="status-value">{{ backendStatus === 'online' ? '在线' : '离线' }}</div>
                </div>
              </div>
            </el-col>
            
            <el-col :span="6">
              <div class="status-item">
                <div class="status-icon status-success">
                  <el-icon><Monitor /></el-icon>
                </div>
                <div class="status-text">
                  <div class="status-title">前端服务</div>
                  <div class="status-value">在线</div>
                </div>
              </div>
            </el-col>
            
            <el-col :span="6">
              <div class="status-item">
                <div class="status-icon" :class="{ 'status-success': databaseStatus === 'online' }">
                  <el-icon><Coin /></el-icon>
                </div>
                <div class="status-text">
                  <div class="status-title">数据库</div>
                  <div class="status-value">{{ databaseStatus === 'online' ? '在线' : '离线' }}</div>
                </div>
              </div>
            </el-col>
            
            <el-col :span="6">
              <div class="status-item">
                <div class="status-icon" :class="{ 'status-success': apiStatus === 'online' }">
                  <el-icon><Link /></el-icon>
                </div>
                <div class="status-text">
                  <div class="status-title">API接口</div>
                  <div class="status-value">{{ apiStatus === 'online' ? '正常' : '异常' }}</div>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>
        
        <!-- 详细检查结果 -->
        <el-card>
          <template #header>
            <span>详细检查结果</span>
          </template>
          
          <el-table :data="diagnosticResults" style="width: 100%">
            <el-table-column prop="item" label="检查项目" width="200" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'">
                  {{ scope.row.status === 'success' ? '正常' : '异常' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="message" label="详细信息" />
            <el-table-column prop="timestamp" label="检查时间" width="180" />
          </el-table>
        </el-card>
        
        <!-- 环境信息 -->
        <el-card>
          <template #header>
            <span>环境信息</span>
          </template>
          
          <el-descriptions :column="2" border>
            <el-descriptions-item label="浏览器">{{ browserInfo }}</el-descriptions-item>
            <el-descriptions-item label="操作系统">{{ osInfo }}</el-descriptions-item>
            <el-descriptions-item label="屏幕分辨率">{{ screenInfo }}</el-descriptions-item>
            <el-descriptions-item label="时区">{{ timezoneInfo }}</el-descriptions-item>
            <el-descriptions-item label="语言">{{ languageInfo }}</el-descriptions-item>
            <el-descriptions-item label="在线状态">{{ navigator.onLine ? '在线' : '离线' }}</el-descriptions-item>
          </el-descriptions>
        </el-card>
        
        <!-- 性能信息 -->
        <el-card>
          <template #header>
            <span>性能信息</span>
          </template>
          
          <el-descriptions :column="2" border>
            <el-descriptions-item label="页面加载时间">{{ loadTime }}ms</el-descriptions-item>
            <el-descriptions-item label="内存使用">{{ memoryInfo }}</el-descriptions-item>
            <el-descriptions-item label="连接类型">{{ connectionInfo }}</el-descriptions-item>
            <el-descriptions-item label="设备像素比">{{ devicePixelRatio }}</el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-space>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { testApi, catApi } from '@/api'

const backendStatus = ref('unknown')
const databaseStatus = ref('unknown')
const apiStatus = ref('unknown')
const diagnosticResults = ref([])

// 环境信息
const browserInfo = computed(() => {
  const ua = navigator.userAgent
  if (ua.includes('Chrome')) return 'Chrome'
  if (ua.includes('Firefox')) return 'Firefox'
  if (ua.includes('Safari')) return 'Safari'
  if (ua.includes('Edge')) return 'Edge'
  return 'Unknown'
})

const osInfo = computed(() => {
  const ua = navigator.userAgent
  if (ua.includes('Windows')) return 'Windows'
  if (ua.includes('Mac')) return 'macOS'
  if (ua.includes('Linux')) return 'Linux'
  if (ua.includes('Android')) return 'Android'
  if (ua.includes('iOS')) return 'iOS'
  return 'Unknown'
})

const screenInfo = computed(() => {
  return `${screen.width} x ${screen.height}`
})

const timezoneInfo = computed(() => {
  return Intl.DateTimeFormat().resolvedOptions().timeZone
})

const languageInfo = computed(() => {
  return navigator.language
})

const loadTime = computed(() => {
  return performance.timing ? 
    performance.timing.loadEventEnd - performance.timing.navigationStart : 0
})

const memoryInfo = computed(() => {
  // @ts-ignore
  const memory = (performance as any).memory
  if (memory) {
    return `${Math.round(memory.usedJSHeapSize / 1024 / 1024)}MB / ${Math.round(memory.totalJSHeapSize / 1024 / 1024)}MB`
  }
  return '不可用'
})

const connectionInfo = computed(() => {
  // @ts-ignore
  const connection = (navigator as any).connection
  return connection ? connection.effectiveType : '不可用'
})

const devicePixelRatio = computed(() => {
  return window.devicePixelRatio
})

const addDiagnosticResult = (item: string, status: string, message: string) => {
  diagnosticResults.value.push({
    item,
    status,
    message,
    timestamp: new Date().toLocaleString()
  })
}

const runDiagnostic = async () => {
  ElMessage.info('开始系统诊断...')
  diagnosticResults.value = []
  
  // 检查后端健康状态
  try {
    await testApi.health()
    backendStatus.value = 'online'
    addDiagnosticResult('后端服务', 'success', '后端服务响应正常')
  } catch (error: any) {
    backendStatus.value = 'offline'
    addDiagnosticResult('后端服务', 'error', `后端服务连接失败: ${error.message}`)
  }
  
  // 检查API接口
  try {
    await testApi.hello()
    apiStatus.value = 'online'
    addDiagnosticResult('API接口', 'success', 'API接口响应正常')
  } catch (error: any) {
    apiStatus.value = 'offline'
    addDiagnosticResult('API接口', 'error', `API接口调用失败: ${error.message}`)
  }
  
  // 检查数据库连接（通过获取猫咪列表）
  try {
    await catApi.getAll()
    databaseStatus.value = 'online'
    addDiagnosticResult('数据库连接', 'success', '数据库连接正常，可以正常查询数据')
  } catch (error: any) {
    databaseStatus.value = 'offline'
    addDiagnosticResult('数据库连接', 'error', `数据库连接异常: ${error.message}`)
  }
  
  // 检查本地存储
  try {
    localStorage.setItem('test', 'test')
    localStorage.removeItem('test')
    addDiagnosticResult('本地存储', 'success', '本地存储功能正常')
  } catch (error: any) {
    addDiagnosticResult('本地存储', 'error', `本地存储不可用: ${error.message}`)
  }
  
  // 检查网络连接
  if (navigator.onLine) {
    addDiagnosticResult('网络连接', 'success', '网络连接正常')
  } else {
    addDiagnosticResult('网络连接', 'error', '网络连接断开')
  }
  
  ElMessage.success('系统诊断完成')
}

onMounted(() => {
  runDiagnostic()
})
</script>

<style scoped>
.diagnostic-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  transition: all 0.3s;
}

.status-item:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.status-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  color: #999;
  font-size: 24px;
}

.status-icon.status-success {
  background-color: #f0f9ff;
  color: #67c23a;
}

.status-text {
  flex: 1;
}

.status-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.status-value {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}
</style>

