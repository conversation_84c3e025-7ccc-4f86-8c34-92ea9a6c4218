<template>
  <div class="file-upload">
    <el-upload
      ref="uploadRef"
      :action="uploadUrl"
      :headers="uploadHeaders"
      :data="uploadData"
      :multiple="multiple"
      :accept="accept"
      :limit="limit"
      :file-list="fileList"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-progress="handleProgress"
      :on-remove="handleRemove"
      :on-exceed="handleExceed"
      :auto-upload="autoUpload"
      :show-file-list="showFileList"
      :list-type="listType"
      :drag="drag"
      :disabled="disabled"
      class="upload-component"
    >
      <template v-if="drag">
        <div class="upload-dragger">
          <el-icon class="upload-icon"><UploadFilled /></el-icon>
          <div class="upload-text">
            <p>将文件拖到此处，或<em>点击上传</em></p>
            <p class="upload-tip">{{ uploadTip }}</p>
          </div>
        </div>
      </template>
      
      <template v-else-if="listType === 'picture-card'">
        <el-icon><Plus /></el-icon>
      </template>
      
      <template v-else>
        <el-button :icon="Upload" :loading="uploading" :disabled="disabled">
          {{ buttonText }}
        </el-button>
      </template>
    </el-upload>

    <!-- 自定义文件列表 -->
    <div v-if="!showFileList && fileList.length > 0" class="custom-file-list">
      <div
        v-for="(file, index) in fileList"
        :key="file.uid || index"
        class="file-item"
      >
        <div class="file-info">
          <el-icon class="file-icon">
            <component :is="getFileIcon(file.name)" />
          </el-icon>
          <div class="file-details">
            <div class="file-name">{{ file.name }}</div>
            <div class="file-meta">
              <span class="file-size">{{ formatFileSize(file.size) }}</span>
              <span v-if="file.status === 'uploading'" class="file-progress">
                {{ file.percentage }}%
              </span>
              <el-tag
                :type="getStatusTagType(file.status)"
                size="small"
                class="file-status"
              >
                {{ getStatusText(file.status) }}
              </el-tag>
            </div>
          </div>
        </div>
        
        <div class="file-actions">
          <el-button
            v-if="file.status === 'success' && file.url"
            type="text"
            size="small"
            @click="previewFile(file)"
          >
            预览
          </el-button>
          <el-button
            v-if="file.status === 'success' && file.url"
            type="text"
            size="small"
            @click="downloadFile(file)"
          >
            下载
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="removeFile(file)"
          >
            删除
          </el-button>
        </div>
      </div>
    </div>

    <!-- 图片预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      title="图片预览"
      width="60%"
      append-to-body
    >
      <div class="preview-container">
        <img :src="previewUrl" alt="预览图片" class="preview-image" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Upload, UploadFilled, Plus, Document, Picture, 
  VideoPlay, Headphone, Files
} from '@element-plus/icons-vue'
import { uploadFile } from '@/api/upload'
import { formatFileSize, isImageFile, downloadFile as utilDownloadFile } from '@/utils'
import { useAuthStore } from '@/stores/auth'

interface FileItem {
  uid?: string
  name: string
  size?: number
  status?: 'ready' | 'uploading' | 'success' | 'fail'
  percentage?: number
  url?: string
  raw?: File
}

interface Props {
  modelValue?: FileItem[]
  multiple?: boolean
  accept?: string
  limit?: number
  maxSize?: number // MB
  autoUpload?: boolean
  showFileList?: boolean
  listType?: 'text' | 'picture' | 'picture-card'
  drag?: boolean
  disabled?: boolean
  category?: string
  buttonText?: string
  uploadTip?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  multiple: false,
  accept: '*',
  limit: 10,
  maxSize: 10,
  autoUpload: true,
  showFileList: true,
  listType: 'text',
  drag: false,
  disabled: false,
  category: 'general',
  buttonText: '选择文件',
  uploadTip: '支持常见文件格式，单个文件不超过10MB'
})

const emit = defineEmits<{
  'update:modelValue': [files: FileItem[]]
  'success': [file: FileItem, response: any]
  'error': [file: FileItem, error: any]
  'remove': [file: FileItem]
  'exceed': [files: File[], fileList: FileItem[]]
}>()

const authStore = useAuthStore()
const uploadRef = ref()
const uploading = ref(false)
const previewVisible = ref(false)
const previewUrl = ref('')

// 计算属性
const fileList = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const uploadUrl = computed(() => '/api/upload/file')

const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${authStore.token}`
}))

const uploadData = computed(() => ({
  category: props.category
}))

// 方法
const beforeUpload = (file: File) => {
  // 检查文件大小
  if (file.size > props.maxSize * 1024 * 1024) {
    ElMessage.error(`文件大小不能超过 ${props.maxSize}MB`)
    return false
  }

  // 检查文件类型
  if (props.accept !== '*') {
    const acceptTypes = props.accept.split(',').map(type => type.trim())
    const fileType = file.type
    const fileName = file.name
    const fileExt = fileName.substring(fileName.lastIndexOf('.'))
    
    const isAccepted = acceptTypes.some(type => {
      if (type.startsWith('.')) {
        return fileExt.toLowerCase() === type.toLowerCase()
      } else if (type.includes('*')) {
        return fileType.startsWith(type.replace('*', ''))
      } else {
        return fileType === type
      }
    })
    
    if (!isAccepted) {
      ElMessage.error('文件类型不支持')
      return false
    }
  }

  uploading.value = true
  return true
}

const handleSuccess = (response: any, file: any) => {
  uploading.value = false
  
  if (response.success) {
    const fileItem: FileItem = {
      uid: file.uid,
      name: file.name,
      size: file.size,
      status: 'success',
      url: response.data.url
    }
    
    const newFileList = [...fileList.value]
    const index = newFileList.findIndex(item => item.uid === file.uid)
    if (index > -1) {
      newFileList[index] = fileItem
    } else {
      newFileList.push(fileItem)
    }
    
    fileList.value = newFileList
    emit('success', fileItem, response)
    ElMessage.success('上传成功')
  } else {
    handleError(new Error(response.message || '上传失败'), file)
  }
}

const handleError = (error: any, file: any) => {
  uploading.value = false
  
  const fileItem: FileItem = {
    uid: file.uid,
    name: file.name,
    size: file.size,
    status: 'fail'
  }
  
  emit('error', fileItem, error)
  ElMessage.error('上传失败: ' + (error.message || '未知错误'))
}

const handleProgress = (event: any, file: any) => {
  const newFileList = [...fileList.value]
  const index = newFileList.findIndex(item => item.uid === file.uid)
  if (index > -1) {
    newFileList[index] = {
      ...newFileList[index],
      status: 'uploading',
      percentage: event.percent
    }
    fileList.value = newFileList
  }
}

const handleRemove = (file: any) => {
  const newFileList = fileList.value.filter(item => item.uid !== file.uid)
  fileList.value = newFileList
  emit('remove', file)
}

const handleExceed = (files: File[]) => {
  ElMessage.warning(`最多只能上传 ${props.limit} 个文件`)
  emit('exceed', files, fileList.value)
}

const removeFile = async (file: FileItem) => {
  try {
    await ElMessageBox.confirm('确定要删除这个文件吗？', '确认删除', {
      type: 'warning'
    })
    
    const newFileList = fileList.value.filter(item => item.uid !== file.uid)
    fileList.value = newFileList
    emit('remove', file)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消删除
  }
}

const previewFile = (file: FileItem) => {
  if (file.url && isImageFile(file.name)) {
    previewUrl.value = file.url
    previewVisible.value = true
  } else if (file.url) {
    window.open(file.url, '_blank')
  }
}

const downloadFile = (file: FileItem) => {
  if (file.url) {
    utilDownloadFile(file.url, file.name)
  }
}

const getFileIcon = (fileName: string) => {
  const ext = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase()
  
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(ext)) {
    return Picture
  } else if (['mp4', 'avi', 'mov', 'wmv', 'flv'].includes(ext)) {
    return VideoPlay
  } else if (['mp3', 'wav', 'flac', 'aac'].includes(ext)) {
    return Headphone
  } else {
    return Document
  }
}

const getStatusTagType = (status?: string) => {
  const typeMap = {
    ready: 'info',
    uploading: 'warning',
    success: 'success',
    fail: 'danger'
  }
  return typeMap[status as keyof typeof typeMap] || 'info'
}

const getStatusText = (status?: string) => {
  const textMap = {
    ready: '准备中',
    uploading: '上传中',
    success: '成功',
    fail: '失败'
  }
  return textMap[status as keyof typeof textMap] || '未知'
}

// 暴露方法给父组件
defineExpose({
  submit: () => uploadRef.value?.submit(),
  clearFiles: () => uploadRef.value?.clearFiles(),
  abort: () => uploadRef.value?.abort()
})
</script>

<style scoped lang="scss">
.file-upload {
  .upload-component {
    width: 100%;
  }

  .upload-dragger {
    text-align: center;
    padding: 40px 20px;

    .upload-icon {
      font-size: 48px;
      color: #c0c4cc;
      margin-bottom: 16px;
    }

    .upload-text {
      p {
        margin: 8px 0;
        color: #606266;
      }

      em {
        color: #409eff;
        font-style: normal;
      }

      .upload-tip {
        font-size: 12px;
        color: #909399;
      }
    }
  }

  .custom-file-list {
    margin-top: 16px;

    .file-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px;
      border: 1px solid #dcdfe6;
      border-radius: 6px;
      margin-bottom: 8px;
      transition: border-color 0.2s;

      &:hover {
        border-color: #409eff;
      }

      .file-info {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 0;

        .file-icon {
          font-size: 24px;
          color: #606266;
          margin-right: 12px;
          flex-shrink: 0;
        }

        .file-details {
          flex: 1;
          min-width: 0;

          .file-name {
            font-weight: 500;
            color: #303133;
            margin-bottom: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .file-meta {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: #909399;

            .file-status {
              margin-left: auto;
            }
          }
        }
      }

      .file-actions {
        display: flex;
        gap: 8px;
        flex-shrink: 0;
      }
    }
  }

  .preview-container {
    text-align: center;

    .preview-image {
      max-width: 100%;
      max-height: 60vh;
      object-fit: contain;
    }
  }
}

:deep(.el-upload-dragger) {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  width: 100%;
  height: auto;
  text-align: center;
  background: #fafafa;
  transition: border-color 0.2s;

  &:hover {
    border-color: #409eff;
  }
}

:deep(.el-upload-list) {
  margin-top: 16px;
}
</style>
