package com.cattery.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/test")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
public class TestController {
    
    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        log.info("健康检查请求");
        
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("message", "服务运行正常");
        response.put("timestamp", LocalDateTime.now());
        response.put("service", "cattery-management-system");
        response.put("version", "1.0.0");
        
        // 系统信息
        Map<String, Object> systemInfo = new HashMap<>();
        systemInfo.put("javaVersion", System.getProperty("java.version"));
        systemInfo.put("osName", System.getProperty("os.name"));
        systemInfo.put("osVersion", System.getProperty("os.version"));
        systemInfo.put("availableProcessors", Runtime.getRuntime().availableProcessors());
        systemInfo.put("maxMemory", Runtime.getRuntime().maxMemory() / 1024 / 1024 + "MB");
        systemInfo.put("totalMemory", Runtime.getRuntime().totalMemory() / 1024 / 1024 + "MB");
        systemInfo.put("freeMemory", Runtime.getRuntime().freeMemory() / 1024 / 1024 + "MB");
        
        response.put("system", systemInfo);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * Hello接口
     */
    @GetMapping("/hello")
    public ResponseEntity<Map<String, Object>> hello(@RequestParam(defaultValue = "World") String name) {
        log.info("Hello请求: {}", name);
        
        Map<String, Object> response = new HashMap<>();
        response.put("message", "Hello, " + name + "!");
        response.put("timestamp", LocalDateTime.now());
        response.put("success", true);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * Echo接口 - 回显请求数据
     */
    @PostMapping("/echo")
    public ResponseEntity<Map<String, Object>> echo(@RequestBody Map<String, Object> data) {
        log.info("Echo请求: {}", data);
        
        Map<String, Object> response = new HashMap<>();
        response.put("echo", data);
        response.put("timestamp", LocalDateTime.now());
        response.put("success", true);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 错误测试接口
     */
    @GetMapping("/error")
    public ResponseEntity<Map<String, Object>> error() {
        log.warn("错误测试请求");
        throw new RuntimeException("这是一个测试错误");
    }
    
    /**
     * 延迟测试接口
     */
    @GetMapping("/delay")
    public ResponseEntity<Map<String, Object>> delay(@RequestParam(defaultValue = "1000") long ms) {
        log.info("延迟测试请求: {}ms", ms);
        
        try {
            Thread.sleep(ms);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("延迟测试被中断");
        }
        
        Map<String, Object> response = new HashMap<>();
        response.put("message", "延迟 " + ms + "ms 后响应");
        response.put("delay", ms);
        response.put("timestamp", LocalDateTime.now());
        response.put("success", true);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取请求头信息
     */
    @GetMapping("/headers")
    public ResponseEntity<Map<String, Object>> headers(@RequestHeader Map<String, String> headers) {
        log.info("请求头测试");
        
        Map<String, Object> response = new HashMap<>();
        response.put("headers", headers);
        response.put("timestamp", LocalDateTime.now());
        response.put("success", true);
        
        return ResponseEntity.ok(response);
    }
}


