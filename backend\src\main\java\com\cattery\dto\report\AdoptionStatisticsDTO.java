package com.cattery.dto.report;

import lombok.Data;
import java.util.Map;

/**
 * 领养统计DTO
 */
@Data
public class AdoptionStatisticsDTO {
    
    /**
     * 成功领养数
     */
    private Long successfulAdoptions;
    
    /**
     * 待领养猫咪数
     */
    private Long catsAvailableForAdoption;
    
    /**
     * 领养申请数
     */
    private Long adoptionApplications;
    
    /**
     * 领养成功率
     */
    private Double adoptionSuccessRate;
    
    /**
     * 平均领养处理时间（天）
     */
    private Double averageProcessingTime;
    
    /**
     * 按年龄段的领养分布
     */
    private Map<String, Long> adoptionsByAge;
    
    /**
     * 按品种的领养分布
     */
    private Map<String, Long> adoptionsByBreed;
    
    /**
     * 退回率
     */
    private Double returnRate;
    
    /**
     * 客户满意度评分
     */
    private Double customerSatisfactionScore;
    
    /**
     * 领养后跟踪完成率
     */
    private Double followUpCompletionRate;
}
