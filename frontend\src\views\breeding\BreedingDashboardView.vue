<template>
  <div class="breeding-dashboard">
    <div class="dashboard-header">
      <h1>繁育看板</h1>
      <p>猫咪繁育管理中心</p>
      <div class="header-actions">
        <el-button type="primary" @click="$router.push('/breeding/mating/create')">
          <el-icon><Plus /></el-icon>
          新增配种记录
        </el-button>
        <el-button @click="$router.push('/breeding/pregnancy/create')">
          <el-icon><Female /></el-icon>
          记录怀孕
        </el-button>
      </div>
    </div>

    <!-- 繁育统计 -->
    <el-row :gutter="20" class="stats-section">
      <el-col :span="6">
        <StatCard
          title="繁育中母猫"
          :value="stats.breedingFemales"
          icon="Female"
          color="#E6A23C"
          :trend="stats.breedingTrend"
        />
      </el-col>
      <el-col :span="6">
        <StatCard
          title="怀孕中"
          :value="stats.pregnantCats"
          icon="Management"
          color="#F56C6C"
          :trend="stats.pregnancyTrend"
        />
      </el-col>
      <el-col :span="6">
        <StatCard
          title="本月新生"
          :value="stats.newbornThisMonth"
          icon="House"
          color="#67C23A"
          :trend="stats.newbornTrend"
        />
      </el-col>
      <el-col :span="6">
        <StatCard
          title="配种成功率"
          :value="stats.matingSuccessRate"
          icon="TrendCharts"
          color="#409EFF"
          format="percentage"
          :trend="stats.successRateTrend"
        />
      </el-col>
    </el-row>

    <!-- 繁育时间线 -->
    <el-row :gutter="20" class="timeline-section">
      <el-col :span="24">
        <el-card class="timeline-card">
          <template #header>
            <div class="card-header">
              <span>繁育时间线</span>
              <el-button-group>
                <el-button size="small" :type="timelineFilter === 'all' ? 'primary' : ''" @click="timelineFilter = 'all'">全部</el-button>
                <el-button size="small" :type="timelineFilter === 'mating' ? 'primary' : ''" @click="timelineFilter = 'mating'">配种</el-button>
                <el-button size="small" :type="timelineFilter === 'pregnancy' ? 'primary' : ''" @click="timelineFilter = 'pregnancy'">怀孕</el-button>
                <el-button size="small" :type="timelineFilter === 'birth' ? 'primary' : ''" @click="timelineFilter = 'birth'">分娩</el-button>
              </el-button-group>
            </div>
          </template>
          
          <el-timeline class="breeding-timeline">
            <el-timeline-item
              v-for="event in filteredTimelineEvents"
              :key="event.id"
              :timestamp="formatDate(event.date)"
              :type="getEventType(event.type)"
              :icon="getEventIcon(event.type)"
            >
              <el-card class="timeline-event-card" @click="viewEventDetail(event)">
                <div class="event-content">
                  <div class="event-header">
                    <h4>{{ event.title }}</h4>
                    <el-tag :type="getEventTagType(event.status)">{{ event.status }}</el-tag>
                  </div>
                  <p class="event-description">{{ event.description }}</p>
                  <div class="event-details">
                    <span v-if="event.motherName">母猫: {{ event.motherName }}</span>
                    <span v-if="event.fatherName">公猫: {{ event.fatherName }}</span>
                    <span v-if="event.expectedDate">预产期: {{ formatDate(event.expectedDate) }}</span>
                  </div>
                </div>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-col>
    </el-row>

    <!-- 怀孕跟踪 -->
    <el-row :gutter="20" class="pregnancy-section">
      <el-col :span="12">
        <el-card class="pregnancy-card">
          <template #header>
            <div class="card-header">
              <span>怀孕跟踪</span>
              <el-button size="small" @click="$router.push('/breeding/pregnancy')">查看全部</el-button>
            </div>
          </template>
          
          <div class="pregnancy-list">
            <div
              v-for="pregnancy in pregnancies"
              :key="pregnancy.id"
              class="pregnancy-item"
              @click="$router.push(`/breeding/pregnancy/${pregnancy.id}`)"
            >
              <div class="pregnancy-info">
                <div class="cat-info">
                  <el-avatar :src="pregnancy.motherPhoto" :size="40">
                    {{ pregnancy.motherName.charAt(0) }}
                  </el-avatar>
                  <div class="cat-details">
                    <h4>{{ pregnancy.motherName }}</h4>
                    <p>{{ pregnancy.breedName }}</p>
                  </div>
                </div>
                <div class="pregnancy-progress">
                  <div class="progress-info">
                    <span>第 {{ pregnancy.gestationDays }} 天</span>
                    <span>{{ pregnancy.remainingDays }} 天后预产</span>
                  </div>
                  <el-progress
                    :percentage="pregnancy.progressPercentage"
                    :color="getProgressColor(pregnancy.progressPercentage)"
                    :stroke-width="8"
                  />
                </div>
              </div>
              <div class="pregnancy-status">
                <el-tag :type="getPregnancyStatusType(pregnancy.status)">
                  {{ pregnancy.status }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 配种计划 -->
      <el-col :span="12">
        <el-card class="mating-plan-card">
          <template #header>
            <div class="card-header">
              <span>配种计划</span>
              <el-button size="small" @click="$router.push('/breeding/mating-plans')">查看全部</el-button>
            </div>
          </template>
          
          <div class="mating-plans">
            <div
              v-for="plan in matingPlans"
              :key="plan.id"
              class="mating-plan-item"
              @click="$router.push(`/breeding/mating-plans/${plan.id}`)"
            >
              <div class="plan-header">
                <h4>{{ plan.title }}</h4>
                <el-tag :type="getPlanStatusType(plan.status)">{{ plan.status }}</el-tag>
              </div>
              <div class="plan-cats">
                <div class="cat-pair">
                  <div class="female-cat">
                    <el-avatar :src="plan.femalePhoto" :size="30">♀</el-avatar>
                    <span>{{ plan.femaleName }}</span>
                  </div>
                  <el-icon class="pair-icon"><Right /></el-icon>
                  <div class="male-cat">
                    <el-avatar :src="plan.malePhoto" :size="30">♂</el-avatar>
                    <span>{{ plan.maleName }}</span>
                  </div>
                </div>
              </div>
              <div class="plan-schedule">
                <span>计划日期: {{ formatDate(plan.plannedDate) }}</span>
                <span v-if="plan.heatCycleDay">发情第 {{ plan.heatCycleDay }} 天</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 健康提醒 -->
    <el-row :gutter="20" class="health-alerts-section">
      <el-col :span="24">
        <el-card class="health-alerts-card">
          <template #header>
            <div class="card-header">
              <span>健康提醒</span>
              <el-badge :value="healthAlerts.length" class="alert-badge" />
            </div>
          </template>
          
          <div class="health-alerts">
            <el-alert
              v-for="alert in healthAlerts"
              :key="alert.id"
              :title="alert.title"
              :description="alert.description"
              :type="alert.type"
              :closable="false"
              class="health-alert-item"
              @click="handleAlertClick(alert)"
            >
              <template #default>
                <div class="alert-content">
                  <div class="alert-info">
                    <h4>{{ alert.title }}</h4>
                    <p>{{ alert.description }}</p>
                  </div>
                  <div class="alert-action">
                    <el-button size="small" type="primary">处理</el-button>
                  </div>
                </div>
              </template>
            </el-alert>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Plus, Female, Management, House, TrendCharts, Right
} from '@element-plus/icons-vue'
import { getBreedingStats } from '@/api/breeding'
import StatCard from '@/components/StatCard.vue'
import type { BreedingStats, TimelineEvent, Pregnancy, MatingPlan, HealthAlert } from '@/types'

const loading = ref(false)
const timelineFilter = ref('all')

const stats = ref<BreedingStats>({
  breedingFemales: 0,
  pregnantCats: 0,
  newbornThisMonth: 0,
  matingSuccessRate: 0,
  breedingTrend: { value: 0, type: 'flat' },
  pregnancyTrend: { value: 0, type: 'flat' },
  newbornTrend: { value: 0, type: 'flat' },
  successRateTrend: { value: 0, type: 'flat' }
})

const timelineEvents = ref<TimelineEvent[]>([])
const pregnancies = ref<Pregnancy[]>([])
const matingPlans = ref<MatingPlan[]>([])
const healthAlerts = ref<HealthAlert[]>([])

const filteredTimelineEvents = computed(() => {
  if (timelineFilter.value === 'all') {
    return timelineEvents.value
  }
  return timelineEvents.value.filter(event => event.type === timelineFilter.value)
})

async function fetchDashboardData() {
  try {
    loading.value = true

    // 获取繁育统计数据
    const statsResponse = await getBreedingStats()
    if (statsResponse.success) {
      const statsData = statsResponse.data
      stats.value = {
        breedingFemales: statsData.activeBreeding || 0,
        pregnantCats: statsData.pregnantCats || 0,
        newbornThisMonth: statsData.newbornThisMonth || 0,
        matingSuccessRate: statsData.successRate || 0,
        breedingTrend: { value: 5, type: 'up' },
        pregnancyTrend: { value: 2, type: 'up' },
        newbornTrend: { value: 3, type: 'up' },
        successRateTrend: { value: 1, type: 'flat' }
      }
    }

    // 模拟其他数据
    timelineEvents.value = [
      {
        id: 1,
        type: 'mating',
        title: '小花配种',
        description: '小花与大橘进行配种',
        date: new Date().toISOString(),
        status: 'completed'
      },
      {
        id: 2,
        type: 'pregnancy',
        title: '小白怀孕确认',
        description: '小白怀孕已确认，预产期为下月15日',
        date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        status: 'active'
      }
    ]

    pregnancies.value = [
      {
        id: 1,
        femaleCatName: '小白',
        maleCatName: '大橘',
        matingDate: '2024-06-15',
        expectedBirthDate: '2024-08-15',
        daysRemaining: 30,
        status: 'healthy'
      }
    ]

    matingPlans.value = [
      {
        id: 1,
        femaleCatName: '小花',
        maleCatName: '大黄',
        plannedDate: '2024-08-01',
        status: 'planned',
        notes: '等待发情期'
      }
    ]

    healthAlerts.value = [
      {
        id: 1,
        catName: '小白',
        alertType: 'checkup',
        message: '需要进行产前检查',
        priority: 'high',
        dueDate: '2024-07-30'
      }
    ]

  } catch (error) {
    console.error('获取繁育数据失败:', error)
    ElMessage.error('获取繁育数据失败')
  } finally {
    loading.value = false
  }
}

function formatDate(date: string | Date) {
  return new Date(date).toLocaleDateString('zh-CN')
}

function getEventType(type: string) {
  const typeMap: Record<string, string> = {
    'mating': 'primary',
    'pregnancy': 'warning',
    'birth': 'success',
    'health': 'info'
  }
  return typeMap[type] || 'info'
}

function getEventIcon(type: string) {
  const iconMap: Record<string, string> = {
    'mating': 'Management',
    'pregnancy': 'Female',
    'birth': 'House',
    'health': 'FirstAidKit'
  }
  return iconMap[type] || 'InfoFilled'
}

function getEventTagType(status: string) {
  const typeMap: Record<string, string> = {
    'completed': 'success',
    'in_progress': 'warning',
    'planned': 'info',
    'cancelled': 'danger'
  }
  return typeMap[status] || 'info'
}

function getProgressColor(percentage: number) {
  if (percentage < 30) return '#409EFF'
  if (percentage < 70) return '#E6A23C'
  return '#F56C6C'
}

function getPregnancyStatusType(status: string) {
  const typeMap: Record<string, string> = {
    'confirmed': 'success',
    'suspected': 'warning',
    'monitoring': 'info'
  }
  return typeMap[status] || 'info'
}

function getPlanStatusType(status: string) {
  const typeMap: Record<string, string> = {
    'scheduled': 'warning',
    'completed': 'success',
    'cancelled': 'danger',
    'pending': 'info'
  }
  return typeMap[status] || 'info'
}

function viewEventDetail(event: TimelineEvent) {
  // 根据事件类型跳转到相应的详情页面
  switch (event.type) {
    case 'mating':
      this.$router.push(`/breeding/mating/${event.relatedId}`)
      break
    case 'pregnancy':
      this.$router.push(`/breeding/pregnancy/${event.relatedId}`)
      break
    case 'birth':
      this.$router.push(`/breeding/birth/${event.relatedId}`)
      break
  }
}

function handleAlertClick(alert: HealthAlert) {
  // 处理健康提醒点击
  this.$router.push(`/health/${alert.relatedId}`)
}

onMounted(() => {
  fetchDashboardData()
})
</script>

<style scoped>
.breeding-dashboard {
  padding: 20px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.dashboard-header h1 {
  margin: 0;
  color: #303133;
}

.dashboard-header p {
  margin: 5px 0 0 0;
  color: #909399;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.stats-section {
  margin-bottom: 20px;
}

.timeline-section,
.pregnancy-section,
.health-alerts-section {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.breeding-timeline {
  max-height: 400px;
  overflow-y: auto;
}

.timeline-event-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.timeline-event-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.event-content {
  padding: 10px;
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.event-header h4 {
  margin: 0;
  color: #303133;
}

.event-description {
  color: #606266;
  margin-bottom: 10px;
}

.event-details {
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: #909399;
}

.pregnancy-list,
.mating-plans {
  max-height: 400px;
  overflow-y: auto;
}

.pregnancy-item,
.mating-plan-item {
  padding: 15px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pregnancy-item:hover,
.mating-plan-item:hover {
  border-color: #409EFF;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.pregnancy-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.cat-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.cat-details h4 {
  margin: 0;
  color: #303133;
}

.cat-details p {
  margin: 0;
  color: #909399;
  font-size: 12px;
}

.pregnancy-progress {
  flex: 1;
  margin-left: 20px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 12px;
  color: #606266;
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.plan-header h4 {
  margin: 0;
  color: #303133;
}

.cat-pair {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.female-cat,
.male-cat {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
}

.pair-icon {
  color: #409EFF;
}

.plan-schedule {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
}

.health-alerts {
  max-height: 300px;
  overflow-y: auto;
}

.health-alert-item {
  margin-bottom: 10px;
  cursor: pointer;
}

.alert-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.alert-info h4 {
  margin: 0 0 5px 0;
  color: #303133;
}

.alert-info p {
  margin: 0;
  color: #606266;
}

.alert-badge {
  margin-left: 10px;
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
}
</style>
