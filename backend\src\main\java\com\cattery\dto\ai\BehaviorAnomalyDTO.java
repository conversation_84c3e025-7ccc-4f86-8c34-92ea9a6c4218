package com.cattery.dto.ai;

import lombok.Data;
import java.util.List;

/**
 * 行为异常DTO
 */
@Data
public class BehaviorAnomalyDTO {
    
    /**
     * 异常类型
     */
    private String anomalyType;
    
    /**
     * 严重程度
     */
    private String severity;
    
    /**
     * 异常评分
     */
    private Double score;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 建议
     */
    private String recommendation;
    
    /**
     * 置信度
     */
    private Double confidence;
    
    /**
     * 检测时间
     */
    private String detectedAt;
    
    /**
     * 可能原因
     */
    private String possibleCause;

    /**
     * 可能原因列表
     */
    private List<String> possibleCauses;
}
