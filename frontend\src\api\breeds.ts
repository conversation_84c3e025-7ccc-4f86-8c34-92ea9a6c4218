import { http } from '@/utils/http'
import type { CatBreed } from '@/types'

export const breedApi = {
  // 获取所有品种
  getAll(): Promise<CatBreed[]> {
    return http.get<CatBreed[]>('/breeds')
  },

  // 根据ID获取品种
  getById(id: number): Promise<CatBreed> {
    return http.get<CatBreed>(`/breeds/${id}`)
  },

  // 创建品种
  create(breed: Omit<CatBreed, 'id'>): Promise<CatBreed> {
    return http.post<CatBreed>('/breeds', breed)
  },

  // 更新品种
  update(id: number, breed: Partial<CatBreed>): Promise<CatBreed> {
    return http.put<CatBreed>(`/breeds/${id}`, breed)
  },

  // 删除品种
  delete(id: number): Promise<void> {
    return http.delete<void>(`/breeds/${id}`)
  }
}
