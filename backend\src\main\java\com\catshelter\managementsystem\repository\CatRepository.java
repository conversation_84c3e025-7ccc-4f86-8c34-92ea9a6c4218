package com.catshelter.managementsystem.repository;

import com.catshelter.managementsystem.model.Cat;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 猫咪Repository
 */
@Repository
public interface CatRepository extends JpaRepository<Cat, Long> {

    /**
     * 根据芯片号查找猫咪
     */
    Optional<Cat> findByChipId(String chipId);

    /**
     * 根据名称查找猫咪
     */
    List<Cat> findByNameContainingIgnoreCase(String name);

    /**
     * 根据品种查找猫咪
     */
    List<Cat> findByBreedIgnoreCase(String breed);

    /**
     * 根据性别查找猫咪
     */
    List<Cat> findByGender(Cat.Gender gender);

    /**
     * 根据繁育状态查找猫咪
     */
    List<Cat> findByBreedingStatus(Cat.BreedingStatus breedingStatus);

    /**
     * 根据健康状态查找猫咪
     */
    List<Cat> findByHealthStatus(Cat.HealthStatus healthStatus);

    /**
     * 查找活跃的猫咪
     */
    List<Cat> findByIsActiveTrue();

    /**
     * 分页查找活跃的猫咪
     */
    Page<Cat> findByIsActiveTrueOrderByCreatedAtDesc(Pageable pageable);

    /**
     * 复合查询
     */
    @Query("SELECT c FROM Cat c WHERE " +
           "(:name IS NULL OR LOWER(c.name) LIKE LOWER(CONCAT('%', :name, '%'))) AND " +
           "(:breed IS NULL OR LOWER(c.breed) = LOWER(:breed)) AND " +
           "(:gender IS NULL OR c.gender = :gender) AND " +
           "(:breedingStatus IS NULL OR c.breedingStatus = :breedingStatus) AND " +
           "(:healthStatus IS NULL OR c.healthStatus = :healthStatus) AND " +
           "c.isActive = true " +
           "ORDER BY c.createdAt DESC")
    Page<Cat> findCatsWithFilters(@Param("name") String name,
                                  @Param("breed") String breed,
                                  @Param("gender") Cat.Gender gender,
                                  @Param("breedingStatus") Cat.BreedingStatus breedingStatus,
                                  @Param("healthStatus") Cat.HealthStatus healthStatus,
                                  Pageable pageable);

    /**
     * 统计各种状态的猫咪数量
     */
    @Query("SELECT c.breedingStatus, COUNT(c) FROM Cat c WHERE c.isActive = true GROUP BY c.breedingStatus")
    List<Object[]> countByBreedingStatus();

    /**
     * 统计各品种的猫咪数量
     */
    @Query("SELECT c.breed, COUNT(c) FROM Cat c WHERE c.isActive = true GROUP BY c.breed")
    List<Object[]> countByBreed();

    /**
     * 统计性别分布
     */
    @Query("SELECT c.gender, COUNT(c) FROM Cat c WHERE c.isActive = true GROUP BY c.gender")
    List<Object[]> countByGender();
}