<template>
  <div class="system-dashboard">
    <div class="page-header">
      <h1>系统管理</h1>
    </div>

    <!-- 系统概览 -->
    <div class="system-overview">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon users">
                <el-icon><User /></el-icon>
              </div>
              <div class="card-info">
                <h3>用户总数</h3>
                <p class="count">{{ totalUsers }}</p>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon roles">
                <el-icon><UserFilled /></el-icon>
              </div>
              <div class="card-info">
                <h3>角色数量</h3>
                <p class="count">{{ totalRoles }}</p>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon permissions">
                <el-icon><Lock /></el-icon>
              </div>
              <div class="card-info">
                <h3>权限数量</h3>
                <p class="count">{{ totalPermissions }}</p>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon online">
                <el-icon><Connection /></el-icon>
              </div>
              <div class="card-info">
                <h3>在线用户</h3>
                <p class="count">{{ onlineUsers }}</p>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 快捷操作 -->
    <div class="quick-actions">
      <el-card>
        <template #header>
          <span>快捷操作</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-button type="primary" size="large" @click="$router.push('/system/users')" block>
              <el-icon><User /></el-icon>
              用户管理
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button type="success" size="large" @click="$router.push('/system/roles')" block>
              <el-icon><UserFilled /></el-icon>
              角色管理
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button type="warning" size="large" @click="$router.push('/system/permissions')" block>
              <el-icon><Lock /></el-icon>
              权限管理
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button type="info" size="large" @click="showSystemInfo" block>
              <el-icon><Monitor /></el-icon>
              系统信息
            </el-button>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <!-- 系统状态 -->
    <div class="system-status">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>系统状态</span>
            </template>
            <div class="status-item">
              <span>数据库状态：</span>
              <el-tag type="success">正常</el-tag>
            </div>
            <div class="status-item">
              <span>缓存状态：</span>
              <el-tag type="success">正常</el-tag>
            </div>
            <div class="status-item">
              <span>文件系统：</span>
              <el-tag type="success">正常</el-tag>
            </div>
            <div class="status-item">
              <span>邮件服务：</span>
              <el-tag type="warning">未配置</el-tag>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>最近登录用户</span>
            </template>
            <el-table :data="recentLogins" size="small">
              <el-table-column prop="username" label="用户名" />
              <el-table-column prop="loginTime" label="登录时间" />
              <el-table-column prop="ip" label="IP地址" />
            </el-table>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { User, UserFilled, Lock, Connection, Monitor } from '@element-plus/icons-vue'

// 响应式数据
const totalUsers = ref(15)
const totalRoles = ref(5)
const totalPermissions = ref(32)
const onlineUsers = ref(3)

const recentLogins = ref([
  {
    username: 'admin',
    loginTime: '2024-01-15 14:30:25',
    ip: '*************'
  },
  {
    username: 'user1',
    loginTime: '2024-01-15 13:45:12',
    ip: '*************'
  },
  {
    username: 'user2',
    loginTime: '2024-01-15 12:20:08',
    ip: '*************'
  }
])

// 方法
const loadSystemData = async () => {
  // 模拟API调用
  console.log('加载系统数据')
}

const showSystemInfo = () => {
  ElMessage.info('系统信息功能开发中')
}

onMounted(() => {
  loadSystemData()
})
</script>

<style scoped>
.system-dashboard {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.system-overview {
  margin-bottom: 20px;
}

.overview-card .card-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.card-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.card-icon.users {
  background: #409eff;
}

.card-icon.roles {
  background: #67c23a;
}

.card-icon.permissions {
  background: #e6a23c;
}

.card-icon.online {
  background: #f56c6c;
}

.card-info h3 {
  margin: 0 0 5px 0;
  font-size: 14px;
  color: #666;
}

.card-info .count {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.quick-actions {
  margin-bottom: 20px;
}

.system-status .status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.system-status .status-item:last-child {
  border-bottom: none;
}
</style>
