<template>
  <el-dialog
    :model-value="modelValue"
    :title="isEdit ? '编辑猫咪' : '添加猫咪'"
    width="600px"
    @update:model-value="$emit('update:modelValue', $event)"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="姓名" prop="name">
            <el-input v-model="form.name" placeholder="请输入猫咪姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="品种" prop="breed">
            <el-input v-model="form.breed" placeholder="请输入品种" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="性别" prop="gender">
            <el-select v-model="form.gender" placeholder="请选择性别">
              <el-option label="公" value="MALE" />
              <el-option label="母" value="FEMALE" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="出生日期" prop="birthDate">
            <el-date-picker
              v-model="form.birthDate"
              type="date"
              placeholder="请选择出生日期"
              style="width: 100%"
            />
          </el-date-picker>
        </el-form-item>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="颜色" prop="color">
            <el-input v-model="form.color" placeholder="请输入颜色" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="form.status" placeholder="请选择状态">
              <el-option label="可售" value="AVAILABLE" />
              <el-option label="预定" value="RESERVED" />
              <el-option label="已售" value="SOLD" />
              <el-option label="繁育中" value="BREEDING" />
              <el-option label="医疗中" value="MEDICAL" />
              <el-option label="退休" value="RETIRED" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="价格" prop="price">
            <el-input-number
              v-model="form.price"
              :min="0"
              :precision="2"
              placeholder="请输入价格"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="芯片号">
            <el-input v-model="form.microchipId" placeholder="请输入芯片号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="描述">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入猫咪描述"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">
        {{ isEdit ? '更新' : '创建' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { catApi } from '@/api/cat'

// Props
const props = defineProps({
  modelValue: Boolean,
  cat: Object,
  isEdit: Boolean
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const formRef = ref()
const loading = ref(false)

const form = reactive({
  name: '',
  breed: '',
  gender: '',
  birthDate: '',
  color: '',
  status: 'AVAILABLE',
  price: null,
  microchipId: '',
  description: ''
})

const rules = {
  name: [
    { required: true, message: '请输入猫咪姓名', trigger: 'blur' }
  ],
  breed: [
    { required: true, message: '请输入品种', trigger: 'blur' }
  ],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ],
  birthDate: [
    { required: true, message: '请选择出生日期', trigger: 'change' }
  ]
}

// 监听cat变化，填充表单
watch(() => props.cat, (newCat) => {
  if (newCat) {
    Object.assign(form, newCat)
  } else {
    resetForm()
  }
}, { immediate: true })

// 方法
const resetForm = () => {
  Object.assign(form, {
    name: '',
    breed: '',
    gender: '',
    birthDate: '',
    color: '',
    status: 'AVAILABLE',
    price: null,
    microchipId: '',
    description: ''
  })
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    loading.value = true
    
    const response = props.isEdit 
      ? await catApi.updateCat(props.cat.id, form)
      : await catApi.createCat(form)
    
    if (response.success) {
      ElMessage.success(props.isEdit ? '更新成功' : '创建成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    console.error('提交失败', error)
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  emit('update:modelValue', false)
  resetForm()
  formRef.value?.clearValidate()
}
</script>