import api from './index'
import type { ApiResponse, PageResponse, PaginationParams } from '@/types/api'

export interface Notification {
  id: number
  title: string
  content: string
  type: 'INFO' | 'WARNING' | 'ERROR' | 'SUCCESS'
  category: 'SYSTEM' | 'HEALTH' | 'BREEDING' | 'FINANCE' | 'CUSTOMER'
  isRead: boolean
  isImportant: boolean
  relatedId?: number
  relatedType?: string
  actionUrl?: string
  actionText?: string
  createdAt: string
  readAt?: string
}

export interface NotificationSettings {
  emailNotifications: boolean
  smsNotifications: boolean
  pushNotifications: boolean
  healthReminders: boolean
  breedingReminders: boolean
  financeAlerts: boolean
  customerInquiries: boolean
  systemUpdates: boolean
  lowStockAlerts: boolean
}

/**
 * 通知和消息API
 */
export const notificationsApi = {
  /**
   * 获取通知列表
   */
  getNotifications: (params: PaginationParams & {
    type?: string
    category?: string
    isRead?: boolean
    isImportant?: boolean
  }): Promise<ApiResponse<PageResponse<Notification>>> => {
    return api.get('/api/notifications', { params }).then(response => response.data)
  },

  /**
   * 获取未读通知数量
   */
  getUnreadCount: (): Promise<ApiResponse<number>> => {
    return api.get('/api/notifications/unread-count').then(response => response.data)
  },

  /**
   * 标记通知为已读
   */
  markAsRead: (id: number): Promise<ApiResponse<void>> => {
    return api.put(`/api/notifications/${id}/read`).then(response => response.data)
  },

  /**
   * 批量标记为已读
   */
  markAllAsRead: (ids?: number[]): Promise<ApiResponse<void>> => {
    return api.put('/api/notifications/mark-all-read', { ids }).then(response => response.data)
  },

  /**
   * 删除通知
   */
  deleteNotification: (id: number): Promise<ApiResponse<void>> => {
    return api.delete(`/api/notifications/${id}`).then(response => response.data)
  },

  /**
   * 批量删除通知
   */
  batchDeleteNotifications: (ids: number[]): Promise<ApiResponse<void>> => {
    return api.delete('/api/notifications/batch', { data: { ids } }).then(response => response.data)
  },

  /**
   * 创建通知
   */
  createNotification: (data: Partial<Notification>): Promise<ApiResponse<Notification>> => {
    return api.post('/api/notifications', data).then(response => response.data)
  },

  /**
   * 获取通知设置
   */
  getNotificationSettings: (): Promise<ApiResponse<NotificationSettings>> => {
    return api.get('/api/notifications/settings').then(response => response.data)
  },

  /**
   * 更新通知设置
   */
  updateNotificationSettings: (settings: Partial<NotificationSettings>): Promise<ApiResponse<NotificationSettings>> => {
    return api.put('/api/notifications/settings', settings).then(response => response.data)
  },

  /**
   * 获取系统提醒
   */
  getSystemReminders: (): Promise<ApiResponse<Array<{
    type: string
    title: string
    message: string
    dueDate?: string
    priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
    relatedId?: number
    relatedType?: string
  }>>> => {
    return api.get('/api/notifications/reminders').then(response => response.data)
  },

  /**
   * 获取健康提醒
   */
  getHealthReminders: (): Promise<ApiResponse<Array<{
    catId: number
    catName: string
    reminderType: 'VACCINATION' | 'CHECKUP' | 'MEDICATION' | 'FOLLOW_UP'
    dueDate: string
    description: string
    priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  }>>> => {
    return api.get('/api/notifications/health-reminders').then(response => response.data)
  },

  /**
   * 获取繁育提醒
   */
  getBreedingReminders: (): Promise<ApiResponse<Array<{
    catId: number
    catName: string
    reminderType: 'MATING' | 'PREGNANCY_CHECK' | 'BIRTH_DUE' | 'WEANING'
    dueDate: string
    description: string
    priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  }>>> => {
    return api.get('/api/notifications/breeding-reminders').then(response => response.data)
  },

  /**
   * 获取库存提醒
   */
  getInventoryReminders: (): Promise<ApiResponse<Array<{
    itemId: number
    itemName: string
    reminderType: 'LOW_STOCK' | 'OUT_OF_STOCK' | 'EXPIRING' | 'EXPIRED'
    currentStock?: number
    minimumStock?: number
    expiryDate?: string
    priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  }>>> => {
    return api.get('/api/notifications/inventory-reminders').then(response => response.data)
  },

  /**
   * 发送测试通知
   */
  sendTestNotification: (type: 'EMAIL' | 'SMS' | 'PUSH'): Promise<ApiResponse<void>> => {
    return api.post('/api/notifications/test', { type }).then(response => response.data)
  },

  /**
   * 获取通知模板
   */
  getNotificationTemplates: (): Promise<ApiResponse<Array<{
    id: number
    name: string
    type: string
    category: string
    title: string
    content: string
    variables: string[]
  }>>> => {
    return api.get('/api/notifications/templates').then(response => response.data)
  },

  /**
   * 创建通知模板
   */
  createNotificationTemplate: (data: {
    name: string
    type: string
    category: string
    title: string
    content: string
    variables?: string[]
  }): Promise<ApiResponse<any>> => {
    return api.post('/api/notifications/templates', data).then(response => response.data)
  },

  /**
   * 更新通知模板
   */
  updateNotificationTemplate: (id: number, data: {
    name?: string
    type?: string
    category?: string
    title?: string
    content?: string
    variables?: string[]
  }): Promise<ApiResponse<any>> => {
    return api.put(`/api/notifications/templates/${id}`, data).then(response => response.data)
  },

  /**
   * 删除通知模板
   */
  deleteNotificationTemplate: (id: number): Promise<ApiResponse<void>> => {
    return api.delete(`/api/notifications/templates/${id}`).then(response => response.data)
  }
}

// 导出单个方法以便直接使用
export const {
  getNotifications,
  getUnreadCount,
  markAsRead,
  markAllAsRead,
  deleteNotification,
  batchDeleteNotifications,
  createNotification,
  getNotificationSettings,
  updateNotificationSettings,
  getSystemReminders,
  getHealthReminders,
  getBreedingReminders,
  getInventoryReminders,
  sendTestNotification,
  getNotificationTemplates,
  createNotificationTemplate,
  updateNotificationTemplate,
  deleteNotificationTemplate
} = notificationsApi
