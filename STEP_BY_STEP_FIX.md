# 🔧 Node.js环境修复 - 按序号执行指南

## 📋 问题确认
根据错误信息：`Cannot find module 'D:\软件\node_modules\npm\bin\npm-prefix.js'`
- ❌ npm核心文件缺失
- ❌ 安装路径异常 (D:\软件\)
- ❌ 环境配置错误

---

## 🎯 序号1: 运行诊断检查

**执行命令：**
```cmd
check-node.cmd
```

**预期结果：**
- 确认Node.js和npm的当前状态
- 查看安装路径和环境变量

---

## 🎯 序号2: 卸载当前Node.js

**手动步骤：**
1. 按 `Win + R`，输入 `appwiz.cpl`，回车
2. 在程序列表中找到所有Node.js相关程序
3. 右键卸载以下程序：
   - Node.js
   - npm (如果单独列出)
   - 任何其他Node.js相关工具

**清理残留文件：**
```cmd
# 删除安装目录 (如果存在)
rmdir /s "D:\软件\node_modules"
rmdir /s "C:\Program Files\nodejs"
rmdir /s "C:\Program Files (x86)\nodejs"

# 清理用户目录
rmdir /s "%APPDATA%\npm"
rmdir /s "%APPDATA%\npm-cache"
```

---

## 🎯 序号3: 清理环境变量

**步骤：**
1. 右键"此电脑" → "属性" → "高级系统设置"
2. 点击"环境变量"
3. 在"系统变量"中找到"Path"，点击"编辑"
4. 删除所有包含以下内容的路径：
   - `nodejs`
   - `npm`
   - `D:\软件\node_modules`
5. 删除`NODE_PATH`变量（如果存在）
6. 点击"确定"保存

---

## 🎯 序号4: 下载新版Node.js

**自动打开下载页面：**
```cmd
install-nodejs.bat
```

**手动下载：**
1. 访问：https://nodejs.org/zh-cn/
2. 下载"LTS版本"（推荐v20.x）
3. 选择"Windows Installer (.msi)" - 64位

---

## 🎯 序号5: 安装Node.js

**安装设置：**
1. 双击下载的.msi文件
2. 选择安装路径：`C:\Program Files\nodejs\`
3. ✅ 勾选"Add to PATH environment variable"
4. ✅ 勾选"Install additional tools for Node.js"（可选）
5. 点击"Install"完成安装

---

## 🎯 序号6: 验证安装

**重启命令行后执行：**
```cmd
# 检查Node.js版本
node --version

# 检查npm版本  
npm --version

# 检查安装路径
where node
where npm
```

**预期输出：**
```
v20.x.x
10.x.x
C:\Program Files\nodejs\node.exe
C:\Program Files\nodejs\npm.cmd
```

---

## 🎯 序号7: 配置npm（可选）

**设置国内镜像源：**
```cmd
npm config set registry https://registry.npmmirror.com/
```

**验证配置：**
```cmd
npm config get registry
```

---

## 🎯 序号8: 安装项目依赖

**自动安装：**
```cmd
npm-troubleshoot.bat
```

**手动安装：**
```cmd
# 进入前端目录
cd D:\噔噔\frontend

# 清理旧依赖
rmdir /s node_modules
del package-lock.json

# 安装依赖
npm install
```

---

## 🎯 序号9: 启动开发服务器

**启动命令：**
```cmd
cd D:\噔噔\frontend
npm run dev
```

**预期结果：**
```
  VITE v4.4.5  ready in 1234 ms

  ➜  Local:   http://localhost:3000/
  ➜  Network: use --host to expose
```

---

## 🎯 序号10: 验证系统运行

**检查项目：**
1. ✅ 前端服务：http://localhost:3000
2. ✅ 后端API：http://localhost:8080
3. ✅ 登录功能正常
4. ✅ 页面跳转正常

---

## 🚨 故障排除

### 如果序号6验证失败：
```cmd
# 重新安装Node.js，确保：
# 1. 完全卸载旧版本
# 2. 清理环境变量
# 3. 安装到默认路径
# 4. 勾选PATH选项
```

### 如果序号8安装依赖失败：
```cmd
# 尝试使用yarn
npm install -g yarn
cd D:\噔噔\frontend
yarn install
yarn dev
```

### 如果序号9启动失败：
```cmd
# 检查端口占用
netstat -ano | findstr :3000

# 或修改端口
npm run dev -- --port 3001
```

---

## 🎉 成功标志

修复完成后，您应该看到：
- ✅ `node --version` 显示v20.x.x
- ✅ `npm --version` 显示10.x.x  
- ✅ `npm install` 成功执行
- ✅ `npm run dev` 启动开发服务器
- ✅ 浏览器能访问http://localhost:3000

---

## 📞 需要帮助？

如果任何步骤失败，请：
1. 记录错误信息
2. 确认执行的具体步骤
3. 检查网络连接
4. 尝试使用管理员权限

**备用方案：** 如果无法修复Node.js，可以运行`start-simple.bat`使用静态文件版本。
