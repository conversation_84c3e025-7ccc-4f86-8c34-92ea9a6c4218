@echo off
echo ========================================
echo npm 故障排除脚本
echo ========================================
echo.

echo 检查当前状态...
echo.

echo Node.js版本:
node --version 2>nul
if %errorlevel% neq 0 (
    echo [错误] Node.js 未安装或PATH配置错误
    goto :nodejs_error
)

echo npm版本:
npm --version 2>nul
if %errorlevel% neq 0 (
    echo [错误] npm 损坏，开始修复...
    goto :npm_repair
)

echo [成功] Node.js和npm都正常工作
goto :install_deps

:nodejs_error
echo.
echo Node.js安装有问题，请:
echo 1. 运行 install-nodejs.bat 重新安装
echo 2. 或者手动从 https://nodejs.org 下载安装
echo.
pause
exit /b 1

:npm_repair
echo.
echo 尝试修复npm...
echo.

echo 方法1: 重新安装npm
npm install -g npm@latest
if %errorlevel% equ 0 (
    echo [成功] npm修复完成
    goto :install_deps
)

echo 方法2: 清理缓存
npm cache clean --force
npm install -g npm@latest
if %errorlevel% equ 0 (
    echo [成功] npm修复完成
    goto :install_deps
)

echo 方法3: 使用yarn替代
echo 正在安装yarn...
npm install -g yarn
if %errorlevel% equ 0 (
    echo [成功] yarn安装完成，可以使用yarn代替npm
    echo 使用方法: yarn install, yarn dev
    goto :install_deps_yarn
)

echo [失败] 无法修复npm，建议重新安装Node.js
pause
exit /b 1

:install_deps
echo.
echo ========================================
echo 安装项目依赖
echo ========================================
echo.

cd /d "%~dp0frontend"
if not exist package.json (
    echo [错误] 未找到package.json文件
    echo 请确保在正确的项目目录中运行此脚本
    pause
    exit /b 1
)

echo 当前目录: %cd%
echo.

echo 清理旧的依赖...
if exist node_modules rmdir /s /q node_modules
if exist package-lock.json del package-lock.json

echo.
echo 安装依赖包...
npm install
if %errorlevel% equ 0 (
    echo [成功] 依赖安装完成
    goto :start_dev
) else (
    echo [失败] 依赖安装失败
    echo 尝试使用国内镜像源...
    npm config set registry https://registry.npmmirror.com/
    npm install
    if %errorlevel% equ 0 (
        echo [成功] 使用镜像源安装成功
        goto :start_dev
    ) else (
        echo [失败] 安装仍然失败，请检查网络连接
        pause
        exit /b 1
    )
)

:install_deps_yarn
echo.
echo 使用yarn安装依赖...
cd /d "%~dp0frontend"
yarn install
if %errorlevel% equ 0 (
    echo [成功] yarn安装依赖完成
    echo.
    echo 启动开发服务器: yarn dev
    yarn dev
) else (
    echo [失败] yarn安装失败
    pause
    exit /b 1
)
goto :end

:start_dev
echo.
echo ========================================
echo 启动开发服务器
echo ========================================
echo.

echo 启动前端开发服务器...
echo 访问地址: http://localhost:3000
echo 按 Ctrl+C 停止服务器
echo.

npm run dev

:end
echo.
echo 脚本执行完成
pause
