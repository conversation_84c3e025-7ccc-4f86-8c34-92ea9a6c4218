<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>猫舍管理系统 API 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, button {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .token-display {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <h1>🐱 猫舍管理系统 API 测试工具</h1>
    
    <!-- 登录测试 -->
    <div class="container">
        <h2>1. 用户登录</h2>
        <div class="form-group">
            <label for="username">用户名:</label>
            <input type="text" id="username" value="admin" placeholder="输入用户名">
        </div>
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" value="admin123" placeholder="输入密码">
        </div>
        <button onclick="login()">登录</button>
        <div id="loginResult" class="result" style="display: none;"></div>
        <div id="tokenDisplay" class="token-display" style="display: none;">
            <strong>JWT Token:</strong>
            <div id="tokenValue"></div>
        </div>
    </div>

    <!-- 获取猫咪列表 -->
    <div class="container">
        <h2>2. 获取猫咪列表</h2>
        <button onclick="getCats()">获取猫咪列表</button>
        <div id="catsResult" class="result" style="display: none;"></div>
    </div>

    <!-- 获取用户信息 -->
    <div class="container">
        <h2>3. 获取当前用户信息</h2>
        <button onclick="getCurrentUser()">获取用户信息</button>
        <div id="userResult" class="result" style="display: none;"></div>
    </div>

    <!-- 系统状态检查 -->
    <div class="container">
        <h2>4. 系统状态检查</h2>
        <button onclick="checkHealth()">检查系统健康状态</button>
        <div id="healthResult" class="result" style="display: none;"></div>
    </div>

    <!-- API 连接测试 -->
    <div class="container">
        <h2>5. API 连接测试</h2>
        <button onclick="testConnection()">测试 API 连接</button>
        <div id="connectionResult" class="result" style="display: none;"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080';
        let authToken = '';

        // 显示结果
        function showResult(elementId, data, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
            element.textContent = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
        }

        // 登录
        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            try {
                const response = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();
                
                if (response.ok) {
                    authToken = data.token;
                    showResult('loginResult', '登录成功！', true);
                    
                    // 显示 token
                    document.getElementById('tokenDisplay').style.display = 'block';
                    document.getElementById('tokenValue').textContent = authToken;
                } else {
                    showResult('loginResult', `登录失败: ${data.message || '未知错误'}`, false);
                }
            } catch (error) {
                showResult('loginResult', `网络错误: ${error.message}`, false);
            }
        }

        // 获取猫咪列表
        async function getCats() {
            if (!authToken) {
                showResult('catsResult', '请先登录获取 token', false);
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/cats`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    showResult('catsResult', data, true);
                } else {
                    showResult('catsResult', `获取失败: ${data.message || '未知错误'}`, false);
                }
            } catch (error) {
                showResult('catsResult', `网络错误: ${error.message}`, false);
            }
        }

        // 获取当前用户信息
        async function getCurrentUser() {
            if (!authToken) {
                showResult('userResult', '请先登录获取 token', false);
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/users/current`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    showResult('userResult', data, true);
                } else {
                    showResult('userResult', `获取失败: ${data.message || '未知错误'}`, false);
                }
            } catch (error) {
                showResult('userResult', `网络错误: ${error.message}`, false);
            }
        }

        // 检查系统健康状态
        async function checkHealth() {
            try {
                const response = await fetch(`${API_BASE}/test/health`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    showResult('healthResult', data, true);
                } else {
                    showResult('healthResult', `检查失败: ${data.message || '未知错误'}`, false);
                }
            } catch (error) {
                showResult('healthResult', `网络错误: ${error.message}`, false);
            }
        }

        // 测试 API 连接
        async function testConnection() {
            try {
                showResult('connectionResult', '正在测试连接...', true);

                // 测试多个端点
                const tests = [
                    { name: '健康检查', url: `${API_BASE}/test/health` },
                    { name: 'Hello 接口', url: `${API_BASE}/test/hello` },
                    { name: '系统信息', url: `${API_BASE}/health/info` }
                ];

                const results = [];

                for (const test of tests) {
                    try {
                        const response = await fetch(test.url);
                        const data = await response.json();

                        results.push({
                            name: test.name,
                            status: response.ok ? '✅ 成功' : '❌ 失败',
                            data: response.ok ? data : `HTTP ${response.status}`
                        });
                    } catch (error) {
                        results.push({
                            name: test.name,
                            status: '❌ 错误',
                            data: error.message
                        });
                    }
                }

                showResult('connectionResult', results, true);

            } catch (error) {
                showResult('connectionResult', `连接测试失败: ${error.message}`, false);
            }
        }
    </script>
</body>
</html>
