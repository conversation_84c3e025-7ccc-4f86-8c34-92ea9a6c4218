# 生产环境配置
spring:
  profiles:
    active: prod
  
  # 数据源配置（生产环境建议使用MySQL或PostgreSQL）
  datasource:
    url: *******************************************************************************************************************************
    username: ${DB_USERNAME:cattery_user}
    password: ${DB_PASSWORD:your_password}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      max-lifetime: 1200000
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: false
        use_sql_comments: false
  
  # Redis配置
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: 0
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 2000ms
  
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 100MB
  
  # 邮件配置
  mail:
    host: ${MAIL_HOST:smtp.gmail.com}
    port: ${MAIL_PORT:587}
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:your-app-password}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true

# 服务器配置
server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: /
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
  http2:
    enabled: true

# 日志配置
logging:
  level:
    root: INFO
    com.cattery: INFO
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/cattery-management.log
    max-size: 100MB
    max-history: 30

# 应用配置
app:
  # JWT配置
  jwt:
    secret: ${JWT_SECRET:your-very-long-and-secure-secret-key-for-production}
    expiration: 86400000 # 24小时
    refresh-expiration: 604800000 # 7天
  
  # 文件存储配置
  file:
    upload-path: ${FILE_UPLOAD_PATH:/var/cattery/uploads}
    max-size: 52428800 # 50MB
    allowed-types: jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx
  
  # 安全配置
  security:
    cors:
      allowed-origins: ${CORS_ALLOWED_ORIGINS:https://your-domain.com}
      allowed-methods: GET,POST,PUT,DELETE,OPTIONS
      allowed-headers: "*"
      allow-credentials: true
    
    # 密码策略
    password:
      min-length: 8
      require-uppercase: true
      require-lowercase: true
      require-digit: true
      require-special: true
  
  # 缓存配置
  cache:
    enabled: true
    default-ttl: 3600 # 1小时
    max-entries: 10000
  
  # 通知配置
  notification:
    email:
      enabled: true
      from: ${NOTIFICATION_EMAIL_FROM:<EMAIL>}
      templates-path: classpath:templates/email/
    sms:
      enabled: false
      provider: aliyun
      access-key: ${SMS_ACCESS_KEY:}
      secret-key: ${SMS_SECRET_KEY:}
  
  # 备份配置
  backup:
    enabled: true
    schedule: "0 0 2 * * ?" # 每天凌晨2点
    retention-days: 30
    storage-path: ${BACKUP_PATH:/var/cattery/backups}
  
  # 监控配置
  monitoring:
    enabled: true
    metrics:
      enabled: true
    health:
      enabled: true
    audit:
      enabled: true
      retention-days: 90

# Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
    metrics:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true

# Swagger配置
springdoc:
  api-docs:
    enabled: false # 生产环境关闭API文档
  swagger-ui:
    enabled: false
