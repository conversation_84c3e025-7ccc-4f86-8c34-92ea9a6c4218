package com.cattery.dto.ai;

import lombok.Data;

/**
 * 行为模式DTO
 */
@Data
public class BehaviorPatternDTO {
    
    /**
     * 模式类型
     */
    private String patternType;
    
    /**
     * 频率
     */
    private Double frequency;
    
    /**
     * 强度
     */
    private Double intensity;
    
    /**
     * 异常评分
     */
    private Double abnormalityScore;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 置信度
     */
    private Double confidence;
    
    /**
     * 持续时间（分钟）
     */
    private Integer duration;
    
    /**
     * 触发因素
     */
    private String trigger;
}
