package com.cattery.controller;

import com.cattery.dto.ApiResponse;
import com.cattery.entity.Cat;
import com.cattery.entity.CatHealthRecord;
import com.cattery.service.HealthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 猫咪健康管理控制器
 */
@RestController
@RequestMapping("/api/cat-health")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "猫咪健康管理", description = "猫咪健康记录、疫苗接种和体检管理")
public class CatHealthController {
    
    private final HealthService healthService;
    
    /**
     * 获取健康统计
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取健康统计", description = "获取健康相关的统计数据")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getHealthStatistics(
            @Parameter(description = "开始日期") 
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @Parameter(description = "结束日期") 
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        
        log.info("获取健康统计: startDate={}, endDate={}", startDate, endDate);
        
        // 默认时间范围：当月
        if (startDate == null) {
            startDate = LocalDateTime.now().withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
        }
        if (endDate == null) {
            endDate = LocalDateTime.now();
        }
        
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("vaccinationCount", healthService.getVaccinationCount(startDate, endDate));
        statistics.put("checkupCount", healthService.getCheckupCount(startDate, endDate));
        statistics.put("catsNeedingCheckup", healthService.getCatsNeedingCheckup().size());
        
        return ResponseEntity.ok(ApiResponse.success("获取健康统计成功", statistics));
    }
    
    /**
     * 获取猫咪的健康记录
     */
    @GetMapping("/cats/{catId}/records")
    @Operation(summary = "获取猫咪健康记录", description = "获取指定猫咪的所有健康记录")
    public ResponseEntity<ApiResponse<List<CatHealthRecord>>> getCatHealthRecords(
            @Parameter(description = "猫咪ID") @PathVariable Long catId,
            @Parameter(description = "记录类型") @RequestParam(required = false) CatHealthRecord.RecordType recordType) {
        
        log.info("获取猫咪健康记录: catId={}, recordType={}", catId, recordType);
        
        List<CatHealthRecord> records;
        if (recordType != null) {
            records = healthService.getCatHealthRecordsByType(catId, recordType);
        } else {
            records = healthService.getCatHealthRecords(catId);
        }
        
        return ResponseEntity.ok(ApiResponse.success("获取猫咪健康记录成功", records));
    }
    
    /**
     * 分页获取猫咪的健康记录
     */
    @GetMapping("/cats/{catId}/records/paged")
    @Operation(summary = "分页获取猫咪健康记录", description = "分页获取指定猫咪的健康记录")
    public ResponseEntity<ApiResponse<Page<CatHealthRecord>>> getCatHealthRecordsPaged(
            @Parameter(description = "猫咪ID") @PathVariable Long catId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        
        log.info("分页获取猫咪健康记录: catId={}, page={}, size={}", catId, page, size);
        
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "recordDate"));
        Page<CatHealthRecord> records = healthService.getCatHealthRecords(catId, pageable);
        
        return ResponseEntity.ok(ApiResponse.success("获取猫咪健康记录成功", records));
    }
    
    /**
     * 添加猫咪健康记录
     */
    @PostMapping("/cats/{catId}/records")
    @Operation(summary = "添加猫咪健康记录", description = "为指定猫咪添加新的健康记录")
    public ResponseEntity<ApiResponse<CatHealthRecord>> addCatHealthRecord(
            @Parameter(description = "猫咪ID") @PathVariable Long catId,
            @Parameter(description = "健康记录信息") @Valid @RequestBody CatHealthRecord healthRecord) {
        
        log.info("添加猫咪健康记录: catId={}, recordType={}", catId, healthRecord.getRecordType());
        
        // 设置猫咪ID
        Cat cat = new Cat();
        cat.setId(catId);
        healthRecord.setCat(cat);
        
        CatHealthRecord createdRecord = healthService.addCatHealthRecord(healthRecord);
        return ResponseEntity.ok(ApiResponse.success("添加猫咪健康记录成功", createdRecord));
    }
    
    /**
     * 更新健康记录
     */
    @PutMapping("/records/{recordId}")
    @Operation(summary = "更新健康记录", description = "更新健康记录信息")
    public ResponseEntity<ApiResponse<CatHealthRecord>> updateHealthRecord(
            @Parameter(description = "记录ID") @PathVariable Long recordId,
            @Parameter(description = "更新的健康记录信息") @Valid @RequestBody CatHealthRecord healthRecord) {
        
        log.info("更新健康记录: recordId={}", recordId);
        
        CatHealthRecord updatedRecord = healthService.updateHealthRecord(recordId, healthRecord);
        return ResponseEntity.ok(ApiResponse.success("更新健康记录成功", updatedRecord));
    }
    
    /**
     * 删除健康记录
     */
    @DeleteMapping("/records/{recordId}")
    @Operation(summary = "删除健康记录", description = "删除健康记录")
    public ResponseEntity<ApiResponse<Void>> deleteHealthRecord(
            @Parameter(description = "记录ID") @PathVariable Long recordId) {
        
        log.info("删除健康记录: recordId={}", recordId);
        
        healthService.deleteHealthRecord(recordId);
        return ResponseEntity.ok(ApiResponse.success("删除健康记录成功", null));
    }
    
    /**
     * 获取最新健康记录
     */
    @GetMapping("/cats/{catId}/latest-record")
    @Operation(summary = "获取最新健康记录", description = "获取指定猫咪的最新健康记录")
    public ResponseEntity<ApiResponse<CatHealthRecord>> getLatestHealthRecord(
            @Parameter(description = "猫咪ID") @PathVariable Long catId) {
        
        log.info("获取最新健康记录: catId={}", catId);
        
        return healthService.getLatestHealthRecord(catId)
            .map(record -> ResponseEntity.ok(ApiResponse.success("获取最新健康记录成功", record)))
            .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * 获取需要体检的猫咪
     */
    @GetMapping("/cats-needing-checkup")
    @Operation(summary = "获取需要体检的猫咪", description = "获取需要进行体检的猫咪列表")
    public ResponseEntity<ApiResponse<List<Cat>>> getCatsNeedingCheckup() {
        log.info("获取需要体检的猫咪");
        
        List<Cat> catsNeedingCheckup = healthService.getCatsNeedingCheckup();
        return ResponseEntity.ok(ApiResponse.success("获取需要体检的猫咪成功", catsNeedingCheckup));
    }
    
    /**
     * 获取疫苗接种记录
     */
    @GetMapping("/cats/{catId}/vaccinations")
    @Operation(summary = "获取疫苗接种记录", description = "获取指定猫咪的疫苗接种记录")
    public ResponseEntity<ApiResponse<List<CatHealthRecord>>> getVaccinationRecords(
            @Parameter(description = "猫咪ID") @PathVariable Long catId) {
        
        log.info("获取疫苗接种记录: catId={}", catId);
        
        List<CatHealthRecord> vaccinations = healthService.getCatHealthRecordsByType(
            catId, CatHealthRecord.RecordType.VACCINATION);
        return ResponseEntity.ok(ApiResponse.success("获取疫苗接种记录成功", vaccinations));
    }
    
    /**
     * 获取体检记录
     */
    @GetMapping("/cats/{catId}/checkups")
    @Operation(summary = "获取体检记录", description = "获取指定猫咪的体检记录")
    public ResponseEntity<ApiResponse<List<CatHealthRecord>>> getCheckupRecords(
            @Parameter(description = "猫咪ID") @PathVariable Long catId) {
        
        log.info("获取体检记录: catId={}", catId);
        
        List<CatHealthRecord> checkups = healthService.getCatHealthRecordsByType(
            catId, CatHealthRecord.RecordType.CHECKUP);
        return ResponseEntity.ok(ApiResponse.success("获取体检记录成功", checkups));
    }
}
