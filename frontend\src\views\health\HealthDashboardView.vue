<template>
  <div class="health-dashboard">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>健康管理</h1>
      <p>猫咪健康状况监控与管理</p>
    </div>

    <!-- 健康统计卡片 -->
    <el-row :gutter="20" class="health-stats">
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card healthy">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon><SuccessFilled /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ healthStats.healthyCats || 0 }}</div>
              <div class="stat-label">健康猫咪</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card sick">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon><WarningFilled /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ healthStats.sickCats || 0 }}</div>
              <div class="stat-label">生病猫咪</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card vaccine">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon><FirstAidKit /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ healthStats.vaccinesDue || 0 }}</div>
              <div class="stat-label">待接种疫苗</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card checkup">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ healthStats.checkupsDue || 0 }}</div>
              <div class="stat-label">待体检</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 健康提醒 -->
    <el-row :gutter="20" class="health-alerts">
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>健康提醒</span>
              <el-button type="text" @click="refreshReminders">
                <el-icon><Refresh /></el-icon>
              </el-button>
            </div>
          </template>
          <div class="reminder-list">
            <div 
              v-for="reminder in healthReminders" 
              :key="reminder.id"
              class="reminder-item"
              :class="reminder.priority"
            >
              <div class="reminder-icon">
                <el-icon v-if="reminder.type === 'VACCINE'"><FirstAidKit /></el-icon>
                <el-icon v-else-if="reminder.type === 'CHECKUP'"><Calendar /></el-icon>
                <el-icon v-else><Bell /></el-icon>
              </div>
              <div class="reminder-content">
                <div class="reminder-title">{{ reminder.title }}</div>
                <div class="reminder-desc">{{ reminder.description }}</div>
                <div class="reminder-time">{{ formatDate(reminder.dueDate) }}</div>
              </div>
              <div class="reminder-actions">
                <el-button size="small" type="primary" @click="handleReminder(reminder)">
                  处理
                </el-button>
              </div>
            </div>
            <div v-if="healthReminders.length === 0" class="no-data">
              暂无健康提醒
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 最近健康记录 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最近健康记录</span>
              <el-button type="text" @click="$router.push('/health-records')">
                查看全部
              </el-button>
            </div>
          </template>
          <div class="record-list">
            <div 
              v-for="record in recentRecords" 
              :key="record.id"
              class="record-item"
            >
              <div class="record-cat">
                <el-avatar :size="40" :src="record.catAvatar">
                  {{ record.catName?.charAt(0) }}
                </el-avatar>
                <div class="cat-info">
                  <div class="cat-name">{{ record.catName }}</div>
                  <div class="record-type">{{ record.recordType }}</div>
                </div>
              </div>
              <div class="record-details">
                <div class="record-desc">{{ record.description }}</div>
                <div class="record-time">{{ formatDate(record.recordDate) }}</div>
              </div>
            </div>
            <div v-if="recentRecords.length === 0" class="no-data">
              暂无健康记录
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 健康趋势图表 -->
    <el-row :gutter="20" class="health-charts">
      <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>健康趋势分析</span>
              <div class="header-actions">
                <el-date-picker
                  v-model="dateRange"
                  type="monthrange"
                  range-separator="至"
                  start-placeholder="开始月份"
                  end-placeholder="结束月份"
                  format="YYYY-MM"
                  value-format="YYYY-MM"
                  @change="refreshChart"
                  size="small"
                />
                <el-button type="text" @click="refreshChart">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </div>
            </div>
          </template>
          <div class="chart-container">
            <v-chart 
              :option="healthTrendOption" 
              :loading="chartLoading"
              style="height: 400px;"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作 -->
    <el-row :gutter="20" class="quick-actions">
      <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <el-card>
          <template #header>
            <span>快速操作</span>
          </template>
          <div class="action-buttons">
            <el-button type="primary" @click="$router.push('/health-records')">
              <el-icon><Plus /></el-icon>
              添加健康记录
            </el-button>
            <el-button type="success" @click="$router.push('/health/vaccines')">
              <el-icon><FirstAidKit /></el-icon>
              疫苗管理
            </el-button>
            <el-button type="warning" @click="$router.push('/health/reminders')">
              <el-icon><Bell /></el-icon>
              健康提醒
            </el-button>
            <el-button type="info" @click="generateHealthReport">
              <el-icon><Document /></el-icon>
              生成健康报告
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import {
  SuccessFilled,
  WarningFilled,
  FirstAidKit,
  Calendar,
  Refresh,
  Bell,
  Plus,
  Document
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { healthApi } from '@/api/health'
import dayjs from 'dayjs'

// 注册 ECharts 组件
use([
  CanvasRenderer,
  LineChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

// 响应式数据
const healthStats = reactive({
  healthyCats: 0,
  sickCats: 0,
  vaccinesDue: 0,
  checkupsDue: 0
})

const healthReminders = ref([])
const recentRecords = ref([])
const dateRange = ref<string[]>([])
const chartLoading = ref(false)
const healthTrendOption = ref({})

// 方法
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD')
}

const loadHealthStats = async () => {
  try {
    const response = await healthApi.getHealthStats()
    Object.assign(healthStats, response.data)
  } catch (error) {
    console.error('加载健康统计失败:', error)
    ElMessage.error('加载健康统计失败')
  }
}

const loadHealthReminders = async () => {
  try {
    const response = await healthApi.getHealthReminders({ limit: 10 })
    healthReminders.value = response.data
  } catch (error) {
    console.error('加载健康提醒失败:', error)
  }
}

const loadRecentRecords = async () => {
  try {
    const response = await healthApi.getRecentRecords({ limit: 10 })
    recentRecords.value = response.data
  } catch (error) {
    console.error('加载最近记录失败:', error)
  }
}

const loadHealthTrend = async () => {
  chartLoading.value = true
  try {
    const params = dateRange.value.length === 2 ? {
      startDate: dateRange.value[0] + '-01',
      endDate: dateRange.value[1] + '-31'
    } : {}
    
    const response = await healthApi.getHealthTrend(params)
    const data = response.data || []
    
    healthTrendOption.value = {
      title: {
        text: '健康记录趋势',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['健康检查', '疫苗接种', '治疗记录']
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: data.map((item: any) => item.date)
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '健康检查',
          type: 'line',
          data: data.map((item: any) => item.checkups || 0)
        },
        {
          name: '疫苗接种',
          type: 'line',
          data: data.map((item: any) => item.vaccines || 0)
        },
        {
          name: '治疗记录',
          type: 'line',
          data: data.map((item: any) => item.treatments || 0)
        }
      ]
    }
  } catch (error) {
    console.error('加载健康趋势失败:', error)
  } finally {
    chartLoading.value = false
  }
}

const refreshReminders = () => {
  loadHealthReminders()
}

const refreshChart = () => {
  loadHealthTrend()
}

const handleReminder = (reminder: any) => {
  // 处理提醒逻辑
  ElMessage.success('提醒已处理')
  loadHealthReminders()
}

const generateHealthReport = () => {
  ElMessage.info('正在生成健康报告...')
  // 生成报告逻辑
}

// 初始化
onMounted(() => {
  // 设置默认日期范围为最近6个月
  const now = new Date()
  const endMonth = now.getFullYear() + '-' + String(now.getMonth() + 1).padStart(2, '0')
  const startMonth = now.getFullYear() + '-' + String(now.getMonth() - 5).padStart(2, '0')
  dateRange.value = [startMonth, endMonth]
  
  // 加载数据
  loadHealthStats()
  loadHealthReminders()
  loadRecentRecords()
  loadHealthTrend()
})
</script>

<style scoped lang="scss">
.health-dashboard {
  padding: 20px;
  
  .page-header {
    margin-bottom: 20px;
    
    h1 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }
    
    p {
      margin: 0;
      color: #909399;
      font-size: 14px;
    }
  }
  
  .health-stats {
    margin-bottom: 20px;
    
    .stat-card {
      margin-bottom: 20px;
      
      &.healthy .stat-icon {
        background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
      }
      
      &.sick .stat-icon {
        background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);
      }
      
      &.vaccine .stat-icon {
        background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
      }
      
      &.checkup .stat-icon {
        background: linear-gradient(135deg, #e6a23c 0%, #ebb563 100%);
      }
      
      .stat-content {
        display: flex;
        align-items: center;
        
        .stat-icon {
          width: 60px;
          height: 60px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          font-size: 24px;
          color: white;
        }
        
        .stat-info {
          flex: 1;
          
          .stat-number {
            font-size: 28px;
            font-weight: 600;
            color: #303133;
            line-height: 1;
            margin-bottom: 4px;
          }
          
          .stat-label {
            font-size: 14px;
            color: #909399;
          }
        }
      }
    }
  }
  
  .health-alerts {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .reminder-list {
      max-height: 400px;
      overflow-y: auto;
      
      .reminder-item {
        display: flex;
        align-items: center;
        padding: 12px;
        border-radius: 8px;
        margin-bottom: 8px;
        border-left: 4px solid #e4e7ed;
        
        &.high {
          border-left-color: #f56c6c;
          background-color: #fef0f0;
        }
        
        &.medium {
          border-left-color: #e6a23c;
          background-color: #fdf6ec;
        }
        
        &.low {
          border-left-color: #409eff;
          background-color: #ecf5ff;
        }
        
        .reminder-icon {
          margin-right: 12px;
          font-size: 20px;
          color: #909399;
        }
        
        .reminder-content {
          flex: 1;
          
          .reminder-title {
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
          }
          
          .reminder-desc {
            font-size: 14px;
            color: #606266;
            margin-bottom: 4px;
          }
          
          .reminder-time {
            font-size: 12px;
            color: #909399;
          }
        }
        
        .reminder-actions {
          margin-left: 12px;
        }
      }
    }
    
    .record-list {
      max-height: 400px;
      overflow-y: auto;
      
      .record-item {
        display: flex;
        align-items: center;
        padding: 12px;
        border-radius: 8px;
        margin-bottom: 8px;
        background-color: #f8f9fa;
        
        .record-cat {
          display: flex;
          align-items: center;
          margin-right: 16px;
          
          .cat-info {
            margin-left: 12px;
            
            .cat-name {
              font-weight: 600;
              color: #303133;
              margin-bottom: 2px;
            }
            
            .record-type {
              font-size: 12px;
              color: #909399;
            }
          }
        }
        
        .record-details {
          flex: 1;
          
          .record-desc {
            color: #606266;
            margin-bottom: 4px;
          }
          
          .record-time {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }
  }
  
  .health-charts {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-actions {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
    
    .chart-container {
      width: 100%;
    }
  }
  
  .quick-actions {
    .action-buttons {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      
      .el-button {
        flex: 1;
        min-width: 120px;
      }
    }
  }
  
  .no-data {
    text-align: center;
    color: #909399;
    padding: 20px;
    font-size: 14px;
  }
}

@media (max-width: 768px) {
  .health-dashboard {
    padding: 12px;
    
    .health-stats {
      .stat-card {
        .stat-content {
          .stat-icon {
            width: 50px;
            height: 50px;
            font-size: 20px;
            margin-right: 12px;
          }
          
          .stat-info {
            .stat-number {
              font-size: 24px;
            }
          }
        }
      }
    }
    
    .quick-actions {
      .action-buttons {
        .el-button {
          min-width: 100px;
        }
      }
    }
  }
}
</style>
