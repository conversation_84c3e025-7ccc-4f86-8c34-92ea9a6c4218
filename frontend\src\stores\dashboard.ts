import { ref } from 'vue'
import { defineStore } from 'pinia'
import { dashboardApi } from '@/api'
import type { DashboardData } from '@/types'

export const useDashboardStore = defineStore('dashboard', () => {
  // 状态
  const stats = ref<DashboardData | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 操作
  async function fetchStats() {
    loading.value = true
    error.value = null
    try {
      stats.value = await dashboardApi.getStats()
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取统计数据失败'
    } finally {
      loading.value = false
    }
  }

  return {
    // 状态
    stats,
    loading,
    error,
    // 操作
    fetchStats
  }
})
