
<template>
  <div
    class="cat-node"
    :class="{
      'is-root': isRoot,
      'is-male': node.gender === 'MALE',
      'is-female': node.gender === 'FEMALE'
    }"
    @click="handleClick"
    @keydown.enter="handleClick"
    @keydown.space.prevent="handleClick"
    tabindex="0"
    role="button"
    :aria-label="`猫咪 ${node.name}，${node.gender === 'MALE' ? '雄性' : '雌性'}${node.breedName ? '，品种：' + node.breedName : ''}${isRoot ? '，当前选中' : ''}`"
  >
    <div class="node-photo">
      <img
        v-if="node.photoUrl"
        :src="node.photoUrl"
        :alt="`${node.name}的照片`"
        @error="handleImageError"
        loading="lazy"
      />
      <div v-else class="no-photo" aria-label="暂无照片">
        <el-icon><Picture /></el-icon>
      </div>
    </div>

    <div class="node-info">
      <div class="node-name" :title="node.name">{{ node.name }}</div>
      <div class="node-breed" v-if="node.breedName" :title="node.breedName">{{ node.breedName }}</div>
      <div class="node-birth" v-if="node.dateOfBirth" :title="`出生日期：${formatDate(node.dateOfBirth)}`">
        {{ formatDate(node.dateOfBirth) }}
      </div>
      <div class="node-age" v-if="node.dateOfBirth" :title="`年龄：${calculateAge(node.dateOfBirth)}`">
        {{ calculateAge(node.dateOfBirth) }}
      </div>
    </div>

    <div class="node-gender" :aria-label="node.gender === 'MALE' ? '雄性' : '雌性'">
      <el-icon v-if="node.gender === 'MALE'" class="male-icon" title="雄性"><Male /></el-icon>
      <el-icon v-if="node.gender === 'FEMALE'" class="female-icon" title="雌性"><Female /></el-icon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Picture, Male, Female } from '@element-plus/icons-vue'
import type { PedigreeNode } from '@/types'

interface Props {
  node: PedigreeNode
  isRoot?: boolean
}

interface Emits {
  (e: 'node-click', node: PedigreeNode): void
}

const props = withDefaults(defineProps<Props>(), {
  isRoot: false
})

const emit = defineEmits<Emits>()

const handleClick = () => {
  emit('node-click', props.node)
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  if (img) {
    // 隐藏损坏的图片，显示默认占位符
    img.style.display = 'none'
    console.warn('图片加载失败:', img.src)
  }
}

const formatDate = (dateStr?: string): string => {
  if (!dateStr) return ''
  try {
    const date = new Date(dateStr)
    // 检查日期是否有效
    if (isNaN(date.getTime())) return ''
    return date.toLocaleDateString('zh-CN')
  } catch (error) {
    console.warn('日期格式化失败:', dateStr, error)
    return ''
  }
}

const calculateAge = (dateStr?: string): string => {
  if (!dateStr) return ''
  try {
    const birthDate = new Date(dateStr)
    // 检查日期是否有效
    if (isNaN(birthDate.getTime())) return ''

    const now = new Date()
    // 确保出生日期不晚于当前日期
    if (birthDate > now) return ''

    // 更精确的年龄计算
    let years = now.getFullYear() - birthDate.getFullYear()
    let months = now.getMonth() - birthDate.getMonth()

    // 如果当前月份的日期还没到生日，则月份减1
    if (now.getDate() < birthDate.getDate()) {
      months--
    }

    // 如果月份为负数，则年份减1，月份加12
    if (months < 0) {
      years--
      months += 12
    }

    const totalMonths = years * 12 + months

    if (totalMonths < 1) {
      // 计算天数
      const diffTime = now.getTime() - birthDate.getTime()
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))
      return diffDays <= 0 ? '新生' : `${diffDays}天`
    } else if (totalMonths < 12) {
      return `${totalMonths}个月`
    } else {
      return months > 0 ? `${years}岁${months}个月` : `${years}岁`
    }
  } catch (error) {
    console.warn('年龄计算失败:', dateStr, error)
    return ''
  }
}
</script>

<style scoped>
.cat-node {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 120px;
  padding: 10px;
  border: 2px solid #ddd;
  border-radius: 8px;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  outline: none;
}

.cat-node:hover,
.cat-node:focus {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
  transform: translateY(-2px);
}

.cat-node:focus {
  outline: 2px solid #409eff;
  outline-offset: 2px;
}

.cat-node:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(64, 158, 255, 0.2);
}

.cat-node.is-root {
  border-color: #409eff;
  border-width: 3px;
  background-color: #f0f9ff;
}

.cat-node.is-male {
  border-color: #67c23a;
}

.cat-node.is-female {
  border-color: #f56c6c;
}

.node-photo {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 8px;
  border: 2px solid #eee;
}

.node-photo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
}

.node-photo img:hover {
  opacity: 0.9;
}

/* 当图片加载失败时的处理 */
.node-photo img[style*="display: none"] {
  display: none !important;
}

.no-photo {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  color: #ccc;
  font-size: 24px;
}

.no-photo .el-icon {
  font-size: 24px;
}

.node-info {
  text-align: center;
  flex: 1;
}

.node-name {
  font-weight: bold;
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100px;
}

.node-breed {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100px;
}

.node-birth {
  font-size: 11px;
  color: #999;
  margin-bottom: 2px;
  white-space: nowrap;
}

.node-age {
  font-size: 11px;
  color: #999;
  white-space: nowrap;
  font-weight: 500;
}

.node-gender {
  position: absolute;
  top: 5px;
  right: 5px;
}

.male-icon {
  color: #67c23a;
}

.female-icon {
  color: #f56c6c;
}
</style>