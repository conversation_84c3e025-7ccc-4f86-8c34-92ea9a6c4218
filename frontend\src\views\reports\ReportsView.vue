<template>
  <div class="reports-container">
    <el-page-header @back="$router.go(-1)" content="报表中心" />
    
    <div class="reports-content">
      <!-- 报表类型选择 -->
      <el-card class="report-selector-card">
        <template #header>
          <div class="card-header">
            <span>报表类型</span>
            <el-button 
              type="primary" 
              :icon="Download" 
              @click="exportReport"
              :disabled="!currentReport || loading"
            >
              导出报表
            </el-button>
          </div>
        </template>
        
        <div class="report-selector">
          <el-radio-group v-model="selectedReportType" @change="handleReportTypeChange">
            <el-radio-button 
              v-for="type in reportTypes" 
              :key="type.value"
              :label="type.value"
            >
              <el-icon>
                <component :is="type.icon" />
              </el-icon>
              {{ type.label }}
            </el-radio-button>
          </el-radio-group>
        </div>
        
        <!-- 时间范围选择 -->
        <div class="date-range-selector">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateRangeChange"
          />
          
          <el-button-group class="quick-date-buttons">
            <el-button 
              v-for="quick in quickDateRanges" 
              :key="quick.value"
              size="small"
              @click="setQuickDateRange(quick.value)"
            >
              {{ quick.label }}
            </el-button>
          </el-button-group>
        </div>
        
        <div class="generate-button">
          <el-button 
            type="primary" 
            size="large"
            :loading="loading"
            @click="generateReport"
            :disabled="!selectedReportType || !dateRange"
          >
            生成报表
          </el-button>
        </div>
      </el-card>

      <!-- 报表内容 -->
      <div v-if="currentReport" class="report-content">
        <!-- 猫咪统计报表 -->
        <cat-statistics-report 
          v-if="selectedReportType === 'cat'"
          :report-data="currentReport"
          @export="exportReport"
        />
        
        <!-- 财务报表 -->
        <financial-report 
          v-else-if="selectedReportType === 'financial'"
          :report-data="currentReport"
          @export="exportReport"
        />
        
        <!-- 健康报表 -->
        <health-report 
          v-else-if="selectedReportType === 'health'"
          :report-data="currentReport"
          @export="exportReport"
        />
        
        <!-- 繁育报表 -->
        <breeding-report 
          v-else-if="selectedReportType === 'breeding'"
          :report-data="currentReport"
          @export="exportReport"
        />
        
        <!-- 客户报表 -->
        <customer-report 
          v-else-if="selectedReportType === 'customer'"
          :report-data="currentReport"
          @export="exportReport"
        />
        
        <!-- 综合仪表盘 -->
        <dashboard-report 
          v-else-if="selectedReportType === 'dashboard'"
          :report-data="currentReport"
          @export="exportReport"
        />
      </div>

      <!-- 空状态 -->
      <el-empty 
        v-else-if="!loading"
        description="请选择报表类型并设置时间范围，然后点击生成报表"
        :image-size="200"
      />

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="8" animated />
      </div>
    </div>

    <!-- 报表历史 -->
    <el-card class="report-history-card">
      <template #header>
        <span>报表历史</span>
      </template>
      
      <el-table :data="reportHistory" style="width: 100%">
        <el-table-column prop="reportType" label="报表类型" width="120">
          <template #default="scope">
            <el-tag>{{ getReportTypeLabel(scope.row.reportType) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="reportPeriod" label="报表周期" width="200" />
        <el-table-column prop="generatedAt" label="生成时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.generatedAt) }}
          </template>
        </el-table-column>
        <el-table-column prop="generatedBy" label="生成人" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag 
              :type="scope.row.status === 'completed' ? 'success' : 'warning'"
            >
              {{ scope.row.status === 'completed' ? '已完成' : '生成中' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button 
              type="text" 
              size="small"
              @click="viewReport(scope.row)"
              :disabled="scope.row.status !== 'completed'"
            >
              查看
            </el-button>
            <el-button 
              type="text" 
              size="small"
              @click="downloadReport(scope.row)"
              :disabled="scope.row.status !== 'completed'"
            >
              下载
            </el-button>
            <el-button 
              type="text" 
              size="small"
              @click="deleteReport(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <el-pagination
        v-model:current-page="historyPagination.current"
        v-model:page-size="historyPagination.size"
        :total="historyPagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleHistorySizeChange"
        @current-change="handleHistoryCurrentChange"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Download, 
  DataAnalysis, 
  Money, 
  TrendCharts, 
  User, 
  Document,
  Dashboard
} from '@element-plus/icons-vue'
import { reportApi } from '@/api/reports'
import { formatDateTime } from '@/utils/date'
import type { ReportType, ReportData, ReportHistoryItem } from '@/types/reports'

// 导入报表组件
import CatStatisticsReport from '@/components/reports/CatStatisticsReport.vue'
import FinancialReport from '@/components/reports/FinancialReport.vue'
import HealthReport from '@/components/reports/HealthReport.vue'
import BreedingReport from '@/components/reports/BreedingReport.vue'
import CustomerReport from '@/components/reports/CustomerReport.vue'
import DashboardReport from '@/components/reports/DashboardReport.vue'

// 响应式数据
const loading = ref(false)
const selectedReportType = ref<ReportType>('')
const dateRange = ref<[string, string]>(['', ''])
const currentReport = ref<ReportData | null>(null)
const reportHistory = ref<ReportHistoryItem[]>([])

// 分页数据
const historyPagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 报表类型配置
const reportTypes = [
  {
    value: 'cat' as ReportType,
    label: '猫咪统计',
    icon: 'DataAnalysis'
  },
  {
    value: 'financial' as ReportType,
    label: '财务报表',
    icon: 'Money'
  },
  {
    value: 'health' as ReportType,
    label: '健康报表',
    icon: 'TrendCharts'
  },
  {
    value: 'breeding' as ReportType,
    label: '繁育报表',
    icon: 'Document'
  },
  {
    value: 'customer' as ReportType,
    label: '客户报表',
    icon: 'User'
  },
  {
    value: 'dashboard' as ReportType,
    label: '综合仪表盘',
    icon: 'Dashboard'
  }
]

// 快速日期范围
const quickDateRanges = [
  { label: '今天', value: 'today' },
  { label: '昨天', value: 'yesterday' },
  { label: '本周', value: 'thisWeek' },
  { label: '本月', value: 'thisMonth' },
  { label: '上月', value: 'lastMonth' },
  { label: '本季度', value: 'thisQuarter' },
  { label: '本年', value: 'thisYear' }
]

// 计算属性
const canGenerateReport = computed(() => {
  return selectedReportType.value && dateRange.value[0] && dateRange.value[1]
})

// 方法
const handleReportTypeChange = (type: ReportType) => {
  currentReport.value = null
  if (type === 'dashboard') {
    // 仪表盘不需要日期范围
    dateRange.value = ['', '']
  }
}

const handleDateRangeChange = (range: [string, string]) => {
  if (range && range[0] && range[1]) {
    currentReport.value = null
  }
}

const setQuickDateRange = (quickType: string) => {
  const today = new Date()
  const formatDate = (date: Date) => date.toISOString().split('T')[0]
  
  switch (quickType) {
    case 'today':
      dateRange.value = [formatDate(today), formatDate(today)]
      break
    case 'yesterday':
      const yesterday = new Date(today)
      yesterday.setDate(yesterday.getDate() - 1)
      dateRange.value = [formatDate(yesterday), formatDate(yesterday)]
      break
    case 'thisWeek':
      const startOfWeek = new Date(today)
      startOfWeek.setDate(today.getDate() - today.getDay())
      dateRange.value = [formatDate(startOfWeek), formatDate(today)]
      break
    case 'thisMonth':
      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)
      dateRange.value = [formatDate(startOfMonth), formatDate(today)]
      break
    case 'lastMonth':
      const startOfLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1)
      const endOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0)
      dateRange.value = [formatDate(startOfLastMonth), formatDate(endOfLastMonth)]
      break
    case 'thisQuarter':
      const quarterStart = new Date(today.getFullYear(), Math.floor(today.getMonth() / 3) * 3, 1)
      dateRange.value = [formatDate(quarterStart), formatDate(today)]
      break
    case 'thisYear':
      const yearStart = new Date(today.getFullYear(), 0, 1)
      dateRange.value = [formatDate(yearStart), formatDate(today)]
      break
  }
  
  currentReport.value = null
}

const generateReport = async () => {
  if (!canGenerateReport.value && selectedReportType.value !== 'dashboard') {
    ElMessage.warning('请选择报表类型和时间范围')
    return
  }

  loading.value = true
  try {
    let reportData: ReportData
    
    switch (selectedReportType.value) {
      case 'cat':
        reportData = await reportApi.generateCatStatistics(dateRange.value[0], dateRange.value[1])
        break
      case 'financial':
        reportData = await reportApi.generateFinancialReport(dateRange.value[0], dateRange.value[1])
        break
      case 'health':
        reportData = await reportApi.generateHealthReport(dateRange.value[0], dateRange.value[1])
        break
      case 'breeding':
        reportData = await reportApi.generateBreedingReport(dateRange.value[0], dateRange.value[1])
        break
      case 'customer':
        reportData = await reportApi.generateCustomerReport(dateRange.value[0], dateRange.value[1])
        break
      case 'dashboard':
        reportData = await reportApi.generateDashboardReport()
        break
      default:
        throw new Error('未知的报表类型')
    }
    
    currentReport.value = reportData
    ElMessage.success('报表生成成功')
    
    // 刷新报表历史
    await loadReportHistory()
    
  } catch (error) {
    console.error('生成报表失败:', error)
    ElMessage.error('生成报表失败，请重试')
  } finally {
    loading.value = false
  }
}

const exportReport = async (format: 'pdf' | 'excel' | 'csv' = 'pdf') => {
  if (!currentReport.value) {
    ElMessage.warning('请先生成报表')
    return
  }

  try {
    const blob = await reportApi.exportReport(
      selectedReportType.value,
      dateRange.value[0],
      dateRange.value[1],
      format
    )
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${getReportTypeLabel(selectedReportType.value)}_${dateRange.value[0]}_${dateRange.value[1]}.${format}`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('报表导出成功')
  } catch (error) {
    console.error('导出报表失败:', error)
    ElMessage.error('导出报表失败，请重试')
  }
}

const loadReportHistory = async () => {
  try {
    const response = await reportApi.getReportHistory({
      page: historyPagination.current - 1,
      size: historyPagination.size
    })
    
    reportHistory.value = response.content
    historyPagination.total = response.totalElements
  } catch (error) {
    console.error('加载报表历史失败:', error)
  }
}

const viewReport = (report: ReportHistoryItem) => {
  // 查看历史报表
  // 可以打开新窗口或模态框显示报表内容
}

const downloadReport = async (report: ReportHistoryItem) => {
  try {
    const blob = await reportApi.downloadHistoryReport(report.id)
    
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${report.reportType}_${report.reportPeriod}.pdf`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('下载成功')
  } catch (error) {
    console.error('下载失败:', error)
    ElMessage.error('下载失败，请重试')
  }
}

const deleteReport = async (report: ReportHistoryItem) => {
  try {
    await ElMessageBox.confirm('确定要删除这个报表吗？', '确认删除', {
      type: 'warning'
    })
    
    await reportApi.deleteReport(report.id)
    ElMessage.success('删除成功')
    
    await loadReportHistory()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败，请重试')
    }
  }
}

const getReportTypeLabel = (type: ReportType) => {
  const reportType = reportTypes.find(t => t.value === type)
  return reportType?.label || type
}

const handleHistorySizeChange = (size: number) => {
  historyPagination.size = size
  loadReportHistory()
}

const handleHistoryCurrentChange = (current: number) => {
  historyPagination.current = current
  loadReportHistory()
}

// 生命周期
onMounted(() => {
  loadReportHistory()
})
</script>

<style scoped>
.reports-container {
  padding: 20px;
}

.reports-content {
  max-width: 1400px;
  margin: 0 auto;
}

.report-selector-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.report-selector {
  margin-bottom: 20px;
}

.date-range-selector {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.quick-date-buttons {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}

.generate-button {
  text-align: center;
}

.report-content {
  margin-bottom: 20px;
}

.loading-container {
  padding: 40px;
}

.report-history-card {
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .date-range-selector {
    flex-direction: column;
    align-items: stretch;
  }
  
  .quick-date-buttons {
    justify-content: center;
  }
  
  .report-selector .el-radio-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  
  .report-selector .el-radio-button {
    width: 100%;
  }
}
</style>
