// 性能优化工具类

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param wait 等待时间（毫秒）
 * @param immediate 是否立即执行
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate = false
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null
      if (!immediate) func(...args)
    }
    
    const callNow = immediate && !timeout
    
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    
    if (callNow) func(...args)
  }
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param limit 时间间隔（毫秒）
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 图片懒加载
 */
export class LazyImageLoader {
  private observer: IntersectionObserver | null = null
  
  constructor() {
    if ('IntersectionObserver' in window) {
      this.observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              const img = entry.target as HTMLImageElement
              const src = img.dataset.src
              
              if (src) {
                img.src = src
                img.removeAttribute('data-src')
                this.observer?.unobserve(img)
              }
            }
          })
        },
        {
          rootMargin: '50px 0px',
          threshold: 0.01
        }
      )
    }
  }
  
  observe(img: HTMLImageElement) {
    if (this.observer) {
      this.observer.observe(img)
    } else {
      // 降级处理
      const src = img.dataset.src
      if (src) {
        img.src = src
        img.removeAttribute('data-src')
      }
    }
  }
  
  disconnect() {
    if (this.observer) {
      this.observer.disconnect()
    }
  }
}

/**
 * 虚拟滚动类
 */
export class VirtualScroller {
  private container: HTMLElement
  private itemHeight: number
  private visibleCount: number
  private totalCount: number
  private scrollTop = 0
  private startIndex = 0
  private endIndex = 0
  
  constructor(
    container: HTMLElement,
    itemHeight: number,
    visibleCount: number,
    totalCount: number
  ) {
    this.container = container
    this.itemHeight = itemHeight
    this.visibleCount = visibleCount
    this.totalCount = totalCount
    
    this.init()
  }
  
  private init() {
    this.container.style.height = `${this.totalCount * this.itemHeight}px`
    this.container.addEventListener('scroll', this.handleScroll.bind(this))
    this.updateVisibleRange()
  }
  
  private handleScroll = throttle(() => {
    this.scrollTop = this.container.scrollTop
    this.updateVisibleRange()
  }, 16) // 60fps
  
  private updateVisibleRange() {
    this.startIndex = Math.floor(this.scrollTop / this.itemHeight)
    this.endIndex = Math.min(
      this.startIndex + this.visibleCount + 1,
      this.totalCount
    )
    
    // 触发更新事件
    this.container.dispatchEvent(new CustomEvent('visibleRangeChange', {
      detail: {
        startIndex: this.startIndex,
        endIndex: this.endIndex
      }
    }))
  }
  
  getVisibleRange() {
    return {
      startIndex: this.startIndex,
      endIndex: this.endIndex
    }
  }
  
  scrollToIndex(index: number) {
    const scrollTop = index * this.itemHeight
    this.container.scrollTop = scrollTop
  }
  
  destroy() {
    this.container.removeEventListener('scroll', this.handleScroll)
  }
}

/**
 * 内存缓存类
 */
export class MemoryCache<T = any> {
  private cache = new Map<string, { data: T; timestamp: number; ttl: number }>()
  private maxSize: number
  
  constructor(maxSize = 100) {
    this.maxSize = maxSize
  }
  
  set(key: string, data: T, ttl = 5 * 60 * 1000) { // 默认5分钟
    // 如果缓存已满，删除最旧的项
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }
    
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }
  
  get(key: string): T | null {
    const item = this.cache.get(key)
    
    if (!item) {
      return null
    }
    
    // 检查是否过期
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key)
      return null
    }
    
    return item.data
  }
  
  has(key: string): boolean {
    return this.get(key) !== null
  }
  
  delete(key: string): boolean {
    return this.cache.delete(key)
  }
  
  clear() {
    this.cache.clear()
  }
  
  size(): number {
    return this.cache.size
  }
  
  // 清理过期项
  cleanup() {
    const now = Date.now()
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key)
      }
    }
  }
}

/**
 * 性能监控类
 */
export class PerformanceMonitor {
  private static instance: PerformanceMonitor
  private metrics: Map<string, number[]> = new Map()
  
  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor()
    }
    return PerformanceMonitor.instance
  }
  
  // 标记性能开始
  mark(name: string) {
    if (performance.mark) {
      performance.mark(`${name}-start`)
    }
  }
  
  // 测量性能
  measure(name: string): number {
    if (performance.mark && performance.measure) {
      performance.mark(`${name}-end`)
      performance.measure(name, `${name}-start`, `${name}-end`)
      
      const entries = performance.getEntriesByName(name)
      if (entries.length > 0) {
        const duration = entries[entries.length - 1].duration
        this.recordMetric(name, duration)
        return duration
      }
    }
    return 0
  }
  
  // 记录指标
  private recordMetric(name: string, value: number) {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, [])
    }
    
    const values = this.metrics.get(name)!
    values.push(value)
    
    // 只保留最近100个值
    if (values.length > 100) {
      values.shift()
    }
  }
  
  // 获取指标统计
  getMetricStats(name: string) {
    const values = this.metrics.get(name)
    if (!values || values.length === 0) {
      return null
    }
    
    const sorted = [...values].sort((a, b) => a - b)
    const sum = values.reduce((a, b) => a + b, 0)
    
    return {
      count: values.length,
      min: sorted[0],
      max: sorted[sorted.length - 1],
      avg: sum / values.length,
      p50: sorted[Math.floor(sorted.length * 0.5)],
      p90: sorted[Math.floor(sorted.length * 0.9)],
      p95: sorted[Math.floor(sorted.length * 0.95)],
      p99: sorted[Math.floor(sorted.length * 0.99)]
    }
  }
  
  // 获取所有指标
  getAllMetrics() {
    const result: Record<string, any> = {}
    for (const [name] of this.metrics) {
      result[name] = this.getMetricStats(name)
    }
    return result
  }
  
  // 清理性能条目
  clearPerformanceEntries() {
    if (performance.clearMarks) {
      performance.clearMarks()
    }
    if (performance.clearMeasures) {
      performance.clearMeasures()
    }
  }
}

/**
 * 资源预加载
 */
export class ResourcePreloader {
  private loadedResources = new Set<string>()
  
  // 预加载图片
  preloadImage(src: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.loadedResources.has(src)) {
        resolve()
        return
      }
      
      const img = new Image()
      img.onload = () => {
        this.loadedResources.add(src)
        resolve()
      }
      img.onerror = reject
      img.src = src
    })
  }
  
  // 预加载多个图片
  preloadImages(srcs: string[]): Promise<void[]> {
    return Promise.all(srcs.map(src => this.preloadImage(src)))
  }
  
  // 预加载脚本
  preloadScript(src: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.loadedResources.has(src)) {
        resolve()
        return
      }
      
      const link = document.createElement('link')
      link.rel = 'preload'
      link.as = 'script'
      link.href = src
      link.onload = () => {
        this.loadedResources.add(src)
        resolve()
      }
      link.onerror = reject
      
      document.head.appendChild(link)
    })
  }
  
  // 预加载样式
  preloadStyle(href: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.loadedResources.has(href)) {
        resolve()
        return
      }
      
      const link = document.createElement('link')
      link.rel = 'preload'
      link.as = 'style'
      link.href = href
      link.onload = () => {
        this.loadedResources.add(href)
        resolve()
      }
      link.onerror = reject
      
      document.head.appendChild(link)
    })
  }
}

// 导出单例实例
export const performanceMonitor = PerformanceMonitor.getInstance()
export const memoryCache = new MemoryCache()
export const lazyImageLoader = new LazyImageLoader()
export const resourcePreloader = new ResourcePreloader()

// 自动清理过期缓存
setInterval(() => {
  memoryCache.cleanup()
}, 5 * 60 * 1000) // 每5分钟清理一次
