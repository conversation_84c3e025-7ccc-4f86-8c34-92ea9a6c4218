package com.cattery.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "cats")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public class Cat {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank(message = "猫咪名称不能为空")
    @Size(max = 50, message = "猫咪名称不能超过50个字符")
    @Column(nullable = false, length = 50)
    private String name;
    
    @Size(max = 50, message = "品种名称不能超过50个字符")
    @Column(length = 50)
    private String breed;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private Gender gender;
    
    @Column(name = "birth_date")
    private LocalDate birthDate;
    
    @Size(max = 30, message = "颜色描述不能超过30个字符")
    @Column(length = 30)
    private String color;
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private Status status = Status.AVAILABLE;
    
    @DecimalMin(value = "0.0", message = "价格不能为负数")
    @Digits(integer = 8, fraction = 2, message = "价格格式不正确")
    @Column(precision = 10, scale = 2)
    private BigDecimal price;
    
    @Size(max = 20, message = "芯片ID不能超过20个字符")
    @Column(name = "microchip_id", length = 20, unique = true)
    private String microchipId;
    
    @Column(name = "primary_photo", length = 500)
    private String primaryPhoto;
    
    @DecimalMin(value = "0.0", message = "体重不能为负数")
    @Digits(integer = 3, fraction = 2, message = "体重格式不正确")
    @Column(precision = 5, scale = 2)
    private BigDecimal weight;
    
    @Size(max = 100, message = "父亲信息不能超过100个字符")
    @Column(name = "father_info", length = 100)
    private String fatherInfo;
    
    @Size(max = 100, message = "母亲信息不能超过100个字符")
    @Column(name = "mother_info", length = 100)
    private String motherInfo;
    
    @Column(name = "is_vaccinated")
    private Boolean isVaccinated = false;
    
    @Column(name = "is_sterilized")
    private Boolean isSterilized = false;
    
    @Column(columnDefinition = "TEXT")
    private String notes;
    
    @OneToMany(mappedBy = "cat", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<CatPhoto> photos;
    
    @OneToMany(mappedBy = "cat", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<HealthRecord> healthRecords;
    
    @CreatedDate
    @Column(name = "created_time", nullable = false, updatable = false)
    private LocalDateTime createdTime;
    
    @LastModifiedDate
    @Column(name = "updated_time")
    private LocalDateTime updatedTime;
    
    public enum Gender {
        MALE("雄性"),
        FEMALE("雌性");
        
        private final String displayName;
        
        Gender(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
    
    public enum Status {
        AVAILABLE("可售"),
        RESERVED("预定"),
        SOLD("已售"),
        BREEDING("繁育中"),
        MEDICAL("医疗中"),
        RETIRED("退休");
        
        private final String displayName;
        
        Status(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
    
    // 为兼容性添加的方法别名
    public LocalDate getDateOfBirth() {
        return this.birthDate;
    }
    
    public String getBreedName() {
        return this.breed;
    }
    
    public BigDecimal getCurrentWeight() {
        return this.weight;
    }
}




