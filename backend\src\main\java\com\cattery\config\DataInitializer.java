package com.cattery.config;

import com.cattery.entity.*;
import com.cattery.repository.*;
import com.cattery.repository.FinanceRecordRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Component
@RequiredArgsConstructor
@Slf4j
public class DataInitializer implements CommandLineRunner {
    
    private final CatRepository catRepository;
    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final PermissionRepository permissionRepository;
    private final PasswordEncoder passwordEncoder;
    private final FinanceRecordRepository financeRecordRepository;
    
    @Override
    public void run(String... args) throws Exception {
        log.info("开始初始化数据...");
        
        // 初始化权限
        initializePermissions();
        
        // 初始化角色
        initializeRoles();
        
        // 初始化用户
        initializeUsers();
        
        log.info("初始化用户数据完成");
        
        // 初始化猫咪数据
        if (catRepository.count() == 0) {
            Cat cat1 = new Cat();
            cat1.setName("小白");
            cat1.setBreed("英国短毛猫");
            cat1.setGender(Cat.Gender.FEMALE);
            cat1.setBirthDate(LocalDate.of(2023, 3, 15));
            cat1.setColor("白色");
            cat1.setDescription("温顺可爱的小白猫，性格活泼，喜欢和人互动。");
            cat1.setStatus(Cat.Status.AVAILABLE);
            cat1.setPrice(new BigDecimal("3000.00"));
            cat1.setMicrochipId("MC001");
            catRepository.save(cat1);
            
            Cat cat2 = new Cat();
            cat2.setName("橘子");
            cat2.setBreed("美国短毛猫");
            cat2.setGender(Cat.Gender.MALE);
            cat2.setBirthDate(LocalDate.of(2023, 1, 20));
            cat2.setColor("橘色");
            cat2.setDescription("活泼好动的橘猫，食量很大，非常亲人。");
            cat2.setStatus(Cat.Status.AVAILABLE);
            cat2.setPrice(new BigDecimal("2800.00"));
            cat2.setMicrochipId("MC002");
            catRepository.save(cat2);
            
            Cat cat3 = new Cat();
            cat3.setName("灰灰");
            cat3.setBreed("俄罗斯蓝猫");
            cat3.setGender(Cat.Gender.FEMALE);
            cat3.setBirthDate(LocalDate.of(2022, 11, 10));
            cat3.setColor("灰蓝色");
            cat3.setDescription("优雅的俄罗斯蓝猫，性格独立但温柔。");
            cat3.setStatus(Cat.Status.BREEDING);
            cat3.setPrice(new BigDecimal("5000.00"));
            cat3.setMicrochipId("MC003");
            catRepository.save(cat3);
            
            log.info("初始化猫咪数据完成");
        }
        
        // 创建示例财务记录
        if (financeRecordRepository.count() == 0) {
            createSampleFinanceRecords();
        }

        log.info("数据初始化完成！");
    }
    
    private void initializePermissions() {
        if (permissionRepository.count() == 0) {
            // 用户管理权限
            createPermission("USER_READ", "查看用户", "用户管理", "查看用户信息");
            createPermission("USER_WRITE", "管理用户", "用户管理", "创建、编辑、删除用户");
            
            // 猫咪管理权限
            createPermission("CAT_READ", "查看猫咪", "猫咪管理", "查看猫咪信息");
            createPermission("CAT_WRITE", "管理猫咪", "猫咪管理", "创建、编辑、删除猫咪档案");
            
            // 健康管理权限
            createPermission("HEALTH_READ", "查看健康记录", "健康管理", "查看猫咪健康记录");
            createPermission("HEALTH_WRITE", "管理健康记录", "健康管理", "创建、编辑健康记录");
            
            // 繁育管理权限
            createPermission("BREEDING_READ", "查看繁育记录", "繁育管理", "查看繁育信息");
            createPermission("BREEDING_WRITE", "管理繁育记录", "繁育管理", "管理繁育计划和记录");
            
            // 客户管理权限
            createPermission("CUSTOMER_READ", "查看客户", "客户管理", "查看客户信息");
            createPermission("CUSTOMER_WRITE", "管理客户", "客户管理", "管理客户档案和合同");
            
            // 财务管理权限
            createPermission("FINANCE_READ", "查看财务", "财务管理", "查看财务记录");
            createPermission("FINANCE_WRITE", "管理财务", "财务管理", "管理财务记录和报表");
            
            // 库存管理权限
            createPermission("INVENTORY_READ", "查看库存", "库存管理", "查看库存信息");
            createPermission("INVENTORY_WRITE", "管理库存", "库存管理", "管理库存和采购");
            
            // 系统管理权限
            createPermission("SYSTEM_ADMIN", "系统管理", "系统管理", "系统配置和管理");
            
            log.info("初始化权限数据完成");
        }
    }
    
    private void initializeRoles() {
        if (roleRepository.count() == 0) {
            // 管理员角色
            Role adminRole = createRole("ROLE_ADMIN", "管理员", "系统管理员，拥有所有权限");
            List<Permission> allPermissions = permissionRepository.findAll();
            Set<Permission> adminPermissions = new HashSet<>();
            for (Permission permission : allPermissions) {
                adminPermissions.add(permission);
            }
            adminRole.setPermissions(adminPermissions);
            roleRepository.save(adminRole);
            
            // 工作人员角色
            Role staffRole = createRole("ROLE_STAFF", "工作人员", "普通工作人员，拥有基本操作权限");
            Set<Permission> staffPermissions = new HashSet<>();
            staffPermissions.add(permissionRepository.findByName("CAT_READ").orElse(null));
            staffPermissions.add(permissionRepository.findByName("CAT_WRITE").orElse(null));
            staffPermissions.add(permissionRepository.findByName("HEALTH_READ").orElse(null));
            staffPermissions.add(permissionRepository.findByName("HEALTH_WRITE").orElse(null));
            staffPermissions.add(permissionRepository.findByName("CUSTOMER_READ").orElse(null));
            staffPermissions.add(permissionRepository.findByName("CUSTOMER_WRITE").orElse(null));
            staffPermissions.add(permissionRepository.findByName("INVENTORY_READ").orElse(null));
            staffRole.setPermissions(staffPermissions);
            roleRepository.save(staffRole);
            
            // 查看者角色
            Role viewerRole = createRole("ROLE_VIEWER", "查看者", "只读权限用户");
            Set<Permission> viewerPermissions = new HashSet<>();
            viewerPermissions.add(permissionRepository.findByName("CAT_READ").orElse(null));
            viewerPermissions.add(permissionRepository.findByName("HEALTH_READ").orElse(null));
            viewerPermissions.add(permissionRepository.findByName("CUSTOMER_READ").orElse(null));
            viewerRole.setPermissions(viewerPermissions);
            roleRepository.save(viewerRole);
            
            log.info("初始化角色数据完成");
        }
    }
    
    private void initializeUsers() {
        if (userRepository.count() == 0) {
            // 创建管理员用户
            User admin = new User();
            admin.setUsername("admin");
            admin.setPassword(passwordEncoder.encode("admin123"));
            admin.setEmail("<EMAIL>");
            admin.setRealName("系统管理员");
            admin.setEnabled(true);
            
            Role adminRole = roleRepository.findByName("ROLE_ADMIN").orElse(null);
            if (adminRole != null) {
                Set<Role> adminRoles = new HashSet<>();
                adminRoles.add(adminRole);
                admin.setRoles(adminRoles);
            }
            
            userRepository.save(admin);
            
            // 创建工作人员用户
            User staff = new User();
            staff.setUsername("staff");
            staff.setPassword(passwordEncoder.encode("staff123"));
            staff.setEmail("<EMAIL>");
            staff.setRealName("工作人员");
            staff.setEnabled(true);
            
            Role staffRole = roleRepository.findByName("ROLE_STAFF").orElse(null);
            if (staffRole != null) {
                Set<Role> staffRoles = new HashSet<>();
                staffRoles.add(staffRole);
                staff.setRoles(staffRoles);
            }
            
            userRepository.save(staff);
            
            log.info("初始化用户数据完成");
        }
    }
    
    private Permission createPermission(String name, String displayName, String group, String description) {
        Permission permission = new Permission();
        permission.setName(name);
        permission.setDisplayName(displayName);
        permission.setPermissionGroup(group);
        permission.setDescription(description);
        permission.setEnabled(true);
        return permissionRepository.save(permission);
    }
    
    private Role createRole(String name, String displayName, String description) {
        Role role = new Role();
        role.setName(name);
        role.setDisplayName(displayName);
        role.setDescription(description);
        role.setEnabled(true);
        return role;
    }

    private void createSampleFinanceRecords() {
        // 收入记录
        FinanceRecord income1 = new FinanceRecord();
        income1.setType(FinanceRecord.RecordType.INCOME);
        income1.setCategory("猫咪销售");
        income1.setAmount(new BigDecimal("8000.00"));
        income1.setDescription("英国短毛猫销售");
        income1.setTransactionDate(LocalDate.now().minusDays(5));
        income1.setPaymentMethod(FinanceRecord.PaymentMethod.BANK_TRANSFER);
        income1.setStatus(FinanceRecord.RecordStatus.COMPLETED);
        financeRecordRepository.save(income1);

        FinanceRecord income2 = new FinanceRecord();
        income2.setType(FinanceRecord.RecordType.INCOME);
        income2.setCategory("繁育服务");
        income2.setAmount(new BigDecimal("2000.00"));
        income2.setDescription("配种服务费");
        income2.setTransactionDate(LocalDate.now().minusDays(10));
        income2.setPaymentMethod(FinanceRecord.PaymentMethod.CASH);
        income2.setStatus(FinanceRecord.RecordStatus.COMPLETED);
        financeRecordRepository.save(income2);

        // 支出记录
        FinanceRecord expense1 = new FinanceRecord();
        expense1.setType(FinanceRecord.RecordType.EXPENSE);
        expense1.setCategory("医疗费用");
        expense1.setAmount(new BigDecimal("500.00"));
        expense1.setDescription("疫苗接种费用");
        expense1.setTransactionDate(LocalDate.now().minusDays(3));
        expense1.setPaymentMethod(FinanceRecord.PaymentMethod.CREDIT_CARD);
        expense1.setStatus(FinanceRecord.RecordStatus.COMPLETED);
        financeRecordRepository.save(expense1);

        FinanceRecord expense2 = new FinanceRecord();
        expense2.setType(FinanceRecord.RecordType.EXPENSE);
        expense2.setCategory("饲料采购");
        expense2.setAmount(new BigDecimal("1200.00"));
        expense2.setDescription("高端猫粮采购");
        expense2.setTransactionDate(LocalDate.now().minusDays(7));
        expense2.setPaymentMethod(FinanceRecord.PaymentMethod.BANK_TRANSFER);
        expense2.setStatus(FinanceRecord.RecordStatus.COMPLETED);
        financeRecordRepository.save(expense2);

        log.info("初始化财务记录数据完成");
    }
}

