package com.cattery.dto.report;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 月度财务数据DTO
 */
@Data
public class MonthlyFinancialDataDTO {
    
    /**
     * 年份
     */
    private Integer year;
    
    /**
     * 月份
     */
    private Integer month;
    
    /**
     * 月度收入
     */
    private BigDecimal income;
    
    /**
     * 月度支出
     */
    private BigDecimal expense;
    
    /**
     * 月度净利润
     */
    private BigDecimal netProfit;
    
    /**
     * 交易数量
     */
    private Integer transactionCount;
    
    /**
     * 平均交易金额
     */
    private BigDecimal averageTransactionAmount;
    
    /**
     * 同比增长率
     */
    private BigDecimal yearOverYearGrowth;
    
    /**
     * 环比增长率
     */
    private BigDecimal monthOverMonthGrowth;
}
