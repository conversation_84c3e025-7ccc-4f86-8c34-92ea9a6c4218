// 基础类型定义

// 猫咪相关类型
export interface Cat {
  id?: number
  name: string
  breedName: string
  gender: 'MALE' | 'FEMALE' | 'UNKNOWN'
  dateOfBirth?: string
  age?: string
  color?: string
  pattern?: string
  currentWeight?: number
  microchipId?: string
  registrationNumber?: string
  status: 'AVAILABLE' | 'ADOPTED' | 'RESERVED' | 'BREEDING' | 'MEDICAL' | 'QUARANTINE'
  statusDescription?: string
  description?: string
  notes?: string
  primaryPhoto?: string
  isNeutered?: boolean
  father?: CatSummary
  mother?: CatSummary
  offspring?: CatSummary[]
  photos?: CatPhoto[]
  recentHealthRecords?: HealthRecordSummary[]
  createdAt?: string
  updatedAt?: string
  createdBy?: string
  updatedBy?: string
}

// 猫咪摘要类型
export interface CatSummary {
  id: number
  name: string
  breedName: string
  gender: string
  color?: string
  dateOfBirth?: string
  primaryPhoto?: string
}

// 猫咪照片类型
export interface CatPhoto {
  id: number
  fileName: string
  filePath: string
  thumbnailPath?: string
  isPrimary: boolean
  sortOrder: number
  description?: string
  takenDate?: string
}

// 品种类型
export interface CatBreed {
  id?: number
  name: string
  description?: string
  createdAt?: string
  updatedAt?: string
}

// 客户类型
export interface Customer {
  id?: number
  name: string
  phone: string
  address?: string
  createdAt?: string
  updatedAt?: string
}

// 商品类型
export interface Product {
  id?: number
  name: string
  category?: string
  description?: string
  price: number
  stock: number
  createdAt?: string
  updatedAt?: string
}

// 健康记录类型
export interface HealthRecord {
  id?: number
  catId: number
  catName?: string
  recordType: 'VACCINATION' | 'CHECKUP' | 'TREATMENT' | 'SURGERY' | 'DENTAL' | 'GROOMING' | 'GENETIC_TEST' | 'WEIGHT_CHECK' | 'EMERGENCY' | 'FOLLOW_UP'
  recordDate: string
  veterinarian?: string
  clinic?: string
  weight?: number
  temperature?: number
  vaccineName?: string
  vaccineBatch?: string
  nextVaccineDate?: string
  diagnosis?: string
  treatment?: string
  medication?: string
  notes?: string
  nextAppointment?: string
  cost?: number
  attachmentPath?: string
  createdAt?: string
  updatedAt?: string
  createdBy?: string
  updatedBy?: string
}

// 健康记录摘要类型
export interface HealthRecordSummary {
  id: number
  recordType: string
  recordDate: string
  veterinarian?: string
  diagnosis?: string
  nextAppointment?: string
}

// 订单类型
export interface Order {
  id?: number
  customerId: number
  customerName?: string
  orderItems: OrderItem[]
  totalPrice: number
  status: string
  orderDate?: string
  notes?: string
}

// 订单项类型
export interface OrderItem {
  id?: number
  orderId?: number
  productId?: number
  productName?: string
  catId?: number
  catName?: string
  quantity: number
  unitPrice: number
}

// 仪表板数据类型
export interface DashboardData {
  totalCats: number
  totalCustomers: number
  totalProducts: number
  totalRevenue: number
  catsByBreed: Array<{
    name: string
    value: number
  }>
}

// 分页参数
export interface PaginationParams {
  page: number
  size: number
  sort?: string
  direction?: 'asc' | 'desc'
}

// 分页响应
export interface PaginatedResponse<T> {
  content: T[]
  totalElements: number
  totalPages: number
  size: number
  number: number
  first: boolean
  last: boolean
}

// 查询参数
export interface QueryParams {
  [key: string]: string | number | boolean | undefined
}

// 血统图节点类型
export interface PedigreeNode {
  id: number
  name: string
  breedName: string
  gender: 'MALE' | 'FEMALE'
  dateOfBirth?: string
  color?: string
  photoUrl?: string
  relationship?: string
  generation?: number
  x?: number
  y?: number
}
