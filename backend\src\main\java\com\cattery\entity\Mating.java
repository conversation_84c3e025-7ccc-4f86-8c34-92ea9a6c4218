package com.cattery.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * 配种记录实体类
 */
@Entity
@Table(name = "matings")
@Data
@EqualsAndHashCode(callSuper = false)
public class Mating {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 雄性猫咪
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "male_parent_id", nullable = false)
    private Cat maleParent;

    /**
     * 雌性猫咪
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "female_parent_id", nullable = false)
    private Cat femaleParent;

    /**
     * 配种日期
     */
    @Column(name = "mating_date", nullable = false)
    private LocalDateTime matingDate;

    /**
     * 配种状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private Status status = Status.PLANNED;

    /**
     * 配种方式
     */
    @Column(name = "mating_type")
    private String matingType;

    /**
     * 配种地点
     */
    @Column(name = "location")
    private String location;

    /**
     * 配种费用
     */
    @Column(name = "fee")
    private Double fee;

    /**
     * 备注
     */
    @Column(name = "notes", columnDefinition = "TEXT")
    private String notes;

    /**
     * 预期分娩日期
     */
    @Column(name = "expected_birth_date")
    private LocalDateTime expectedBirthDate;

    /**
     * 是否成功受孕
     */
    @Column(name = "is_pregnant")
    private Boolean isPregnant;

    /**
     * 确认怀孕日期
     */
    @Column(name = "pregnancy_confirmed_date")
    private LocalDateTime pregnancyConfirmedDate;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 配种状态枚举
     */
    public enum Status {
        PLANNED("计划中"),
        IN_PROGRESS("进行中"),
        SUCCESS("成功"),
        FAILED("失败"),
        CANCELLED("已取消");

        private final String description;

        Status(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 配种结果枚举（为了兼容ReportService）
     */
    public enum MatingResult {
        SUCCESSFUL("成功"),
        FAILED("失败"),
        PENDING("待定");

        private final String description;

        MatingResult(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 获取配种结果（基于状态）
     */
    public MatingResult getResult() {
        switch (this.status) {
            case SUCCESS:
                return MatingResult.SUCCESSFUL;
            case FAILED:
                return MatingResult.FAILED;
            default:
                return MatingResult.PENDING;
        }
    }
}
