package com.cattery.dto.ai;

import lombok.Data;
import java.util.List;

/**
 * 品种信息DTO
 */
@Data
public class BreedInfoDTO {
    
    /**
     * 品种名称
     */
    private String breedName;
    
    /**
     * 品种描述
     */
    private String description;
    
    /**
     * 原产地
     */
    private String origin;
    
    /**
     * 体型大小
     */
    private String size;
    
    /**
     * 毛发长度
     */
    private String coatLength;
    
    /**
     * 毛发类型
     */
    private String coatType;
    
    /**
     * 常见颜色
     */
    private List<String> commonColors;
    
    /**
     * 性格特点
     */
    private List<String> temperament;
    
    /**
     * 平均寿命
     */
    private String lifespan;

    /**
     * 预期寿命
     */
    private String lifeExpectancy;

    /**
     * 特征
     */
    private List<String> characteristics;
    
    /**
     * 平均体重
     */
    private String weight;
    
    /**
     * 护理需求
     */
    private String careRequirements;
    
    /**
     * 健康问题
     */
    private List<String> commonHealthIssues;
    
    /**
     * 活动水平
     */
    private String activityLevel;
    
    /**
     * 适合家庭
     */
    private Boolean familyFriendly;
    
    /**
     * 适合儿童
     */
    private Boolean childFriendly;
    
    /**
     * 适合其他宠物
     */
    private Boolean petFriendly;
}
