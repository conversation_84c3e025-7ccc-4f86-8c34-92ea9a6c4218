# Node.js Environment Cleanup and Fresh Install
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Node.js Environment Cleanup & Install" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Step 1: Clean up residual files
Write-Host "Step 1: Cleaning up residual files" -ForegroundColor Yellow
Write-Host ""

$residualFiles = @(
    "D:\软件\npm.ps1",
    "D:\软件\npm.cmd",
    "D:\软件\npx.ps1",
    "D:\软件\npx.cmd"
)

foreach ($file in $residualFiles) {
    if (Test-Path $file) {
        try {
            Remove-Item $file -Force
            Write-Host "Removed: $file" -ForegroundColor Green
        } catch {
            Write-Host "Failed to remove: $file" -ForegroundColor Red
        }
    } else {
        Write-Host "Not found: $file" -ForegroundColor Gray
    }
}

# Step 2: Clean environment variables
Write-Host ""
Write-Host "Step 2: Checking environment variables" -ForegroundColor Yellow
Write-Host ""

$pathVariable = [Environment]::GetEnvironmentVariable("PATH", "Machine")
$userPathVariable = [Environment]::GetEnvironmentVariable("PATH", "User")

$nodeRelatedPaths = @()
if ($pathVariable) {
    $nodeRelatedPaths += $pathVariable -split ';' | Where-Object { $_ -like '*node*' -or $_ -like '*npm*' }
}
if ($userPathVariable) {
    $nodeRelatedPaths += $userPathVariable -split ';' | Where-Object { $_ -like '*node*' -or $_ -like '*npm*' }
}

if ($nodeRelatedPaths.Count -gt 0) {
    Write-Host "Found Node.js related paths in environment variables:" -ForegroundColor Yellow
    foreach ($path in $nodeRelatedPaths) {
        Write-Host "  - $path" -ForegroundColor Cyan
    }
    Write-Host ""
    Write-Host "These will be cleaned during Node.js installation" -ForegroundColor Yellow
} else {
    Write-Host "No Node.js related paths found in environment variables" -ForegroundColor Green
}

# Step 3: Download Node.js
Write-Host ""
Write-Host "Step 3: Opening Node.js download page" -ForegroundColor Yellow
Write-Host ""

Write-Host "Opening Node.js official website..." -ForegroundColor Cyan
Start-Process "https://nodejs.org/zh-cn/"

Write-Host ""
Write-Host "Download Instructions:" -ForegroundColor Yellow
Write-Host "1. Choose LTS version (recommended)" -ForegroundColor Cyan
Write-Host "2. Download Windows Installer (.msi)" -ForegroundColor Cyan
Write-Host "3. Choose 64-bit version" -ForegroundColor Cyan

# Step 4: Installation guide
Write-Host ""
Write-Host "Step 4: Installation Guide" -ForegroundColor Yellow
Write-Host ""

Write-Host "IMPORTANT Installation Settings:" -ForegroundColor Red
Write-Host "✓ Install Location: C:\Program Files\nodejs\" -ForegroundColor Green
Write-Host "✓ Add to PATH: YES (must check this box)" -ForegroundColor Green
Write-Host "✓ Install additional tools: YES (recommended)" -ForegroundColor Green

# Step 5: Verification
Write-Host ""
Write-Host "Step 5: Post-Installation Verification" -ForegroundColor Yellow
Write-Host ""

Write-Host "After installation, open a NEW command prompt and run:" -ForegroundColor Cyan
Write-Host "  node --version" -ForegroundColor White
Write-Host "  npm --version" -ForegroundColor White
Write-Host ""
Write-Host "Expected output:" -ForegroundColor Cyan
Write-Host "  v20.x.x (or v18.x.x)" -ForegroundColor Green
Write-Host "  10.x.x (or 9.x.x)" -ForegroundColor Green

# Step 6: Project setup
Write-Host ""
Write-Host "Step 6: Project Setup" -ForegroundColor Yellow
Write-Host ""

Write-Host "Once Node.js is verified working:" -ForegroundColor Cyan
Write-Host "1. cd D:\噔噔\frontend" -ForegroundColor White
Write-Host "2. npm install" -ForegroundColor White
Write-Host "3. npm run dev" -ForegroundColor White

Write-Host ""
Write-Host "Alternative with Yarn:" -ForegroundColor Cyan
Write-Host "1. Install Yarn: https://yarnpkg.com/getting-started/install" -ForegroundColor White
Write-Host "2. yarn install" -ForegroundColor White
Write-Host "3. yarn dev" -ForegroundColor White

# Open Yarn as backup
Write-Host ""
Write-Host "Opening Yarn website as backup option..." -ForegroundColor Cyan
Start-Process "https://yarnpkg.com/getting-started/install"

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Summary" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Current Issues Identified:" -ForegroundColor Red
Write-Host "❌ Node.js not installed" -ForegroundColor Red
Write-Host "❌ npm not working (residual files)" -ForegroundColor Red
Write-Host "❌ Environment variables may need cleanup" -ForegroundColor Red

Write-Host ""
Write-Host "Solution:" -ForegroundColor Green
Write-Host "✓ Download and install fresh Node.js" -ForegroundColor Green
Write-Host "✓ Ensure PATH is configured correctly" -ForegroundColor Green
Write-Host "✓ Verify installation with version commands" -ForegroundColor Green
Write-Host "✓ Install project dependencies" -ForegroundColor Green

Write-Host ""
Read-Host "Press Enter to continue with manual installation"
