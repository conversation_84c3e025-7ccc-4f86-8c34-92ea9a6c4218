package com.cattery.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户实体类
 */
@Entity
@Table(name = "customers")
@Data
@EqualsAndHashCode(callSuper = false)
public class Customer {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 客户姓名
     */
    @Column(nullable = false, length = 100)
    private String name;

    /**
     * 邮箱
     */
    @Column(unique = true, nullable = false, length = 100)
    private String email;

    /**
     * 电话
     */
    @Column(length = 20)
    private String phone;

    /**
     * 地址
     */
    @Column(columnDefinition = "TEXT")
    private String address;

    /**
     * 客户类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "customer_type", nullable = false)
    private CustomerType customerType = CustomerType.POTENTIAL;

    /**
     * 身份证号
     */
    @Column(name = "id_number", length = 50)
    private String idNumber;

    /**
     * 职业
     */
    @Column(length = 100)
    private String occupation;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 性别
     */
    @Enumerated(EnumType.STRING)
    private Gender gender;

    /**
     * 婚姻状况
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "marital_status")
    private MaritalStatus maritalStatus;

    /**
     * 家庭成员数量
     */
    @Column(name = "family_size")
    private Integer familySize;

    /**
     * 是否有养宠经验
     */
    @Column(name = "has_pet_experience")
    private Boolean hasPetExperience = false;

    /**
     * 住房类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "housing_type")
    private HousingType housingType;

    /**
     * 是否有院子
     */
    @Column(name = "has_yard")
    private Boolean hasYard = false;

    /**
     * 偏好的猫咪品种
     */
    @Column(name = "preferred_breed", length = 100)
    private String preferredBreed;

    /**
     * 偏好的猫咪性别
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "preferred_gender")
    private Cat.Gender preferredGender;

    /**
     * 偏好的猫咪年龄范围
     */
    @Column(name = "preferred_age_range", length = 50)
    private String preferredAgeRange;

    /**
     * 预算范围
     */
    @Column(name = "budget_range", length = 50)
    private String budgetRange;

    /**
     * 备注
     */
    @Column(columnDefinition = "TEXT")
    private String notes;

    /**
     * 客户评分
     */
    @Column(name = "customer_score", precision = 3, scale = 1)
    private BigDecimal customerScore;

    /**
     * 最后联系时间
     */
    @Column(name = "last_contact_date")
    private LocalDateTime lastContactDate;

    /**
     * 领养记录
     */
    @OneToMany(mappedBy = "customer", fetch = FetchType.LAZY)
    private List<AdoptionRecord> adoptionRecords;

    /**
     * 咨询记录
     */
    @OneToMany(mappedBy = "customer", fetch = FetchType.LAZY)
    private List<CustomerInquiry> inquiries;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 创建者ID
     */
    @Column(name = "created_by")
    private Long createdBy;

    /**
     * 更新者ID
     */
    @Column(name = "updated_by")
    private Long updatedBy;

    /**
     * 客户状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private Status status = Status.ACTIVE;

    /**
     * 客户类型枚举
     */
    public enum CustomerType {
        POTENTIAL("潜在客户"),
        ACTIVE("活跃客户"),
        ADOPTED("已领养客户"),
        BREEDER("繁育者"),
        VIP("VIP客户");

        private final String description;

        CustomerType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 性别枚举
     */
    public enum Gender {
        MALE("男"),
        FEMALE("女"),
        OTHER("其他");

        private final String description;

        Gender(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 婚姻状况枚举
     */
    public enum MaritalStatus {
        SINGLE("单身"),
        MARRIED("已婚"),
        DIVORCED("离异"),
        WIDOWED("丧偶");

        private final String description;

        MaritalStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 住房类型枚举
     */
    public enum HousingType {
        APARTMENT("公寓"),
        HOUSE("独栋房屋"),
        TOWNHOUSE("联排别墅"),
        CONDO("共管公寓"),
        OTHER("其他");

        private final String description;

        HousingType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 客户状态枚举
     */
    public enum Status {
        ACTIVE("活跃"),
        INACTIVE("非活跃"),
        BLACKLISTED("黑名单"),
        DELETED("已删除");

        private final String description;

        Status(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
