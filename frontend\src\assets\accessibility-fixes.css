/* 可访问性修复 - 解决表单和交互控件问题 */

/* 1. 表单元素必须有标签 */

/* 修复前 - 缺少标签 */
/*
<input type="text" placeholder="请输入用户名">
<select>
  <option>选择选项</option>
</select>
*/

/* 修复后 - 添加标签和属性 */
.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #303133;
}

.form-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
}

.form-input:focus {
  border-color: #409eff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

/* 为没有可见标签的输入框提供aria-label */
.form-input[aria-label] {
  /* 确保有aria-label的输入框有适当的样式 */
}

.form-input[title] {
  /* 确保有title的输入框有适当的样式 */
}

/* 2. Select元素必须有可访问的名称 */

.form-select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  background-color: white;
  cursor: pointer;
}

.form-select:focus {
  border-color: #409eff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

/* 为select添加必要的属性 */
.form-select[aria-label] {
  /* 有aria-label的select */
}

.form-select[title] {
  /* 有title的select */
}

/* 3. 交互控件不能嵌套 */

/* 修复前 - 嵌套的交互控件 */
/*
<button>
  <a href="#">链接</a>
</button>
*/

/* 修复后 - 避免嵌套交互控件 */
.button-link {
  display: inline-block;
  padding: 0.5rem 1rem;
  background: #409eff;
  color: white;
  text-decoration: none;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-size: 14px;
}

.button-link:hover {
  background: #337ecc;
  color: white;
}

.button-link:focus {
  outline: 2px solid #409eff;
  outline-offset: 2px;
}

/* 4. 正确的label for属性使用 */

/* 修复前 - 错误的label for */
/*
<label for="username">用户名</label>
<input type="text" name="user">
*/

/* 修复后 - 正确的label for */
.form-field {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
}

.form-field label {
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #303133;
}

.form-field input,
.form-field select,
.form-field textarea {
  padding: 0.5rem;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
}

.form-field input:focus,
.form-field select:focus,
.form-field textarea:focus {
  border-color: #409eff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

/* 5. 表单字段必须有id或name属性 */

/* 确保所有表单元素都有适当的标识 */
input[id],
input[name],
select[id],
select[name],
textarea[id],
textarea[name] {
  /* 有id或name的表单元素 */
}

/* 为没有id的表单元素添加视觉提示 */
input:not([id]):not([name]),
select:not([id]):not([name]),
textarea:not([id]):not([name]) {
  border-color: #f56c6c !important;
  box-shadow: 0 0 0 1px #f56c6c;
}

/* 6. 可访问性增强 */

/* 焦点指示器 */
:focus-visible {
  outline: 2px solid #409eff;
  outline-offset: 2px;
}

/* 移除默认焦点样式，但保留可访问性 */
:focus:not(:focus-visible) {
  outline: none;
}

/* 跳转链接 */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #409eff;
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}

/* 屏幕阅读器专用文本 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .form-input,
  .form-select,
  .form-textarea {
    border: 2px solid #000000;
  }
  
  .form-input:focus,
  .form-select:focus,
  .form-textarea:focus {
    border-color: #000000;
    box-shadow: 0 0 0 3px #000000;
  }
  
  .button-link {
    border: 2px solid #000000;
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  .form-input,
  .form-select,
  .button-link {
    transition: none;
  }
}

/* 7. 错误状态和验证 */

.form-field.error input,
.form-field.error select,
.form-field.error textarea {
  border-color: #f56c6c;
  box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2);
}

.form-field.error .error-message {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 0.25rem;
}

.form-field.success input,
.form-field.success select,
.form-field.success textarea {
  border-color: #67c23a;
  box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.2);
}

/* 8. 必填字段指示 */

.form-field.required label::after {
  content: " *";
  color: #f56c6c;
}

.form-field.required input,
.form-field.required select,
.form-field.required textarea {
  border-left: 3px solid #f56c6c;
}

/* 9. 帮助文本 */

.form-field .help-text {
  font-size: 12px;
  color: #909399;
  margin-top: 0.25rem;
}

/* 10. 组合框和下拉菜单 */

.combobox {
  position: relative;
}

.combobox-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.combobox-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #dcdfe6;
  border-top: none;
  border-radius: 0 0 4px 4px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
}

.combobox-option {
  padding: 0.5rem;
  cursor: pointer;
}

.combobox-option:hover,
.combobox-option[aria-selected="true"] {
  background: #f5f7fa;
}

.combobox-option:focus {
  background: #409eff;
  color: white;
  outline: none;
}
