# NVM for Windows Setup Script
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "NVM for Windows Setup Guide" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Step 1: Check if NVM is installed
Write-Host "Step 1: Checking NVM installation" -ForegroundColor Yellow
Write-Host ""

try {
    $nvmVersion = nvm version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ NVM is installed and working!" -ForegroundColor Green
        Write-Host "Version: $nvmVersion" -ForegroundColor Green
        $nvmInstalled = $true
    } else {
        throw "NVM not found"
    }
} catch {
    Write-Host "❌ NVM is not installed or not working" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please:" -ForegroundColor Yellow
    Write-Host "1. Make sure you've run nvm-setup.exe" -ForegroundColor Cyan
    Write-Host "2. Restart this PowerShell window" -ForegroundColor Cyan
    Write-Host "3. Or restart your computer" -ForegroundColor Cyan
    Write-Host ""
    
    # Check if NVM directory exists
    $nvmPath = "$env:USERPROFILE\AppData\Roaming\nvm"
    if (Test-Path $nvmPath) {
        Write-Host "NVM directory found at: $nvmPath" -ForegroundColor Yellow
        Write-Host "NVM may be installed but not in PATH" -ForegroundColor Yellow
    } else {
        Write-Host "NVM directory not found. Please install NVM first." -ForegroundColor Red
        Write-Host "Download from: https://github.com/coreybutler/nvm-windows/releases" -ForegroundColor Cyan
    }
    
    $nvmInstalled = $false
}

if (-not $nvmInstalled) {
    Write-Host ""
    Read-Host "Press Enter to exit and install NVM first"
    exit
}

# Step 2: Show available Node.js versions
Write-Host ""
Write-Host "Step 2: Available Node.js versions" -ForegroundColor Yellow
Write-Host ""

Write-Host "Fetching available Node.js versions..." -ForegroundColor Cyan
try {
    nvm list available
} catch {
    Write-Host "Could not fetch available versions" -ForegroundColor Red
}

# Step 3: Install Node.js
Write-Host ""
Write-Host "Step 3: Install Node.js" -ForegroundColor Yellow
Write-Host ""

$recommendedVersion = "20.11.0"
Write-Host "Recommended version: $recommendedVersion (LTS)" -ForegroundColor Green

$version = Read-Host "Enter Node.js version to install (or press Enter for $recommendedVersion)"
if ([string]::IsNullOrWhiteSpace($version)) {
    $version = $recommendedVersion
}

Write-Host ""
Write-Host "Installing Node.js $version..." -ForegroundColor Cyan

try {
    nvm install $version
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Node.js $version installed successfully!" -ForegroundColor Green
        
        Write-Host ""
        Write-Host "Setting Node.js $version as active..." -ForegroundColor Cyan
        nvm use $version
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Node.js $version is now active!" -ForegroundColor Green
        } else {
            Write-Host "❌ Failed to activate Node.js $version" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ Failed to install Node.js $version" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Error installing Node.js: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 4: Verify installation
Write-Host ""
Write-Host "Step 4: Verifying installation" -ForegroundColor Yellow
Write-Host ""

Write-Host "Testing Node.js..." -ForegroundColor Cyan
try {
    $nodeVersion = node --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Node.js is working!" -ForegroundColor Green
        Write-Host "Version: $nodeVersion" -ForegroundColor Green
    } else {
        Write-Host "❌ Node.js is not working" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Node.js test failed" -ForegroundColor Red
}

Write-Host ""
Write-Host "Testing npm..." -ForegroundColor Cyan
try {
    $npmVersion = npm --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ npm is working!" -ForegroundColor Green
        Write-Host "Version: $npmVersion" -ForegroundColor Green
    } else {
        Write-Host "❌ npm is not working" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ npm test failed" -ForegroundColor Red
}

# Step 5: Show NVM commands
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "NVM Commands Reference" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "nvm list                 - Show installed versions" -ForegroundColor White
Write-Host "nvm list available       - Show available versions" -ForegroundColor White
Write-Host "nvm install [version]    - Install a version" -ForegroundColor White
Write-Host "nvm use [version]        - Switch to a version" -ForegroundColor White
Write-Host "nvm uninstall [version]  - Remove a version" -ForegroundColor White

# Step 6: Install project dependencies
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Project Setup" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

$installDeps = Read-Host "Install project dependencies now? (Y/N)"

if ($installDeps -eq 'Y' -or $installDeps -eq 'y') {
    $frontendPath = "D:\噔噔\frontend"
    
    if (Test-Path $frontendPath) {
        Write-Host ""
        Write-Host "Navigating to frontend directory..." -ForegroundColor Cyan
        Set-Location $frontendPath
        
        if (Test-Path "package.json") {
            Write-Host "Found package.json, installing dependencies..." -ForegroundColor Cyan
            Write-Host ""
            
            try {
                npm install
                if ($LASTEXITCODE -eq 0) {
                    Write-Host ""
                    Write-Host "✅ Dependencies installed successfully!" -ForegroundColor Green
                    Write-Host ""
                    
                    $startDev = Read-Host "Start development server now? (Y/N)"
                    if ($startDev -eq 'Y' -or $startDev -eq 'y') {
                        Write-Host ""
                        Write-Host "Starting development server..." -ForegroundColor Cyan
                        Write-Host "Access the application at: http://localhost:3000" -ForegroundColor Green
                        Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Yellow
                        Write-Host ""
                        npm run dev
                    }
                } else {
                    Write-Host "❌ Failed to install dependencies" -ForegroundColor Red
                    Write-Host ""
                    Write-Host "Try these solutions:" -ForegroundColor Yellow
                    Write-Host "1. npm cache clean --force" -ForegroundColor Cyan
                    Write-Host "2. Delete node_modules and package-lock.json" -ForegroundColor Cyan
                    Write-Host "3. Run npm install again" -ForegroundColor Cyan
                }
            } catch {
                Write-Host "❌ Error during npm install: $($_.Exception.Message)" -ForegroundColor Red
            }
        } else {
            Write-Host "❌ package.json not found in $frontendPath" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ Frontend directory not found: $frontendPath" -ForegroundColor Red
    }
} else {
    Write-Host ""
    Write-Host "You can install dependencies later with:" -ForegroundColor Yellow
    Write-Host "  cd D:\噔噔\frontend" -ForegroundColor Cyan
    Write-Host "  npm install" -ForegroundColor Cyan
    Write-Host "  npm run dev" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Setup Complete!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Read-Host "Press Enter to exit"
