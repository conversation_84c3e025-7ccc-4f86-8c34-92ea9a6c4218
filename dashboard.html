<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>猫舍管理系统 - 仪表盘</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            padding: 0 20px;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 70px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo {
            font-size: 24px;
            font-weight: 700;
            color: white;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo::before {
            content: '🐱';
            font-size: 28px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
            color: white;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        .user-name {
            color: white;
            font-weight: 500;
        }

        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-1px);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .dashboard-title {
            text-align: center;
            margin-bottom: 40px;
        }

        .page-title {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .page-title::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
        }

        .dashboard-title h1 {
            color: #333;
            font-size: 36px;
            margin-bottom: 15px;
            font-weight: 700;
        }

        .dashboard-title p {
            color: #666;
            font-size: 18px;
            max-width: 600px;
            margin: 0 auto;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--card-color, #667eea);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .stat-title {
            color: #666;
            font-size: 14px;
            font-weight: 500;
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
        }

        .stat-icon.cats { background: #667eea; }
        .stat-icon.customers { background: #f093fb; }
        .stat-icon.revenue { background: #43e97b; }
        .stat-icon.inventory { background: #4facfe; }

        .stat-value {
            font-size: 32px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .stat-change {
            font-size: 14px;
            color: #10b981;
        }

        .quick-actions {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .quick-actions h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 18px;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .action-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.2s ease;
        }

        .action-btn:hover {
            background: #5a6fd8;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .error {
            background: #fef2f2;
            border: 1px solid #ef4444;
            color: #dc2626;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">猫舍管理系统</div>
            <div class="user-info">
                <div class="user-avatar" id="userAvatar">A</div>
                <span class="user-name" id="userName">加载中...</span>
                <button class="logout-btn" onclick="logout()">退出</button>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="dashboard-title">
            <div class="page-title">
                <h1>仪表盘</h1>
                <p>欢迎使用猫舍管理系统，这里是您的数据概览和快速操作中心</p>
            </div>
        </div>

        <div id="errorMessage" class="error" style="display: none;"></div>

        <div id="loadingMessage" class="loading">正在加载数据...</div>

        <div id="dashboardContent" style="display: none;">
            <div class="stats-grid">
                <div class="stat-card" style="--card-color: #667eea;">
                    <div class="stat-header">
                        <span class="stat-title">总猫咪数量</span>
                        <div class="stat-icon cats">🐱</div>
                    </div>
                    <div class="stat-value" id="totalCats">0</div>
                    <div class="stat-change">+5 本月新增</div>
                </div>

                <div class="stat-card" style="--card-color: #f093fb;">
                    <div class="stat-header">
                        <span class="stat-title">客户数量</span>
                        <div class="stat-icon customers">👥</div>
                    </div>
                    <div class="stat-value" id="totalCustomers">0</div>
                    <div class="stat-change">+2 本月新增</div>
                </div>

                <div class="stat-card" style="--card-color: #43e97b;">
                    <div class="stat-header">
                        <span class="stat-title">本月收入</span>
                        <div class="stat-icon revenue">💰</div>
                    </div>
                    <div class="stat-value" id="monthlyRevenue">¥0</div>
                    <div class="stat-change">+12% 较上月</div>
                </div>

                <div class="stat-card" style="--card-color: #4facfe;">
                    <div class="stat-header">
                        <span class="stat-title">库存物品</span>
                        <div class="stat-icon inventory">📦</div>
                    </div>
                    <div class="stat-value" id="totalInventory">0</div>
                    <div class="stat-change">3 项低库存</div>
                </div>
            </div>

            <div class="quick-actions">
                <h3>快速操作</h3>
                <div class="actions-grid">
                    <button class="action-btn" onclick="window.location.href='/cats-management.html'">猫咪管理</button>
                    <button class="action-btn" onclick="window.location.href='/customers-management.html'">客户管理</button>
                    <button class="action-btn" onclick="window.location.href='/health-management.html'">健康记录</button>
                    <button class="action-btn" onclick="window.location.href='/financial-reports.html'">财务报表</button>
                    <button class="action-btn" onclick="window.location.href='/breeding-management.html'">繁育管理</button>
                    <button class="action-btn" onclick="testAPI('/inventory')">库存管理</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080/api';
        
        // 检查登录状态
        function checkAuth() {
            const token = localStorage.getItem('token');
            const userInfo = localStorage.getItem('userInfo');
            
            if (!token || !userInfo) {
                window.location.href = '/login-test.html';
                return false;
            }
            
            try {
                const user = JSON.parse(userInfo);
                const userName = user.realName || user.username;
                document.getElementById('userName').textContent = userName;
                document.getElementById('userAvatar').textContent = userName.charAt(0).toUpperCase();
                return true;
            } catch (error) {
                console.error('解析用户信息失败:', error);
                logout();
                return false;
            }
        }
        
        // 退出登录
        function logout() {
            localStorage.removeItem('token');
            localStorage.removeItem('userInfo');
            window.location.href = '/login-test.html';
        }
        
        // 测试API
        async function testAPI(endpoint) {
            const token = localStorage.getItem('token');
            
            try {
                const response = await fetch(`${API_BASE_URL}${endpoint}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert(`API测试成功！\n端点: ${endpoint}\n数据: ${JSON.stringify(data.data, null, 2)}`);
                } else {
                    alert(`API测试失败！\n端点: ${endpoint}\n错误: ${data.message}`);
                }
            } catch (error) {
                console.error('API测试错误:', error);
                alert(`API测试错误！\n端点: ${endpoint}\n错误: ${error.message}`);
            }
        }
        
        // 加载仪表盘数据
        async function loadDashboardData() {
            const token = localStorage.getItem('token');
            
            try {
                // 并行请求多个API
                const [catsResponse, customersResponse, financeResponse] = await Promise.allSettled([
                    fetch(`${API_BASE_URL}/cats`, {
                        headers: { 'Authorization': `Bearer ${token}` }
                    }),
                    fetch(`${API_BASE_URL}/customers`, {
                        headers: { 'Authorization': `Bearer ${token}` }
                    }),
                    fetch(`${API_BASE_URL}/financial/stats`, {
                        headers: { 'Authorization': `Bearer ${token}` }
                    })
                ]);
                
                // 处理猫咪数据
                if (catsResponse.status === 'fulfilled') {
                    const catsData = await catsResponse.value.json();
                    if (catsData.success) {
                        const count = Array.isArray(catsData.data) ? catsData.data.length : (catsData.data.total || 0);
                        document.getElementById('totalCats').textContent = count;
                    }
                }
                
                // 处理客户数据
                if (customersResponse.status === 'fulfilled') {
                    const customersData = await customersResponse.value.json();
                    if (customersData.success) {
                        const count = Array.isArray(customersData.data) ? customersData.data.length : (customersData.data.total || 0);
                        document.getElementById('totalCustomers').textContent = count;
                    }
                }
                
                // 处理财务数据
                if (financeResponse.status === 'fulfilled') {
                    const financeData = await financeResponse.value.json();
                    if (financeData.success) {
                        const revenue = financeData.data.monthlyIncome || 0;
                        document.getElementById('monthlyRevenue').textContent = `¥${revenue.toLocaleString()}`;
                    }
                }
                
                // 显示内容，隐藏加载提示
                document.getElementById('loadingMessage').style.display = 'none';
                document.getElementById('dashboardContent').style.display = 'block';
                
            } catch (error) {
                console.error('加载仪表盘数据失败:', error);
                document.getElementById('loadingMessage').style.display = 'none';
                document.getElementById('errorMessage').textContent = '加载数据失败: ' + error.message;
                document.getElementById('errorMessage').style.display = 'block';
            }
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            if (checkAuth()) {
                loadDashboardData();
            }
        });
    </script>
</body>
</html>
