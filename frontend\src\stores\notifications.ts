import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElNotification } from 'element-plus'
import { getNotifications, getUnreadCount, markAsRead, markAllAsRead } from '@/api/notifications'
import type { Notification } from '@/api/notifications'

export const useNotificationStore = defineStore('notifications', () => {
  // 状态
  const notifications = ref<Notification[]>([])
  const unreadCount = ref(0)
  const loading = ref(false)
  const lastFetchTime = ref<Date | null>(null)

  // 计算属性
  const unreadNotifications = computed(() => 
    notifications.value.filter(n => !n.isRead)
  )

  const importantNotifications = computed(() => 
    notifications.value.filter(n => n.isImportant && !n.isRead)
  )

  const hasUnreadNotifications = computed(() => unreadCount.value > 0)

  // 方法
  const fetchNotifications = async (params: {
    page?: number
    size?: number
    type?: string
    category?: string
    isRead?: boolean
  } = {}) => {
    try {
      loading.value = true
      const response = await getNotifications({
        page: 0,
        size: 50,
        sortBy: 'createdAt',
        sortDir: 'desc',
        ...params
      })
      
      if (response.success) {
        notifications.value = response.data.content
        lastFetchTime.value = new Date()
      }
    } catch (error) {
      console.error('获取通知失败:', error)
    } finally {
      loading.value = false
    }
  }

  const fetchUnreadCount = async () => {
    try {
      const response = await getUnreadCount()
      if (response.success) {
        unreadCount.value = response.data
      }
    } catch (error) {
      console.error('获取未读通知数量失败:', error)
    }
  }

  const markNotificationAsRead = async (id: number) => {
    try {
      const response = await markAsRead(id)
      if (response.success) {
        const notification = notifications.value.find(n => n.id === id)
        if (notification) {
          notification.isRead = true
          notification.readAt = new Date().toISOString()
        }
        unreadCount.value = Math.max(0, unreadCount.value - 1)
      }
    } catch (error) {
      console.error('标记通知为已读失败:', error)
    }
  }

  const markAllNotificationsAsRead = async (ids?: number[]) => {
    try {
      const response = await markAllAsRead(ids)
      if (response.success) {
        if (ids) {
          // 标记指定的通知为已读
          notifications.value.forEach(n => {
            if (ids.includes(n.id)) {
              n.isRead = true
              n.readAt = new Date().toISOString()
            }
          })
          unreadCount.value = Math.max(0, unreadCount.value - ids.length)
        } else {
          // 标记所有通知为已读
          notifications.value.forEach(n => {
            n.isRead = true
            n.readAt = new Date().toISOString()
          })
          unreadCount.value = 0
        }
      }
    } catch (error) {
      console.error('批量标记通知为已读失败:', error)
    }
  }

  const addNotification = (notification: Notification) => {
    notifications.value.unshift(notification)
    if (!notification.isRead) {
      unreadCount.value++
    }
    
    // 显示系统通知
    showSystemNotification(notification)
  }

  const removeNotification = (id: number) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      const notification = notifications.value[index]
      notifications.value.splice(index, 1)
      if (!notification.isRead) {
        unreadCount.value = Math.max(0, unreadCount.value - 1)
      }
    }
  }

  const showSystemNotification = (notification: Notification) => {
    const typeMap = {
      INFO: 'info',
      SUCCESS: 'success',
      WARNING: 'warning',
      ERROR: 'error'
    } as const

    ElNotification({
      title: notification.title,
      message: notification.content,
      type: typeMap[notification.type] || 'info',
      duration: notification.isImportant ? 0 : 4500,
      onClick: () => {
        markNotificationAsRead(notification.id)
        if (notification.actionUrl) {
          window.open(notification.actionUrl, '_blank')
        }
      }
    })
  }

  const clearNotifications = () => {
    notifications.value = []
    unreadCount.value = 0
  }

  const refreshNotifications = async () => {
    await Promise.all([
      fetchNotifications(),
      fetchUnreadCount()
    ])
  }

  // 初始化
  const init = async () => {
    await refreshNotifications()
    
    // 设置定时刷新（每5分钟）
    setInterval(() => {
      fetchUnreadCount()
    }, 5 * 60 * 1000)
  }

  return {
    // 状态
    notifications,
    unreadCount,
    loading,
    lastFetchTime,
    
    // 计算属性
    unreadNotifications,
    importantNotifications,
    hasUnreadNotifications,
    
    // 方法
    fetchNotifications,
    fetchUnreadCount,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    addNotification,
    removeNotification,
    showSystemNotification,
    clearNotifications,
    refreshNotifications,
    init
  }
})
