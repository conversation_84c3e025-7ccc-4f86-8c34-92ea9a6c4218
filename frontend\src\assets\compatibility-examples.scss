// 使用兼容性 Mixins 的示例样式
@import '../styles/variables';

/* 使用 appearance Mixin 的表单元素 */
.form-input {
  @include appearance(none);
  border: 1px solid $border-base;
  border-radius: $border-radius-base;
  padding: $spacing-sm;
  
  &:focus {
    border-color: $primary-color;
    outline: none;
  }
}

.form-button {
  @include appearance(none);
  @include performance-animation(0.2s, ease);
  background: $primary-color;
  color: white;
  border: none;
  border-radius: $border-radius-base;
  padding: $spacing-sm $spacing-md;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
  
  @include respect-user-preferences;
}

/* 使用 image-rendering Mixin 的图片 */
.cat-avatar {
  @include image-rendering(crisp-edges);
  border-radius: $border-radius-circle;
  width: 64px;
  height: 64px;
  object-fit: cover;
}

/* 使用 mask Mixin 的装饰元素 */
.decorative-element {
  @include mask(url('/assets/icons/paw.svg'), contain);
  background: linear-gradient(45deg, $primary-color, $success-color);
  width: 32px;
  height: 32px;
}

/* 使用 text-size-adjust Mixin 的文本容器 */
.text-container {
  @include text-size-adjust(100%);
  font-family: $font-family;
  line-height: 1.6;
}

/* 使用 scrollbar-style Mixin 的滚动容器 */
.custom-scrollbar {
  @include scrollbar-style(6px, #f1f1f1, $primary-color);
  max-height: 300px;
  overflow-y: auto;
  padding: $spacing-md;
}

/* 使用 slide-animation Mixin 的动画元素 */
.slide-in-element {
  @include slide-animation(left, 100%);
  @include performance-animation(0.3s, ease-out);
  
  &.visible {
    @extend .slide-in-element.active;
  }
}

/* 使用 safe-area Mixin 的移动端容器 */
.mobile-container {
  @include safe-area(padding, 1rem);
  background: $bg-color;
  min-height: 100vh;
}

/* 使用 touch-optimization Mixin 的按钮 */
.touch-button {
  @include touch-optimization;
  @include appearance(none);
  background: $success-color;
  color: white;
  border: none;
  border-radius: $border-radius-base;
  padding: $spacing-md;
  font-size: $font-size-base;
  cursor: pointer;
}

/* 使用 dark-mode Mixin 的主题元素 */
.theme-text {
  @include dark-mode($text-primary, #ffffff);
  font-size: $font-size-base;
}

.theme-background {
  @include dark-mode($bg-color, #1a1a1a);
  padding: $spacing-lg;
  border-radius: $border-radius-base;
}

/* 使用 print-optimization Mixin 的打印友好元素 */
.printable-content {
  @include print-optimization;
  padding: $spacing-lg;
  
  .no-print {
    @media print {
      display: none !important;
    }
  }
}

/* 使用 browser-specific Mixin 的浏览器特定样式 */
.browser-optimized {
  // Safari 特定优化
  @include browser-specific(safari) {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  // Firefox 特定优化
  @include browser-specific(firefox) {
    -moz-font-feature-settings: "kern" 1;
  }
  
  // Chrome 特定优化
  @include browser-specific(chrome) {
    font-feature-settings: "kern" 1, "liga" 1;
  }
}

/* 使用 feature-support Mixin 的现代特性 */
.modern-grid {
  @include feature-support(display, grid, (
    display: flex,
    flex-wrap: wrap
  ));
  
  @supports (display: grid) {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: $spacing-md;
  }
}

/* 组合使用多个 Mixin 的复杂组件 */
.cat-card {
  @include appearance(none);
  @include performance-animation(0.3s, ease);
  @include respect-user-preferences;
  @include touch-optimization;
  @include print-optimization;
  
  background: $bg-color;
  border: 1px solid $border-light;
  border-radius: $border-radius-base;
  padding: $spacing-lg;
  box-shadow: $box-shadow-base;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: $box-shadow-light;
  }
  
  .cat-image {
    @include image-rendering(crisp-edges);
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: $border-radius-base;
  }
  
  .cat-info {
    @include dark-mode($text-primary, #ffffff);
    margin-top: $spacing-md;
  }
  
  .cat-actions {
    @include safe-area(margin, $spacing-sm);
    display: flex;
    gap: $spacing-sm;
    margin-top: $spacing-md;
  }
}

/* 响应式设计结合兼容性 */
.responsive-layout {
  @include performance-animation();
  @include respect-user-preferences;
  
  display: grid;
  grid-template-columns: 1fr;
  gap: $spacing-md;
  
  @media (min-width: $breakpoint-sm) {
    grid-template-columns: 1fr 1fr;
  }
  
  @media (min-width: $breakpoint-lg) {
    grid-template-columns: repeat(3, 1fr);
  }
  
  // 高对比度模式支持
  @media (prefers-contrast: high) {
    border: 2px solid currentColor;
    gap: $spacing-lg;
  }
  
  // 减少动画偏好支持
  @media (prefers-reduced-motion: reduce) {
    transition: none;
    transform: none;
  }
}
