import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getSystemConfig, updateSystemConfig, getSystemInfo } from '@/api/system'
import type { SystemConfig, SystemInfo } from '@/api/system'

export const useSystemStore = defineStore('system', () => {
  // 状态
  const config = ref<SystemConfig | null>(null)
  const systemInfo = ref<SystemInfo | null>(null)
  const loading = ref(false)
  const lastUpdateTime = ref<Date | null>(null)

  // 主题设置
  const theme = ref<'light' | 'dark'>('light')
  const primaryColor = ref('#409EFF')
  const sidebarCollapsed = ref(false)
  const language = ref('zh-CN')

  // 用户偏好设置
  const userPreferences = ref({
    pageSize: 10,
    dateFormat: 'YYYY-MM-DD',
    timeFormat: 'HH:mm:ss',
    timezone: 'Asia/Shanghai',
    autoSave: true,
    showNotifications: true,
    soundEnabled: true,
    animationsEnabled: true
  })

  // 计算属性
  const isDarkMode = computed(() => theme.value === 'dark')
  const siteName = computed(() => config.value?.siteName || '猫舍管理系统')
  const siteDescription = computed(() => config.value?.siteDescription || '专业的猫舍管理解决方案')

  // 方法
  const fetchSystemConfig = async () => {
    try {
      loading.value = true
      const response = await getSystemConfig()
      if (response.success) {
        config.value = response.data
        
        // 应用系统配置到用户偏好
        if (response.data.language) {
          language.value = response.data.language
        }
        if (response.data.dateFormat) {
          userPreferences.value.dateFormat = response.data.dateFormat
        }
        if (response.data.timeFormat) {
          userPreferences.value.timeFormat = response.data.timeFormat
        }
        if (response.data.timezone) {
          userPreferences.value.timezone = response.data.timezone
        }
      }
    } catch (error) {
      console.error('获取系统配置失败:', error)
    } finally {
      loading.value = false
    }
  }

  const updateConfig = async (newConfig: Partial<SystemConfig>) => {
    try {
      loading.value = true
      const response = await updateSystemConfig(newConfig)
      if (response.success) {
        config.value = response.data
        lastUpdateTime.value = new Date()
        return true
      }
      return false
    } catch (error) {
      console.error('更新系统配置失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  const fetchSystemInfo = async () => {
    try {
      const response = await getSystemInfo()
      if (response.success) {
        systemInfo.value = response.data
      }
    } catch (error) {
      console.error('获取系统信息失败:', error)
    }
  }

  const setTheme = (newTheme: 'light' | 'dark') => {
    theme.value = newTheme
    document.documentElement.setAttribute('data-theme', newTheme)
    localStorage.setItem('theme', newTheme)
  }

  const setPrimaryColor = (color: string) => {
    primaryColor.value = color
    document.documentElement.style.setProperty('--el-color-primary', color)
    localStorage.setItem('primaryColor', color)
  }

  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
    localStorage.setItem('sidebarCollapsed', sidebarCollapsed.value.toString())
  }

  const setSidebarCollapsed = (collapsed: boolean) => {
    sidebarCollapsed.value = collapsed
    localStorage.setItem('sidebarCollapsed', collapsed.toString())
  }

  const setLanguage = (lang: string) => {
    language.value = lang
    localStorage.setItem('language', lang)
  }

  const updateUserPreferences = (preferences: Partial<typeof userPreferences.value>) => {
    userPreferences.value = { ...userPreferences.value, ...preferences }
    localStorage.setItem('userPreferences', JSON.stringify(userPreferences.value))
  }

  const loadUserPreferences = () => {
    // 从 localStorage 加载用户偏好
    const savedTheme = localStorage.getItem('theme') as 'light' | 'dark'
    if (savedTheme) {
      setTheme(savedTheme)
    }

    const savedPrimaryColor = localStorage.getItem('primaryColor')
    if (savedPrimaryColor) {
      setPrimaryColor(savedPrimaryColor)
    }

    const savedSidebarCollapsed = localStorage.getItem('sidebarCollapsed')
    if (savedSidebarCollapsed) {
      sidebarCollapsed.value = savedSidebarCollapsed === 'true'
    }

    const savedLanguage = localStorage.getItem('language')
    if (savedLanguage) {
      language.value = savedLanguage
    }

    const savedPreferences = localStorage.getItem('userPreferences')
    if (savedPreferences) {
      try {
        const preferences = JSON.parse(savedPreferences)
        userPreferences.value = { ...userPreferences.value, ...preferences }
      } catch (error) {
        console.error('解析用户偏好设置失败:', error)
      }
    }
  }

  const resetUserPreferences = () => {
    userPreferences.value = {
      pageSize: 10,
      dateFormat: 'YYYY-MM-DD',
      timeFormat: 'HH:mm:ss',
      timezone: 'Asia/Shanghai',
      autoSave: true,
      showNotifications: true,
      soundEnabled: true,
      animationsEnabled: true
    }
    setTheme('light')
    setPrimaryColor('#409EFF')
    setSidebarCollapsed(false)
    setLanguage('zh-CN')
    
    // 清除 localStorage
    localStorage.removeItem('userPreferences')
    localStorage.removeItem('theme')
    localStorage.removeItem('primaryColor')
    localStorage.removeItem('sidebarCollapsed')
    localStorage.removeItem('language')
  }

  const formatDate = (date: string | Date, includeTime = false) => {
    const d = typeof date === 'string' ? new Date(date) : date
    const format = includeTime 
      ? `${userPreferences.value.dateFormat} ${userPreferences.value.timeFormat}`
      : userPreferences.value.dateFormat
    
    // 简单的日期格式化（实际项目中建议使用 dayjs 或 moment.js）
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    const hours = String(d.getHours()).padStart(2, '0')
    const minutes = String(d.getMinutes()).padStart(2, '0')
    const seconds = String(d.getSeconds()).padStart(2, '0')
    
    let result = format
      .replace('YYYY', year.toString())
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds)
    
    return result
  }

  const formatCurrency = (amount: number) => {
    const currency = config.value?.currency || 'CNY'
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: currency
    }).format(amount)
  }

  // 初始化
  const init = async () => {
    loadUserPreferences()
    await Promise.all([
      fetchSystemConfig(),
      fetchSystemInfo()
    ])
  }

  return {
    // 状态
    config,
    systemInfo,
    loading,
    lastUpdateTime,
    theme,
    primaryColor,
    sidebarCollapsed,
    language,
    userPreferences,
    
    // 计算属性
    isDarkMode,
    siteName,
    siteDescription,
    
    // 方法
    fetchSystemConfig,
    updateConfig,
    fetchSystemInfo,
    setTheme,
    setPrimaryColor,
    toggleSidebar,
    setSidebarCollapsed,
    setLanguage,
    updateUserPreferences,
    loadUserPreferences,
    resetUserPreferences,
    formatDate,
    formatCurrency,
    init
  }
})
