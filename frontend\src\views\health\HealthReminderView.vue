<template>
  <div class="health-reminder-view">
    <div class="page-header">
      <h1>健康提醒</h1>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        新增提醒
      </el-button>
    </div>

    <div class="search-bar">
      <el-form :model="searchForm" inline>
        <el-form-item label="猫咪">
          <el-select v-model="searchForm.catId" placeholder="选择猫咪" clearable>
            <el-option
              v-for="cat in cats"
              :key="cat.id"
              :label="cat.name"
              :value="cat.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="提醒类型">
          <el-select v-model="searchForm.reminderType" placeholder="选择提醒类型" clearable>
            <el-option label="疫苗接种" value="VACCINATION" />
            <el-option label="体检" value="CHECKUP" />
            <el-option label="驱虫" value="DEWORMING" />
            <el-option label="洗澡" value="GROOMING" />
            <el-option label="其他" value="OTHER" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
            <el-option label="待处理" value="PENDING" />
            <el-option label="已完成" value="COMPLETED" />
            <el-option label="已过期" value="OVERDUE" />
            <el-option label="已取消" value="CANCELLED" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchReminders">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table :data="reminders" v-loading="loading">
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column label="猫咪">
        <template #default="{ row }">
          <div class="cat-info">
            <img v-if="row.cat?.primaryPhoto" :src="row.cat.primaryPhoto" class="cat-avatar" />
            <span>{{ row.cat?.name }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="提醒类型">
        <template #default="{ row }">
          <el-tag :type="getReminderTypeColor(row.reminderType)">
            {{ getReminderTypeText(row.reminderType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="title" label="标题" />
      <el-table-column prop="dueDate" label="截止日期" />
      <el-table-column label="优先级">
        <template #default="{ row }">
          <el-tag :type="getPriorityColor(row.priority)">
            {{ getPriorityText(row.priority) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态">
        <template #default="{ row }">
          <el-tag :type="getStatusColor(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button size="small" @click="markAsCompleted(row)" v-if="row.status === 'PENDING'">
            标记完成
          </el-button>
          <el-button size="small" type="primary" @click="editReminder(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="deleteReminder(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingReminder ? '编辑健康提醒' : '新增健康提醒'"
      width="600px"
    >
      <el-form :model="reminderForm" :rules="formRules" ref="formRef" label-width="100px">
        <el-form-item label="猫咪" prop="catId">
          <el-select v-model="reminderForm.catId" placeholder="选择猫咪">
            <el-option
              v-for="cat in cats"
              :key="cat.id"
              :label="cat.name"
              :value="cat.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="提醒类型" prop="reminderType">
          <el-select v-model="reminderForm.reminderType" placeholder="选择提醒类型">
            <el-option label="疫苗接种" value="VACCINATION" />
            <el-option label="体检" value="CHECKUP" />
            <el-option label="驱虫" value="DEWORMING" />
            <el-option label="洗澡" value="GROOMING" />
            <el-option label="其他" value="OTHER" />
          </el-select>
        </el-form-item>
        <el-form-item label="标题" prop="title">
          <el-input v-model="reminderForm.title" placeholder="输入提醒标题" />
        </el-form-item>
        <el-form-item label="截止日期" prop="dueDate">
          <el-date-picker
            v-model="reminderForm.dueDate"
            type="date"
            placeholder="选择截止日期"
          />
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <el-select v-model="reminderForm.priority" placeholder="选择优先级">
            <el-option label="低" value="LOW" />
            <el-option label="中" value="MEDIUM" />
            <el-option label="高" value="HIGH" />
            <el-option label="紧急" value="URGENT" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="reminderForm.status" placeholder="选择状态">
            <el-option label="待处理" value="PENDING" />
            <el-option label="已完成" value="COMPLETED" />
            <el-option label="已过期" value="OVERDUE" />
            <el-option label="已取消" value="CANCELLED" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="reminderForm.description"
            type="textarea"
            :rows="3"
            placeholder="输入提醒描述"
          />
        </el-form-item>
        <el-form-item label="备注" prop="notes">
          <el-input
            v-model="reminderForm.notes"
            type="textarea"
            :rows="3"
            placeholder="输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveReminder">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const showCreateDialog = ref(false)
const editingReminder = ref(null)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

const reminders = ref([])
const cats = ref([])

const searchForm = reactive({
  catId: '',
  reminderType: '',
  status: ''
})

const reminderForm = reactive({
  catId: '',
  reminderType: '',
  title: '',
  dueDate: '',
  priority: 'MEDIUM',
  status: 'PENDING',
  description: '',
  notes: ''
})

const formRules = {
  catId: [{ required: true, message: '请选择猫咪', trigger: 'change' }],
  reminderType: [{ required: true, message: '请选择提醒类型', trigger: 'change' }],
  title: [{ required: true, message: '请输入提醒标题', trigger: 'blur' }],
  dueDate: [{ required: true, message: '请选择截止日期', trigger: 'change' }],
  priority: [{ required: true, message: '请选择优先级', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

// 方法
const loadReminders = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    reminders.value = [
      {
        id: 1,
        cat: { id: 1, name: '美美', primaryPhoto: '/images/cat1.jpg' },
        reminderType: 'VACCINATION',
        title: '三联疫苗接种',
        dueDate: '2024-03-15',
        priority: 'HIGH',
        status: 'PENDING',
        description: '需要接种三联疫苗',
        notes: '上次接种日期：2023-03-15'
      },
      {
        id: 2,
        cat: { id: 2, name: '帅帅', primaryPhoto: '/images/cat2.jpg' },
        reminderType: 'CHECKUP',
        title: '年度体检',
        dueDate: '2024-02-10',
        priority: 'MEDIUM',
        status: 'COMPLETED',
        description: '年度常规体检',
        notes: '需要空腹'
      }
    ]
    total.value = 2
  } catch (error) {
    ElMessage.error('加载健康提醒失败')
  } finally {
    loading.value = false
  }
}

const loadCats = async () => {
  try {
    // 模拟API调用
    cats.value = [
      { id: 1, name: '美美' },
      { id: 2, name: '帅帅' },
      { id: 3, name: '花花' }
    ]
  } catch (error) {
    ElMessage.error('加载猫咪列表失败')
  }
}

const searchReminders = () => {
  currentPage.value = 1
  loadReminders()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    catId: '',
    reminderType: '',
    status: ''
  })
  searchReminders()
}

const markAsCompleted = async (reminder: any) => {
  try {
    await ElMessageBox.confirm('确定要将此提醒标记为已完成吗？', '确认', {
      type: 'info'
    })
    ElMessage.success('已标记为完成')
    loadReminders()
  } catch {
    // 用户取消操作
  }
}

const editReminder = (reminder: any) => {
  editingReminder.value = reminder
  Object.assign(reminderForm, reminder)
  showCreateDialog.value = true
}

const deleteReminder = async (reminder: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这条健康提醒吗？', '确认删除', {
      type: 'warning'
    })
    ElMessage.success('删除成功')
    loadReminders()
  } catch {
    // 用户取消删除
  }
}

const saveReminder = () => {
  ElMessage.success('保存成功')
  showCreateDialog.value = false
  loadReminders()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  loadReminders()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadReminders()
}

const getReminderTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    VACCINATION: 'success',
    CHECKUP: 'primary',
    DEWORMING: 'warning',
    GROOMING: 'info',
    OTHER: ''
  }
  return colors[type] || ''
}

const getReminderTypeText = (type: string) => {
  const texts: Record<string, string> = {
    VACCINATION: '疫苗接种',
    CHECKUP: '体检',
    DEWORMING: '驱虫',
    GROOMING: '洗澡',
    OTHER: '其他'
  }
  return texts[type] || type
}

const getPriorityColor = (priority: string) => {
  const colors: Record<string, string> = {
    LOW: 'info',
    MEDIUM: '',
    HIGH: 'warning',
    URGENT: 'danger'
  }
  return colors[priority] || ''
}

const getPriorityText = (priority: string) => {
  const texts: Record<string, string> = {
    LOW: '低',
    MEDIUM: '中',
    HIGH: '高',
    URGENT: '紧急'
  }
  return texts[priority] || priority
}

const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    PENDING: 'warning',
    COMPLETED: 'success',
    OVERDUE: 'danger',
    CANCELLED: 'info'
  }
  return colors[status] || ''
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    PENDING: '待处理',
    COMPLETED: '已完成',
    OVERDUE: '已过期',
    CANCELLED: '已取消'
  }
  return texts[status] || status
}

onMounted(() => {
  loadReminders()
  loadCats()
})
</script>

<style scoped>
.health-reminder-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-bar {
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 4px;
}

.cat-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.cat-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
