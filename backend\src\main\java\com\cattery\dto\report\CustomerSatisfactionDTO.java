package com.cattery.dto.report;

import lombok.Data;
import java.util.Map;

/**
 * 客户满意度DTO
 */
@Data
public class CustomerSatisfactionDTO {
    
    /**
     * 总体满意度评分 (1-5)
     */
    private Double overallSatisfactionScore;
    
    /**
     * 服务质量评分
     */
    private Double serviceQualityScore;
    
    /**
     * 沟通效果评分
     */
    private Double communicationScore;
    
    /**
     * 专业程度评分
     */
    private Double professionalismScore;
    
    /**
     * 设施环境评分
     */
    private Double facilityScore;
    
    /**
     * 按评分等级分布
     */
    private Map<String, Long> satisfactionDistribution;
    
    /**
     * 推荐意愿评分
     */
    private Double recommendationScore;
    
    /**
     * 客户反馈数量
     */
    private Long feedbackCount;
    
    /**
     * 投诉数量
     */
    private Long complaintCount;
    
    /**
     * 投诉解决率
     */
    private Double complaintResolutionRate;
    
    /**
     * 客户忠诚度指数
     */
    private Double customerLoyaltyIndex;
}
