package com.cattery.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 猫咪健康记录实体类
 */
@Entity
@Table(name = "cat_health_records")
@Data
@EqualsAndHashCode(callSuper = false)
public class CatHealthRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 关联的猫咪
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_id", nullable = false)
    private Cat cat;

    /**
     * 记录类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "record_type", nullable = false)
    private RecordType recordType;

    /**
     * 记录日期
     */
    @Column(name = "record_date", nullable = false)
    private LocalDateTime recordDate;

    /**
     * 兽医姓名
     */
    @Column(length = 100)
    private String veterinarian;

    /**
     * 诊所名称
     */
    @Column(length = 200)
    private String clinic;

    /**
     * 体重（克）
     */
    @Column(precision = 8, scale = 2)
    private BigDecimal weight;

    /**
     * 体温（摄氏度）
     */
    @Column(precision = 4, scale = 1)
    private BigDecimal temperature;

    /**
     * 疫苗名称
     */
    @Column(name = "vaccine_name", length = 100)
    private String vaccineName;

    /**
     * 疫苗批次号
     */
    @Column(name = "vaccine_batch", length = 50)
    private String vaccineBatch;

    /**
     * 下次疫苗日期
     */
    @Column(name = "next_vaccine_date")
    private LocalDateTime nextVaccineDate;

    /**
     * 诊断结果
     */
    @Column(columnDefinition = "TEXT")
    private String diagnosis;

    /**
     * 治疗方案
     */
    @Column(columnDefinition = "TEXT")
    private String treatment;

    /**
     * 用药信息
     */
    @Column(columnDefinition = "TEXT")
    private String medication;

    /**
     * 备注
     */
    @Column(columnDefinition = "TEXT")
    private String notes;

    /**
     * 下次预约日期
     */
    @Column(name = "next_appointment")
    private LocalDateTime nextAppointment;

    /**
     * 费用
     */
    @Column(precision = 10, scale = 2)
    private BigDecimal cost;

    /**
     * 附件文件路径
     */
    @Column(name = "attachment_path", length = 500)
    private String attachmentPath;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 创建者ID
     */
    @Column(name = "created_by")
    private Long createdBy;

    /**
     * 更新者ID
     */
    @Column(name = "updated_by")
    private Long updatedBy;

    /**
     * 记录类型枚举
     */
    public enum RecordType {
        VACCINATION("疫苗接种"),
        CHECKUP("体检"),
        TREATMENT("治疗"),
        SURGERY("手术"),
        DENTAL("牙科"),
        GROOMING("美容"),
        GENETIC_TEST("基因检测"),
        WEIGHT_CHECK("体重检查"),
        EMERGENCY("急诊"),
        FOLLOW_UP("复查");

        private final String description;

        RecordType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
