package com.cattery.dto.ai;

import lombok.Data;
import java.util.List;

/**
 * 品种识别结果DTO
 */
@Data
public class BreedRecognitionResultDTO {
    
    /**
     * 识别置信度 (0.0 - 1.0)
     */
    private Double confidence;
    
    /**
     * 识别的品种名称
     */
    private String breedName;
    
    /**
     * 品种详细信息
     */
    private BreedInfoDTO breedInfo;
    
    /**
     * 可能的品种列表（按置信度排序）
     */
    private List<BreedPrediction> possibleBreeds;
    
    /**
     * 识别特征
     */
    private List<String> identifiedFeatures;
    
    /**
     * 品种预测内部类
     */
    @Data
    public static class BreedPrediction {
        private String breedName;
        private Double confidence;
        private String reason;
    }

    /**
     * 品种候选内部类
     */
    @Data
    public static class BreedCandidate {
        private String breedName;
        private Double confidence;
        private List<String> characteristics;
    }

    /**
     * 候选品种列表
     */
    private List<BreedCandidate> candidates;
}
