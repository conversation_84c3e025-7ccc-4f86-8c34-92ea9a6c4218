import request from '@/utils/request'

export interface HealthRecord {
  id?: number
  catId: number
  recordType: 'VACCINATION' | 'CHECKUP' | 'TREATMENT' | 'SURGERY' | 'DENTAL' | 'GROOMING' | 'GENETIC_TEST' | 'WEIGHT_CHECK' | 'EMERGENCY' | 'FOLLOW_UP'
  recordDate: string
  veterinarian?: string
  clinic?: string
  diagnosis?: string
  treatment?: string
  medication?: string
  notes?: string
  cost?: number
  weight?: number
  temperature?: number
  vaccineName?: string
  vaccineBatch?: string
  nextVaccineDate?: string
  nextAppointment?: string
  attachmentPath?: string
  createdAt?: string
  updatedAt?: string
  createdBy?: number
  updatedBy?: number
}

export interface HealthQuery {
  page?: number
  size?: number
  catId?: number
  recordType?: string
  startDate?: string
  endDate?: string
  veterinarian?: string
  clinic?: string
}

// 获取健康记录列表
export const getHealthRecords = (params?: HealthQuery) => {
  return request.get('/health-records', { params })
}

// 根据ID获取健康记录
export const getHealthRecordById = (id: number) => {
  return request.get(`/health-records/${id}`)
}

// 创建健康记录
export const createHealthRecord = (data: Omit<HealthRecord, 'id' | 'createdAt' | 'updatedAt'>) => {
  return request.post('/health-records', data)
}

// 更新健康记录
export const updateHealthRecord = (id: number, data: Partial<HealthRecord>) => {
  return request.put(`/health-records/${id}`, data)
}

// 删除健康记录
export const deleteHealthRecord = (id: number) => {
  return request.delete(`/health-records/${id}`)
}

// 获取猫咪的健康记录
export const getCatHealthRecords = (catId: number, params?: HealthQuery) => {
  return request.get(`/cats/${catId}/health-records`, { params })
}

// 获取健康统计信息
export const getHealthStats = () => {
  return request.get('/health-records/stats')
}

// 获取疫苗提醒
export const getVaccineReminders = () => {
  return request.get('/health-records/vaccine-reminders')
}

// 获取体检提醒
export const getCheckupReminders = () => {
  return request.get('/health-records/checkup-reminders')
}

// 上传健康记录附件
export const uploadHealthAttachment = (recordId: number, file: FormData) => {
  return request.post(`/health-records/${recordId}/attachment`, file, {
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

// 下载健康记录附件
export const downloadHealthAttachment = (recordId: number) => {
  return request.get(`/health-records/${recordId}/attachment`, {
    responseType: 'blob'
  })
}

// 删除健康记录附件
export const deleteHealthAttachment = (recordId: number) => {
  return request.delete(`/health-records/${recordId}/attachment`)
}

// 导出健康记录
export const exportHealthRecords = (params?: HealthQuery) => {
  return request.get('/health-records/export', {
    params,
    responseType: 'blob'
  })
}

// 批量删除健康记录
export const batchDeleteHealthRecords = (ids: number[]) => {
  return request.delete('/health-records/batch', { data: { ids } })
}
