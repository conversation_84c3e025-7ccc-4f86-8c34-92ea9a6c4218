import { createI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'

// 导入语言包
import zhCN from './locales/zh-CN'
import enUS from './locales/en-US'
import jaJP from './locales/ja-JP'
import koKR from './locales/ko-KR'

// Element Plus 语言包
import zhCnElementPlus from 'element-plus/dist/locale/zh-cn.mjs'
import enElementPlus from 'element-plus/dist/locale/en.mjs'
import jaElementPlus from 'element-plus/dist/locale/ja.mjs'
import koElementPlus from 'element-plus/dist/locale/ko.mjs'

// 支持的语言列表
export const SUPPORT_LOCALES = [
  {
    code: 'zh-CN',
    name: '简体中文',
    flag: '🇨🇳',
    elementPlusLocale: zhCnElementPlus
  },
  {
    code: 'en-US',
    name: 'English',
    flag: '🇺🇸',
    elementPlusLocale: enElementPlus
  },
  {
    code: 'ja-JP',
    name: '日本語',
    flag: '🇯🇵',
    elementPlusLocale: jaElementPlus
  },
  {
    code: 'ko-KR',
    name: '한국어',
    flag: '🇰🇷',
    elementPlusLocale: koElementPlus
  }
]

// 获取浏览器默认语言
export function getDefaultLocale(): string {
  const browserLang = navigator.language || navigator.languages[0]
  
  // 检查是否支持浏览器语言
  const supportedLang = SUPPORT_LOCALES.find(locale => 
    locale.code === browserLang || locale.code.startsWith(browserLang.split('-')[0])
  )
  
  return supportedLang?.code || 'zh-CN'
}

// 从本地存储获取语言设置
export function getStoredLocale(): string {
  return localStorage.getItem('locale') || getDefaultLocale()
}

// 保存语言设置到本地存储
export function setStoredLocale(locale: string): void {
  localStorage.setItem('locale', locale)
}

// 获取Element Plus语言包
export function getElementPlusLocale(locale: string) {
  const supportedLocale = SUPPORT_LOCALES.find(item => item.code === locale)
  return supportedLocale?.elementPlusLocale || zhCnElementPlus
}

// 创建i18n实例
const i18n = createI18n({
  legacy: false, // 使用 Composition API
  locale: getStoredLocale(),
  fallbackLocale: 'zh-CN',
  messages: {
    'zh-CN': zhCN,
    'en-US': enUS,
    'ja-JP': jaJP,
    'ko-KR': koKR
  },
  globalInjection: true,
  silentTranslationWarn: true,
  silentFallbackWarn: true
})

// 切换语言
export async function setLocale(locale: string): Promise<void> {
  if (!SUPPORT_LOCALES.find(item => item.code === locale)) {
    throw new Error(`Unsupported locale: ${locale}`)
  }

  try {
    // 设置i18n语言
    i18n.global.locale.value = locale as any
    
    // 保存到本地存储
    setStoredLocale(locale)
    
    // 设置HTML lang属性
    document.documentElement.lang = locale
    
    // 设置页面标题
    const title = i18n.global.t('app.title')
    document.title = title
    
    ElMessage.success(i18n.global.t('common.languageChanged'))
    
  } catch (error) {
    console.error('Failed to set locale:', error)
    ElMessage.error(i18n.global.t('common.languageChangeFailed'))
    throw error
  }
}

// 获取当前语言
export function getCurrentLocale(): string {
  return i18n.global.locale.value
}

// 获取当前语言信息
export function getCurrentLocaleInfo() {
  const currentLocale = getCurrentLocale()
  return SUPPORT_LOCALES.find(locale => locale.code === currentLocale)
}

// 格式化数字
export function formatNumber(value: number, locale?: string): string {
  const currentLocale = locale || getCurrentLocale()
  return new Intl.NumberFormat(currentLocale).format(value)
}

// 格式化货币
export function formatCurrency(value: number, currency = 'CNY', locale?: string): string {
  const currentLocale = locale || getCurrentLocale()
  return new Intl.NumberFormat(currentLocale, {
    style: 'currency',
    currency: currency
  }).format(value)
}

// 格式化日期
export function formatDate(date: Date | string, options?: Intl.DateTimeFormatOptions, locale?: string): string {
  const currentLocale = locale || getCurrentLocale()
  const dateObj = typeof date === 'string' ? new Date(date) : date
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }
  
  return new Intl.DateTimeFormat(currentLocale, { ...defaultOptions, ...options }).format(dateObj)
}

// 格式化相对时间
export function formatRelativeTime(date: Date | string, locale?: string): string {
  const currentLocale = locale || getCurrentLocale()
  const dateObj = typeof date === 'string' ? new Date(date) : date
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000)
  
  const rtf = new Intl.RelativeTimeFormat(currentLocale, { numeric: 'auto' })
  
  if (diffInSeconds < 60) {
    return rtf.format(-diffInSeconds, 'second')
  } else if (diffInSeconds < 3600) {
    return rtf.format(-Math.floor(diffInSeconds / 60), 'minute')
  } else if (diffInSeconds < 86400) {
    return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour')
  } else if (diffInSeconds < 2592000) {
    return rtf.format(-Math.floor(diffInSeconds / 86400), 'day')
  } else if (diffInSeconds < 31536000) {
    return rtf.format(-Math.floor(diffInSeconds / 2592000), 'month')
  } else {
    return rtf.format(-Math.floor(diffInSeconds / 31536000), 'year')
  }
}

// 获取本地化的性别文本
export function getGenderText(gender: string): string {
  const genderMap: Record<string, string> = {
    'MALE': i18n.global.t('cat.gender.male'),
    'FEMALE': i18n.global.t('cat.gender.female'),
    'UNKNOWN': i18n.global.t('cat.gender.unknown')
  }
  return genderMap[gender] || gender
}

// 获取本地化的状态文本
export function getStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    'AVAILABLE': i18n.global.t('cat.status.available'),
    'ADOPTED': i18n.global.t('cat.status.adopted'),
    'RESERVED': i18n.global.t('cat.status.reserved'),
    'BREEDING': i18n.global.t('cat.status.breeding'),
    'MEDICAL': i18n.global.t('cat.status.medical'),
    'QUARANTINE': i18n.global.t('cat.status.quarantine')
  }
  return statusMap[status] || status
}

// 获取本地化的健康记录类型文本
export function getHealthRecordTypeText(type: string): string {
  const typeMap: Record<string, string> = {
    'VACCINATION': i18n.global.t('health.recordType.vaccination'),
    'CHECKUP': i18n.global.t('health.recordType.checkup'),
    'TREATMENT': i18n.global.t('health.recordType.treatment'),
    'SURGERY': i18n.global.t('health.recordType.surgery'),
    'DENTAL': i18n.global.t('health.recordType.dental'),
    'GROOMING': i18n.global.t('health.recordType.grooming'),
    'GENETIC_TEST': i18n.global.t('health.recordType.geneticTest'),
    'WEIGHT_CHECK': i18n.global.t('health.recordType.weightCheck')
  }
  return typeMap[type] || type
}

// 验证翻译键是否存在
export function hasTranslation(key: string, locale?: string): boolean {
  const currentLocale = locale || getCurrentLocale()
  try {
    const translation = i18n.global.t(key, {}, { locale: currentLocale })
    return translation !== key
  } catch {
    return false
  }
}

// 获取翻译文本，如果不存在则返回默认值
export function getTranslation(key: string, defaultValue?: string, params?: Record<string, any>): string {
  try {
    const translation = i18n.global.t(key, params || {})
    return translation !== key ? translation : (defaultValue || key)
  } catch {
    return defaultValue || key
  }
}

// 加载异步语言包
export async function loadLocaleMessages(locale: string): Promise<void> {
  try {
    // 动态导入语言包
    const messages = await import(`./locales/${locale}.ts`)
    i18n.global.setLocaleMessage(locale, messages.default)
  } catch (error) {
    console.error(`Failed to load locale messages for ${locale}:`, error)
    throw error
  }
}

// 预加载所有语言包
export async function preloadAllLocales(): Promise<void> {
  const loadPromises = SUPPORT_LOCALES.map(locale => 
    loadLocaleMessages(locale.code).catch(error => {
      console.warn(`Failed to preload locale ${locale.code}:`, error)
    })
  )
  
  await Promise.allSettled(loadPromises)
}

// 获取RTL语言列表
export const RTL_LOCALES = ['ar', 'he', 'fa']

// 检查是否为RTL语言
export function isRTL(locale?: string): boolean {
  const currentLocale = locale || getCurrentLocale()
  return RTL_LOCALES.some(rtlLocale => currentLocale.startsWith(rtlLocale))
}

// 设置页面方向
export function setPageDirection(locale?: string): void {
  const currentLocale = locale || getCurrentLocale()
  const direction = isRTL(currentLocale) ? 'rtl' : 'ltr'
  document.documentElement.dir = direction
}

export default i18n
