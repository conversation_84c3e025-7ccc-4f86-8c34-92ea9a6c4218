{"name": "cattery-management-frontend", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "pinia": "^2.1.6", "element-plus": "^2.3.8", "@element-plus/icons-vue": "^2.1.0", "axios": "^1.4.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "typescript": "^5.0.2", "vue-tsc": "^1.8.5", "vite": "^4.4.5", "@types/node": "^20.4.5"}}