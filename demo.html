<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>猫舍管理系统 - 登录演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 900px;
            max-width: 90vw;
            display: flex;
        }

        .login-left {
            flex: 1;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }

        .login-left h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            font-weight: 300;
        }

        .login-left p {
            font-size: 1.1rem;
            opacity: 0.9;
            line-height: 1.6;
            margin-bottom: 30px;
        }

        .features {
            list-style: none;
            text-align: left;
        }

        .features li {
            margin: 10px 0;
            padding-left: 25px;
            position: relative;
        }

        .features li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #4CAF50;
            font-weight: bold;
        }

        .login-right {
            flex: 1;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .login-form h2 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2rem;
            font-weight: 300;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .login-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s;
            margin-bottom: 20px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .demo-accounts {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }

        .demo-accounts h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .account-item {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 8px;
            background: white;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .account-item:hover {
            background: #e9ecef;
        }

        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            font-weight: 500;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.loading {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .api-info {
            margin-top: 20px;
            padding: 15px;
            background: #e7f3ff;
            border-radius: 10px;
            border-left: 4px solid #2196F3;
        }

        .api-info h4 {
            color: #1976D2;
            margin-bottom: 10px;
        }

        .api-info p {
            color: #555;
            margin: 5px 0;
            font-size: 14px;
        }

        @media (max-width: 768px) {
            .login-container {
                flex-direction: column;
                width: 95vw;
            }
            
            .login-left, .login-right {
                padding: 40px 30px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-left">
            <h1>🐱 猫舍管理系统</h1>
            <p>专业的猫舍管理解决方案，提供全方位的猫咪管理、客户服务、健康跟踪和财务管理功能。</p>
            
            <ul class="features">
                <li>猫咪档案管理</li>
                <li>客户关系管理</li>
                <li>健康记录跟踪</li>
                <li>繁育管理系统</li>
                <li>财务统计分析</li>
                <li>库存管理功能</li>
            </ul>
        </div>
        
        <div class="login-right">
            <div class="login-form">
                <h2>系统登录</h2>
                
                <form id="loginForm">
                    <div class="form-group">
                        <label for="username">用户名</label>
                        <input type="text" id="username" name="username" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">密码</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    
                    <button type="submit" class="login-btn" id="loginBtn">登录</button>
                </form>
                
                <div class="demo-accounts">
                    <h3>演示账户</h3>
                    <div class="account-item" onclick="fillAccount('admin', 'admin123')">
                        <span><strong>管理员</strong> - admin</span>
                        <span>admin123</span>
                    </div>
                    <div class="account-item" onclick="fillAccount('user', 'user123')">
                        <span><strong>普通用户</strong> - user</span>
                        <span>user123</span>
                    </div>
                </div>
                
                <div id="status" class="status" style="display: none;"></div>
                
                <div class="api-info">
                    <h4>🔧 后端服务状态</h4>
                    <p><strong>API地址:</strong> http://localhost:8080</p>
                    <p><strong>API文档:</strong> <a href="http://localhost:8080/swagger-ui.html" target="_blank">Swagger UI</a></p>
                    <p><strong>数据库:</strong> H2 内存数据库</p>
                    <p><strong>状态:</strong> <span id="serverStatus">检查中...</span></p>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080';
        
        // 检查服务器状态
        async function checkServerStatus() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/auth/test`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                if (response.ok) {
                    document.getElementById('serverStatus').innerHTML = '<span style="color: #4CAF50;">✓ 运行正常</span>';
                } else {
                    document.getElementById('serverStatus').innerHTML = '<span style="color: #FF9800;">⚠ 连接异常</span>';
                }
            } catch (error) {
                document.getElementById('serverStatus').innerHTML = '<span style="color: #F44336;">✗ 无法连接</span>';
            }
        }
        
        // 填充账户信息
        function fillAccount(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
        }
        
        // 显示状态信息
        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
        }
        
        // 登录处理
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            
            if (!username || !password) {
                showStatus('请输入用户名和密码', 'error');
                return;
            }
            
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';
            showStatus('正在验证用户信息...', 'loading');
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    showStatus('登录成功！正在跳转到系统...', 'success');
                    
                    // 存储token
                    localStorage.setItem('token', data.data.token);
                    localStorage.setItem('user', JSON.stringify(data.data.user));
                    
                    // 模拟跳转到主系统
                    setTimeout(() => {
                        showSystemDashboard(data.data.user);
                    }, 1500);
                    
                } else {
                    showStatus(data.message || '登录失败，请检查用户名和密码', 'error');
                }
                
            } catch (error) {
                console.error('登录错误:', error);
                showStatus('网络连接错误，请检查后端服务是否启动', 'error');
            } finally {
                loginBtn.disabled = false;
                loginBtn.textContent = '登录';
            }
        });
        
        // 显示系统仪表盘
        function showSystemDashboard(user) {
            document.body.innerHTML = `
                <div style="min-height: 100vh; background: #f5f7fa; padding: 20px;">
                    <div style="max-width: 1200px; margin: 0 auto;">
                        <header style="background: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <h1 style="color: #333; margin: 0;">🐱 猫舍管理系统</h1>
                                <div style="display: flex; align-items: center; gap: 15px;">
                                    <span style="color: #666;">欢迎，${user.username}</span>
                                    <button onclick="logout()" style="padding: 8px 16px; background: #dc3545; color: white; border: none; border-radius: 5px; cursor: pointer;">退出登录</button>
                                </div>
                            </div>
                        </header>
                        
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px;">
                            <div style="background: white; padding: 25px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                                <h3 style="color: #667eea; margin-bottom: 15px;">🐱 猫咪管理</h3>
                                <p style="color: #666; margin-bottom: 15px;">管理猫咪档案、照片和基本信息</p>
                                <button onclick="showCats()" style="padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 5px; cursor: pointer;">查看猫咪</button>
                            </div>
                            
                            <div style="background: white; padding: 25px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                                <h3 style="color: #28a745; margin-bottom: 15px;">👥 客户管理</h3>
                                <p style="color: #666; margin-bottom: 15px;">管理客户信息和咨询记录</p>
                                <button onclick="showCustomers()" style="padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer;">查看客户</button>
                            </div>
                            
                            <div style="background: white; padding: 25px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                                <h3 style="color: #ffc107; margin-bottom: 15px;">🏥 健康管理</h3>
                                <p style="color: #666; margin-bottom: 15px;">记录疫苗、体检和医疗信息</p>
                                <button onclick="showHealth()" style="padding: 10px 20px; background: #ffc107; color: white; border: none; border-radius: 5px; cursor: pointer;">健康记录</button>
                            </div>
                            
                            <div style="background: white; padding: 25px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                                <h3 style="color: #17a2b8; margin-bottom: 15px;">💕 繁育管理</h3>
                                <p style="color: #666; margin-bottom: 15px;">管理配种、怀孕和繁育记录</p>
                                <button onclick="showBreeding()" style="padding: 10px 20px; background: #17a2b8; color: white; border: none; border-radius: 5px; cursor: pointer;">繁育记录</button>
                            </div>
                            
                            <div style="background: white; padding: 25px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                                <h3 style="color: #6f42c1; margin-bottom: 15px;">💰 财务管理</h3>
                                <p style="color: #666; margin-bottom: 15px;">记录收支和财务统计分析</p>
                                <button onclick="showFinance()" style="padding: 10px 20px; background: #6f42c1; color: white; border: none; border-radius: 5px; cursor: pointer;">财务记录</button>
                            </div>
                            
                            <div style="background: white; padding: 25px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                                <h3 style="color: #fd7e14; margin-bottom: 15px;">📊 系统统计</h3>
                                <p style="color: #666; margin-bottom: 15px;">查看系统数据和统计报表</p>
                                <button onclick="showStats()" style="padding: 10px 20px; background: #fd7e14; color: white; border: none; border-radius: 5px; cursor: pointer;">统计报表</button>
                            </div>
                        </div>
                        
                        <div id="content" style="background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                            <h2 style="color: #333; margin-bottom: 20px;">系统概览</h2>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                                <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                                    <h3 style="color: #667eea; margin-bottom: 10px;">3</h3>
                                    <p style="color: #666;">猫咪总数</p>
                                </div>
                                <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                                    <h3 style="color: #28a745; margin-bottom: 10px;">2</h3>
                                    <p style="color: #666;">用户总数</p>
                                </div>
                                <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                                    <h3 style="color: #ffc107; margin-bottom: 10px;">4</h3>
                                    <p style="color: #666;">财务记录</p>
                                </div>
                                <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                                    <h3 style="color: #17a2b8; margin-bottom: 10px;">15</h3>
                                    <p style="color: #666;">系统权限</p>
                                </div>
                            </div>
                            
                            <div style="margin-top: 30px; padding: 20px; background: #e7f3ff; border-radius: 8px; border-left: 4px solid #2196F3;">
                                <h4 style="color: #1976D2; margin-bottom: 10px;">🎉 系统功能演示</h4>
                                <p style="color: #555; margin-bottom: 10px;">恭喜！您已成功登录猫舍管理系统。</p>
                                <p style="color: #555; margin-bottom: 10px;">点击上方的功能模块按钮可以查看对应的API数据。</p>
                                <p style="color: #555;"><strong>提示:</strong> 系统已预置了示例数据，包括3只猫咪和4条财务记录。</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        // 功能模块函数
        async function showCats() {
            await loadData('/api/cats', '猫咪管理', '🐱');
        }
        
        async function showCustomers() {
            await loadData('/api/customers', '客户管理', '👥');
        }
        
        async function showHealth() {
            await loadData('/api/health-records', '健康记录', '🏥');
        }
        
        async function showBreeding() {
            await loadData('/api/breeding-records', '繁育记录', '💕');
        }
        
        async function showFinance() {
            await loadData('/api/finance-records', '财务记录', '💰');
        }
        
        async function showStats() {
            document.getElementById('content').innerHTML = `
                <h2 style="color: #333; margin-bottom: 20px;">📊 系统统计</h2>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                    <div style="padding: 20px; background: #f8f9fa; border-radius: 8px; text-align: center;">
                        <h3 style="color: #667eea;">API接口</h3>
                        <p style="font-size: 2rem; color: #333; margin: 10px 0;">50+</p>
                        <p style="color: #666;">RESTful API接口</p>
                    </div>
                    <div style="padding: 20px; background: #f8f9fa; border-radius: 8px; text-align: center;">
                        <h3 style="color: #28a745;">数据表</h3>
                        <p style="font-size: 2rem; color: #333; margin: 10px 0;">20+</p>
                        <p style="color: #666;">数据库表结构</p>
                    </div>
                    <div style="padding: 20px; background: #f8f9fa; border-radius: 8px; text-align: center;">
                        <h3 style="color: #ffc107;">功能模块</h3>
                        <p style="font-size: 2rem; color: #333; margin: 10px 0;">6</p>
                        <p style="color: #666;">核心业务模块</p>
                    </div>
                    <div style="padding: 20px; background: #f8f9fa; border-radius: 8px; text-align: center;">
                        <h3 style="color: #17a2b8;">权限控制</h3>
                        <p style="font-size: 2rem; color: #333; margin: 10px 0;">15</p>
                        <p style="color: #666;">细粒度权限</p>
                    </div>
                </div>
                
                <div style="margin-top: 30px; padding: 20px; background: #d4edda; border-radius: 8px; border-left: 4px solid #28a745;">
                    <h4 style="color: #155724; margin-bottom: 10px;">✅ 系统特性</h4>
                    <ul style="color: #155724; margin-left: 20px;">
                        <li>Spring Boot 3.2 + Spring Security</li>
                        <li>JWT认证 + 基于角色的权限控制</li>
                        <li>H2内存数据库 + JPA/Hibernate</li>
                        <li>Swagger API文档</li>
                        <li>完整的CRUD操作</li>
                        <li>数据验证和异常处理</li>
                    </ul>
                </div>
            `;
        }
        
        // 通用数据加载函数
        async function loadData(endpoint, title, icon) {
            const content = document.getElementById('content');
            content.innerHTML = `
                <h2 style="color: #333; margin-bottom: 20px;">${icon} ${title}</h2>
                <div style="text-align: center; padding: 40px;">
                    <div style="color: #666;">正在加载数据...</div>
                </div>
            `;
            
            try {
                const token = localStorage.getItem('token');
                const response = await fetch(`${API_BASE_URL}${endpoint}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    displayData(data.data, title, icon);
                } else {
                    content.innerHTML = `
                        <h2 style="color: #333; margin-bottom: 20px;">${icon} ${title}</h2>
                        <div style="padding: 20px; background: #f8d7da; border-radius: 8px; color: #721c24;">
                            <strong>加载失败:</strong> ${data.message || '未知错误'}
                        </div>
                    `;
                }
            } catch (error) {
                content.innerHTML = `
                    <h2 style="color: #333; margin-bottom: 20px;">${icon} ${title}</h2>
                    <div style="padding: 20px; background: #f8d7da; border-radius: 8px; color: #721c24;">
                        <strong>网络错误:</strong> ${error.message}
                    </div>
                `;
            }
        }
        
        // 显示数据
        function displayData(data, title, icon) {
            const content = document.getElementById('content');
            
            if (Array.isArray(data)) {
                if (data.length === 0) {
                    content.innerHTML = `
                        <h2 style="color: #333; margin-bottom: 20px;">${icon} ${title}</h2>
                        <div style="text-align: center; padding: 40px; color: #666;">
                            暂无数据
                        </div>
                    `;
                    return;
                }
                
                let tableHTML = `
                    <h2 style="color: #333; margin-bottom: 20px;">${icon} ${title}</h2>
                    <div style="overflow-x: auto;">
                        <table style="width: 100%; border-collapse: collapse; background: white;">
                            <thead>
                                <tr style="background: #f8f9fa;">
                `;
                
                // 生成表头
                const keys = Object.keys(data[0]);
                keys.forEach(key => {
                    tableHTML += `<th style="padding: 12px; border: 1px solid #dee2e6; text-align: left;">${key}</th>`;
                });
                
                tableHTML += `
                                </tr>
                            </thead>
                            <tbody>
                `;
                
                // 生成数据行
                data.forEach(item => {
                    tableHTML += '<tr>';
                    keys.forEach(key => {
                        let value = item[key];
                        if (value === null || value === undefined) {
                            value = '-';
                        } else if (typeof value === 'object') {
                            value = JSON.stringify(value);
                        }
                        tableHTML += `<td style="padding: 12px; border: 1px solid #dee2e6;">${value}</td>`;
                    });
                    tableHTML += '</tr>';
                });
                
                tableHTML += `
                            </tbody>
                        </table>
                    </div>
                    <div style="margin-top: 20px; padding: 15px; background: #d1ecf1; border-radius: 8px; color: #0c5460;">
                        <strong>数据统计:</strong> 共 ${data.length} 条记录
                    </div>
                `;
                
                content.innerHTML = tableHTML;
            } else {
                content.innerHTML = `
                    <h2 style="color: #333; margin-bottom: 20px;">${icon} ${title}</h2>
                    <pre style="background: #f8f9fa; padding: 20px; border-radius: 8px; overflow-x: auto;">${JSON.stringify(data, null, 2)}</pre>
                `;
            }
        }
        
        // 退出登录
        function logout() {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            location.reload();
        }
        
        // 页面加载时检查服务器状态
        window.addEventListener('load', () => {
            checkServerStatus();
        });
    </script>
</body>
</html>
