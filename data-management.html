<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据管理 - 猫舍管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            padding: 0 20px;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 70px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo {
            font-size: 24px;
            font-weight: 700;
            color: white;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo::before {
            content: '🐱';
            font-size: 28px;
        }

        .nav-links {
            display: flex;
            gap: 5px;
        }

        .nav-links a {
            color: rgba(255,255,255,0.9);
            text-decoration: none;
            padding: 10px 18px;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-links a:hover,
        .nav-links a.active {
            color: white;
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
            color: white;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-1px);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .page-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .page-title {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .page-title::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
        }

        .page-title h1 {
            color: #333;
            font-size: 36px;
            margin-bottom: 15px;
            font-weight: 700;
        }

        .page-title p {
            color: #666;
            font-size: 18px;
            max-width: 600px;
            margin: 0 auto;
        }

        .data-sections {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
        }

        .data-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .data-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--section-color, #667eea);
        }

        .data-section:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
        }

        .section-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 25px;
        }

        .section-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            background: var(--section-color, #667eea);
        }

        .section-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
        }

        .section-description {
            color: #666;
            margin-bottom: 25px;
            line-height: 1.6;
        }

        .upload-section { --section-color: #10b981; }
        .export-section { --section-color: #f59e0b; }
        .backup-section { --section-color: #8b5cf6; }
        .notification-section { --section-color: #ef4444; }

        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 15px;
            padding: 40px 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            margin-bottom: 20px;
        }

        .upload-area:hover,
        .upload-area.dragover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }

        .upload-icon {
            font-size: 48px;
            margin-bottom: 15px;
            color: #999;
        }

        .upload-text {
            color: #666;
            font-size: 16px;
            margin-bottom: 10px;
        }

        .upload-hint {
            color: #999;
            font-size: 14px;
        }

        .file-input {
            display: none;
        }

        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-align: center;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #ddd;
        }

        .btn-secondary:hover {
            background: #e9ecef;
            transform: translateY(-1px);
        }

        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(16, 185, 129, 0.4);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
        }

        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(245, 158, 11, 0.4);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            overflow: hidden;
            margin: 15px 0;
            display: none;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }

        .status-message {
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            font-size: 14px;
            display: none;
        }

        .status-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }

        .status-info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }

        .notification-settings {
            display: grid;
            gap: 15px;
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .setting-label {
            font-weight: 500;
            color: #333;
        }

        .setting-description {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }

        .toggle-switch {
            position: relative;
            width: 50px;
            height: 24px;
            background: #ddd;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .toggle-switch.active {
            background: #667eea;
        }

        .toggle-switch::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
        }

        .toggle-switch.active::before {
            transform: translateX(26px);
        }

        @media (max-width: 768px) {
            .data-sections {
                grid-template-columns: 1fr;
            }

            .page-title {
                padding: 30px 20px;
            }

            .page-title h1 {
                font-size: 28px;
            }

            .section-header {
                flex-direction: column;
                text-align: center;
                gap: 10px;
            }

            .upload-area {
                padding: 30px 15px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">猫舍管理系统</div>
            <div class="nav-links">
                <a href="/dashboard.html">仪表盘</a>
                <a href="/cats-management.html">猫咪管理</a>
                <a href="/customers-management.html">客户管理</a>
                <a href="/health-management.html">健康记录</a>
                <a href="/breeding-management.html">繁育管理</a>
                <a href="/financial-reports.html">财务报表</a>
                <a href="/data-management.html" class="active">数据管理</a>
            </div>
            <div class="user-info">
                <div class="user-avatar" id="userAvatar">A</div>
                <span id="userName">加载中...</span>
                <button class="logout-btn" onclick="logout()">退出</button>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="page-header">
            <div class="page-title">
                <h1>数据管理</h1>
                <p>文件上传、数据导入导出、系统备份和通知设置等高级功能</p>
            </div>
        </div>

        <div class="data-sections">
            <!-- 文件上传区域 -->
            <div class="data-section upload-section">
                <div class="section-header">
                    <div class="section-icon">📁</div>
                    <div class="section-title">文件上传</div>
                </div>
                <div class="section-description">
                    上传猫咪照片、健康证明、血统证书等文件。支持 JPG、PNG、PDF 格式，单个文件最大 10MB。
                </div>
                
                <div class="upload-area" onclick="document.getElementById('fileInput').click()">
                    <div class="upload-icon">📤</div>
                    <div class="upload-text">点击选择文件或拖拽文件到此处</div>
                    <div class="upload-hint">支持 JPG, PNG, PDF 格式，最大 10MB</div>
                </div>
                
                <input type="file" id="fileInput" class="file-input" multiple accept=".jpg,.jpeg,.png,.pdf">
                
                <div class="progress-bar" id="uploadProgress">
                    <div class="progress-fill"></div>
                </div>
                
                <div class="status-message" id="uploadStatus"></div>
                
                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="uploadFiles()">开始上传</button>
                    <button class="btn btn-secondary" onclick="clearFiles()">清空文件</button>
                </div>
            </div>

            <!-- 数据导出区域 -->
            <div class="data-section export-section">
                <div class="section-header">
                    <div class="section-icon">📊</div>
                    <div class="section-title">数据导出</div>
                </div>
                <div class="section-description">
                    导出猫咪信息、客户数据、健康记录等为 Excel 或 CSV 格式，便于数据分析和备份。
                </div>
                
                <div class="action-buttons">
                    <button class="btn btn-success" onclick="exportData('cats')">导出猫咪数据</button>
                    <button class="btn btn-success" onclick="exportData('customers')">导出客户数据</button>
                    <button class="btn btn-success" onclick="exportData('health')">导出健康记录</button>
                    <button class="btn btn-success" onclick="exportData('financial')">导出财务数据</button>
                    <button class="btn btn-primary" onclick="exportData('all')">导出全部数据</button>
                </div>
                
                <div class="status-message" id="exportStatus"></div>
            </div>

            <!-- 系统备份区域 -->
            <div class="data-section backup-section">
                <div class="section-header">
                    <div class="section-icon">💾</div>
                    <div class="section-title">系统备份</div>
                </div>
                <div class="section-description">
                    创建完整的系统数据备份，包括所有数据表和上传的文件。建议定期备份以确保数据安全。
                </div>
                
                <div class="action-buttons">
                    <button class="btn btn-warning" onclick="createBackup()">创建备份</button>
                    <button class="btn btn-secondary" onclick="viewBackups()">查看备份历史</button>
                    <button class="btn btn-secondary" onclick="restoreBackup()">恢复备份</button>
                </div>
                
                <div class="status-message" id="backupStatus"></div>
                
                <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 10px;">
                    <div style="font-weight: 500; color: #333; margin-bottom: 5px;">最近备份</div>
                    <div style="font-size: 14px; color: #666;" id="lastBackupInfo">暂无备份记录</div>
                </div>
            </div>

            <!-- 通知设置区域 -->
            <div class="data-section notification-section">
                <div class="section-header">
                    <div class="section-icon">🔔</div>
                    <div class="section-title">通知设置</div>
                </div>
                <div class="section-description">
                    配置系统通知，包括疫苗提醒、健康检查提醒、繁育周期提醒等。
                </div>
                
                <div class="notification-settings">
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">疫苗接种提醒</div>
                            <div class="setting-description">在疫苗到期前7天发送提醒</div>
                        </div>
                        <div class="toggle-switch active" onclick="toggleNotification(this, 'vaccine')"></div>
                    </div>
                    
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">健康检查提醒</div>
                            <div class="setting-description">定期健康检查提醒</div>
                        </div>
                        <div class="toggle-switch active" onclick="toggleNotification(this, 'health')"></div>
                    </div>
                    
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">繁育周期提醒</div>
                            <div class="setting-description">发情期和配对时间提醒</div>
                        </div>
                        <div class="toggle-switch" onclick="toggleNotification(this, 'breeding')"></div>
                    </div>
                    
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">库存不足提醒</div>
                            <div class="setting-description">猫粮、用品库存不足时提醒</div>
                        </div>
                        <div class="toggle-switch active" onclick="toggleNotification(this, 'inventory')"></div>
                    </div>
                </div>
                
                <div class="action-buttons" style="margin-top: 20px;">
                    <button class="btn btn-primary" onclick="saveNotificationSettings()">保存设置</button>
                    <button class="btn btn-secondary" onclick="testNotification()">测试通知</button>
                </div>
                
                <div class="status-message" id="notificationStatus"></div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080/api';
        let selectedFiles = [];
        let notificationSettings = {
            vaccine: true,
            health: true,
            breeding: false,
            inventory: true
        };
        
        // 检查登录状态
        function checkAuth() {
            const token = localStorage.getItem('token');
            const userInfo = localStorage.getItem('userInfo');
            
            if (!token || !userInfo) {
                window.location.href = '/login-test.html';
                return false;
            }
            
            try {
                const user = JSON.parse(userInfo);
                const userName = user.realName || user.username;
                document.getElementById('userName').textContent = userName;
                document.getElementById('userAvatar').textContent = userName.charAt(0).toUpperCase();
                return true;
            } catch (error) {
                logout();
                return false;
            }
        }
        
        // 退出登录
        function logout() {
            localStorage.removeItem('token');
            localStorage.removeItem('userInfo');
            window.location.href = '/login-test.html';
        }
        
        // 文件上传相关功能
        document.getElementById('fileInput').addEventListener('change', function(e) {
            selectedFiles = Array.from(e.target.files);
            updateUploadStatus();
        });
        
        // 拖拽上传
        const uploadArea = document.querySelector('.upload-area');
        
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
            selectedFiles = Array.from(e.dataTransfer.files);
            updateUploadStatus();
        });
        
        function updateUploadStatus() {
            const statusDiv = document.getElementById('uploadStatus');
            if (selectedFiles.length > 0) {
                statusDiv.className = 'status-message status-info';
                statusDiv.style.display = 'block';
                statusDiv.textContent = `已选择 ${selectedFiles.length} 个文件`;
            } else {
                statusDiv.style.display = 'none';
            }
        }
        
        function uploadFiles() {
            if (selectedFiles.length === 0) {
                showStatus('uploadStatus', 'error', '请先选择要上传的文件');
                return;
            }
            
            const progressBar = document.getElementById('uploadProgress');
            const progressFill = progressBar.querySelector('.progress-fill');
            
            progressBar.style.display = 'block';
            progressFill.style.width = '0%';
            
            // 模拟上传进度
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 20;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(interval);
                    setTimeout(() => {
                        progressBar.style.display = 'none';
                        showStatus('uploadStatus', 'success', `成功上传 ${selectedFiles.length} 个文件`);
                        selectedFiles = [];
                        document.getElementById('fileInput').value = '';
                    }, 500);
                }
                progressFill.style.width = progress + '%';
            }, 200);
        }
        
        function clearFiles() {
            selectedFiles = [];
            document.getElementById('fileInput').value = '';
            document.getElementById('uploadStatus').style.display = 'none';
        }
        
        // 数据导出功能
        function exportData(type) {
            const statusDiv = document.getElementById('exportStatus');
            
            showStatus('exportStatus', 'info', '正在准备导出数据...');
            
            // 模拟导出过程
            setTimeout(() => {
                const fileName = `${type}_data_${new Date().toISOString().split('T')[0]}.xlsx`;
                showStatus('exportStatus', 'success', `数据导出成功！文件名: ${fileName}`);
                
                // 这里可以触发实际的文件下载
                // const link = document.createElement('a');
                // link.href = 'data:text/plain;charset=utf-8,' + encodeURIComponent('模拟数据');
                // link.download = fileName;
                // link.click();
            }, 2000);
        }
        
        // 系统备份功能
        function createBackup() {
            const statusDiv = document.getElementById('backupStatus');
            
            showStatus('backupStatus', 'info', '正在创建系统备份...');
            
            setTimeout(() => {
                const backupName = `backup_${new Date().toISOString().replace(/[:.]/g, '-')}.sql`;
                showStatus('backupStatus', 'success', `备份创建成功！文件名: ${backupName}`);
                
                // 更新最近备份信息
                document.getElementById('lastBackupInfo').textContent = 
                    `${new Date().toLocaleString('zh-CN')} - ${backupName}`;
            }, 3000);
        }
        
        function viewBackups() {
            alert('备份历史功能开发中...\n\n将显示：\n• 备份文件列表\n• 备份时间\n• 文件大小\n• 备份状态');
        }
        
        function restoreBackup() {
            if (confirm('确定要恢复备份吗？此操作将覆盖当前数据，请确保已做好准备。')) {
                alert('备份恢复功能开发中...\n\n将包含：\n• 选择备份文件\n• 数据验证\n• 恢复进度显示\n• 恢复结果确认');
            }
        }
        
        // 通知设置功能
        function toggleNotification(element, type) {
            element.classList.toggle('active');
            notificationSettings[type] = element.classList.contains('active');
        }
        
        function saveNotificationSettings() {
            showStatus('notificationStatus', 'info', '正在保存通知设置...');
            
            setTimeout(() => {
                showStatus('notificationStatus', 'success', '通知设置保存成功！');
            }, 1000);
        }
        
        function testNotification() {
            showStatus('notificationStatus', 'info', '发送测试通知...');
            
            setTimeout(() => {
                showStatus('notificationStatus', 'success', '测试通知发送成功！请检查您的通知。');
            }, 1500);
        }
        
        // 显示状态消息
        function showStatus(elementId, type, message) {
            const statusDiv = document.getElementById(elementId);
            statusDiv.className = `status-message status-${type}`;
            statusDiv.textContent = message;
            statusDiv.style.display = 'block';
            
            if (type === 'success') {
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 5000);
            }
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            if (checkAuth()) {
                // 初始化页面
                console.log('数据管理页面加载完成');
            }
        });
    </script>
</body>
</html>
