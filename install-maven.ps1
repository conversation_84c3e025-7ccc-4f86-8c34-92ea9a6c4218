# Install Maven and Build Project
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Install Maven and Build Project" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if Maven is already installed
try {
    $mavenVersion = mvn --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Maven is already installed:" -ForegroundColor Green
        Write-Host $mavenVersion
        $mavenInstalled = $true
    } else {
        throw "Maven not found"
    }
} catch {
    Write-Host "Maven is not installed" -ForegroundColor Yellow
    $mavenInstalled = $false
}

if (-not $mavenInstalled) {
    Write-Host ""
    Write-Host "Installing Maven..." -ForegroundColor Cyan
    
    # Create tools directory
    $toolsDir = "C:\tools"
    if (-not (Test-Path $toolsDir)) {
        New-Item -ItemType Directory -Path $toolsDir -Force
        Write-Host "Created tools directory: $toolsDir" -ForegroundColor Green
    }
    
    # Download Maven
    $mavenVersion = "3.9.6"
    $mavenUrl = "https://archive.apache.org/dist/maven/maven-3/$mavenVersion/binaries/apache-maven-$mavenVersion-bin.zip"
    $mavenZip = "$toolsDir\apache-maven-$mavenVersion-bin.zip"
    $mavenDir = "$toolsDir\apache-maven-$mavenVersion"
    
    try {
        Write-Host "Downloading Maven from: $mavenUrl" -ForegroundColor Yellow
        Invoke-WebRequest -Uri $mavenUrl -OutFile $mavenZip
        Write-Host "Maven downloaded successfully" -ForegroundColor Green
        
        # Extract Maven
        Write-Host "Extracting Maven..." -ForegroundColor Yellow
        Expand-Archive -Path $mavenZip -DestinationPath $toolsDir -Force
        Write-Host "Maven extracted to: $mavenDir" -ForegroundColor Green
        
        # Add Maven to PATH for current session
        $env:PATH += ";$mavenDir\bin"
        Write-Host "Maven added to PATH for current session" -ForegroundColor Green
        
        # Test Maven installation
        $mavenTest = & "$mavenDir\bin\mvn.cmd" --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Maven installation successful!" -ForegroundColor Green
            Write-Host $mavenTest
            $mavenInstalled = $true
        } else {
            Write-Host "Maven installation failed" -ForegroundColor Red
            return
        }
        
        # Clean up
        Remove-Item $mavenZip -Force
        
    } catch {
        Write-Host "Failed to install Maven: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host ""
        Write-Host "Manual installation steps:" -ForegroundColor Yellow
        Write-Host "1. Download Maven from: https://maven.apache.org/download.cgi" -ForegroundColor Cyan
        Write-Host "2. Extract to C:\tools\apache-maven-3.9.6" -ForegroundColor Cyan
        Write-Host "3. Add C:\tools\apache-maven-3.9.6\bin to PATH" -ForegroundColor Cyan
        return
    }
}

if ($mavenInstalled) {
    Write-Host ""
    Write-Host "Building Spring Boot project..." -ForegroundColor Cyan
    
    # Set location to backend directory
    Set-Location "D:\噔噔\backend"
    Write-Host "Current directory: $(Get-Location)" -ForegroundColor Green
    
    # Clean and compile
    Write-Host ""
    Write-Host "Step 1: Clean project..." -ForegroundColor Yellow
    mvn clean
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Clean successful!" -ForegroundColor Green
        
        Write-Host ""
        Write-Host "Step 2: Compile project..." -ForegroundColor Yellow
        mvn compile
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Compile successful!" -ForegroundColor Green
            
            Write-Host ""
            Write-Host "Step 3: Package project..." -ForegroundColor Yellow
            mvn package -DskipTests
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "Package successful!" -ForegroundColor Green
                
                # Find the generated jar
                $jarFiles = Get-ChildItem "target" -Filter "*.jar" | Where-Object { $_.Name -notlike "*sources*" -and $_.Name -notlike "*javadoc*" }
                
                if ($jarFiles) {
                    $jarFile = $jarFiles[0]
                    Write-Host ""
                    Write-Host "Generated JAR: $($jarFile.Name)" -ForegroundColor Green
                    Write-Host "Size: $($jarFile.Length) bytes" -ForegroundColor Green
                    
                    Write-Host ""
                    Write-Host "Starting Spring Boot application..." -ForegroundColor Cyan
                    Write-Host "This may take a few minutes..." -ForegroundColor Yellow
                    Write-Host ""
                    
                    # Run the jar file
                    & "$env:JAVA_HOME\bin\java.exe" -jar "target\$($jarFile.Name)"
                    
                } else {
                    Write-Host "No JAR file found in target directory" -ForegroundColor Red
                    
                    Write-Host ""
                    Write-Host "Trying to run with Maven..." -ForegroundColor Yellow
                    mvn spring-boot:run
                }
            } else {
                Write-Host "Package failed" -ForegroundColor Red
            }
        } else {
            Write-Host "Compile failed" -ForegroundColor Red
        }
    } else {
        Write-Host "Clean failed" -ForegroundColor Red
    }
}

Write-Host ""
Read-Host "Press Enter to exit"
