<template>
  <div class="inventory-category-view">
    <div class="page-header">
      <h1>库存分类</h1>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        新增分类
      </el-button>
    </div>

    <el-table :data="categories" v-loading="loading">
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="name" label="分类名称" />
      <el-table-column prop="description" label="描述" />
      <el-table-column prop="itemCount" label="物品数量" />
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button size="small" type="primary" @click="editCategory(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="deleteCategory(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 创建/编辑对话框 -->
    <el-dialog v-model="showCreateDialog" title="库存分类" width="500px">
      <el-form :model="categoryForm" label-width="100px">
        <el-form-item label="分类名称">
          <el-input v-model="categoryForm.name" placeholder="输入分类名称" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="categoryForm.description" type="textarea" :rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveCategory">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

const loading = ref(false)
const showCreateDialog = ref(false)
const categories = ref([
  { id: 1, name: '食品', description: '猫粮、零食等', itemCount: 15 },
  { id: 2, name: '用品', description: '猫砂、玩具等', itemCount: 8 }
])

const categoryForm = reactive({
  name: '',
  description: ''
})

const editCategory = (category: any) => {
  Object.assign(categoryForm, category)
  showCreateDialog.value = true
}

const deleteCategory = (category: any) => {
  ElMessage.success('删除成功')
}

const saveCategory = () => {
  ElMessage.success('保存成功')
  showCreateDialog.value = false
}

onMounted(() => {
  loading.value = false
})
</script>

<style scoped>
.inventory-category-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
</style>
