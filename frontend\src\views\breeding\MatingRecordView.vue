<template>
  <div class="mating-record">
    <div class="page-header">
      <h1>配种记录</h1>
      <p>猫咪配种记录管理</p>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新增配种记录
        </el-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <el-form :model="filters" inline>
        <el-form-item label="母猫">
          <el-select v-model="filters.femaleId" placeholder="选择母猫" clearable style="width: 200px">
            <el-option
              v-for="cat in femaleCats"
              :key="cat.id"
              :label="cat.name"
              :value="cat.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="公猫">
          <el-select v-model="filters.maleId" placeholder="选择公猫" clearable style="width: 200px">
            <el-option
              v-for="cat in maleCats"
              :key="cat.id"
              :label="cat.name"
              :value="cat.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="配种日期">
          <el-date-picker
            v-model="filters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="filters.status" placeholder="选择状态" clearable style="width: 150px">
            <el-option label="成功" value="SUCCESS" />
            <el-option label="失败" value="FAILED" />
            <el-option label="待确认" value="PENDING" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="fetchMatingRecords">查询</el-button>
          <el-button @click="resetFilters">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 配种记录列表 -->
    <el-card class="records-card">
      <el-table :data="matingRecords" v-loading="loading" stripe>
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column label="母猫" width="150">
          <template #default="scope">
            <div class="cat-info">
              <el-avatar :src="scope.row.femalePhoto" :size="30">
                {{ scope.row.femaleName.charAt(0) }}
              </el-avatar>
              <span>{{ scope.row.femaleName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="公猫" width="150">
          <template #default="scope">
            <div class="cat-info">
              <el-avatar :src="scope.row.malePhoto" :size="30">
                {{ scope.row.maleName.charAt(0) }}
              </el-avatar>
              <span>{{ scope.row.maleName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="matingDate" label="配种日期" width="120">
          <template #default="scope">
            {{ formatDate(scope.row.matingDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="heatCycleDay" label="发情天数" width="100" />
        <el-table-column prop="method" label="配种方式" width="120">
          <template #default="scope">
            {{ getMatingMethodText(scope.row.method) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="successRate" label="成功率" width="100">
          <template #default="scope">
            <span v-if="scope.row.successRate">{{ scope.row.successRate }}%</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="viewDetail(scope.row)">详情</el-button>
            <el-button size="small" @click="editRecord(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="deleteRecord(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="fetchMatingRecords"
        @current-change="fetchMatingRecords"
      />
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingRecord ? '编辑配种记录' : '新增配种记录'"
      width="600px"
    >
      <MatingRecordForm
        :record="editingRecord"
        @submit="handleSubmit"
        @cancel="handleCancel"
      />
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="配种记录详情" width="800px">
      <MatingRecordDetail
        v-if="selectedRecord"
        :record="selectedRecord"
        @edit="editRecord"
        @delete="deleteRecord"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { breedingApi, catApi } from '@/api'
import MatingRecordForm from '@/components/breeding/MatingRecordForm.vue'
import MatingRecordDetail from '@/components/breeding/MatingRecordDetail.vue'
import type { MatingRecord, Cat } from '@/types'

const loading = ref(false)
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const editingRecord = ref<MatingRecord | null>(null)
const selectedRecord = ref<MatingRecord | null>(null)

const filters = ref({
  femaleId: null,
  maleId: null,
  dateRange: null,
  status: null
})

const pagination = ref({
  page: 1,
  size: 20,
  total: 0
})

const matingRecords = ref<MatingRecord[]>([])
const femaleCats = ref<Cat[]>([])
const maleCats = ref<Cat[]>([])

async function fetchMatingRecords() {
  try {
    loading.value = true
    const params = {
      page: pagination.value.page,
      size: pagination.value.size,
      ...filters.value
    }
    
    const response = await breedingApi.getMatingRecords(params)
    matingRecords.value = response.data
    pagination.value.total = response.total
  } catch (error) {
    ElMessage.error('获取配种记录失败')
  } finally {
    loading.value = false
  }
}

async function fetchCats() {
  try {
    const [females, males] = await Promise.all([
      catApi.getByGender('FEMALE'),
      catApi.getByGender('MALE')
    ])
    femaleCats.value = females
    maleCats.value = males
  } catch (error) {
    ElMessage.error('获取猫咪列表失败')
  }
}

function resetFilters() {
  filters.value = {
    femaleId: null,
    maleId: null,
    dateRange: null,
    status: null
  }
  pagination.value.page = 1
  fetchMatingRecords()
}

function viewDetail(record: MatingRecord) {
  selectedRecord.value = record
  showDetailDialog.value = true
}

function editRecord(record: MatingRecord) {
  editingRecord.value = { ...record }
  showCreateDialog.value = true
}

async function deleteRecord(id: number) {
  try {
    await ElMessageBox.confirm('确定要删除这条配种记录吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await breedingApi.deleteMatingRecord(id)
    ElMessage.success('删除成功')
    fetchMatingRecords()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

async function handleSubmit(recordData: Partial<MatingRecord>) {
  try {
    if (editingRecord.value) {
      await breedingApi.updateMatingRecord(editingRecord.value.id, recordData)
      ElMessage.success('更新成功')
    } else {
      await breedingApi.createMatingRecord(recordData)
      ElMessage.success('创建成功')
    }
    
    showCreateDialog.value = false
    editingRecord.value = null
    fetchMatingRecords()
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

function handleCancel() {
  showCreateDialog.value = false
  editingRecord.value = null
}

function formatDate(date: string | Date) {
  return new Date(date).toLocaleDateString('zh-CN')
}

function getMatingMethodText(method: string) {
  const methodMap: Record<string, string> = {
    'NATURAL': '自然交配',
    'ARTIFICIAL': '人工授精',
    'SUPERVISED': '监督交配'
  }
  return methodMap[method] || method
}

function getStatusType(status: string) {
  const typeMap: Record<string, string> = {
    'SUCCESS': 'success',
    'FAILED': 'danger',
    'PENDING': 'warning'
  }
  return typeMap[status] || 'info'
}

function getStatusText(status: string) {
  const textMap: Record<string, string> = {
    'SUCCESS': '成功',
    'FAILED': '失败',
    'PENDING': '待确认'
  }
  return textMap[status] || status
}

onMounted(() => {
  fetchMatingRecords()
  fetchCats()
})
</script>

<style scoped>
.mating-record {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #303133;
}

.page-header p {
  margin: 5px 0 0 0;
  color: #909399;
}

.filter-card,
.records-card {
  margin-bottom: 20px;
}

.cat-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.cat-info span {
  font-size: 14px;
  color: #303133;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .header-actions {
    width: 100%;
  }
}
</style>
