package com.cattery.dto.ai;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * AI训练反馈DTO
 */
@Data
public class AITrainingFeedbackDTO {
    
    /**
     * 反馈类型 (RECOGNITION_CORRECTION, HEALTH_PREDICTION_FEEDBACK, BEHAVIOR_ANALYSIS_FEEDBACK)
     */
    private String feedbackType;
    
    /**
     * 相关猫咪ID
     */
    private Long catId;
    
    /**
     * 原始预测结果
     */
    private String originalPrediction;
    
    /**
     * 正确结果
     */
    private String correctResult;
    
    /**
     * 反馈内容
     */
    private String feedbackContent;
    
    /**
     * 置信度评分 (1-5)
     */
    private Integer confidenceScore;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 反馈时间
     */
    private LocalDateTime feedbackTime;
    
    /**
     * 附加数据
     */
    private Map<String, Object> additionalData;
    
    /**
     * 图片URL（如果适用）
     */
    private String imageUrl;
    
    /**
     * 反馈状态 (PENDING, PROCESSED, REJECTED)
     */
    private String status;
    
    /**
     * 处理备注
     */
    private String processingNotes;
}
