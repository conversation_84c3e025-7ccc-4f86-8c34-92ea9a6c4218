<template>
  <div class="inventory-dashboard">
    <PageHeader
      title="库存管理"
      subtitle="库存物品监控与管理"
    >
      <template #actions>
        <el-button type="primary" @click="$router.push('/inventory/items')">
          <el-icon><Plus /></el-icon>
          添加物品
        </el-button>
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </template>
    </PageHeader>

    <!-- 库存统计卡片 -->
    <el-row :gutter="20" class="inventory-stats">
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <StatCard
          :value="inventoryStats.totalItems"
          label="库存物品总数"
          icon="Goods"
          :gradient="['#667eea', '#764ba2']"
        />
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <StatCard
          :value="inventoryStats.lowStockItems"
          label="低库存预警"
          icon="WarningFilled"
          :gradient="['#f56c6c', '#f78989']"
        />
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <StatCard
          :value="inventoryStats.totalValue"
          label="库存总价值"
          icon="Coin"
          format="currency"
          :precision="2"
          :gradient="['#43e97b', '#38f9d7']"
        />
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <StatCard
          :value="inventoryStats.categories"
          label="物品分类"
          icon="Management"
          :gradient="['#4facfe', '#00f2fe']"
        />
      </el-col>
    </el-row>

    <!-- 库存预警和最近记录 -->
    <el-row :gutter="20" class="inventory-alerts">
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>库存预警</span>
              <el-button type="text" @click="refreshAlerts">
                <el-icon><Refresh /></el-icon>
              </el-button>
            </div>
          </template>
          <div class="alert-list">
            <div 
              v-for="alert in stockAlerts" 
              :key="alert.id"
              class="alert-item"
              :class="alert.level"
            >
              <div class="alert-icon">
                <el-icon><WarningFilled /></el-icon>
              </div>
              <div class="alert-content">
                <div class="alert-title">{{ alert.itemName }}</div>
                <div class="alert-desc">
                  当前库存: {{ alert.currentStock }} {{ alert.unit }}
                  / 最低库存: {{ alert.minStock }} {{ alert.unit }}
                </div>
                <div class="alert-time">{{ formatDate(alert.alertTime) }}</div>
              </div>
              <div class="alert-actions">
                <el-button size="small" type="primary" @click="handleAlert(alert)">
                  补货
                </el-button>
              </div>
            </div>
            <div v-if="stockAlerts.length === 0" class="no-data">
              暂无库存预警
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 最近库存记录 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最近库存记录</span>
              <el-button type="text" @click="$router.push('/inventory/records')">
                查看全部
              </el-button>
            </div>
          </template>
          <div class="record-list">
            <div 
              v-for="record in recentRecords" 
              :key="record.id"
              class="record-item"
            >
              <div class="record-type">
                <el-tag 
                  :type="getRecordTypeColor(record.recordType)" 
                  size="small"
                >
                  {{ getRecordTypeText(record.recordType) }}
                </el-tag>
              </div>
              <div class="record-details">
                <div class="record-item-name">{{ record.itemName }}</div>
                <div class="record-desc">
                  数量: {{ record.quantity }} {{ record.unit }}
                  <span v-if="record.recordType === 'IN'">
                    (供应商: {{ record.supplier }})
                  </span>
                </div>
                <div class="record-time">{{ formatDate(record.recordTime) }}</div>
              </div>
            </div>
            <div v-if="recentRecords.length === 0" class="no-data">
              暂无库存记录
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 库存分析图表 -->
    <el-row :gutter="20" class="inventory-charts">
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <ChartCard
          title="库存分类分布"
          :loading="categoryChartLoading"
          :has-data="categoryChartData.length > 0"
          @refresh="loadCategoryChart"
        >
          <v-chart 
            :option="categoryChartOption" 
            style="height: 300px;"
          />
        </ChartCard>
      </el-col>

      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <ChartCard
          title="库存状态分布"
          :loading="statusChartLoading"
          :has-data="statusChartData.length > 0"
          @refresh="loadStatusChart"
        >
          <v-chart 
            :option="statusChartOption" 
            style="height: 300px;"
          />
        </ChartCard>
      </el-col>

      <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <ChartCard
          title="库存变化趋势"
          :loading="trendChartLoading"
          :has-data="trendChartData.length > 0"
          @refresh="loadTrendChart"
        >
          <template #actions>
            <el-date-picker
              v-model="dateRange"
              type="monthrange"
              range-separator="至"
              start-placeholder="开始月份"
              end-placeholder="结束月份"
              format="YYYY-MM"
              value-format="YYYY-MM"
              @change="loadTrendChart"
              size="small"
            />
          </template>
          <v-chart 
            :option="trendChartOption" 
            style="height: 400px;"
          />
        </ChartCard>
      </el-col>
    </el-row>

    <!-- 快速操作 -->
    <el-row :gutter="20" class="quick-actions">
      <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <el-card>
          <template #header>
            <span>快速操作</span>
          </template>
          <div class="action-buttons">
            <el-button type="primary" @click="$router.push('/inventory/items')">
              <el-icon><Plus /></el-icon>
              添加物品
            </el-button>
            <el-button type="success" @click="$router.push('/inventory/records')">
              <el-icon><DocumentCopy /></el-icon>
              库存记录
            </el-button>
            <el-button type="warning" @click="$router.push('/inventory/categories')">
              <el-icon><Management /></el-icon>
              分类管理
            </el-button>
            <el-button type="info" @click="$router.push('/inventory/feeding')">
              <el-icon><ShoppingCart /></el-icon>
              喂养管理
            </el-button>
            <el-button type="danger" @click="generateInventoryReport">
              <el-icon><Document /></el-icon>
              生成报告
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { PieChart, LineChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import {
  Plus,
  Refresh,
  WarningFilled,
  DocumentCopy,
  Management,
  ShoppingCart,
  Document
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { inventoryApi } from '@/api/inventory'
import dayjs from 'dayjs'

// 注册 ECharts 组件
use([
  CanvasRenderer,
  PieChart,
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

// 响应式数据
const inventoryStats = reactive({
  totalItems: 0,
  lowStockItems: 0,
  totalValue: 0,
  categories: 0
})

const stockAlerts = ref([])
const recentRecords = ref([])
const dateRange = ref<string[]>([])

const categoryChartLoading = ref(false)
const statusChartLoading = ref(false)
const trendChartLoading = ref(false)

const categoryChartData = ref([])
const statusChartData = ref([])
const trendChartData = ref([])

const categoryChartOption = ref({})
const statusChartOption = ref({})
const trendChartOption = ref({})

// 方法
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

const getRecordTypeColor = (type: string) => {
  const colorMap = {
    'IN': 'success',
    'OUT': 'warning',
    'ADJUST': 'info'
  }
  return colorMap[type] || ''
}

const getRecordTypeText = (type: string) => {
  const textMap = {
    'IN': '入库',
    'OUT': '出库',
    'ADJUST': '调整'
  }
  return textMap[type] || type
}

const loadInventoryStats = async () => {
  try {
    const response = await inventoryApi.getInventoryStats()
    Object.assign(inventoryStats, response.data)
  } catch (error) {
    console.error('加载库存统计失败:', error)
  }
}

const loadStockAlerts = async () => {
  try {
    const response = await inventoryApi.getStockAlerts({ limit: 10 })
    stockAlerts.value = response.data
  } catch (error) {
    console.error('加载库存预警失败:', error)
  }
}

const loadRecentRecords = async () => {
  try {
    const response = await inventoryApi.getRecentRecords({ limit: 10 })
    recentRecords.value = response.data
  } catch (error) {
    console.error('加载最近记录失败:', error)
  }
}

const loadCategoryChart = async () => {
  categoryChartLoading.value = true
  try {
    const response = await inventoryApi.getCategoryDistribution()
    categoryChartData.value = response.data
    
    categoryChartOption.value = {
      title: {
        text: '分类分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: '分类',
          type: 'pie',
          radius: '50%',
          data: categoryChartData.value,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
  } catch (error) {
    console.error('加载分类图表失败:', error)
  } finally {
    categoryChartLoading.value = false
  }
}

const loadStatusChart = async () => {
  statusChartLoading.value = true
  try {
    const response = await inventoryApi.getStatusDistribution()
    statusChartData.value = response.data
    
    statusChartOption.value = {
      title: {
        text: '库存状态',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: '状态',
          type: 'pie',
          radius: ['40%', '70%'],
          data: statusChartData.value
        }
      ]
    }
  } catch (error) {
    console.error('加载状态图表失败:', error)
  } finally {
    statusChartLoading.value = false
  }
}

const loadTrendChart = async () => {
  trendChartLoading.value = true
  try {
    const params = dateRange.value.length === 2 ? {
      startDate: dateRange.value[0] + '-01',
      endDate: dateRange.value[1] + '-31'
    } : {}
    
    const response = await inventoryApi.getInventoryTrend(params)
    trendChartData.value = response.data
    
    trendChartOption.value = {
      title: {
        text: '库存变化趋势',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['入库', '出库', '库存余量']
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: trendChartData.value.map((item: any) => item.date)
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '入库',
          type: 'line',
          data: trendChartData.value.map((item: any) => item.inStock)
        },
        {
          name: '出库',
          type: 'line',
          data: trendChartData.value.map((item: any) => item.outStock)
        },
        {
          name: '库存余量',
          type: 'line',
          data: trendChartData.value.map((item: any) => item.currentStock)
        }
      ]
    }
  } catch (error) {
    console.error('加载趋势图表失败:', error)
  } finally {
    trendChartLoading.value = false
  }
}

const refreshData = () => {
  loadInventoryStats()
  loadStockAlerts()
  loadRecentRecords()
  loadCategoryChart()
  loadStatusChart()
  loadTrendChart()
}

const refreshAlerts = () => {
  loadStockAlerts()
}

const handleAlert = (alert: any) => {
  // 处理库存预警
  ElMessage.success('已添加补货提醒')
}

const generateInventoryReport = () => {
  ElMessage.info('正在生成库存报告...')
}

// 初始化
onMounted(() => {
  // 设置默认日期范围为最近6个月
  const now = new Date()
  const endMonth = now.getFullYear() + '-' + String(now.getMonth() + 1).padStart(2, '0')
  const startMonth = now.getFullYear() + '-' + String(now.getMonth() - 5).padStart(2, '0')
  dateRange.value = [startMonth, endMonth]
  
  // 加载数据
  refreshData()
})
</script>

<style scoped lang="scss">
.inventory-dashboard {
  .inventory-stats {
    margin-bottom: 20px;
  }
  
  .inventory-alerts {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .alert-list {
      max-height: 400px;
      overflow-y: auto;
      
      .alert-item {
        display: flex;
        align-items: center;
        padding: 12px;
        border-radius: 8px;
        margin-bottom: 8px;
        border-left: 4px solid #e4e7ed;
        background-color: #fafafa;
        
        &.high {
          border-left-color: #f56c6c;
          background-color: #fef0f0;
        }
        
        &.medium {
          border-left-color: #e6a23c;
          background-color: #fdf6ec;
        }
        
        &.low {
          border-left-color: #409eff;
          background-color: #ecf5ff;
        }
        
        .alert-icon {
          margin-right: 12px;
          font-size: 20px;
          color: #f56c6c;
        }
        
        .alert-content {
          flex: 1;
          
          .alert-title {
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
          }
          
          .alert-desc {
            font-size: 14px;
            color: #606266;
            margin-bottom: 4px;
          }
          
          .alert-time {
            font-size: 12px;
            color: #909399;
          }
        }
        
        .alert-actions {
          margin-left: 12px;
        }
      }
    }
    
    .record-list {
      max-height: 400px;
      overflow-y: auto;
      
      .record-item {
        display: flex;
        align-items: center;
        padding: 12px;
        border-radius: 8px;
        margin-bottom: 8px;
        background-color: #f8f9fa;
        
        .record-type {
          margin-right: 16px;
        }
        
        .record-details {
          flex: 1;
          
          .record-item-name {
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
          }
          
          .record-desc {
            color: #606266;
            margin-bottom: 4px;
          }
          
          .record-time {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }
  }
  
  .inventory-charts {
    margin-bottom: 20px;
  }
  
  .quick-actions {
    .action-buttons {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      
      .el-button {
        flex: 1;
        min-width: 120px;
      }
    }
  }
  
  .no-data {
    text-align: center;
    color: #909399;
    padding: 20px;
    font-size: 14px;
  }
}

@media (max-width: 768px) {
  .inventory-dashboard {
    .quick-actions {
      .action-buttons {
        .el-button {
          min-width: 100px;
        }
      }
    }
  }
}
</style>
