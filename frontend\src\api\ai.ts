import request from '@/utils/request'
import type { 
  CatRecognitionResult, 
  BreedRecognitionResult, 
  HealthPredictionResult,
  BehaviorAnalysisResult,
  AIServiceStatus,
  AIModelInfo,
  AITrainingFeedback,
  AIUsageStatistics
} from '@/types/ai'

/**
 * AI功能相关API
 */
export const aiApi = {
  /**
   * 猫咪个体识别
   */
  recognizeCat(formData: FormData): Promise<CatRecognitionResult> {
    return request({
      url: '/api/ai/recognition/cat',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 品种识别
   */
  recognizeBreed(formData: FormData): Promise<BreedRecognitionResult> {
    return request({
      url: '/api/ai/recognition/breed',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 健康预测
   */
  predictHealth(catId: number, data: any): Promise<HealthPredictionResult> {
    return request({
      url: `/api/ai/prediction/health/${catId}`,
      method: 'post',
      data
    })
  },

  /**
   * 行为分析
   */
  analyzeBehavior(catId: number, data: any): Promise<BehaviorAnalysisResult> {
    return request({
      url: `/api/ai/analysis/behavior/${catId}`,
      method: 'post',
      data
    })
  },

  /**
   * 批量健康评估
   */
  batchHealthAssessment(catIds: number[]): Promise<any[]> {
    return request({
      url: '/api/ai/assessment/batch',
      method: 'post',
      data: catIds
    })
  },

  /**
   * 获取AI服务状态
   */
  getServiceStatus(): Promise<AIServiceStatus> {
    return request({
      url: '/api/ai/status',
      method: 'get'
    })
  },

  /**
   * 获取AI模型信息
   */
  getModelInfo(): Promise<AIModelInfo> {
    return request({
      url: '/api/ai/models/info',
      method: 'get'
    })
  },

  /**
   * 提交训练反馈
   */
  submitTrainingFeedback(feedback: AITrainingFeedback): Promise<void> {
    return request({
      url: '/api/ai/training/feedback',
      method: 'post',
      data: feedback
    })
  },

  /**
   * 获取AI使用统计
   */
  getUsageStatistics(startDate?: string, endDate?: string): Promise<AIUsageStatistics> {
    return request({
      url: '/api/ai/statistics',
      method: 'get',
      params: {
        startDate,
        endDate
      }
    })
  }
}
