<template>
  <div class="financial-category-view">
    <div class="page-header">
      <h1>财务分类</h1>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        新增分类
      </el-button>
    </div>

    <el-table :data="categories" v-loading="loading">
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="name" label="分类名称" />
      <el-table-column label="分类类型">
        <template #default="{ row }">
          <el-tag :type="getTypeColor(row.categoryType)">
            {{ getTypeText(row.categoryType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="description" label="描述" />
      <el-table-column label="状态">
        <template #default="{ row }">
          <el-tag :type="row.enabled ? 'success' : 'danger'">
            {{ row.enabled ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button size="small" type="primary" @click="editCategory(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="deleteCategory(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog v-model="showCreateDialog" title="财务分类" width="500px">
      <el-form :model="categoryForm" label-width="100px">
        <el-form-item label="分类名称">
          <el-input v-model="categoryForm.name" placeholder="输入分类名称" />
        </el-form-item>
        <el-form-item label="分类类型">
          <el-select v-model="categoryForm.categoryType" placeholder="选择分类类型">
            <el-option label="收入" value="INCOME" />
            <el-option label="支出" value="EXPENSE" />
            <el-option label="通用" value="BOTH" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="categoryForm.description" type="textarea" :rows="3" />
        </el-form-item>
        <el-form-item label="状态">
          <el-switch v-model="categoryForm.enabled" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveCategory">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

const loading = ref(false)
const showCreateDialog = ref(false)
const categories = ref([
  { id: 1, name: '猫咪销售', categoryType: 'INCOME', description: '猫咪销售收入', enabled: true },
  { id: 2, name: '饲料采购', categoryType: 'EXPENSE', description: '猫粮等饲料采购支出', enabled: true }
])

const categoryForm = reactive({
  name: '',
  categoryType: '',
  description: '',
  enabled: true
})

const editCategory = (category: any) => {
  Object.assign(categoryForm, category)
  showCreateDialog.value = true
}

const deleteCategory = (category: any) => ElMessage.success('删除成功')
const saveCategory = () => {
  ElMessage.success('保存成功')
  showCreateDialog.value = false
}

const getTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    INCOME: 'success',
    EXPENSE: 'warning',
    BOTH: 'primary'
  }
  return colors[type] || ''
}

const getTypeText = (type: string) => {
  const texts: Record<string, string> = {
    INCOME: '收入',
    EXPENSE: '支出',
    BOTH: '通用'
  }
  return texts[type] || type
}

onMounted(() => {
  loading.value = false
})
</script>

<style scoped>
.financial-category-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
</style>
