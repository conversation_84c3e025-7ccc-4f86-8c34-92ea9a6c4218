# Node.js 自动修复脚本 (PowerShell)
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Node.js 环境自动修复脚本" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 序号1: 检查当前状态
Write-Host "序号1: 检查当前Node.js状态..." -ForegroundColor Yellow
Write-Host ""

try {
    $nodeVersion = node --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Node.js版本: $nodeVersion" -ForegroundColor Green
    } else {
        Write-Host "❌ Node.js未安装或无法执行" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Node.js检查失败: $($_.Exception.Message)" -ForegroundColor Red
}

try {
    $npmVersion = npm --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ npm版本: $npmVersion" -ForegroundColor Green
    } else {
        Write-Host "❌ npm损坏或无法执行" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ npm检查失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# 序号2: 检查安装路径
Write-Host "序号2: 检查安装路径..." -ForegroundColor Yellow
Write-Host ""

try {
    $nodePath = Get-Command node -ErrorAction SilentlyContinue
    if ($nodePath) {
        Write-Host "Node.js路径: $($nodePath.Source)" -ForegroundColor Cyan
    } else {
        Write-Host "❌ 无法找到Node.js路径" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 路径检查失败" -ForegroundColor Red
}

# 序号3: 尝试修复npm
Write-Host "序号3: 尝试修复npm..." -ForegroundColor Yellow
Write-Host ""

Write-Host "方法1: 清理npm缓存..." -ForegroundColor Cyan
try {
    npm cache clean --force 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ npm缓存清理成功" -ForegroundColor Green
    } else {
        Write-Host "❌ npm缓存清理失败" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 无法清理npm缓存" -ForegroundColor Red
}

Write-Host ""
Write-Host "方法2: 重新安装npm..." -ForegroundColor Cyan
try {
    npm install -g npm@latest 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ npm重新安装成功" -ForegroundColor Green
    } else {
        Write-Host "❌ npm重新安装失败" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 无法重新安装npm" -ForegroundColor Red
}

# 序号4: 验证修复结果
Write-Host ""
Write-Host "序号4: 验证修复结果..." -ForegroundColor Yellow
Write-Host ""

try {
    $npmVersionAfter = npm --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ npm修复成功! 版本: $npmVersionAfter" -ForegroundColor Green
        $npmFixed = $true
    } else {
        Write-Host "❌ npm仍然无法工作" -ForegroundColor Red
        $npmFixed = $false
    }
} catch {
    Write-Host "❌ npm验证失败" -ForegroundColor Red
    $npmFixed = $false
}

# 序号5: 安装项目依赖
if ($npmFixed) {
    Write-Host ""
    Write-Host "序号5: 安装项目依赖..." -ForegroundColor Yellow
    Write-Host ""
    
    $frontendPath = Join-Path $PSScriptRoot "frontend"
    if (Test-Path $frontendPath) {
        Set-Location $frontendPath
        Write-Host "进入目录: $frontendPath" -ForegroundColor Cyan
        
        # 清理旧依赖
        if (Test-Path "node_modules") {
            Write-Host "清理旧的node_modules..." -ForegroundColor Cyan
            Remove-Item "node_modules" -Recurse -Force -ErrorAction SilentlyContinue
        }
        
        if (Test-Path "package-lock.json") {
            Write-Host "清理package-lock.json..." -ForegroundColor Cyan
            Remove-Item "package-lock.json" -Force -ErrorAction SilentlyContinue
        }
        
        # 安装依赖
        Write-Host "安装项目依赖..." -ForegroundColor Cyan
        try {
            npm install
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ 项目依赖安装成功!" -ForegroundColor Green
                
                # 序号6: 启动开发服务器
                Write-Host ""
                Write-Host "序号6: 启动开发服务器..." -ForegroundColor Yellow
                Write-Host ""
                Write-Host "即将启动开发服务器..." -ForegroundColor Cyan
                Write-Host "访问地址: http://localhost:3000" -ForegroundColor Green
                Write-Host "按 Ctrl+C 停止服务器" -ForegroundColor Yellow
                Write-Host ""
                
                Start-Sleep -Seconds 3
                npm run dev
            } else {
                Write-Host "❌ 项目依赖安装失败" -ForegroundColor Red
            }
        } catch {
            Write-Host "❌ 安装过程出错: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ 未找到frontend目录: $frontendPath" -ForegroundColor Red
    }
} else {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Red
    Write-Host "npm修复失败 - 建议重新安装Node.js" -ForegroundColor Red
    Write-Host "========================================" -ForegroundColor Red
    Write-Host ""
    Write-Host "请执行以下步骤:" -ForegroundColor Yellow
    Write-Host "1. 运行: install-nodejs.bat" -ForegroundColor Cyan
    Write-Host "2. 或访问: https://nodejs.org" -ForegroundColor Cyan
    Write-Host "3. 下载并安装最新LTS版本" -ForegroundColor Cyan
    Write-Host ""
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "修复脚本执行完成" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# 返回原目录
Set-Location $PSScriptRoot
