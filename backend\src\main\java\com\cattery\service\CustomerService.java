package com.cattery.service;

import com.cattery.entity.Customer;
import com.cattery.entity.CustomerInquiry;
import com.cattery.entity.AdoptionRecord;
import com.cattery.repository.CustomerRepository;
import com.cattery.repository.AdoptionRecordRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 客户管理服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class CustomerService {
    
    private final CustomerRepository customerRepository;
    private final AdoptionRecordRepository adoptionRecordRepository;
    
    /**
     * 创建客户
     */
    public Customer createCustomer(Customer customer) {
        log.info("创建客户: name={}, email={}", customer.getName(), customer.getEmail());
        
        // 检查邮箱是否已存在
        if (customerRepository.existsByEmail(customer.getEmail())) {
            throw new IllegalArgumentException("邮箱已存在: " + customer.getEmail());
        }
        
        // 检查电话是否已存在
        if (customer.getPhone() != null && customerRepository.existsByPhone(customer.getPhone())) {
            throw new IllegalArgumentException("电话号码已存在: " + customer.getPhone());
        }
        
        customer.setStatus(Customer.Status.ACTIVE);
        customer.setCreatedAt(LocalDateTime.now());
        customer.setUpdatedAt(LocalDateTime.now());
        
        return customerRepository.save(customer);
    }
    
    /**
     * 更新客户信息
     */
    public Customer updateCustomer(Long customerId, Customer updatedCustomer) {
        log.info("更新客户信息: customerId={}", customerId);
        
        Customer existingCustomer = customerRepository.findById(customerId)
            .orElseThrow(() -> new IllegalArgumentException("客户不存在"));
        
        // 检查邮箱唯一性（排除当前客户）
        if (!existingCustomer.getEmail().equals(updatedCustomer.getEmail()) &&
            customerRepository.existsByEmail(updatedCustomer.getEmail())) {
            throw new IllegalArgumentException("邮箱已存在: " + updatedCustomer.getEmail());
        }
        
        // 检查电话唯一性（排除当前客户）
        if (updatedCustomer.getPhone() != null &&
            !updatedCustomer.getPhone().equals(existingCustomer.getPhone()) &&
            customerRepository.existsByPhone(updatedCustomer.getPhone())) {
            throw new IllegalArgumentException("电话号码已存在: " + updatedCustomer.getPhone());
        }
        
        // 更新字段
        existingCustomer.setName(updatedCustomer.getName());
        existingCustomer.setEmail(updatedCustomer.getEmail());
        existingCustomer.setPhone(updatedCustomer.getPhone());
        existingCustomer.setAddress(updatedCustomer.getAddress());
        existingCustomer.setGender(updatedCustomer.getGender());
        existingCustomer.setAge(updatedCustomer.getAge());
        existingCustomer.setOccupation(updatedCustomer.getOccupation());
        existingCustomer.setMaritalStatus(updatedCustomer.getMaritalStatus());
        existingCustomer.setHousingType(updatedCustomer.getHousingType());
        existingCustomer.setHasPetExperience(updatedCustomer.getHasPetExperience());
        // 注释掉不存在的字段
        // existingCustomer.setExperience(updatedCustomer.getExperience());
        // existingCustomer.setPreferences(updatedCustomer.getPreferences());
        existingCustomer.setNotes(updatedCustomer.getNotes());
        existingCustomer.setUpdatedAt(LocalDateTime.now());
        
        return customerRepository.save(existingCustomer);
    }
    
    /**
     * 获取所有客户
     */
    @Transactional(readOnly = true)
    public Page<Customer> getAllCustomers(Pageable pageable) {
        log.debug("获取所有客户: pageable={}", pageable);
        return customerRepository.findAll(pageable);
    }
    
    /**
     * 根据ID获取客户
     */
    @Transactional(readOnly = true)
    public Optional<Customer> getCustomerById(Long customerId) {
        log.debug("根据ID获取客户: customerId={}", customerId);
        return customerRepository.findById(customerId);
    }
    
    /**
     * 根据邮箱获取客户
     */
    @Transactional(readOnly = true)
    public Optional<Customer> getCustomerByEmail(String email) {
        log.debug("根据邮箱获取客户: email={}", email);
        return customerRepository.findByEmail(email);
    }
    
    /**
     * 根据电话获取客户
     */
    @Transactional(readOnly = true)
    public Optional<Customer> getCustomerByPhone(String phone) {
        log.debug("根据电话获取客户: phone={}", phone);
        return customerRepository.findByPhone(phone);
    }
    
    /**
     * 搜索客户
     */
    @Transactional(readOnly = true)
    public Page<Customer> searchCustomers(String keyword, Pageable pageable) {
        log.debug("搜索客户: keyword={}, pageable={}", keyword, pageable);
        return customerRepository.findByNameContainingIgnoreCaseOrEmailContainingIgnoreCase(
            keyword, keyword, pageable);
    }
    
    /**
     * 根据状态获取客户
     */
    @Transactional(readOnly = true)
    public Page<Customer> getCustomersByStatus(Customer.Status status, Pageable pageable) {
        log.debug("根据状态获取客户: status={}, pageable={}", status, pageable);
        return customerRepository.findByStatus(status, pageable);
    }
    
    /**
     * 更新客户状态
     */
    public Customer updateCustomerStatus(Long customerId, Customer.Status status) {
        log.info("更新客户状态: customerId={}, status={}", customerId, status);
        
        Customer customer = customerRepository.findById(customerId)
            .orElseThrow(() -> new IllegalArgumentException("客户不存在"));
        
        customer.setStatus(status);
        customer.setUpdatedAt(LocalDateTime.now());
        
        return customerRepository.save(customer);
    }
    
    /**
     * 删除客户
     */
    public void deleteCustomer(Long customerId) {
        log.info("删除客户: customerId={}", customerId);
        
        Customer customer = customerRepository.findById(customerId)
            .orElseThrow(() -> new IllegalArgumentException("客户不存在"));
        
        // 检查是否有相关的领养记录
        List<AdoptionRecord> adoptionRecords = adoptionRecordRepository.findByCustomerId(customerId);
        if (!adoptionRecords.isEmpty()) {
            throw new IllegalStateException("无法删除客户，存在相关的领养记录");
        }
        
        customerRepository.delete(customer);
    }
    
    /**
     * 获取客户统计
     */
    @Transactional(readOnly = true)
    public long getCustomerCount() {
        log.debug("获取客户总数");
        return customerRepository.count();
    }
    
    /**
     * 获取活跃客户数量
     */
    @Transactional(readOnly = true)
    public long getActiveCustomerCount() {
        log.debug("获取活跃客户数量");
        return customerRepository.countByStatus(Customer.Status.ACTIVE);
    }
    
    /**
     * 获取新客户数量
     */
    @Transactional(readOnly = true)
    public long getNewCustomerCount(LocalDateTime startDate, LocalDateTime endDate) {
        log.debug("获取新客户数量: {} - {}", startDate, endDate);
        return customerRepository.countByCreatedAtBetween(startDate, endDate);
    }
    
    /**
     * 获取客户的领养记录
     */
    @Transactional(readOnly = true)
    public List<AdoptionRecord> getCustomerAdoptions(Long customerId) {
        log.debug("获取客户的领养记录: customerId={}", customerId);
        return adoptionRecordRepository.findByCustomerIdOrderByAdoptionDateDesc(customerId);
    }
}
