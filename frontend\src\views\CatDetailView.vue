<template>
  <div class="cat-detail">
    <el-page-header @back="$router.go(-1)" content="猫咪详情" />
    
    <div class="detail-container" v-if="cat">
      <el-row :gutter="20">
        <!-- 左侧：基本信息和照片 -->
        <el-col :span="12">
          <el-card class="info-card">
            <template #header>
              <div class="card-header">
                <span>基本信息</span>
                <el-button type="primary" size="small" @click="editMode = !editMode">
                  {{ editMode ? '取消编辑' : '编辑' }}
                </el-button>
              </div>
            </template>
            
            <el-form :model="catForm" :disabled="!editMode" label-width="100px">
              <el-form-item label="名字">
                <el-input v-model="catForm.name" />
              </el-form-item>
              <el-form-item label="品种">
                <el-select v-model="catForm.breedId" placeholder="请选择品种" style="width: 100%">
                  <el-option v-for="breed in breeds" :key="breed.id" :label="breed.name" :value="breed.id" />
                </el-select>
              </el-form-item>
              <el-form-item label="性别">
                <el-radio-group v-model="catForm.gender">
                  <el-radio value="MALE">雄性</el-radio>
                  <el-radio value="FEMALE">雌性</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="出生日期">
                <el-date-picker v-model="catForm.dateOfBirth" type="date" style="width: 100%" />
              </el-form-item>
              <el-form-item label="毛色">
                <el-input v-model="catForm.color" />
              </el-form-item>
              <el-form-item label="状态">
                <el-select v-model="catForm.status" style="width: 100%">
                  <el-option label="待领养" value="PENDING_ADOPTION" />
                  <el-option label="已预定" value="RESERVED" />
                  <el-option label="已领养" value="ADOPTED" />
                  <el-option label="非卖品" value="NOT_FOR_SALE" />
                </el-select>
              </el-form-item>
              <el-form-item label="描述">
                <el-input v-model="catForm.description" type="textarea" :rows="3" />
              </el-form-item>
              
              <el-form-item v-if="editMode">
                <el-button type="primary" @click="handleSave">保存</el-button>
                <el-button @click="resetForm">重置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
          
          <!-- 照片管理 -->
          <el-card class="photo-card">
            <template #header>
              <span>照片管理</span>
            </template>
            
            <el-upload
              class="photo-uploader"
              :action="uploadUrl"
              :headers="uploadHeaders"
              :show-file-list="false"
              :on-success="handlePhotoSuccess"
              :before-upload="beforePhotoUpload"
              accept="image/*"
            >
              <el-button type="primary" :icon="Plus">上传照片</el-button>
            </el-upload>
            
            <div class="photo-gallery" v-if="photos.length > 0">
              <div v-for="photo in photos" :key="photo.id" class="photo-item">
                <el-image
                  :src="photo.url"
                  :preview-src-list="photoUrls"
                  fit="cover"
                  class="photo-image"
                />
                <div class="photo-actions">
                  <el-button size="small" type="danger" @click="deletePhoto(photo.id)">删除</el-button>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <!-- 右侧：血统关系和健康记录 -->
        <el-col :span="12">
          <!-- 血统关系图 -->
          <el-card class="pedigree-card">
            <template #header>
              <span>血统关系</span>
            </template>
            <PedigreeChart :cat-id="cat.id" />
          </el-card>
          
          <!-- 健康记录摘要 -->
          <el-card class="health-card">
            <template #header>
              <div class="card-header">
                <span>健康记录</span>
                <el-button type="primary" size="small" @click="$router.push(`/health/${cat.id}`)">
                  查看详情
                </el-button>
              </div>
            </template>
            
            <div v-if="healthSummary">
              <el-descriptions :column="1" border>
                <el-descriptions-item label="最近体检">
                  {{ healthSummary.lastCheckup || '无记录' }}
                </el-descriptions-item>
                <el-descriptions-item label="疫苗状态">
                  <el-tag :type="healthSummary.vaccinationStatus === 'UP_TO_DATE' ? 'success' : 'warning'">
                    {{ getVaccinationStatusText(healthSummary.vaccinationStatus) }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="健康状态">
                  <el-tag :type="getHealthStatusType(healthSummary.healthStatus)">
                    {{ getHealthStatusText(healthSummary.healthStatus) }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="当前体重">
                  {{ healthSummary.currentWeight || '未记录' }}kg
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-card>
          
          <!-- 繁育记录摘要 -->
          <el-card class="breeding-card" v-if="cat.gender === 'FEMALE'">
            <template #header>
              <div class="card-header">
                <span>繁育记录</span>
                <el-button type="primary" size="small" @click="$router.push(`/breeding/${cat.id}`)">
                  查看详情
                </el-button>
              </div>
            </template>
            
            <div v-if="breedingSummary">
              <el-descriptions :column="1" border>
                <el-descriptions-item label="繁育次数">
                  {{ breedingSummary.breedingCount || 0 }}次
                </el-descriptions-item>
                <el-descriptions-item label="最近发情">
                  {{ breedingSummary.lastHeat || '无记录' }}
                </el-descriptions-item>
                <el-descriptions-item label="怀孕状态">
                  <el-tag v-if="breedingSummary.isPregnant" type="warning">怀孕中</el-tag>
                  <el-tag v-else type="info">未怀孕</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="总产仔数">
                  {{ breedingSummary.totalOffspring || 0 }}只
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <div v-else-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>
    
    <el-empty v-else description="未找到猫咪信息" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import { catApi, breedApi, healthApi, breedingApi } from '@/api';
import { useAuthStore } from '@/stores/auth';
import PedigreeChart from '@/components/PedigreeChart.vue';
import type { Cat, CatBreed, Photo, HealthSummary, BreedingSummary } from '@/types';

const route = useRoute();
const authStore = useAuthStore();

const cat = ref<Cat | null>(null);
const catForm = ref<Partial<Cat>>({});
const breeds = ref<CatBreed[]>([]);
const photos = ref<Photo[]>([]);
const healthSummary = ref<HealthSummary | null>(null);
const breedingSummary = ref<BreedingSummary | null>(null);
const loading = ref(true);
const editMode = ref(false);

const catId = computed(() => Number(route.params.id));

const uploadUrl = computed(() => `${import.meta.env.VITE_API_BASE_URL}/api/cats/${catId.value}/photos`);
const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${authStore.token}`
}));

const photoUrls = computed(() => photos.value.map(photo => photo.url));

async function fetchCatDetail() {
  try {
    loading.value = true;
    const [catData, breedsData, photosData] = await Promise.all([
      catApi.getById(catId.value),
      breedApi.getAll(),
      catApi.getPhotos(catId.value)
    ]);
    
    cat.value = catData;
    catForm.value = { ...catData };
    breeds.value = breedsData;
    photos.value = photosData;
    
    // 获取健康记录摘要
    try {
      healthSummary.value = await healthApi.getSummary(catId.value);
    } catch (error) {
      console.warn('获取健康记录失败:', error);
    }
    
    // 获取繁育记录摘要（仅母猫）
    if (catData.gender === 'FEMALE') {
      try {
        breedingSummary.value = await breedingApi.getSummary(catId.value);
      } catch (error) {
        console.warn('获取繁育记录失败:', error);
      }
    }
  } catch (error) {
    ElMessage.error('获取猫咪详情失败');
  } finally {
    loading.value = false;
  }
}

async function handleSave() {
  try {
    await catApi.update(catId.value, catForm.value);
    cat.value = { ...cat.value, ...catForm.value } as Cat;
    editMode.value = false;
    ElMessage.success('保存成功');
  } catch (error) {
    ElMessage.error('保存失败');
  }
}

function resetForm() {
  catForm.value = { ...cat.value };
}

function beforePhotoUpload(file: File) {
  const isImage = file.type.startsWith('image/');
  const isLt5M = file.size / 1024 / 1024 < 5;

  if (!isImage) {
    ElMessage.error('只能上传图片文件!');
    return false;
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!');
    return false;
  }
  return true;
}

function handlePhotoSuccess(response: any) {
  photos.value.push(response.data);
  ElMessage.success('照片上传成功');
}

async function deletePhoto(photoId: number) {
  try {
    await catApi.deletePhoto(catId.value, photoId);
    photos.value = photos.value.filter(photo => photo.id !== photoId);
    ElMessage.success('删除成功');
  } catch (error) {
    ElMessage.error('删除失败');
  }
}

function getVaccinationStatusText(status: string) {
  const statusMap: Record<string, string> = {
    'UP_TO_DATE': '已接种',
    'OVERDUE': '过期',
    'PARTIAL': '部分接种',
    'NONE': '未接种'
  };
  return statusMap[status] || status;
}

function getHealthStatusText(status: string) {
  const statusMap: Record<string, string> = {
    'EXCELLENT': '优秀',
    'GOOD': '良好',
    'FAIR': '一般',
    'POOR': '较差',
    'CRITICAL': '危急'
  };
  return statusMap[status] || status;
}

function getHealthStatusType(status: string) {
  const typeMap: Record<string, string> = {
    'EXCELLENT': 'success',
    'GOOD': 'success',
    'FAIR': 'warning',
    'POOR': 'danger',
    'CRITICAL': 'danger'
  };
  return typeMap[status] || '';
}

onMounted(() => {
  fetchCatDetail();
});
</script>

<style scoped>
.cat-detail {
  padding: 20px;
}

.detail-container {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-card,
.photo-card,
.pedigree-card,
.health-card,
.breeding-card {
  margin-bottom: 20px;
}

.photo-uploader {
  margin-bottom: 20px;
}

.photo-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 15px;
}

.photo-item {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}

.photo-image {
  width: 100%;
  height: 150px;
}

.photo-actions {
  position: absolute;
  top: 5px;
  right: 5px;
  opacity: 0;
  transition: opacity 0.3s;
}

.photo-item:hover .photo-actions {
  opacity: 1;
}

.loading-container {
  padding: 20px;
}
</style>
