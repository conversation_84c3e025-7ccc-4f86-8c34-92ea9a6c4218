<template>
  <div class="inventory-item-view">
    <div class="page-header">
      <h1>库存物品</h1>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        新增物品
      </el-button>
    </div>

    <div class="search-bar">
      <el-form :model="searchForm" inline>
        <el-form-item label="物品名称">
          <el-input v-model="searchForm.itemName" placeholder="输入物品名称" clearable />
        </el-form-item>
        <el-form-item label="分类">
          <el-select v-model="searchForm.categoryId" placeholder="选择分类" clearable>
            <el-option
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="库存状态">
          <el-select v-model="searchForm.stockStatus" placeholder="选择库存状态" clearable>
            <el-option label="充足" value="SUFFICIENT" />
            <el-option label="不足" value="LOW" />
            <el-option label="缺货" value="OUT_OF_STOCK" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchItems">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table :data="items" v-loading="loading">
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="itemName" label="物品名称" />
      <el-table-column prop="category.name" label="分类" />
      <el-table-column prop="currentStock" label="当前库存" />
      <el-table-column prop="unit" label="单位" />
      <el-table-column prop="minStockLevel" label="最低库存" />
      <el-table-column prop="unitPrice" label="单价" />
      <el-table-column label="库存状态">
        <template #default="{ row }">
          <el-tag :type="getStockStatusColor(row.stockStatus)">
            {{ getStockStatusText(row.stockStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button size="small" @click="viewItem(row)">查看</el-button>
          <el-button size="small" type="primary" @click="editItem(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="deleteItem(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingItem ? '编辑库存物品' : '新增库存物品'"
      width="600px"
    >
      <el-form :model="itemForm" :rules="formRules" ref="formRef" label-width="100px">
        <el-form-item label="物品名称" prop="itemName">
          <el-input v-model="itemForm.itemName" placeholder="输入物品名称" />
        </el-form-item>
        <el-form-item label="分类" prop="categoryId">
          <el-select v-model="itemForm.categoryId" placeholder="选择分类">
            <el-option
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="当前库存" prop="currentStock">
          <el-input-number v-model="itemForm.currentStock" :min="0" />
        </el-form-item>
        <el-form-item label="单位" prop="unit">
          <el-input v-model="itemForm.unit" placeholder="如：个、包、袋" />
        </el-form-item>
        <el-form-item label="最低库存" prop="minStockLevel">
          <el-input-number v-model="itemForm.minStockLevel" :min="0" />
        </el-form-item>
        <el-form-item label="单价" prop="unitPrice">
          <el-input-number v-model="itemForm.unitPrice" :min="0" :precision="2" />
        </el-form-item>
        <el-form-item label="供应商" prop="supplier">
          <el-input v-model="itemForm.supplier" placeholder="输入供应商名称" />
        </el-form-item>
        <el-form-item label="存储位置" prop="storageLocation">
          <el-input v-model="itemForm.storageLocation" placeholder="输入存储位置" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="itemForm.description"
            type="textarea"
            :rows="3"
            placeholder="输入物品描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveItem">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const showCreateDialog = ref(false)
const editingItem = ref(null)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

const items = ref([])
const categories = ref([])

const searchForm = reactive({
  itemName: '',
  categoryId: '',
  stockStatus: ''
})

const itemForm = reactive({
  itemName: '',
  categoryId: '',
  currentStock: 0,
  unit: '',
  minStockLevel: 0,
  unitPrice: 0,
  supplier: '',
  storageLocation: '',
  description: ''
})

const formRules = {
  itemName: [{ required: true, message: '请输入物品名称', trigger: 'blur' }],
  categoryId: [{ required: true, message: '请选择分类', trigger: 'change' }],
  currentStock: [{ required: true, message: '请输入当前库存', trigger: 'blur' }],
  unit: [{ required: true, message: '请输入单位', trigger: 'blur' }],
  minStockLevel: [{ required: true, message: '请输入最低库存', trigger: 'blur' }],
  unitPrice: [{ required: true, message: '请输入单价', trigger: 'blur' }]
}

// 方法
const loadItems = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    items.value = [
      {
        id: 1,
        itemName: '猫粮（成猫）',
        category: { id: 1, name: '食品' },
        currentStock: 50,
        unit: '袋',
        minStockLevel: 10,
        unitPrice: 89.90,
        supplier: '皇家宠物食品',
        storageLocation: 'A区-1号货架',
        stockStatus: 'SUFFICIENT',
        description: '成猫专用营养猫粮'
      }
    ]
    total.value = 1
  } catch (error) {
    ElMessage.error('加载库存物品失败')
  } finally {
    loading.value = false
  }
}

const loadCategories = async () => {
  try {
    // 模拟API调用
    categories.value = [
      { id: 1, name: '食品' },
      { id: 2, name: '用品' },
      { id: 3, name: '医疗' },
      { id: 4, name: '玩具' }
    ]
  } catch (error) {
    ElMessage.error('加载分类列表失败')
  }
}

const searchItems = () => {
  currentPage.value = 1
  loadItems()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    itemName: '',
    categoryId: '',
    stockStatus: ''
  })
  searchItems()
}

const viewItem = (item: any) => {
  ElMessage.info('查看库存物品功能开发中')
}

const editItem = (item: any) => {
  editingItem.value = item
  Object.assign(itemForm, item)
  showCreateDialog.value = true
}

const deleteItem = async (item: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这个库存物品吗？', '确认删除', {
      type: 'warning'
    })
    ElMessage.success('删除成功')
    loadItems()
  } catch {
    // 用户取消删除
  }
}

const saveItem = () => {
  ElMessage.success('保存成功')
  showCreateDialog.value = false
  loadItems()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  loadItems()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadItems()
}

const getStockStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    SUFFICIENT: 'success',
    LOW: 'warning',
    OUT_OF_STOCK: 'danger'
  }
  return colors[status] || ''
}

const getStockStatusText = (status: string) => {
  const texts: Record<string, string> = {
    SUFFICIENT: '充足',
    LOW: '不足',
    OUT_OF_STOCK: '缺货'
  }
  return texts[status] || status
}

onMounted(() => {
  loadItems()
  loadCategories()
})
</script>

<style scoped>
.inventory-item-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-bar {
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 4px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
