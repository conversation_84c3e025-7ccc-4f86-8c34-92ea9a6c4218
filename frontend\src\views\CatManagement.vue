<template>
  <div class="cat-management">
    <!-- 头部操作栏 -->
    <el-card class="header-card">
      <el-row :gutter="20" align="middle">
        <el-col :span="8">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索猫咪名称或品种"
            @keyup.enter="handleSearch"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="statusFilter" placeholder="状态筛选" @change="handleFilter">
            <el-option label="全部" value="" />
            <el-option label="可售" value="AVAILABLE" />
            <el-option label="已售" value="SOLD" />
            <el-option label="繁育中" value="BREEDING" />
            <el-option label="医疗中" value="MEDICAL" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-button type="primary" @click="showAddDialog">
            <el-icon><Plus /></el-icon>
            添加猫咪
          </el-button>
        </el-col>
        <el-col :span="8">
          <div class="stats">
            <el-statistic title="总数" :value="statistics.total" />
            <el-statistic title="可售" :value="statistics.available" />
            <el-statistic title="已售" :value="statistics.sold" />
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 猫咪列表 -->
    <el-card class="table-card">
      <el-table 
        :data="cats" 
        style="width: 100%" 
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="照片" width="80">
          <template #default="scope">
            <el-avatar 
              :src="scope.row.primaryPhoto || '/default-cat.jpg'" 
              shape="square" 
              size="large"
            />
          </template>
        </el-table-column>
        <el-table-column prop="name" label="姓名" sortable />
        <el-table-column prop="breed" label="品种" />
        <el-table-column label="性别">
          <template #default="scope">
            <el-tag :type="scope.row.gender === 'MALE' ? 'primary' : 'danger'">
              {{ scope.row.gender === 'MALE' ? '公' : '母' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="年龄">
          <template #default="scope">
            {{ calculateAge(scope.row.birthDate) }}岁
          </template>
        </el-table-column>
        <el-table-column prop="color" label="颜色" />
        <el-table-column label="状态">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="价格">
          <template #default="scope">
            <span v-if="scope.row.price">¥{{ scope.row.price }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="viewCat(scope.row)">查看</el-button>
            <el-button size="small" type="primary" @click="editCat(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="deleteCat(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑对话框 -->
    <CatDialog
      v-model="dialogVisible"
      :cat="currentCat"
      :is-edit="isEdit"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus } from '@element-plus/icons-vue'
import CatDialog from '@/components/CatDialog.vue'
import { catApi } from '@/api/cat'

// 响应式数据
const loading = ref(false)
const cats = ref([])
const searchKeyword = ref('')
const statusFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const selectedCats = ref([])

const dialogVisible = ref(false)
const currentCat = ref(null)
const isEdit = ref(false)

const statistics = reactive({
  total: 0,
  available: 0,
  sold: 0,
  breeding: 0
})

// 方法
const fetchCats = async () => {
  loading.value = true
  try {
    const response = await catApi.getCats({
      page: currentPage.value - 1,
      size: pageSize.value
    })
    if (response.success) {
      cats.value = response.data.content
      total.value = response.data.totalElements
    }
  } catch (error) {
    ElMessage.error('获取猫咪列表失败')
  } finally {
    loading.value = false
  }
}

const fetchStatistics = async () => {
  try {
    const response = await catApi.getStatistics()
    if (response.success) {
      Object.assign(statistics, response.data)
    }
  } catch (error) {
    console.error('获取统计信息失败', error)
  }
}

const handleSearch = () => {
  currentPage.value = 1
  fetchCats()
}

const handleFilter = () => {
  currentPage.value = 1
  fetchCats()
}

const showAddDialog = () => {
  currentCat.value = null
  isEdit.value = false
  dialogVisible.value = true
}

const editCat = (cat) => {
  currentCat.value = { ...cat }
  isEdit.value = true
  dialogVisible.value = true
}

const viewCat = (cat) => {
  // 跳转到猫咪详情页
  console.log('查看猫咪', cat)
}

const deleteCat = async (cat) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除猫咪 "${cat.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    const response = await catApi.deleteCat(cat.id)
    if (response.success) {
      ElMessage.success('删除成功')
      fetchCats()
      fetchStatistics()
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleDialogSuccess = () => {
  fetchCats()
  fetchStatistics()
}

const handleSelectionChange = (selection) => {
  selectedCats.value = selection
}

const handleSizeChange = (size) => {
  pageSize.value = size
  fetchCats()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchCats()
}

// 工具方法
const calculateAge = (birthDate) => {
  if (!birthDate) return 0
  const birth = new Date(birthDate)
  const now = new Date()
  return now.getFullYear() - birth.getFullYear()
}

const getStatusType = (status) => {
  const types = {
    AVAILABLE: 'success',
    RESERVED: 'warning',
    SOLD: 'info',
    BREEDING: 'primary',
    MEDICAL: 'danger',
    RETIRED: 'info'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    AVAILABLE: '可售',
    RESERVED: '预定',
    SOLD: '已售',
    BREEDING: '繁育中',
    MEDICAL: '医疗中',
    RETIRED: '退休'
  }
  return texts[status] || status
}

// 生命周期
onMounted(() => {
  fetchCats()
  fetchStatistics()
})
</script>

<style scoped>
.cat-management {
  padding: 20px;
}

.header-card {
  margin-bottom: 20px;
}

.stats {
  display: flex;
  gap: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>