package com.cattery.dto.report;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 健康预警DTO
 */
@Data
public class HealthAlertDTO {
    
    /**
     * 预警ID
     */
    private Long id;
    
    /**
     * 预警类型 (VACCINATION_DUE, HEALTH_CHECK_OVERDUE, MEDICATION_REMINDER, EMERGENCY)
     */
    private String alertType;
    
    /**
     * 预警级别 (CRITICAL, HIGH, MEDIUM, LOW)
     */
    private String alertLevel;
    
    /**
     * 预警标题
     */
    private String title;
    
    /**
     * 预警描述
     */
    private String description;
    
    /**
     * 相关猫咪ID
     */
    private Long catId;
    
    /**
     * 相关猫咪名称
     */
    private String catName;
    
    /**
     * 预警时间
     */
    private LocalDateTime alertTime;
    
    /**
     * 截止时间
     */
    private LocalDateTime dueDate;
    
    /**
     * 状态 (ACTIVE, ACKNOWLEDGED, RESOLVED, DISMISSED)
     */
    private String status;
    
    /**
     * 建议行动
     */
    private String recommendedAction;
    
    /**
     * 紧急程度
     */
    private Boolean isUrgent;
}
