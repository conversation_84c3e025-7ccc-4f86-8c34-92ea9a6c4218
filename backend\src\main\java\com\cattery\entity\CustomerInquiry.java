package com.cattery.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * 客户咨询实体类
 */
@Entity
@Table(name = "customer_inquiries")
@Data
@EqualsAndHashCode(callSuper = false)
public class CustomerInquiry {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 关联的客户
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "customer_id", nullable = false)
    private Customer customer;

    /**
     * 咨询类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "inquiry_type", nullable = false)
    private InquiryType inquiryType;

    /**
     * 咨询主题
     */
    @Column(nullable = false, length = 200)
    private String subject;

    /**
     * 咨询内容
     */
    @Column(nullable = false, columnDefinition = "TEXT")
    private String content;

    /**
     * 咨询状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "inquiry_status", nullable = false)
    private InquiryStatus inquiryStatus = InquiryStatus.PENDING;

    /**
     * 优先级
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private Priority priority = Priority.MEDIUM;

    /**
     * 处理人ID
     */
    @Column(name = "handler_id")
    private Long handlerId;

    /**
     * 回复内容
     */
    @Column(columnDefinition = "TEXT")
    private String reply;

    /**
     * 回复时间
     */
    @Column(name = "reply_time")
    private LocalDateTime replyTime;

    /**
     * 关联的猫咪（如果适用）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_id")
    private Cat cat;

    /**
     * 联系方式偏好
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "contact_preference")
    private ContactPreference contactPreference = ContactPreference.EMAIL;

    /**
     * 是否紧急
     */
    @Column(name = "is_urgent")
    private Boolean isUrgent = false;

    /**
     * 标签
     */
    @Column(length = 500)
    private String tags;

    /**
     * 附件路径
     */
    @Column(name = "attachment_path", length = 500)
    private String attachmentPath;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 创建者ID
     */
    @Column(name = "created_by")
    private Long createdBy;

    /**
     * 更新者ID
     */
    @Column(name = "updated_by")
    private Long updatedBy;

    /**
     * 咨询类型枚举
     */
    public enum InquiryType {
        ADOPTION("领养咨询"),
        BREEDING("繁育咨询"),
        HEALTH("健康咨询"),
        GENERAL("一般咨询"),
        COMPLAINT("投诉建议"),
        AFTER_SALES("售后服务");

        private final String description;

        InquiryType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 咨询状态枚举
     */
    public enum InquiryStatus {
        PENDING("待处理"),
        IN_PROGRESS("处理中"),
        RESOLVED("已解决"),
        CLOSED("已关闭");

        private final String description;

        InquiryStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 优先级枚举
     */
    public enum Priority {
        LOW("低"),
        MEDIUM("中"),
        HIGH("高"),
        URGENT("紧急");

        private final String description;

        Priority(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 联系方式偏好枚举
     */
    public enum ContactPreference {
        EMAIL("邮箱"),
        PHONE("电话"),
        SMS("短信"),
        WECHAT("微信");

        private final String description;

        ContactPreference(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
