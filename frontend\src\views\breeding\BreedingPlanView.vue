<template>
  <div class="breeding-plan-view">
    <div class="page-header">
      <h1>繁育计划</h1>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        新增繁育计划
      </el-button>
    </div>

    <div class="search-bar">
      <el-form :model="searchForm" inline>
        <el-form-item label="计划名称">
          <el-input v-model="searchForm.planName" placeholder="输入计划名称" clearable />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
            <el-option label="草稿" value="DRAFT" />
            <el-option label="活跃" value="ACTIVE" />
            <el-option label="完成" value="COMPLETED" />
            <el-option label="取消" value="CANCELLED" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchPlans">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table :data="breedingPlans" v-loading="loading">
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="planName" label="计划名称" />
      <el-table-column prop="targetBreed" label="目标品种" />
      <el-table-column prop="startDate" label="开始日期" />
      <el-table-column prop="endDate" label="结束日期" />
      <el-table-column label="状态">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="expectedLitters" label="预期窝数" />
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button size="small" @click="viewPlan(row)">查看</el-button>
          <el-button size="small" type="primary" @click="editPlan(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="deletePlan(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingPlan ? '编辑繁育计划' : '新增繁育计划'"
      width="600px"
    >
      <el-form :model="planForm" :rules="formRules" ref="formRef" label-width="100px">
        <el-form-item label="计划名称" prop="planName">
          <el-input v-model="planForm.planName" placeholder="输入计划名称" />
        </el-form-item>
        <el-form-item label="目标品种" prop="targetBreed">
          <el-input v-model="planForm.targetBreed" placeholder="输入目标品种" />
        </el-form-item>
        <el-form-item label="开始日期" prop="startDate">
          <el-date-picker
            v-model="planForm.startDate"
            type="date"
            placeholder="选择开始日期"
          />
        </el-form-item>
        <el-form-item label="结束日期" prop="endDate">
          <el-date-picker
            v-model="planForm.endDate"
            type="date"
            placeholder="选择结束日期"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="planForm.status" placeholder="选择状态">
            <el-option label="草稿" value="DRAFT" />
            <el-option label="活跃" value="ACTIVE" />
            <el-option label="完成" value="COMPLETED" />
            <el-option label="取消" value="CANCELLED" />
          </el-select>
        </el-form-item>
        <el-form-item label="预期窝数" prop="expectedLitters">
          <el-input-number v-model="planForm.expectedLitters" :min="1" :max="50" />
        </el-form-item>
        <el-form-item label="计划描述" prop="description">
          <el-input
            v-model="planForm.description"
            type="textarea"
            :rows="3"
            placeholder="输入计划描述"
          />
        </el-form-item>
        <el-form-item label="备注" prop="notes">
          <el-input
            v-model="planForm.notes"
            type="textarea"
            :rows="3"
            placeholder="输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="savePlan">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const showCreateDialog = ref(false)
const editingPlan = ref(null)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

const breedingPlans = ref([])

const searchForm = reactive({
  planName: '',
  status: ''
})

const planForm = reactive({
  planName: '',
  targetBreed: '',
  startDate: '',
  endDate: '',
  status: 'DRAFT',
  expectedLitters: 1,
  description: '',
  notes: ''
})

const formRules = {
  planName: [{ required: true, message: '请输入计划名称', trigger: 'blur' }],
  targetBreed: [{ required: true, message: '请输入目标品种', trigger: 'blur' }],
  startDate: [{ required: true, message: '请选择开始日期', trigger: 'change' }],
  endDate: [{ required: true, message: '请选择结束日期', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

// 方法
const loadBreedingPlans = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    breedingPlans.value = [
      {
        id: 1,
        planName: '2024年春季繁育计划',
        targetBreed: '英国短毛猫',
        startDate: '2024-03-01',
        endDate: '2024-06-30',
        status: 'ACTIVE',
        expectedLitters: 3,
        description: '专注于培育优质英国短毛猫',
        notes: '重点关注毛色和体型'
      }
    ]
    total.value = 1
  } catch (error) {
    ElMessage.error('加载繁育计划失败')
  } finally {
    loading.value = false
  }
}

const searchPlans = () => {
  currentPage.value = 1
  loadBreedingPlans()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    planName: '',
    status: ''
  })
  searchPlans()
}

const viewPlan = (plan: any) => {
  ElMessage.info('查看繁育计划功能开发中')
}

const editPlan = (plan: any) => {
  editingPlan.value = plan
  Object.assign(planForm, plan)
  showCreateDialog.value = true
}

const deletePlan = async (plan: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这个繁育计划吗？', '确认删除', {
      type: 'warning'
    })
    ElMessage.success('删除成功')
    loadBreedingPlans()
  } catch {
    // 用户取消删除
  }
}

const savePlan = () => {
  ElMessage.success('保存成功')
  showCreateDialog.value = false
  loadBreedingPlans()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  loadBreedingPlans()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadBreedingPlans()
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    DRAFT: 'info',
    ACTIVE: 'success',
    COMPLETED: '',
    CANCELLED: 'danger'
  }
  return types[status] || ''
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    DRAFT: '草稿',
    ACTIVE: '活跃',
    COMPLETED: '完成',
    CANCELLED: '取消'
  }
  return texts[status] || status
}

onMounted(() => {
  loadBreedingPlans()
})
</script>

<style scoped>
.breeding-plan-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-bar {
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 4px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
