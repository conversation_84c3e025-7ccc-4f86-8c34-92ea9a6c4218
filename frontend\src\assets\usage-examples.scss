// 使用示例 - 展示如何正确使用兼容性 Mixins
@import '../styles/variables';

/* 使用示例：表单元素 */
.example-form {
  .form-input {
    @include appearance(none);
    @include text-size-adjust(100%);
    border: 1px solid $border-base;
    border-radius: $border-radius-base;
    padding: $spacing-sm $spacing-md;
    font-size: $font-size-base;
    
    &:focus {
      border-color: $primary-color;
      outline: none;
    }
  }
  
  .form-button {
    @include appearance(none);
    @include performance-animation(0.2s, ease);
    background: $primary-color;
    color: white;
    border: none;
    border-radius: $border-radius-base;
    padding: $spacing-sm $spacing-md;
    cursor: pointer;
    
    &:hover {
      transform: translateY(-1px);
    }
    
    @include respect-user-preferences;
  }
}

/* 使用示例：图片元素 */
.example-gallery {
  .gallery-image {
    @include image-rendering(crisp-edges);
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: $border-radius-base;
  }
  
  .gallery-thumbnail {
    @include image-rendering(crisp-edges);
    width: 64px;
    height: 64px;
    object-fit: cover;
    border-radius: $border-radius-circle;
  }
}

/* 使用示例：滚动容器 */
.example-scroll {
  .scroll-list {
    @include scrollbar-style(6px, #f1f1f1, $primary-color);
    max-height: 300px;
    overflow-y: auto;
    padding: $spacing-md;
    
    .list-item {
      padding: $spacing-sm;
      border-bottom: 1px solid $border-lighter;
      
      &:last-child {
        border-bottom: none;
      }
    }
  }
}

/* 使用示例：遮罩图标 */
.example-icons {
  .icon {
    @include mask(url('/icons/star.svg'), contain);
    background: $primary-color;
    width: 24px;
    height: 24px;
    display: inline-block;
    
    &.success {
      background: $success-color;
    }
    
    &.warning {
      background: $warning-color;
    }
    
    &.danger {
      background: $danger-color;
    }
  }
}

/* 使用示例：动画卡片 */
.example-cards {
  .card {
    @include performance-animation(0.3s, ease);
    @include respect-user-preferences;
    background: $bg-color;
    border: 1px solid $border-light;
    border-radius: $border-radius-base;
    padding: $spacing-lg;
    box-shadow: $box-shadow-base;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: $box-shadow-light;
    }
    
    .card-image {
      @include image-rendering(crisp-edges);
      width: 100%;
      height: 200px;
      object-fit: cover;
      border-radius: $border-radius-base;
      margin-bottom: $spacing-md;
    }
    
    .card-title {
      @include text-size-adjust(100%);
      font-size: $font-size-large;
      font-weight: 600;
      margin-bottom: $spacing-sm;
    }
    
    .card-content {
      @include text-size-adjust(100%);
      color: $text-regular;
      line-height: 1.6;
    }
  }
}

/* 使用示例：移动端优化 */
.example-mobile {
  @include safe-area(padding, 1rem);
  
  .mobile-button {
    @include touch-optimization;
    @include appearance(none);
    background: $primary-color;
    color: white;
    border: none;
    border-radius: $border-radius-base;
    padding: $spacing-md;
    font-size: $font-size-base;
    cursor: pointer;
  }
  
  .mobile-nav {
    @include safe-area(padding, 0.5rem);
    background: $bg-color;
    border-bottom: 1px solid $border-light;
    
    .nav-item {
      @include touch-optimization;
      padding: $spacing-md;
      text-decoration: none;
      color: $text-primary;
      display: block;
      
      &:hover {
        background: $bg-page;
      }
    }
  }
}

/* 使用示例：主题切换 */
.example-theme {
  .theme-card {
    @include dark-mode($bg-color, #2d2d2d);
    @include performance-animation();
    border: 1px solid $border-light;
    border-radius: $border-radius-base;
    padding: $spacing-lg;
    
    .theme-text {
      @include dark-mode($text-primary, #ffffff);
      margin-bottom: $spacing-md;
    }
    
    .theme-button {
      @include appearance(none);
      @include dark-mode($primary-color, #5dade2);
      color: white;
      border: none;
      border-radius: $border-radius-base;
      padding: $spacing-sm $spacing-md;
      cursor: pointer;
    }
  }
}

/* 使用示例：响应式布局 */
.example-responsive {
  @include performance-animation();
  display: grid;
  grid-template-columns: 1fr;
  gap: $spacing-md;
  
  @media (min-width: $breakpoint-sm) {
    grid-template-columns: 1fr 1fr;
  }
  
  @media (min-width: $breakpoint-lg) {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .responsive-item {
    @include performance-animation();
    @include respect-user-preferences;
    background: $bg-color;
    border-radius: $border-radius-base;
    padding: $spacing-md;
    
    &:hover {
      transform: scale(1.02);
    }
  }
}

/* 使用示例：打印优化 */
.example-print {
  @include print-optimization;
  
  .print-content {
    padding: $spacing-lg;
    
    .no-print {
      @media print {
        display: none !important;
      }
    }
    
    .print-only {
      display: none;
      
      @media print {
        display: block !important;
      }
    }
  }
}

/* 使用示例：浏览器特定优化 */
.example-browser {
  // Safari 特定优化
  @include browser-specific(safari) {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  // Firefox 特定优化
  @include browser-specific(firefox) {
    -moz-font-feature-settings: "kern" 1;
  }
  
  // Chrome 特定优化
  @include browser-specific(chrome) {
    font-feature-settings: "kern" 1, "liga" 1;
  }
  
  .browser-text {
    font-family: $font-family;
    font-size: $font-size-base;
    line-height: 1.6;
  }
}
