package com.cattery.dto.report;

import lombok.Data;
import java.util.Map;

/**
 * 疫苗接种统计DTO
 */
@Data
public class VaccinationStatisticsDTO {
    
    /**
     * 总疫苗接种次数
     */
    private Long totalVaccinations;
    
    /**
     * 完全接种的猫咪数量
     */
    private Long fullyVaccinatedCats;
    
    /**
     * 部分接种的猫咪数量
     */
    private Long partiallyVaccinatedCats;
    
    /**
     * 未接种的猫咪数量
     */
    private Long unvaccinatedCats;
    
    /**
     * 按疫苗类型统计
     */
    private Map<String, Long> vaccinationsByType;
    
    /**
     * 按年龄段的接种率
     */
    private Map<String, Double> vaccinationRateByAge;
    
    /**
     * 即将到期的疫苗数量
     */
    private Long upcomingVaccinations;
    
    /**
     * 过期疫苗数量
     */
    private Long overdueVaccinations;
    
    /**
     * 疫苗接种合规率
     */
    private Double complianceRate;
}
