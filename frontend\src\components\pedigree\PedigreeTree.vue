<template>
  <div class="pedigree-tree">
    <div class="tree-controls">
      <el-button-group>
        <el-button :icon="ZoomIn" @click="zoomIn" :disabled="zoom >= 1.5">放大</el-button>
        <el-button :icon="ZoomOut" @click="zoomOut" :disabled="zoom <= 0.5">缩小</el-button>
        <el-button :icon="RefreshRight" @click="resetZoom">重置</el-button>
      </el-button-group>
      
      <el-select v-model="generations" placeholder="显示代数" @change="updateGenerations">
        <el-option :value="2" label="2代" />
        <el-option :value="3" label="3代" />
        <el-option :value="4" label="4代" />
        <el-option :value="5" label="5代" />
      </el-select>
      
      <el-button :icon="Printer" @click="printPedigree">打印谱系图</el-button>
    </div>
    
    <div class="tree-container" ref="treeContainer">
      <div 
        class="tree-content" 
        :style="{ transform: `scale(${zoom})`, transformOrigin: 'center top' }"
      >
        <div class="generation-row" v-for="(gen, genIndex) in visibleGenerations" :key="genIndex">
          <div 
            class="generation-label" 
            :style="{ width: `${100 / Math.pow(2, genIndex)}%` }"
          >
            第{{ genIndex + 1 }}代
          </div>
          
          <div class="generation-cats">
            <div 
              v-for="(cat, catIndex) in gen" 
              :key="catIndex"
              class="cat-node-wrapper"
              :style="{ width: `${100 / Math.pow(2, genIndex)}%` }"
            >
              <cat-node 
                :cat="cat" 
                :generation="genIndex + 1"
                @click="handleCatClick(cat)"
              />
              
              <!-- 连接线 -->
              <div 
                v-if="genIndex < visibleGenerations.length - 1" 
                class="connector"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 猫咪详情对话框 -->
    <el-dialog
      v-model="catDialogVisible"
      :title="selectedCat ? selectedCat.name + ' 详情' : '猫咪详情'"
      width="50%"
      append-to-body
    >
      <div v-if="selectedCat" class="cat-details">
        <div class="cat-header">
          <div class="cat-avatar">
            <el-avatar 
              :size="100" 
              :src="selectedCat.primaryPhoto || '/placeholder-cat.jpg'" 
              fit="cover"
            />
          </div>
          <div class="cat-info">
            <h2>{{ selectedCat.name }}</h2>
            <div class="cat-meta">
              <el-tag>{{ selectedCat.breed }}</el-tag>
              <el-tag type="success">{{ selectedCat.gender === 'MALE' ? '公猫' : '母猫' }}</el-tag>
              <el-tag type="warning">{{ formatCatStatus(selectedCat.status) }}</el-tag>
            </div>
            <div class="cat-attributes">
              <div class="attribute">
                <span class="label">出生日期:</span>
                <span class="value">{{ formatDate(selectedCat.birthDate) }}</span>
              </div>
              <div class="attribute">
                <span class="label">颜色:</span>
                <span class="value">{{ selectedCat.color }}</span>
              </div>
              <div class="attribute">
                <span class="label">体重:</span>
                <span class="value">{{ selectedCat.weight }} kg</span>
              </div>
              <div class="attribute">
                <span class="label">价格:</span>
                <span class="value">¥{{ selectedCat.price }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <el-divider />
        
        <div class="cat-description">
          <h3>描述</h3>
          <p>{{ selectedCat.description || '暂无描述' }}</p>
        </div>
        
        <div class="cat-actions">
          <el-button type="primary" @click="viewCatDetails(selectedCat)">查看完整信息</el-button>
          <el-button @click="catDialogVisible = false">关闭</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ZoomIn, ZoomOut, RefreshRight, Printer } from '@element-plus/icons-vue'
import CatNode from './CatNode.vue'
import { formatDate } from '@/utils'
import type { Cat } from '@/types/api'

interface Props {
  rootCat: Cat
  pedigreeData: Record<string, Cat>
}

const props = defineProps<Props>()

const router = useRouter()
const treeContainer = ref<HTMLElement | null>(null)
const zoom = ref(1)
const generations = ref(3)
const catDialogVisible = ref(false)
const selectedCat = ref<Cat | null>(null)

// 计算属性
const visibleGenerations = computed(() => {
  const result: Cat[][] = []
  
  // 第一代（根猫咪）
  result.push([props.rootCat])
  
  // 第二代及以上
  for (let gen = 1; gen < generations.value; gen++) {
    const prevGen = result[gen - 1]
    const currentGen: Cat[] = []
    
    for (const cat of prevGen) {
      // 父亲
      if (cat.fatherInfo) {
        const fatherId = extractCatId(cat.fatherInfo)
        const father = fatherId ? props.pedigreeData[fatherId] : createUnknownCat('父亲', 'MALE')
        currentGen.push(father)
      } else {
        currentGen.push(createUnknownCat('父亲', 'MALE'))
      }
      
      // 母亲
      if (cat.motherInfo) {
        const motherId = extractCatId(cat.motherInfo)
        const mother = motherId ? props.pedigreeData[motherId] : createUnknownCat('母亲', 'FEMALE')
        currentGen.push(mother)
      } else {
        currentGen.push(createUnknownCat('母亲', 'FEMALE'))
      }
    }
    
    result.push(currentGen)
  }
  
  return result
})

// 方法
const zoomIn = () => {
  zoom.value = Math.min(1.5, zoom.value + 0.1)
}

const zoomOut = () => {
  zoom.value = Math.max(0.5, zoom.value - 0.1)
}

const resetZoom = () => {
  zoom.value = 1
}

const updateGenerations = (value: number) => {
  generations.value = value
}

const printPedigree = () => {
  window.print()
}

const handleCatClick = (cat: Cat) => {
  if (cat.id) {
    selectedCat.value = cat
    catDialogVisible.value = true
  }
}

const viewCatDetails = (cat: Cat) => {
  if (cat.id) {
    router.push(`/cats/${cat.id}`)
  }
  catDialogVisible.value = false
}

const extractCatId = (catInfo: string): string | null => {
  // 假设格式为 "猫咪名称 (ID:123)"
  const match = catInfo.match(/\(ID:(\d+)\)/)
  return match ? match[1] : null
}

const createUnknownCat = (label: string, gender: 'MALE' | 'FEMALE'): Cat => {
  return {
    id: 0,
    name: `未知${label}`,
    breed: '未知',
    gender,
    birthDate: '',
    color: '未知',
    weight: 0,
    price: 0,
    status: 'UNKNOWN' as any,
    isVaccinated: false,
    isSterilized: false,
    createdTime: ''
  }
}

const formatCatStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    'AVAILABLE': '可售',
    'RESERVED': '已预订',
    'SOLD': '已售出',
    'BREEDING': '繁育中',
    'MEDICAL': '治疗中',
    'RETIRED': '已退役',
    'UNKNOWN': '未知'
  }
  return statusMap[status] || '未知'
}

// 生命周期
onMounted(() => {
  // 初始化
})
</script>

<style scoped lang="scss">
.pedigree-tree {
  width: 100%;
  overflow: hidden;
  
  .tree-controls {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 10px;
  }
  
  .tree-container {
    width: 100%;
    overflow-x: auto;
    padding: 20px 0;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    background-color: #f8f9fa;
    
    .tree-content {
      min-width: 800px;
      transition: transform 0.3s;
      
      .generation-row {
        display: flex;
        margin-bottom: 40px;
        
        .generation-label {
          text-align: center;
          font-weight: bold;
          color: #606266;
          padding: 5px;
          flex-shrink: 0;
        }
        
        .generation-cats {
          display: flex;
          flex-wrap: nowrap;
          width: 100%;
          
          .cat-node-wrapper {
            position: relative;
            display: flex;
            justify-content: center;
            padding: 0 10px;
            
            .connector {
              position: absolute;
              bottom: -20px;
              left: 50%;
              width: 2px;
              height: 20px;
              background-color: #dcdfe6;
              
              &::before,
              &::after {
                content: '';
                position: absolute;
                bottom: 0;
                width: 50%;
                height: 2px;
                background-color: #dcdfe6;
              }
              
              &::before {
                right: 0;
              }
              
              &::after {
                left: 0;
              }
            }
          }
        }
      }
    }
  }
}

.cat-details {
  .cat-header {
    display: flex;
    gap: 20px;
    
    .cat-avatar {
      flex-shrink: 0;
    }
    
    .cat-info {
      h2 {
        margin-top: 0;
        margin-bottom: 10px;
      }
      
      .cat-meta {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
      }
      
      .cat-attributes {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
        
        .attribute {
          .label {
            font-weight: bold;
            color: #606266;
            margin-right: 5px;
          }
          
          .value {
            color: #303133;
          }
        }
      }
    }
  }
  
  .cat-description {
    margin: 20px 0;
    
    h3 {
      margin-top: 0;
      margin-bottom: 10px;
      font-size: 16px;
    }
    
    p {
      color: #606266;
      line-height: 1.6;
    }
  }
  
  .cat-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
  }
}

@media print {
  .tree-controls,
  .cat-actions {
    display: none !important;
  }
  
  .tree-container {
    border: none !important;
    overflow: visible !important;
    
    .tree-content {
      transform: scale(1) !important;
    }
  }
}
</style>
