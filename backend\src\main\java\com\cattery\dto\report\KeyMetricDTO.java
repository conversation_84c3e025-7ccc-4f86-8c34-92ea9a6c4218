package com.cattery.dto.report;

import lombok.Data;

/**
 * 关键指标DTO
 */
@Data
public class KeyMetricDTO {
    
    /**
     * 指标名称
     */
    private String name;
    
    /**
     * 指标值
     */
    private String value;
    
    /**
     * 单位
     */
    private String unit;
    
    /**
     * 变化趋势 (UP, DOWN, STABLE)
     */
    private String trend;
    
    /**
     * 变化百分比
     */
    private Double changePercentage;
    
    /**
     * 指标类型 (COUNT, PERCENTAGE, CURRENCY, RATIO)
     */
    private String type;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 图标
     */
    private String icon;
    
    /**
     * 颜色
     */
    private String color;
    
    /**
     * 是否为关键指标
     */
    private Boolean isKey;
}
