package com.cattery.dto.ai;

import lombok.Data;
import java.util.List;

/**
 * 营养建议DTO
 */
@Data
public class NutritionRecommendationDTO {
    
    /**
     * 每日卡路里需求
     */
    private Integer dailyCalories;
    
    /**
     * 蛋白质百分比
     */
    private Double proteinPercentage;
    
    /**
     * 脂肪百分比
     */
    private Double fatPercentage;
    
    /**
     * 碳水化合物百分比
     */
    private Double carbPercentage;
    
    /**
     * 营养建议列表
     */
    private List<String> recommendations;
    
    /**
     * 食物建议列表
     */
    private List<String> foodSuggestions;
    
    /**
     * 喂食频率
     */
    private String feedingFrequency;
    
    /**
     * 特殊饮食要求
     */
    private List<String> specialDietaryRequirements;
    
    /**
     * 补充剂建议
     */
    private List<String> supplementRecommendations;
    
    /**
     * 水分摄入建议
     */
    private String hydrationAdvice;
}
