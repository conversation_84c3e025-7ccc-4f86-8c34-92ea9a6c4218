package com.cattery.service;

import com.cattery.entity.Cat;
import com.cattery.repository.CatRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class CatService {
    
    private final CatRepository catRepository;
    
    /**
     * 获取所有猫咪
     */
    @Transactional(readOnly = true)
    public List<Cat> findAll() {
        log.debug("获取所有猫咪");
        return catRepository.findAll();
    }
    
    /**
     * 分页获取猫咪
     */
    @Transactional(readOnly = true)
    public Page<Cat> findAll(Pageable pageable) {
        log.debug("分页获取猫咪: {}", pageable);
        return catRepository.findAll(pageable);
    }
    
    /**
     * 根据ID获取猫咪
     */
    @Transactional(readOnly = true)
    public Optional<Cat> findById(Long id) {
        log.debug("根据ID获取猫咪: {}", id);
        return catRepository.findById(id);
    }
    
    /**
     * 保存猫咪
     */
    public Cat save(Cat cat) {
        log.info("保存猫咪: {}", cat.getName());
        
        // 验证芯片ID唯一性
        if (cat.getMicrochipId() != null && !cat.getMicrochipId().trim().isEmpty()) {
            if (cat.getId() == null) {
                // 新建时检查
                if (catRepository.existsByMicrochipId(cat.getMicrochipId())) {
                    throw new IllegalArgumentException("芯片ID已存在: " + cat.getMicrochipId());
                }
            } else {
                // 更新时检查
                Optional<Cat> existingCat = catRepository.findByMicrochipId(cat.getMicrochipId());
                if (existingCat.isPresent() && !existingCat.get().getId().equals(cat.getId())) {
                    throw new IllegalArgumentException("芯片ID已存在: " + cat.getMicrochipId());
                }
            }
        }
        
        return catRepository.save(cat);
    }
    
    /**
     * 删除猫咪
     */
    public void deleteById(Long id) {
        log.info("删除猫咪: {}", id);
        if (!catRepository.existsById(id)) {
            throw new IllegalArgumentException("猫咪不存在: " + id);
        }
        catRepository.deleteById(id);
    }
    
    /**
     * 根据状态查找猫咪
     */
    @Transactional(readOnly = true)
    public List<Cat> findByStatus(Cat.Status status) {
        log.debug("根据状态查找猫咪: {}", status);
        return catRepository.findByStatus(status);
    }
    
    /**
     * 统计指定状态的猫咪数量
     */
    @Transactional(readOnly = true)
    public long countByStatus(Cat.Status status) {
        log.debug("统计状态为{}的猫咪数量", status);
        return catRepository.countByStatus(status);
    }
    
    /**
     * 关键词搜索
     */
    @Transactional(readOnly = true)
    public List<Cat> searchByKeyword(String keyword) {
        log.debug("关键词搜索: {}", keyword);
        if (keyword == null || keyword.trim().isEmpty()) {
            return findAll();
        }
        return catRepository.searchByKeyword(keyword.trim());
    }
    
    /**
     * 分页关键词搜索
     */
    @Transactional(readOnly = true)
    public Page<Cat> searchByKeyword(String keyword, Pageable pageable) {
        log.debug("分页关键词搜索: {}, {}", keyword, pageable);
        if (keyword == null || keyword.trim().isEmpty()) {
            return findAll(pageable);
        }
        return catRepository.searchByKeyword(keyword.trim(), pageable);
    }
    
    /**
     * 获取品种统计
     */
    @Transactional(readOnly = true)
    public Map<String, Long> getBreedStatistics() {
        log.debug("获取品种统计");
        List<Map<String, Object>> results = catRepository.getBreedStatistics();
        Map<String, Long> statistics = new HashMap<>();
        
        for (Map<String, Object> result : results) {
            String breed = (String) result.get("breed");
            Long count = ((Number) result.get("count")).longValue();
            statistics.put(breed, count);
        }
        
        return statistics;
    }
    
    /**
     * 获取状态统计
     */
    @Transactional(readOnly = true)
    public Map<String, Long> getStatusStatistics() {
        log.debug("获取状态统计");
        List<Map<String, Object>> results = catRepository.getStatusStatistics();
        Map<String, Long> statistics = new HashMap<>();
        
        for (Map<String, Object> result : results) {
            Cat.Status status = (Cat.Status) result.get("status");
            Long count = ((Number) result.get("count")).longValue();
            statistics.put(status.name(), count);
        }
        
        return statistics;
    }
    
    /**
     * 获取可售猫咪
     */
    @Transactional(readOnly = true)
    public List<Cat> getAvailableCats() {
        log.debug("获取可售猫咪");
        return catRepository.findAvailableCats();
    }
    
    /**
     * 分页获取可售猫咪
     */
    @Transactional(readOnly = true)
    public Page<Cat> getAvailableCats(Pageable pageable) {
        log.debug("分页获取可售猫咪: {}", pageable);
        return catRepository.findAvailableCats(pageable);
    }
    
    /**
     * 根据价格范围查找
     */
    @Transactional(readOnly = true)
    public List<Cat> findByPriceRange(BigDecimal minPrice, BigDecimal maxPrice) {
        log.debug("根据价格范围查找: {} - {}", minPrice, maxPrice);
        return catRepository.findByPriceRange(minPrice, maxPrice);
    }
    
    /**
     * 更新猫咪状态
     */
    public Cat updateStatus(Long id, Cat.Status status) {
        log.info("更新猫咪状态: {} -> {}", id, status);
        Cat cat = catRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("猫咪不存在: " + id));
        
        cat.setStatus(status);
        return catRepository.save(cat);
    }
    
    /**
     * 检查猫咪是否存在
     */
    @Transactional(readOnly = true)
    public boolean existsById(Long id) {
        return catRepository.existsById(id);
    }
    
    /**
     * 根据芯片ID查找
     */
    @Transactional(readOnly = true)
    public Optional<Cat> findByMicrochipId(String microchipId) {
        log.debug("根据芯片ID查找: {}", microchipId);
        return catRepository.findByMicrochipId(microchipId);
    }
}



