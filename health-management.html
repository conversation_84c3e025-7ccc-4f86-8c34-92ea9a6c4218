<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>健康记录管理 - 猫舍管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            line-height: 1.6;
        }

        .header {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 0 20px;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-links a {
            color: #666;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .nav-links a:hover, .nav-links a.active {
            background: #667eea;
            color: white;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logout-btn {
            background: #f56565;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .page-title h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 8px;
        }

        .page-title p {
            color: #666;
            font-size: 16px;
        }

        .header-actions {
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-secondary {
            background: white;
            color: #666;
            border: 1px solid #ddd;
        }

        .btn-secondary:hover {
            background: #f8f9fa;
        }

        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin: 0 auto 15px;
            color: white;
        }

        .stat-icon.vaccination { background: #10b981; }
        .stat-icon.checkup { background: #3b82f6; }
        .stat-icon.treatment { background: #f59e0b; }
        .stat-icon.emergency { background: #ef4444; }

        .stat-value {
            font-size: 32px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .filters {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }

        .filters-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-group label {
            color: #333;
            font-weight: 500;
            font-size: 14px;
        }

        .filter-group input, .filter-group select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }

        .health-records {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }

        .records-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #eee;
        }

        .records-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .records-list {
            max-height: 600px;
            overflow-y: auto;
        }

        .record-item {
            padding: 20px;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s ease;
        }

        .record-item:hover {
            background: #f8f9fa;
        }

        .record-item:last-child {
            border-bottom: none;
        }

        .record-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .record-info {
            flex: 1;
        }

        .record-cat {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .record-type {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 10px;
        }

        .type-vaccination {
            background: #d1fae5;
            color: #065f46;
        }

        .type-checkup {
            background: #dbeafe;
            color: #1e40af;
        }

        .type-treatment {
            background: #fef3c7;
            color: #92400e;
        }

        .type-emergency {
            background: #fee2e2;
            color: #991b1b;
        }

        .record-date {
            color: #666;
            font-size: 14px;
        }

        .record-actions {
            display: flex;
            gap: 8px;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            font-weight: 500;
        }

        .btn-edit {
            background: #f59e0b;
            color: white;
        }

        .btn-view {
            background: #10b981;
            color: white;
        }

        .btn-delete {
            background: #ef4444;
            color: white;
        }

        .record-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .detail-item {
            font-size: 14px;
            color: #666;
        }

        .detail-label {
            font-weight: 500;
            color: #333;
        }

        .record-notes {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            font-size: 14px;
            color: #666;
            line-height: 1.5;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #333;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 12px;
            padding: 30px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .form-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        @media (max-width: 768px) {
            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .header-actions {
                width: 100%;
                justify-content: flex-start;
            }

            .filters-row {
                grid-template-columns: 1fr;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .record-details {
                grid-template-columns: 1fr;
            }

            .record-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">🐱 猫舍管理系统</div>
            <div class="nav-links">
                <a href="/dashboard.html">仪表盘</a>
                <a href="/cats-management.html">猫咪管理</a>
                <a href="/customers-management.html">客户管理</a>
                <a href="/health-management.html" class="active">健康记录</a>
                <a href="/breeding-management.html">繁育管理</a>
            </div>
            <div class="user-info">
                <span id="userName">加载中...</span>
                <button class="logout-btn" onclick="logout()">退出</button>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="page-header">
            <div class="page-title">
                <h1>健康记录管理</h1>
                <p>管理所有猫咪的健康记录和医疗信息</p>
            </div>
            <div class="header-actions">
                <button class="btn btn-secondary" onclick="refreshData()">刷新数据</button>
                <button class="btn btn-primary" onclick="showAddRecordModal()">添加记录</button>
            </div>
        </div>

        <div class="stats-cards">
            <div class="stat-card">
                <div class="stat-icon vaccination">💉</div>
                <div class="stat-value" id="vaccinationCount">0</div>
                <div class="stat-label">疫苗接种</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon checkup">🔍</div>
                <div class="stat-value" id="checkupCount">0</div>
                <div class="stat-label">健康检查</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon treatment">💊</div>
                <div class="stat-value" id="treatmentCount">0</div>
                <div class="stat-label">治疗记录</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon emergency">🚨</div>
                <div class="stat-value" id="emergencyCount">0</div>
                <div class="stat-label">紧急处理</div>
            </div>
        </div>

        <div class="filters">
            <div class="filters-row">
                <div class="filter-group">
                    <label for="searchCat">搜索猫咪</label>
                    <input type="text" id="searchCat" placeholder="输入猫咪名称">
                </div>
                <div class="filter-group">
                    <label for="filterType">记录类型</label>
                    <select id="filterType">
                        <option value="">全部类型</option>
                        <option value="VACCINATION">疫苗接种</option>
                        <option value="CHECKUP">健康检查</option>
                        <option value="TREATMENT">治疗</option>
                        <option value="SURGERY">手术</option>
                        <option value="EMERGENCY">紧急处理</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="filterDateFrom">开始日期</label>
                    <input type="date" id="filterDateFrom">
                </div>
                <div class="filter-group">
                    <label for="filterDateTo">结束日期</label>
                    <input type="date" id="filterDateTo">
                </div>
                <div class="filter-group">
                    <button class="btn btn-primary" onclick="applyFilters()">应用筛选</button>
                </div>
            </div>
        </div>

        <div id="loadingMessage" class="loading">正在加载健康记录...</div>
        
        <div id="healthRecords" class="health-records" style="display: none;">
            <div class="records-header">
                <div class="records-title">健康记录列表</div>
            </div>
            <div class="records-list" id="recordsList">
            </div>
        </div>
        
        <div id="emptyState" class="empty-state" style="display: none;">
            <h3>暂无健康记录</h3>
            <p>点击"添加记录"按钮开始添加第一条健康记录</p>
        </div>
    </div>

    <!-- 添加/编辑健康记录模态框 -->
    <div id="recordModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">添加健康记录</h3>
                <button class="close-btn" onclick="closeRecordModal()">&times;</button>
            </div>
            <form id="recordForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="recordCatId">猫咪 *</label>
                        <select id="recordCatId" required>
                            <option value="">请选择猫咪</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="recordType">记录类型 *</label>
                        <select id="recordType" required>
                            <option value="">请选择类型</option>
                            <option value="VACCINATION">疫苗接种</option>
                            <option value="CHECKUP">健康检查</option>
                            <option value="TREATMENT">治疗</option>
                            <option value="SURGERY">手术</option>
                            <option value="EMERGENCY">紧急处理</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="recordDate">记录日期 *</label>
                        <input type="date" id="recordDate" required>
                    </div>
                    <div class="form-group">
                        <label for="veterinarian">兽医师</label>
                        <input type="text" id="veterinarian" placeholder="兽医师姓名">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="clinic">诊所/医院</label>
                        <input type="text" id="clinic" placeholder="诊所或医院名称">
                    </div>
                    <div class="form-group">
                        <label for="cost">费用 (元)</label>
                        <input type="number" id="cost" step="0.01" min="0" placeholder="0.00">
                    </div>
                </div>
                <div class="form-group">
                    <label for="diagnosis">诊断结果</label>
                    <textarea id="diagnosis" placeholder="诊断结果或检查结论..."></textarea>
                </div>
                <div class="form-group">
                    <label for="treatment">治疗方案</label>
                    <textarea id="treatment" placeholder="治疗方案或处理措施..."></textarea>
                </div>
                <div class="form-group">
                    <label for="medication">用药情况</label>
                    <textarea id="medication" placeholder="药物名称、剂量、用法等..."></textarea>
                </div>
                <div class="form-group">
                    <label for="notes">备注</label>
                    <textarea id="notes" placeholder="其他备注信息..."></textarea>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeRecordModal()">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080/api';
        let currentRecords = [];
        let availableCats = [];
        let editingRecordId = null;
        
        // 检查登录状态
        function checkAuth() {
            const token = localStorage.getItem('token');
            const userInfo = localStorage.getItem('userInfo');
            
            if (!token || !userInfo) {
                window.location.href = '/login-test.html';
                return false;
            }
            
            try {
                const user = JSON.parse(userInfo);
                document.getElementById('userName').textContent = user.realName || user.username;
                return true;
            } catch (error) {
                logout();
                return false;
            }
        }
        
        // 退出登录
        function logout() {
            localStorage.removeItem('token');
            localStorage.removeItem('userInfo');
            window.location.href = '/login-test.html';
        }
        
        // 加载健康记录数据
        async function loadHealthRecords() {
            const token = localStorage.getItem('token');
            
            try {
                document.getElementById('loadingMessage').style.display = 'block';
                document.getElementById('healthRecords').style.display = 'none';
                document.getElementById('emptyState').style.display = 'none';
                
                // 并行加载健康记录和猫咪列表
                const [recordsResponse, catsResponse] = await Promise.allSettled([
                    fetch(`${API_BASE_URL}/health`, {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    }),
                    fetch(`${API_BASE_URL}/cats`, {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    })
                ]);
                
                // 处理健康记录数据
                if (recordsResponse.status === 'fulfilled') {
                    const recordsData = await recordsResponse.value.json();
                    if (recordsData.success) {
                        currentRecords = Array.isArray(recordsData.data) ? recordsData.data : [];
                        displayHealthRecords(currentRecords);
                        updateStats(currentRecords);
                    }
                }
                
                // 处理猫咪数据
                if (catsResponse.status === 'fulfilled') {
                    const catsData = await catsResponse.value.json();
                    if (catsData.success) {
                        availableCats = Array.isArray(catsData.data) ? catsData.data : [];
                        updateCatSelect();
                    }
                }
                
                if (currentRecords.length === 0) {
                    document.getElementById('emptyState').style.display = 'block';
                }
                
            } catch (error) {
                console.error('加载健康记录失败:', error);
                alert('加载健康记录失败: ' + error.message);
            } finally {
                document.getElementById('loadingMessage').style.display = 'none';
            }
        }
        
        // 更新统计数据
        function updateStats(records) {
            const stats = {
                vaccination: 0,
                checkup: 0,
                treatment: 0,
                emergency: 0
            };
            
            records.forEach(record => {
                switch (record.recordType) {
                    case 'VACCINATION':
                        stats.vaccination++;
                        break;
                    case 'CHECKUP':
                        stats.checkup++;
                        break;
                    case 'TREATMENT':
                    case 'SURGERY':
                        stats.treatment++;
                        break;
                    case 'EMERGENCY':
                        stats.emergency++;
                        break;
                }
            });
            
            document.getElementById('vaccinationCount').textContent = stats.vaccination;
            document.getElementById('checkupCount').textContent = stats.checkup;
            document.getElementById('treatmentCount').textContent = stats.treatment;
            document.getElementById('emergencyCount').textContent = stats.emergency;
        }
        
        // 更新猫咪选择下拉框
        function updateCatSelect() {
            const select = document.getElementById('recordCatId');
            select.innerHTML = '<option value="">请选择猫咪</option>';
            
            availableCats.forEach(cat => {
                const option = document.createElement('option');
                option.value = cat.id;
                option.textContent = `${cat.name} (${cat.breed || '未知品种'})`;
                select.appendChild(option);
            });
        }
        
        // 显示健康记录列表
        function displayHealthRecords(records) {
            const recordsList = document.getElementById('recordsList');
            const healthRecords = document.getElementById('healthRecords');
            const emptyState = document.getElementById('emptyState');
            
            if (records.length === 0) {
                healthRecords.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }
            
            recordsList.innerHTML = records.map(record => {
                const cat = availableCats.find(c => c.id === record.catId);
                const catName = cat ? cat.name : '未知猫咪';
                
                return `
                    <div class="record-item">
                        <div class="record-header">
                            <div class="record-info">
                                <div class="record-cat">${catName}</div>
                                <div class="record-type ${getTypeClass(record.recordType)}">${getTypeText(record.recordType)}</div>
                                <div class="record-date">${formatDate(record.recordDate)}</div>
                            </div>
                            <div class="record-actions">
                                <button class="btn-small btn-view" onclick="viewRecord(${record.id})">查看</button>
                                <button class="btn-small btn-edit" onclick="editRecord(${record.id})">编辑</button>
                                <button class="btn-small btn-delete" onclick="deleteRecord(${record.id})">删除</button>
                            </div>
                        </div>
                        <div class="record-details">
                            ${record.veterinarian ? `<div class="detail-item"><span class="detail-label">兽医师:</span> ${record.veterinarian}</div>` : ''}
                            ${record.clinic ? `<div class="detail-item"><span class="detail-label">诊所:</span> ${record.clinic}</div>` : ''}
                            ${record.cost ? `<div class="detail-item"><span class="detail-label">费用:</span> ¥${record.cost}</div>` : ''}
                        </div>
                        ${record.diagnosis || record.treatment || record.notes ? `
                            <div class="record-notes">
                                ${record.diagnosis ? `<div><strong>诊断:</strong> ${record.diagnosis}</div>` : ''}
                                ${record.treatment ? `<div><strong>治疗:</strong> ${record.treatment}</div>` : ''}
                                ${record.notes ? `<div><strong>备注:</strong> ${record.notes}</div>` : ''}
                            </div>
                        ` : ''}
                    </div>
                `;
            }).join('');
            
            healthRecords.style.display = 'block';
            emptyState.style.display = 'none';
        }
        
        // 获取记录类型样式类
        function getTypeClass(type) {
            switch (type) {
                case 'VACCINATION': return 'type-vaccination';
                case 'CHECKUP': return 'type-checkup';
                case 'TREATMENT':
                case 'SURGERY': return 'type-treatment';
                case 'EMERGENCY': return 'type-emergency';
                default: return 'type-checkup';
            }
        }
        
        // 获取记录类型文本
        function getTypeText(type) {
            switch (type) {
                case 'VACCINATION': return '疫苗接种';
                case 'CHECKUP': return '健康检查';
                case 'TREATMENT': return '治疗';
                case 'SURGERY': return '手术';
                case 'EMERGENCY': return '紧急处理';
                default: return '未知';
            }
        }
        
        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '';
            try {
                return new Date(dateStr).toLocaleDateString('zh-CN');
            } catch (error) {
                return '';
            }
        }
        
        // 应用筛选
        function applyFilters() {
            const searchCat = document.getElementById('searchCat').value.toLowerCase();
            const filterType = document.getElementById('filterType').value;
            const filterDateFrom = document.getElementById('filterDateFrom').value;
            const filterDateTo = document.getElementById('filterDateTo').value;
            
            const filteredRecords = currentRecords.filter(record => {
                const cat = availableCats.find(c => c.id === record.catId);
                const catName = cat ? cat.name.toLowerCase() : '';
                
                const matchCat = !searchCat || catName.includes(searchCat);
                const matchType = !filterType || record.recordType === filterType;
                
                let matchDate = true;
                if (filterDateFrom || filterDateTo) {
                    const recordDate = new Date(record.recordDate);
                    if (filterDateFrom) {
                        matchDate = matchDate && recordDate >= new Date(filterDateFrom);
                    }
                    if (filterDateTo) {
                        matchDate = matchDate && recordDate <= new Date(filterDateTo);
                    }
                }
                
                return matchCat && matchType && matchDate;
            });
            
            displayHealthRecords(filteredRecords);
            updateStats(filteredRecords);
        }
        
        // 刷新数据
        function refreshData() {
            loadHealthRecords();
        }
        
        // 显示添加记录模态框
        function showAddRecordModal() {
            editingRecordId = null;
            document.getElementById('modalTitle').textContent = '添加健康记录';
            document.getElementById('recordForm').reset();
            document.getElementById('recordDate').value = new Date().toISOString().split('T')[0];
            document.getElementById('recordModal').style.display = 'block';
        }
        
        // 关闭模态框
        function closeRecordModal() {
            document.getElementById('recordModal').style.display = 'none';
        }
        
        // 查看记录详情
        function viewRecord(recordId) {
            const record = currentRecords.find(r => r.id === recordId);
            if (record) {
                const cat = availableCats.find(c => c.id === record.catId);
                const catName = cat ? cat.name : '未知猫咪';
                
                alert(`健康记录详情:\n\n猫咪: ${catName}\n类型: ${getTypeText(record.recordType)}\n日期: ${formatDate(record.recordDate)}\n兽医师: ${record.veterinarian || '未填写'}\n诊所: ${record.clinic || '未填写'}\n费用: ${record.cost ? '¥' + record.cost : '未填写'}\n诊断: ${record.diagnosis || '无'}\n治疗: ${record.treatment || '无'}\n用药: ${record.medication || '无'}\n备注: ${record.notes || '无'}`);
            }
        }
        
        // 编辑记录
        function editRecord(recordId) {
            const record = currentRecords.find(r => r.id === recordId);
            if (record) {
                editingRecordId = recordId;
                document.getElementById('modalTitle').textContent = '编辑健康记录';
                
                // 填充表单
                document.getElementById('recordCatId').value = record.catId || '';
                document.getElementById('recordType').value = record.recordType || '';
                document.getElementById('recordDate').value = record.recordDate || '';
                document.getElementById('veterinarian').value = record.veterinarian || '';
                document.getElementById('clinic').value = record.clinic || '';
                document.getElementById('cost').value = record.cost || '';
                document.getElementById('diagnosis').value = record.diagnosis || '';
                document.getElementById('treatment').value = record.treatment || '';
                document.getElementById('medication').value = record.medication || '';
                document.getElementById('notes').value = record.notes || '';
                
                document.getElementById('recordModal').style.display = 'block';
            }
        }
        
        // 删除记录
        function deleteRecord(recordId) {
            const record = currentRecords.find(r => r.id === recordId);
            if (record && confirm(`确定要删除这条健康记录吗？此操作不可恢复。`)) {
                // 这里应该调用删除API
                alert('删除功能暂未实现，请联系开发人员');
            }
        }
        
        // 表单提交处理
        document.getElementById('recordForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = {
                catId: parseInt(document.getElementById('recordCatId').value),
                recordType: document.getElementById('recordType').value,
                recordDate: document.getElementById('recordDate').value,
                veterinarian: document.getElementById('veterinarian').value,
                clinic: document.getElementById('clinic').value,
                cost: parseFloat(document.getElementById('cost').value) || null,
                diagnosis: document.getElementById('diagnosis').value,
                treatment: document.getElementById('treatment').value,
                medication: document.getElementById('medication').value,
                notes: document.getElementById('notes').value
            };
            
            try {
                const token = localStorage.getItem('token');
                const url = editingRecordId ? `${API_BASE_URL}/health/${editingRecordId}` : `${API_BASE_URL}/health`;
                const method = editingRecordId ? 'PUT' : 'POST';
                
                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert(editingRecordId ? '健康记录更新成功！' : '健康记录添加成功！');
                    closeRecordModal();
                    loadHealthRecords(); // 重新加载数据
                } else {
                    throw new Error(data.message || '操作失败');
                }
            } catch (error) {
                console.error('保存健康记录失败:', error);
                alert('保存失败: ' + error.message);
            }
        });
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            if (checkAuth()) {
                loadHealthRecords();
            }
            
            // 绑定筛选事件
            document.getElementById('searchCat').addEventListener('input', applyFilters);
            document.getElementById('filterType').addEventListener('change', applyFilters);
            document.getElementById('filterDateFrom').addEventListener('change', applyFilters);
            document.getElementById('filterDateTo').addEventListener('change', applyFilters);
        });
        
        // 点击模态框外部关闭
        document.getElementById('recordModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeRecordModal();
            }
        });
    </script>
</body>
</html>
