/**
 * 报表相关类型定义
 */

// 报表类型
export type ReportType = 'cat' | 'financial' | 'health' | 'breeding' | 'customer' | 'dashboard'

// 基础报表数据接口
export interface BaseReportData {
  reportPeriod: string
  generatedAt: string
}

// 猫咪统计报表
export interface CatStatisticsReport extends BaseReportData {
  totalCats: number
  newCatsInPeriod: number
  genderDistribution: Record<string, number>
  breedDistribution: Record<string, number>
  statusDistribution: Record<string, number>
  ageDistribution: Record<string, number>
  healthStatistics: {
    healthyCats: number
    catsNeedingAttention: number
    vaccinationRate: number
    checkupCoverage: number
    averageHealthScore: number
    commonHealthIssues: Record<string, number>
  }
}

// 财务报表
export interface FinancialReport extends BaseReportData {
  totalIncome: number
  totalExpense: number
  netIncome: number
  incomeByCategory: Record<string, number>
  expenseByCategory: Record<string, number>
  monthlyTrend: MonthlyFinancialData[]
  paymentMethodStatistics: Record<string, number>
}

// 月度财务数据
export interface MonthlyFinancialData {
  month: string
  income: number
  expense: number
  netIncome: number
}

// 健康报表
export interface HealthReport extends BaseReportData {
  totalHealthRecords: number
  recordTypeDistribution: Record<string, number>
  vaccinationStatistics: VaccinationStatistics
  checkupStatistics: CheckupStatistics
  healthIssueStatistics: HealthIssueStatistics[]
  veterinarianStatistics: Record<string, number>
}

// 疫苗统计
export interface VaccinationStatistics {
  totalVaccinations: number
  upToDateCats: number
  overdueCats: number
  dueSoonCats: number
  vaccinationRate: number
}

// 体检统计
export interface CheckupStatistics {
  totalCheckups: number
  normalResults: number
  abnormalResults: number
  pendingResults: number
}

// 健康问题统计
export interface HealthIssueStatistics {
  issueName: string
  occurrences: number
  severity: string
  affectedCats: number
}

// 繁育报表
export interface BreedingReport extends BaseReportData {
  totalMatings: number
  matingSuccessRate: number
  totalPregnancies: number
  pregnancySuccessRate: number
  breedingByBreed: Record<string, number>
  monthlyTrend: MonthlyBreedingData[]
  breedingEfficiency: BreedingEfficiency
}

// 月度繁育数据
export interface MonthlyBreedingData {
  month: string
  matings: number
  pregnancies: number
  births: number
}

// 繁育效率
export interface BreedingEfficiency {
  averageLitterSize: number
  survivalRate: number
  weaningSuccessRate: number
  timeToWeaning: number
}

// 客户报表
export interface CustomerReport extends BaseReportData {
  totalCustomers: number
  newCustomers: number
  customerTypeDistribution: Record<string, number>
  totalAdoptions: number
  adoptionStatistics: AdoptionStatistics
  customerSatisfaction: CustomerSatisfaction
  regionDistribution: Record<string, number>
}

// 领养统计
export interface AdoptionStatistics {
  successfulAdoptions: number
  pendingAdoptions: number
  cancelledAdoptions: number
  averageAdoptionTime: number
  adoptionSuccessRate: number
}

// 客户满意度
export interface CustomerSatisfaction {
  averageRating: number
  totalReviews: number
  ratingDistribution: Record<string, number>
  satisfactionTrend: number
}

// 综合仪表盘报表
export interface DashboardReport {
  generatedAt: string
  totalCats: number
  totalCustomers: number
  totalHealthRecords: number
  monthlyIncome: number
  monthlyExpense: number
  todoItems: TodoItem[]
  recentActivities: RecentActivity[]
  healthAlerts: HealthAlert[]
}

// 待办事项
export interface TodoItem {
  id: number
  title: string
  description: string
  priority: 'high' | 'medium' | 'low'
  dueDate: string
  completed: boolean
}

// 最近活动
export interface RecentActivity {
  id: number
  type: string
  title: string
  description: string
  timestamp: string
  userId: number
  userName: string
}

// 健康警告
export interface HealthAlert {
  id: number
  catId: number
  catName: string
  alertType: string
  severity: 'high' | 'medium' | 'low'
  message: string
  createdAt: string
}

// 报表数据联合类型
export type ReportData = 
  | CatStatisticsReport 
  | FinancialReport 
  | HealthReport 
  | BreedingReport 
  | CustomerReport 
  | DashboardReport

// 报表历史项
export interface ReportHistoryItem {
  id: number
  reportType: ReportType
  reportPeriod: string
  generatedAt: string
  generatedBy: string
  status: 'completed' | 'generating' | 'failed'
  fileSize?: number
  downloadCount: number
}

// 分页响应
export interface PaginationResponse<T> {
  content: T[]
  totalElements: number
  totalPages: number
  size: number
  number: number
  first: boolean
  last: boolean
}
