package com.cattery.config;

import io.swagger.v3.oas.models.examples.Example;
import io.swagger.v3.oas.models.media.Content;
import io.swagger.v3.oas.models.media.MediaType;
import io.swagger.v3.oas.models.media.Schema;
import io.swagger.v3.oas.models.responses.ApiResponse;
import io.swagger.v3.oas.models.responses.ApiResponses;
import org.springdoc.core.customizers.OpenApiCustomiser;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * API文档增强配置
 * 添加通用响应示例和错误码说明
 */
@Configuration
public class ApiDocumentationConfig {

    /**
     * 自定义OpenAPI配置
     * 添加通用响应示例
     */
    @Bean
    public OpenApiCustomiser openApiCustomizer() {
        return openApi -> {
            // 添加通用响应组件
            addCommonResponses(openApi);
            
            // 添加示例数据
            addExamples(openApi);
            
            // 为所有路径添加通用响应
            openApi.getPaths().values().forEach(pathItem -> 
                pathItem.readOperations().forEach(operation -> {
                    if (operation.getResponses() != null) {
                        addCommonResponsesToOperation(operation.getResponses());
                    }
                })
            );
        };
    }

    /**
     * 添加通用响应定义
     */
    private void addCommonResponses(io.swagger.v3.oas.models.OpenAPI openApi) {
        if (openApi.getComponents() == null) {
            openApi.setComponents(new io.swagger.v3.oas.models.Components());
        }

        // 添加ApiResponse模式定义
        addApiResponseSchema(openApi);

        // 400 错误响应
        openApi.getComponents().addResponses("BadRequest", 
            new ApiResponse()
                .description("请求参数错误")
                .content(new Content()
                    .addMediaType("application/json", 
                        new MediaType()
                            .schema(new Schema<>().$ref("#/components/schemas/ApiResponse"))
                            .example("""
                                {
                                    "success": false,
                                    "message": "请求参数验证失败",
                                    "timestamp": "2024-01-01T12:00:00Z",
                                    "path": "/api/cats",
                                    "errors": [
                                        {
                                            "field": "name",
                                            "message": "猫咪名称不能为空"
                                        }
                                    ]
                                }
                                """)
                    )
                )
        );

        // 401 未授权响应
        openApi.getComponents().addResponses("Unauthorized", 
            new ApiResponse()
                .description("未授权访问")
                .content(new Content()
                    .addMediaType("application/json", 
                        new MediaType()
                            .schema(new Schema<>().$ref("#/components/schemas/ApiResponse"))
                            .example("""
                                {
                                    "success": false,
                                    "message": "访问令牌无效或已过期",
                                    "timestamp": "2024-01-01T12:00:00Z",
                                    "path": "/api/cats"
                                }
                                """)
                    )
                )
        );

        // 403 禁止访问响应
        openApi.getComponents().addResponses("Forbidden", 
            new ApiResponse()
                .description("权限不足")
                .content(new Content()
                    .addMediaType("application/json", 
                        new MediaType()
                            .schema(new Schema<>().$ref("#/components/schemas/ApiResponse"))
                            .example("""
                                {
                                    "success": false,
                                    "message": "您没有执行此操作的权限",
                                    "timestamp": "2024-01-01T12:00:00Z",
                                    "path": "/api/cats"
                                }
                                """)
                    )
                )
        );

        // 404 未找到响应
        openApi.getComponents().addResponses("NotFound", 
            new ApiResponse()
                .description("资源未找到")
                .content(new Content()
                    .addMediaType("application/json", 
                        new MediaType()
                            .schema(new Schema<>().$ref("#/components/schemas/ApiResponse"))
                            .example("""
                                {
                                    "success": false,
                                    "message": "猫咪不存在，ID: 999",
                                    "timestamp": "2024-01-01T12:00:00Z",
                                    "path": "/api/cats/999"
                                }
                                """)
                    )
                )
        );

        // 500 服务器错误响应
        openApi.getComponents().addResponses("InternalServerError", 
            new ApiResponse()
                .description("服务器内部错误")
                .content(new Content()
                    .addMediaType("application/json", 
                        new MediaType()
                            .schema(new Schema<>().$ref("#/components/schemas/ApiResponse"))
                            .example("""
                                {
                                    "success": false,
                                    "message": "服务器内部错误，请稍后重试",
                                    "timestamp": "2024-01-01T12:00:00Z",
                                    "path": "/api/cats"
                                }
                                """)
                    )
                )
        );
    }

    /**
     * 添加示例数据
     */
    private void addExamples(io.swagger.v3.oas.models.OpenAPI openApi) {
        if (openApi.getComponents() == null) {
            openApi.setComponents(new io.swagger.v3.oas.models.Components());
        }

        // 成功响应示例
        openApi.getComponents().addExamples("SuccessResponse", 
            new Example()
                .summary("成功响应示例")
                .description("API调用成功时的标准响应格式")
                .value("""
                    {
                        "success": true,
                        "message": "操作成功",
                        "data": {},
                        "timestamp": "2024-01-01T12:00:00Z"
                    }
                    """)
        );

        // 猫咪数据示例
        openApi.getComponents().addExamples("CatExample", 
            new Example()
                .summary("猫咪信息示例")
                .description("猫咪详细信息的示例数据")
                .value("""
                    {
                        "id": 1,
                        "name": "小花",
                        "microchipId": "123456789012345",
                        "breedName": "英国短毛猫",
                        "gender": "FEMALE",
                        "dateOfBirth": "2023-06-15",
                        "color": "银渐层",
                        "status": "AVAILABLE",
                        "currentWeight": 3200,
                        "description": "性格温顺，健康活泼的小母猫",
                        "createdAt": "2023-07-01T10:00:00Z",
                        "updatedAt": "2023-07-01T10:00:00Z"
                    }
                    """)
        );

        // 分页响应示例
        openApi.getComponents().addExamples("PageResponse", 
            new Example()
                .summary("分页响应示例")
                .description("分页查询结果的示例数据")
                .value("""
                    {
                        "success": true,
                        "message": "获取数据成功",
                        "data": {
                            "content": [
                                {
                                    "id": 1,
                                    "name": "小花",
                                    "breedName": "英国短毛猫",
                                    "gender": "FEMALE",
                                    "status": "AVAILABLE"
                                }
                            ],
                            "pageable": {
                                "pageNumber": 0,
                                "pageSize": 10,
                                "sort": {
                                    "sorted": true,
                                    "ascending": false
                                }
                            },
                            "totalElements": 25,
                            "totalPages": 3,
                            "first": true,
                            "last": false,
                            "numberOfElements": 10
                        },
                        "timestamp": "2024-01-01T12:00:00Z"
                    }
                    """)
        );
    }

    /**
     * 添加ApiResponse模式定义
     */
    private void addApiResponseSchema(io.swagger.v3.oas.models.OpenAPI openApi) {
        Schema<?> apiResponseSchema = new Schema<>()
            .type("object")
            .description("统一API响应格式")
            .addProperty("success", new Schema<>()
                .type("boolean")
                .description("是否成功"))
            .addProperty("message", new Schema<>()
                .type("string")
                .description("响应消息"))
            .addProperty("data", new Schema<>()
                .description("响应数据"))
            .addProperty("errorCode", new Schema<>()
                .type("string")
                .description("错误码"))
            .addProperty("timestamp", new Schema<>()
                .type("string")
                .format("date-time")
                .description("时间戳"))
            .addProperty("path", new Schema<>()
                .type("string")
                .description("请求路径"));

        openApi.getComponents().addSchemas("ApiResponse", apiResponseSchema);
    }

    /**
     * 为操作添加通用响应
     */
    private void addCommonResponsesToOperation(ApiResponses responses) {
        // 只为没有定义的状态码添加通用响应
        if (!responses.containsKey("400")) {
            responses.addApiResponse("400", new ApiResponse().$ref("#/components/responses/BadRequest"));
        }
        if (!responses.containsKey("401")) {
            responses.addApiResponse("401", new ApiResponse().$ref("#/components/responses/Unauthorized"));
        }
        if (!responses.containsKey("403")) {
            responses.addApiResponse("403", new ApiResponse().$ref("#/components/responses/Forbidden"));
        }
        if (!responses.containsKey("500")) {
            responses.addApiResponse("500", new ApiResponse().$ref("#/components/responses/InternalServerError"));
        }
    }
}
