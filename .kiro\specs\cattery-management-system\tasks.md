# 实现计划

- [x] 1. 建立项目基础架构和核心配置


  - 配置Spring Boot项目的基础设置，包括数据库连接、安全配置、跨域设置
  - 创建基础的实体类、DTO类和响应包装类
  - 设置全局异常处理和统一响应格式
  - _需求: 7.1, 7.2, 7.3_







- [x] 2. 实现用户认证和权限管理系统



  - 创建用户实体和角色实体，实现JWT认证机制
  - 编写用户注册、登录、权限验证的业务逻辑
  - 实现基于角色的访问控制(RBAC)


  - 编写认证相关的单元测试和集成测试
  - _需求: 7.1, 7.4_





- [x] 3. 构建猫咪品种和基础数据管理


  - 创建CatBreed实体和相关的Repository、Service、Controller


  - 实现品种的CRUD操作和数据验证
  - 编写品种管理的API接口和测试用例
  - _需求: 1.1, 1.2_










- [x] 4. 实现猫咪档案核心功能


- [ ] 4.1 创建猫咪实体和基础CRUD操作
  - 实现Cat实体类，包含芯片号、名字、性别、生日等基础字段
  - 创建CatRepository、CatService和CatController
  - 实现猫咪的创建、查询、更新、删除功能
  - 编写猫咪CRUD操作的单元测试


  - _需求: 1.1, 1.2_




- [x] 4.2 实现猫咪状态管理和筛选功能


  - 添加繁育状态、健康状态、销售状态等枚举字段


  - 实现按状态、品种、年龄、性别的筛选查询
  - 创建分页查询和排序功能
  - 编写状态管理和筛选功能的测试
  - _需求: 1.2, 1.3_



- [ ] 4.3 构建血统关系和谱系管理
  - 实现Cat实体的父母关系映射


  - 创建血统树查询和近亲繁殖检测算法
  - 实现谱系可视化数据结构


  - 编写血统关系和近亲检测的测试用例
  - _需求: 1.5_

- [x] 5. 开发媒体文件管理系统


- [x] 5.1 实现文件上传和存储功能

  - 创建CatMedia实体和文件存储服务
  - 实现安全的文件上传，包括类型验证和大小限制
  - 配置文件存储路径和访问URL
  - 编写文件上传的安全性测试
  - _需求: 1.1, 7.3_

- [x] 5.2 构建照片和视频管理功能


  - 实现按时间线组织媒体文件的功能
  - 创建媒体文件的缩略图生成
  - 实现媒体文件的批量上传和删除
  - 编写媒体管理功能的集成测试
  - _需求: 1.1_

- [ ] 6. 实现健康管理核心功能
- [ ] 6.1 创建健康记录基础架构
  - 实现HealthRecord实体，支持多种健康记录类型
  - 创建健康记录的Repository、Service和Controller
  - 实现健康记录的创建和查询功能
  - 编写健康记录基础功能的单元测试
  - _需求: 3.1, 3.2_

- [ ] 6.2 实现疫苗管理和提醒系统
  - 创建疫苗接种计划和记录功能
  - 实现疫苗到期自动提醒机制
  - 创建疫苗接种时间轴显示
  - 编写疫苗管理和提醒功能的测试
  - _需求: 3.1, 3.4_

- [ ] 6.3 构建体检和遗传病筛查管理
  - 实现体重趋势图和体况评分记录
  - 创建遗传病检测结果管理（PKD、HCM等）
  - 实现健康数据的统计分析功能
  - 编写体检和遗传病管理的测试用例
  - _需求: 3.2, 3.3_

- [ ] 7. 开发繁育管理系统
- [ ] 7.1 实现发情周期和配种管理
  - 创建HeatCycle和Mating实体
  - 实现发情周期记录和最佳配种时间预测
  - 创建配种记录和预产期计算功能
  - 编写发情和配种管理的业务逻辑测试
  - _需求: 2.1, 2.2_

- [ ] 7.2 构建孕期和分娩管理
  - 实现Pregnancy和Litter实体
  - 创建B超记录、产房分配和营养计划管理
  - 实现分娩记录和幼猫信息登记
  - 编写孕期和分娩管理的集成测试
  - _需求: 2.3, 2.4_

- [ ] 7.3 实现幼猫成长跟踪
  - 创建幼猫断奶、社会化训练记录功能
  - 实现幼猫成长里程碑跟踪
  - 创建幼猫离窝时间管理
  - 编写幼猫成长跟踪的功能测试
  - _需求: 2.5_

- [ ] 8. 构建客户关系管理系统
- [ ] 8.1 实现客户档案和咨询管理
  - 创建Customer和Inquiry实体
  - 实现客户信息的CRUD操作
  - 创建客户需求登记和跟进记录功能
  - 编写客户管理的基础功能测试
  - _需求: 4.1, 4.2_

- [ ] 8.2 开发合同和销售管理
  - 实现Contract实体和电子合同生成
  - 创建销售合同、绝育协议、健康保证书模板
  - 实现合同状态跟踪和管理
  - 编写合同管理的业务逻辑测试
  - _需求: 4.2_

- [ ] 8.3 构建售后跟踪和回访系统
  - 实现FollowUp实体和售后跟踪功能
  - 创建自动回访提醒机制
  - 实现客户反馈收集和处理
  - 编写售后跟踪系统的功能测试
  - _需求: 4.3, 4.4_

- [ ] 9. 实现库存管理系统
- [ ] 9.1 创建库存基础管理
  - 实现Inventory实体和基础CRUD操作
  - 创建物品分类管理（猫粮、药品、营养品、消耗品）
  - 实现库存入库、出库记录功能
  - 编写库存基础管理的单元测试
  - _需求: 5.1_

- [ ] 9.2 实现库存预警和喂养计划
  - 创建库存不足自动预警机制
  - 实现个体化喂食量记录和计划
  - 创建库存使用统计和分析功能
  - 编写库存预警和喂养计划的测试
  - _需求: 5.1, 5.2_

- [ ] 10. 开发财务管理系统
- [ ] 10.1 实现财务记录和流水管理
  - 创建FinancialRecord实体和收支记录功能
  - 实现种猫引进、配种费、销售收入、日常开支的分类记录
  - 创建财务流水查询和统计功能
  - 编写财务记录管理的单元测试
  - _需求: 6.1, 6.2_

- [ ] 10.2 构建财务分析和报表系统
  - 实现按品种/血统的盈利能力分析
  - 创建财务报表生成和导出功能
  - 实现预算控制和超支预警
  - 编写财务分析和报表的功能测试
  - _需求: 6.2_

- [ ] 11. 开发前端核心页面和组件
- [ ] 11.1 创建项目基础架构和路由
  - 设置Vue 3项目结构和Element Plus配置
  - 创建路由配置和页面布局组件
  - 实现用户认证和权限控制的前端逻辑
  - 编写前端基础架构的单元测试
  - _需求: 8.1, 8.2_

- [ ] 11.2 实现仪表盘和数据看板
  - 创建Dashboard页面和核心数据卡片组件
  - 实现待办提醒、统计数据、日历视图
  - 创建健康预警和关键指标显示
  - 编写仪表盘组件的功能测试
  - _需求: 8.2_

- [ ] 11.3 构建猫咪管理界面
  - 创建猫咪列表页面和筛选组件
  - 实现猫咪卡片视图和详情页面
  - 创建猫咪档案编辑和媒体上传界面
  - 编写猫咪管理界面的E2E测试
  - _需求: 8.3, 8.4_

- [ ] 12. 实现专业功能界面
- [ ] 12.1 开发血统树和繁育管理界面
  - 创建可视化血统树组件
  - 实现繁育看板和配种记录界面
  - 创建孕期跟踪和分娩记录页面
  - 编写血统和繁育界面的交互测试
  - _需求: 8.4_

- [ ] 12.2 构建健康管理和客户管理界面
  - 创建健康记录时间轴组件
  - 实现疫苗计划和提醒界面
  - 创建客户列表和跟进记录页面
  - 编写健康和客户管理界面的功能测试
  - _需求: 8.4_

- [ ] 13. 实现系统集成和优化
- [ ] 13.1 完成前后端API集成
  - 集成所有前端页面与后端API接口
  - 实现统一的错误处理和加载状态管理
  - 优化API调用性能和用户体验
  - 编写完整的API集成测试
  - _需求: 8.1, 8.5_

- [ ] 13.2 实现系统性能优化和部署准备
  - 优化数据库查询性能和添加必要索引
  - 实现缓存策略和前端性能优化
  - 配置生产环境部署脚本和Docker容器
  - 编写系统性能测试和部署验证测试
  - _需求: 7.3, 7.5_