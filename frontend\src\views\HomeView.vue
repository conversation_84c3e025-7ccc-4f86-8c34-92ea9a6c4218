<template>
  <div class="home-container">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="welcome-card">
          <template #header>
            <div class="card-header">
              <span>🏠 欢迎使用猫舍管理系统</span>
            </div>
          </template>
          <div class="welcome-content">
            <p>这是一个专业的猫舍管理系统，帮助您更好地管理猫咪信息、健康记录和日常运营。</p>
            <div class="quick-actions">
              <el-button type="primary" @click="$router.push('/cats')">
                <el-icon><Pets /></el-icon>
                查看猫咪
              </el-button>
              <el-button type="success" @click="$router.push('/api-test')">
                <el-icon><Connection /></el-icon>
                API测试
              </el-button>
              <el-button type="info" @click="$router.push('/diagnostic')">
                <el-icon><Tools /></el-icon>
                系统诊断
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="8">
        <el-card>
          <div class="stat-card">
            <div class="stat-icon">🐱</div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.total || 0 }}</div>
              <div class="stat-label">总猫咪数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card>
          <div class="stat-card">
            <div class="stat-icon">✅</div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.available || 0 }}</div>
              <div class="stat-label">可售猫咪</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card>
          <div class="stat-card">
            <div class="stat-icon">💰</div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.sold || 0 }}</div>
              <div class="stat-label">已售猫咪</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

const stats = ref({
  total: 0,
  available: 0,
  sold: 0
})

const loadStats = async () => {
  try {
    const response = await fetch('/api/cats/statistics')
    if (response.ok) {
      const result = await response.json()
      if (result.success) {
        stats.value = result.data
      }
    }
  } catch (error) {
    console.warn('加载统计信息失败:', error)
  }
}

onMounted(() => {
  loadStats()
})
</script>

<style scoped>
.home-container {
  padding: 20px;
}

.welcome-card {
  margin-bottom: 20px;
}

.welcome-content {
  text-align: center;
}

.quick-actions {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 10px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
}

.stat-icon {
  font-size: 3rem;
  margin-right: 20px;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  color: #409eff;
}

.stat-label {
  color: #666;
  margin-top: 5px;
}
</style>
