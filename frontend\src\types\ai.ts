/**
 * AI功能相关类型定义
 */

// 猫咪识别结果
export interface CatRecognitionResult {
  confidence: number
  breedName: string
  color: string
  gender: string
  estimatedAge: number
  facialFeatures: FacialFeature[]
  matchedCats: CatSummary[]
  similarityMatches: SimilarityMatch[]
  bodySize?: string
  coatLength?: string
  coatPattern?: string
}

// 面部特征
export interface FacialFeature {
  featureType: string
  color?: string
  shape?: string
  size?: string
  confidence?: number
}

// 猫咪摘要
export interface CatSummary {
  id: number
  name: string
  breedName: string
  gender: string
  color: string
  dateOfBirth: string
  primaryPhoto?: string
}

// 相似度匹配
export interface SimilarityMatch {
  catId: number
  similarity: number
  matchedFeatures: string[]
}

// 品种识别结果
export interface BreedRecognitionResult {
  confidence: number
  breedName: string
  candidates: BreedCandidate[]
  breedInfo?: BreedInfo
}

// 品种候选
export interface BreedCandidate {
  breedName: string
  confidence: number
  characteristics: string[]
}

// 品种信息
export interface BreedInfo {
  breedName: string
  origin: string
  characteristics: string[]
  commonHealthIssues: string[]
  lifeExpectancy: string
}

// 健康预测结果
export interface HealthPredictionResult {
  overallHealthScore: number
  riskLevel: string
  predictionConfidence: number
  diseaseRisks: DiseaseRisk[]
  healthTrend: HealthTrend
  recommendations: HealthRecommendation[]
}

// 疾病风险
export interface DiseaseRisk {
  diseaseName: string
  riskScore: number
  riskLevel: string
  riskFactors: string[]
  preventiveMeasures: string[]
}

// 健康趋势
export interface HealthTrend {
  trendDirection: string
  trendStrength: number
  predictionPeriod: number
}

// 健康建议
export interface HealthRecommendation {
  title: string
  content: string
  type: string
}

// 行为分析结果
export interface BehaviorAnalysisResult {
  behaviorScore: number
  overallAssessment: string
  behaviorPatterns: BehaviorPattern[]
  anomalies: BehaviorAnomaly[]
  recommendations: BehaviorRecommendation[]
}

// 行为模式
export interface BehaviorPattern {
  patternType: string
  frequency: number
  intensity: number
  abnormalityScore: number
  description: string
}

// 行为异常
export interface BehaviorAnomaly {
  anomalyType: string
  severity: string
  confidence: number
  description: string
  possibleCauses: string[]
}

// 行为建议
export interface BehaviorRecommendation {
  title: string
  content: string
  type: string
}

// AI服务状态
export interface AIServiceStatus {
  catRecognitionAvailable: boolean
  healthPredictionAvailable: boolean
  behaviorAnalysisAvailable: boolean
  imageProcessingAvailable: boolean
  overallAvailable: boolean
  serviceVersion: string
  lastUpdateTime: string
}

// AI模型信息
export interface AIModelInfo {
  catRecognitionModel: string
  breedClassificationModel: string
  healthPredictionModel: string
  behaviorAnalysisModel: string
  lastUpdated: string
  accuracy: number
}

// AI训练反馈
export interface AITrainingFeedback {
  feedbackType: string
  modelType: string
  inputData: any
  expectedOutput: any
  actualOutput: any
  feedback: string
  userId: number
}

// AI使用统计
export interface AIUsageStatistics {
  totalRecognitions: number
  totalHealthPredictions: number
  totalBehaviorAnalyses: number
  averageAccuracy: number
  mostUsedFeature: string
  dailyUsage?: DailyUsage[]
}

// 每日使用量
export interface DailyUsage {
  date: string
  recognitions: number
  predictions: number
  analyses: number
}
