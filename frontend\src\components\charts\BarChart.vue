<template>
  <div ref="chartRef" :style="{ width: '100%', height: height }"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import type { ECharts, EChartsOption } from 'echarts'

interface Props {
  data: {
    labels: string[]
    datasets: Array<{
      label?: string
      data: number[]
      backgroundColor?: string | string[]
      borderColor?: string | string[]
      borderWidth?: number
    }>
  }
  options?: any
  height?: string
}

const props = withDefaults(defineProps<Props>(), {
  height: '400px',
  options: () => ({})
})

const chartRef = ref<HTMLElement>()
const chartInstance = ref<ECharts>()

const defaultOptions: EChartsOption = {
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: []
  },
  yAxis: {
    type: 'value'
  },
  series: []
}

const initChart = async () => {
  if (!chartRef.value) return
  
  await nextTick()
  
  if (chartInstance.value) {
    chartInstance.value.dispose()
  }
  
  chartInstance.value = echarts.init(chartRef.value)
  updateChart()
  
  window.addEventListener('resize', handleResize)
}

const updateChart = () => {
  if (!chartInstance.value || !props.data) return
  
  const series = props.data.datasets.map((dataset, index) => ({
    name: dataset.label || `数据${index + 1}`,
    type: 'bar',
    data: dataset.data,
    itemStyle: {
      color: Array.isArray(dataset.backgroundColor) 
        ? (params: any) => dataset.backgroundColor[params.dataIndex % dataset.backgroundColor.length]
        : dataset.backgroundColor || '#5470c6'
    }
  }))
  
  const option: EChartsOption = {
    ...defaultOptions,
    ...props.options,
    xAxis: {
      ...defaultOptions.xAxis,
      data: props.data.labels
    },
    series
  }
  
  chartInstance.value.setOption(option, true)
}

const handleResize = () => {
  if (chartInstance.value) {
    chartInstance.value.resize()
  }
}

watch(
  () => props.data,
  () => {
    updateChart()
  },
  { deep: true }
)

watch(
  () => props.options,
  () => {
    updateChart()
  },
  { deep: true }
)

onMounted(() => {
  initChart()
})

onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose()
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
/* 图表容器样式 */
</style>
