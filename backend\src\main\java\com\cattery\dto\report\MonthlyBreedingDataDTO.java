package com.cattery.dto.report;

import lombok.Data;

/**
 * 月度繁育数据DTO
 */
@Data
public class MonthlyBreedingDataDTO {
    
    /**
     * 年份
     */
    private Integer year;
    
    /**
     * 月份
     */
    private Integer month;
    
    /**
     * 配种次数
     */
    private Integer matings;
    
    /**
     * 怀孕次数
     */
    private Integer pregnancies;
    
    /**
     * 分娩次数
     */
    private Integer births;
    
    /**
     * 新生小猫数
     */
    private Integer kittens;
    
    /**
     * 成功率
     */
    private Double successRate;
    
    /**
     * 平均窝产仔数
     */
    private Double averageLitterSize;
    
    /**
     * 同比增长率
     */
    private Double yearOverYearGrowth;
}
