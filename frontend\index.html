<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="猫舍管理系统 - 专业的猫咪繁育和健康管理平台">
    <meta name="keywords" content="猫舍管理,猫咪繁育,宠物管理,健康记录">
    <title>猫舍管理系统</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="icon" type="image/x-icon" href="favicon.ico">
</head>
<body>
    <div id="app">
        <!-- 导航栏 -->
        <nav class="navbar" role="navigation" aria-label="主导航">
            <div class="nav-container">
                <div class="nav-brand">
                    <h1>猫舍管理系统</h1>
                </div>
                <ul class="nav-menu" role="menubar">
                    <li role="none">
                        <a href="#dashboard" role="menuitem" aria-label="仪表盘">
                            <span class="nav-icon">📊</span>
                            <span>仪表盘</span>
                        </a>
                    </li>
                    <li role="none">
                        <a href="#cats" role="menuitem" aria-label="猫咪管理">
                            <span class="nav-icon">🐱</span>
                            <span>猫咪管理</span>
                        </a>
                    </li>
                    <li role="none">
                        <a href="#breeding" role="menuitem" aria-label="繁育管理">
                            <span class="nav-icon">💕</span>
                            <span>繁育管理</span>
                        </a>
                    </li>
                    <li role="none">
                        <a href="#health" role="menuitem" aria-label="健康管理">
                            <span class="nav-icon">🏥</span>
                            <span>健康管理</span>
                        </a>
                    </li>
                    <li role="none">
                        <a href="#customers" role="menuitem" aria-label="客户管理">
                            <span class="nav-icon">👥</span>
                            <span>客户管理</span>
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- 主内容区域 -->
        <main class="main-content" role="main">
            <!-- 仪表盘 -->
            <section id="dashboard" class="content-section active" aria-labelledby="dashboard-title">
                <header class="section-header">
                    <h2 id="dashboard-title">仪表盘</h2>
                    <p>猫舍运营概览</p>
                </header>

                <div class="dashboard-grid">
                    <div class="stat-card">
                        <h3>总猫咪数量</h3>
                        <div class="stat-number" aria-label="总猫咪数量">
                            <span id="total-cats">0</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <h3>繁育中</h3>
                        <div class="stat-number" aria-label="繁育中的猫咪数量">
                            <span id="breeding-cats">0</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <h3>健康提醒</h3>
                        <div class="stat-number" aria-label="健康提醒数量">
                            <span id="health-alerts">0</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <h3>客户咨询</h3>
                        <div class="stat-number" aria-label="待处理客户咨询数量">
                            <span id="customer-inquiries">0</span>
                        </div>
                    </div>
                </div>

                <div class="dashboard-charts">
                    <div class="chart-container">
                        <h3>月度统计</h3>
                        <canvas id="monthly-chart" aria-label="月度统计图表"></canvas>
                    </div>
                </div>
            </section>

            <!-- 猫咪管理 -->
            <section id="cats" class="content-section" aria-labelledby="cats-title">
                <header class="section-header">
                    <h2 id="cats-title">猫咪管理</h2>
                    <div class="section-actions">
                        <button type="button" class="btn btn-primary" onclick="showAddCatModal()" aria-label="添加新猫咪">
                            <span class="btn-icon">➕</span>
                            添加猫咪
                        </button>
                    </div>
                </header>

                <!-- 搜索和筛选 -->
                <div class="filter-section" role="search" aria-label="猫咪搜索和筛选">
                    <div class="search-box">
                        <label for="cat-search" class="sr-only">搜索猫咪</label>
                        <input 
                            type="search" 
                            id="cat-search" 
                            name="cat-search"
                            placeholder="搜索猫咪名称、芯片号..." 
                            aria-label="搜索猫咪名称或芯片号"
                        >
                        <button type="button" class="search-btn" aria-label="执行搜索">🔍</button>
                    </div>
                    
                    <div class="filter-controls">
                        <div class="filter-group">
                            <label for="breed-filter">品种筛选</label>
                            <select id="breed-filter" name="breed-filter" aria-label="按品种筛选猫咪">
                                <option value="">全部品种</option>
                                <option value="british-shorthair">英国短毛猫</option>
                                <option value="persian">波斯猫</option>
                                <option value="ragdoll">布偶猫</option>
                                <option value="maine-coon">缅因猫</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="status-filter">状态筛选</label>
                            <select id="status-filter" name="status-filter" aria-label="按状态筛选猫咪">
                                <option value="">全部状态</option>
                                <option value="available">可繁育</option>
                                <option value="pregnant">怀孕中</option>
                                <option value="nursing">哺乳中</option>
                                <option value="retired">已退役</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 猫咪列表 -->
                <div class="cats-grid" id="cats-grid" role="grid" aria-label="猫咪列表">
                    <!-- 猫咪卡片将通过JavaScript动态生成 -->
                </div>
            </section>

            <!-- 媒体管理 -->
            <section id="media" class="content-section" aria-labelledby="media-title">
                <header class="section-header">
                    <h2 id="media-title">媒体管理</h2>
                </header>

                <!-- 文件上传区域 -->
                <div class="upload-section">
                    <div class="upload-area" id="upload-area" role="button" tabindex="0" aria-label="点击或拖拽文件到此处上传">
                        <div class="upload-content">
                            <div class="upload-icon">📁</div>
                            <p>点击或拖拽文件到此处上传</p>
                            <p class="upload-hint">支持 JPG, PNG, GIF, MP4 格式，最大 10MB</p>
                        </div>
                        <input 
                            type="file" 
                            id="file-input" 
                            name="media-files"
                            multiple 
                            accept="image/*,video/*" 
                            aria-label="选择媒体文件上传"
                            style="display: none;"
                        >
                    </div>
                    
                    <div class="upload-progress" id="upload-progress" style="display: none;" role="progressbar" aria-label="上传进度">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progress-fill"></div>
                        </div>
                        <span class="progress-text" id="progress-text">0%</span>
                    </div>
                </div>

                <!-- 媒体展示区域 -->
                <div class="media-gallery" id="media-gallery" role="region" aria-label="媒体文件展示">
                    <!-- 媒体文件将通过JavaScript动态生成 -->
                </div>
            </section>
        </main>

        <!-- 添加猫咪模态框 -->
        <div id="add-cat-modal" class="modal" role="dialog" aria-labelledby="add-cat-title" aria-hidden="true">
            <div class="modal-content">
                <header class="modal-header">
                    <h3 id="add-cat-title">添加新猫咪</h3>
                    <button type="button" class="modal-close" onclick="closeModal('add-cat-modal')" aria-label="关闭对话框">×</button>
                </header>
                
                <form id="add-cat-form" class="modal-body" novalidate>
                    <div class="form-group">
                        <label for="cat-name">猫咪名称 <span class="required" aria-label="必填项">*</span></label>
                        <input 
                            type="text" 
                            id="cat-name" 
                            name="cat-name" 
                            required 
                            aria-required="true"
                            aria-describedby="cat-name-error"
                        >
                        <div id="cat-name-error" class="error-message" role="alert" aria-live="polite"></div>
                    </div>
                    
                    <div class="form-group">
                        <label for="cat-chip-id">芯片号</label>
                        <input 
                            type="text" 
                            id="cat-chip-id" 
                            name="cat-chip-id"
                            aria-describedby="cat-chip-id-help"
                        >
                        <div id="cat-chip-id-help" class="help-text">15位数字芯片编号</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="cat-breed">品种 <span class="required" aria-label="必填项">*</span></label>
                        <select 
                            id="cat-breed" 
                            name="cat-breed" 
                            required 
                            aria-required="true"
                            aria-describedby="cat-breed-error"
                        >
                            <option value="">请选择品种</option>
                            <option value="british-shorthair">英国短毛猫</option>
                            <option value="persian">波斯猫</option>
                            <option value="ragdoll">布偶猫</option>
                            <option value="maine-coon">缅因猫</option>
                        </select>
                        <div id="cat-breed-error" class="error-message" role="alert" aria-live="polite"></div>
                    </div>
                    
                    <div class="form-group">
                        <fieldset>
                            <legend>性别 <span class="required" aria-label="必填项">*</span></legend>
                            <div class="radio-group">
                                <label class="radio-label">
                                    <input type="radio" name="cat-gender" value="male" required aria-required="true">
                                    <span class="radio-custom"></span>
                                    公猫
                                </label>
                                <label class="radio-label">
                                    <input type="radio" name="cat-gender" value="female" required aria-required="true">
                                    <span class="radio-custom"></span>
                                    母猫
                                </label>
                            </div>
                        </fieldset>
                    </div>
                    
                    <div class="form-group">
                        <label for="cat-birth-date">出生日期</label>
                        <input 
                            type="date" 
                            id="cat-birth-date" 
                            name="cat-birth-date"
                            aria-describedby="cat-birth-date-help"
                        >
                        <div id="cat-birth-date-help" class="help-text">如果不确定，可以留空</div>
                    </div>
                </form>
                
                <footer class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('add-cat-modal')">取消</button>
                    <button type="submit" form="add-cat-form" class="btn btn-primary">保存</button>
                </footer>
            </div>
        </div>

        <!-- 加载提示 -->
        <div id="loading" class="loading-overlay" style="display: none;" role="status" aria-label="正在加载">
            <div class="loading-spinner"></div>
            <p>正在加载...</p>
        </div>

        <!-- 消息提示 -->
        <div id="message-container" class="message-container" role="region" aria-live="polite" aria-label="系统消息"></div>
    </div>

    <script src="scripts/main.js"></script>
</body>
</html>