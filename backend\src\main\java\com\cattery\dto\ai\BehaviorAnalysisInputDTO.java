package com.cattery.dto.ai;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 行为分析输入DTO
 */
@Data
public class BehaviorAnalysisInputDTO {
    
    /**
     * 猫咪ID
     */
    private Long catId;
    
    /**
     * 年龄
     */
    private Integer age;
    
    /**
     * 品种
     */
    private String breed;
    
    /**
     * 性别
     */
    private String gender;
    
    /**
     * 行为观察数据
     */
    private List<BehaviorObservationDTO> behaviorObservations;
    
    /**
     * 环境因素
     */
    private Map<String, Object> environmentFactors;
    
    /**
     * 观察周期
     */
    private String observationPeriod;
    
    /**
     * 行为观察DTO
     */
    @Data
    public static class BehaviorObservationDTO {
        /**
         * 行为类型
         */
        private String behaviorType;
        
        /**
         * 频率
         */
        private Double frequency;
        
        /**
         * 强度
         */
        private Double intensity;
        
        /**
         * 持续时间
         */
        private Double duration;
        
        /**
         * 观察时间
         */
        private String observationTime;
        
        /**
         * 备注
         */
        private String notes;
    }
}
