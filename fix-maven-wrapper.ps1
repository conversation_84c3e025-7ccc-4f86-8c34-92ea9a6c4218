# Fix Maven Wrapper Script
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Fix Maven Wrapper" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Set location to backend directory
Set-Location "D:\噔噔\backend"
Write-Host "Current directory: $(Get-Location)" -ForegroundColor Green

# Check if maven-wrapper.jar exists and is valid
$wrapperJar = ".\.mvn\wrapper\maven-wrapper.jar"
if (Test-Path $wrapperJar) {
    $jarSize = (Get-Item $wrapperJar).Length
    Write-Host "Maven wrapper jar exists, size: $jarSize bytes" -ForegroundColor Yellow
    
    if ($jarSize -lt 1000) {
        Write-Host "Maven wrapper jar seems corrupted (too small)" -ForegroundColor Red
        $needsRedownload = $true
    } else {
        Write-Host "Maven wrapper jar seems OK" -ForegroundColor Green
        $needsRedownload = $false
    }
} else {
    Write-Host "Maven wrapper jar not found" -ForegroundColor Red
    $needsRedownload = $true
}

if ($needsRedownload) {
    Write-Host ""
    Write-Host "Downloading fresh Maven wrapper..." -ForegroundColor Cyan
    
    try {
        # Download maven-wrapper.jar
        $wrapperUrl = "https://repo.maven.apache.org/maven2/org/apache/maven/wrapper/maven-wrapper/3.2.0/maven-wrapper-3.2.0.jar"
        Write-Host "Downloading from: $wrapperUrl" -ForegroundColor Yellow
        
        # Create directory if not exists
        $wrapperDir = ".\.mvn\wrapper"
        if (-not (Test-Path $wrapperDir)) {
            New-Item -ItemType Directory -Path $wrapperDir -Force
        }
        
        # Download the jar
        Invoke-WebRequest -Uri $wrapperUrl -OutFile $wrapperJar
        Write-Host "Maven wrapper downloaded successfully" -ForegroundColor Green
        
        # Verify download
        if (Test-Path $wrapperJar) {
            $newSize = (Get-Item $wrapperJar).Length
            Write-Host "New jar size: $newSize bytes" -ForegroundColor Green
        }
    } catch {
        Write-Host "Failed to download Maven wrapper: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host ""
        Write-Host "Alternative solutions:" -ForegroundColor Yellow
        Write-Host "1. Install Maven manually" -ForegroundColor Cyan
        Write-Host "2. Use IDE to run the project" -ForegroundColor Cyan
        Write-Host "3. Try running with java directly" -ForegroundColor Cyan
        return
    }
}

Write-Host ""
Write-Host "Testing Maven wrapper..." -ForegroundColor Cyan

try {
    # Test maven wrapper
    $output = & ".\mvnw.cmd" --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Maven wrapper is working!" -ForegroundColor Green
        Write-Host $output
        
        Write-Host ""
        Write-Host "Starting Spring Boot application..." -ForegroundColor Cyan
        & ".\mvnw.cmd" spring-boot:run
    } else {
        Write-Host "Maven wrapper test failed:" -ForegroundColor Red
        Write-Host $output
        
        Write-Host ""
        Write-Host "Trying alternative approach..." -ForegroundColor Yellow
        
        # Try to run with java directly
        Write-Host "Attempting to run with java directly..." -ForegroundColor Cyan
        
        # Check if target directory exists and has compiled classes
        if (Test-Path "target\classes") {
            Write-Host "Found compiled classes, trying to run directly..." -ForegroundColor Yellow
            
            # Find the main class
            $mainClass = "com.cattery.CatteryManagementApplication"
            
            # Try to run with java
            & "$env:JAVA_HOME\bin\java.exe" -cp "target\classes;target\dependency\*" $mainClass
        } else {
            Write-Host "No compiled classes found. Need to compile first." -ForegroundColor Red
            Write-Host ""
            Write-Host "Manual compilation steps:" -ForegroundColor Yellow
            Write-Host "1. Install Maven from https://maven.apache.org/download.cgi" -ForegroundColor Cyan
            Write-Host "2. Add Maven to PATH" -ForegroundColor Cyan
            Write-Host "3. Run: mvn clean compile spring-boot:run" -ForegroundColor Cyan
        }
    }
} catch {
    Write-Host "Error running Maven wrapper: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Read-Host "Press Enter to exit"
