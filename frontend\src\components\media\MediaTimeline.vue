<template>
  <div class="media-timeline">
    <div class="timeline-header">
      <h3>{{ title }}</h3>
      <div class="timeline-controls">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          @change="handleDateRangeChange"
        />
        
        <el-select v-model="selectedMediaType" placeholder="媒体类型" clearable @change="handleTypeChange">
          <el-option label="全部" value="" />
          <el-option label="照片" value="PHOTO" />
          <el-option label="视频" value="VIDEO" />
        </el-select>
      </div>
    </div>
    
    <el-empty v-if="timelineData.length === 0" description="暂无媒体记录" />
    
    <div v-else class="timeline-content">
      <el-timeline>
        <el-timeline-item
          v-for="group in timelineData"
          :key="group.date"
          :timestamp="formatDate(group.date)"
          placement="top"
        >
          <div class="timeline-group">
            <div class="group-header">
              <span class="group-date">{{ formatDateFull(group.date) }}</span>
              <span class="group-count">{{ group.mediaFiles.length }} 个文件</span>
            </div>
            
            <div class="media-grid">
              <div
                v-for="media in group.mediaFiles"
                :key="media.id"
                class="media-item"
                :class="{ 'is-primary': media.isPrimary }"
                @click="handleMediaClick(media)"
              >
                <div class="media-thumbnail">
                  <img
                    v-if="media.mediaType === 'PHOTO' && media.thumbnailUrl"
                    :src="media.thumbnailUrl"
                    :alt="media.title || media.originalFileName"
                  />
                  <div v-else-if="media.mediaType === 'VIDEO'" class="video-thumbnail">
                    <img
                      v-if="media.previewImageUrl"
                      :src="media.previewImageUrl"
                      :alt="media.title || media.originalFileName"
                    />
                    <el-icon class="video-icon"><VideoPlay /></el-icon>
                  </div>
                  <div v-else class="default-thumbnail">
                    <el-icon><Document /></el-icon>
                  </div>
                  
                  <div class="media-overlay">
                    <div class="media-actions">
                      <el-button size="small" type="primary" circle @click.stop="handleMediaClick(media)">
                        <el-icon><View /></el-icon>
                      </el-button>
                      <el-button
                        v-if="canSetPrimary && media.mediaType === 'PHOTO' && !media.isPrimary"
                        size="small"
                        type="success"
                        circle
                        @click.stop="setPrimaryPhoto(media)"
                      >
                        <el-icon><Star /></el-icon>
                      </el-button>
                      <el-button
                        v-if="canDelete"
                        size="small"
                        type="danger"
                        circle
                        @click.stop="deleteMedia(media)"
                      >
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </div>
                  </div>
                  
                  <div class="media-badges">
                    <el-tag v-if="media.isPrimary" size="small" type="success">主要</el-tag>
                  </div>
                </div>
                
                <div class="media-info">
                  <div class="media-title">{{ media.title || formatFileName(media.originalFileName) }}</div>
                  <div class="media-meta">
                    <span class="media-time">{{ formatTime(media.createdAt) }}</span>
                    <span class="media-size">{{ formatFileSize(media.fileSize) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>
    
    <!-- 媒体预览对话框 -->
    <el-dialog v-model="previewDialogVisible" :title="selectedMedia?.title || '媒体预览'" width="800px">
      <div v-if="selectedMedia" class="media-preview">
        <div v-if="selectedMedia.mediaType === 'PHOTO'" class="photo-preview">
          <img :src="selectedMedia.fileUrl" :alt="selectedMedia.title || selectedMedia.originalFileName" />
        </div>
        
        <div v-else-if="selectedMedia.mediaType === 'VIDEO'" class="video-preview">
          <video controls>
            <source :src="selectedMedia.fileUrl" :type="selectedMedia.mimeType">
            您的浏览器不支持视频播放
          </video>
        </div>
        
        <div class="media-details">
          <div class="detail-item">
            <span class="detail-label">文件名:</span>
            <span class="detail-value">{{ selectedMedia.originalFileName }}</span>
          </div>
          
          <div class="detail-item">
            <span class="detail-label">类型:</span>
            <span class="detail-value">{{ formatMediaType(selectedMedia.mediaType) }}</span>
          </div>
          
          <div class="detail-item">
            <span class="detail-label">大小:</span>
            <span class="detail-value">{{ formatFileSize(selectedMedia.fileSize) }}</span>
          </div>
          
          <div class="detail-item">
            <span class="detail-label">上传时间:</span>
            <span class="detail-value">{{ formatDateTime(selectedMedia.createdAt) }}</span>
          </div>
          
          <div v-if="selectedMedia.width && selectedMedia.height" class="detail-item">
            <span class="detail-label">尺寸:</span>
            <span class="detail-value">{{ selectedMedia.width }}x{{ selectedMedia.height }}</span>
          </div>
          
          <div v-if="selectedMedia.description" class="detail-item description">
            <span class="detail-label">描述:</span>
            <span class="detail-value">{{ selectedMedia.description }}</span>
          </div>
        </div>
        
        <div class="media-actions">
          <el-button-group>
            <el-button
              v-if="canSetPrimary && selectedMedia.mediaType === 'PHOTO' && !selectedMedia.isPrimary"
              type="success"
              @click="setPrimaryPhoto(selectedMedia)"
            >
              设为主要照片
            </el-button>
            <el-button
              v-if="canEdit"
              type="primary"
              @click="editMedia(selectedMedia)"
            >
              编辑信息
            </el-button>
            <el-button
              v-if="canDelete"
              type="danger"
              @click="deleteMedia(selectedMedia)"
            >
              删除
            </el-button>
          </el-button-group>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { VideoPlay, Document, View, Star, Delete } from '@element-plus/icons-vue'
import { mediaApi } from '@/api/media'

export default {
  name: 'MediaTimeline',
  components: {
    VideoPlay,
    Document,
    View,
    Star,
    Delete
  },
  props: {
    // 猫咪ID
    catId: {
      type: [String, Number],
      required: true
    },
    // 时间线标题
    title: {
      type: String,
      default: '媒体时间线'
    },
    // 是否可以设置主要照片
    canSetPrimary: {
      type: Boolean,
      default: true
    },
    // 是否可以编辑
    canEdit: {
      type: Boolean,
      default: true
    },
    // 是否可以删除
    canDelete: {
      type: Boolean,
      default: true
    }
  },
  emits: ['media-updated', 'primary-photo-changed'],
  setup(props, { emit }) {
    // 响应式数据
    const mediaFiles = ref([])
    const dateRange = ref([])
    const selectedMediaType = ref('')
    const previewDialogVisible = ref(false)
    const selectedMedia = ref(null)
    const loading = ref(false)
    
    // 计算属性 - 时间线数据
    const timelineData = computed(() => {
      const filtered = mediaFiles.value.filter(media => {
        // 按类型筛选
        if (selectedMediaType.value && media.mediaType !== selectedMediaType.value) {
          return false
        }
        
        // 按日期范围筛选
        if (dateRange.value && dateRange.value.length === 2) {
          const mediaDate = new Date(media.createdAt).toDateString()
          const startDate = new Date(dateRange.value[0]).toDateString()
          const endDate = new Date(dateRange.value[1]).toDateString()
          
          if (mediaDate < startDate || mediaDate > endDate) {
            return false
          }
        }
        
        return true
      })
      
      // 按日期分组
      const grouped = {}
      filtered.forEach(media => {
        const date = new Date(media.createdAt).toDateString()
        if (!grouped[date]) {
          grouped[date] = []
        }
        grouped[date].push(media)
      })
      
      // 转换为数组并排序
      return Object.keys(grouped)
        .sort((a, b) => new Date(b) - new Date(a)) // 最新的在前
        .map(date => ({
          date,
          mediaFiles: grouped[date].sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
        }))
    })
    
    // 加载媒体文件
    const loadMediaFiles = async () => {
      loading.value = true
      try {
        const response = await mediaApi.getCatMediaFiles(props.catId)
        mediaFiles.value = response.data || []
      } catch (error) {
        console.error('加载媒体文件失败:', error)
        ElMessage.error('加载媒体文件失败')
      } finally {
        loading.value = false
      }
    }
    
    // 处理日期范围变更
    const handleDateRangeChange = (range) => {
      dateRange.value = range
    }
    
    // 处理类型变更
    const handleTypeChange = (type) => {
      selectedMediaType.value = type
    }
    
    // 处理媒体点击
    const handleMediaClick = (media) => {
      selectedMedia.value = media
      previewDialogVisible.value = true
    }
    
    // 设置主要照片
    const setPrimaryPhoto = async (media) => {
      try {
        await mediaApi.setPrimaryPhoto(props.catId, media.id)
        ElMessage.success('设置主要照片成功')
        
        // 更新本地数据
        mediaFiles.value.forEach(item => {
          item.isPrimary = item.id === media.id
        })
        
        emit('primary-photo-changed', media)
        emit('media-updated')
        
        if (previewDialogVisible.value) {
          selectedMedia.value.isPrimary = true
        }
      } catch (error) {
        console.error('设置主要照片失败:', error)
        ElMessage.error('设置主要照片失败')
      }
    }
    
    // 删除媒体文件
    const deleteMedia = async (media) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除媒体文件 "${media.title || media.originalFileName}" 吗？`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        await mediaApi.deleteMedia(props.catId, media.id)
        ElMessage.success('删除成功')
        
        // 从列表中移除
        const index = mediaFiles.value.findIndex(item => item.id === media.id)
        if (index > -1) {
          mediaFiles.value.splice(index, 1)
        }
        
        emit('media-updated')
        
        // 如果正在预览被删除的媒体，关闭预览
        if (previewDialogVisible.value && selectedMedia.value?.id === media.id) {
          previewDialogVisible.value = false
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除媒体文件失败:', error)
          ElMessage.error('删除失败')
        }
      }
    }
    
    // 编辑媒体信息
    const editMedia = (media) => {
      // 这里可以触发编辑对话框或跳转到编辑页面
      emit('edit-media', media)
    }
    
    // 格式化文件名
    const formatFileName = (fileName) => {
      if (!fileName) return ''
      const lastDotIndex = fileName.lastIndexOf('.')
      return lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName
    }
    
    // 格式化文件大小
    const formatFileSize = (size) => {
      if (!size) return '0 B'
      
      if (size < 1024) {
        return size + ' B'
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(1) + ' KB'
      } else if (size < 1024 * 1024 * 1024) {
        return (size / (1024 * 1024)).toFixed(1) + ' MB'
      } else {
        return (size / (1024 * 1024 * 1024)).toFixed(1) + ' GB'
      }
    }
    
    // 格式化媒体类型
    const formatMediaType = (type) => {
      const typeMap = {
        'PHOTO': '照片',
        'VIDEO': '视频',
        'DOCUMENT': '文档'
      }
      return typeMap[type] || type
    }
    
    // 格式化日期
    const formatDate = (dateStr) => {
      const date = new Date(dateStr)
      const today = new Date()
      const yesterday = new Date(today)
      yesterday.setDate(yesterday.getDate() - 1)
      
      if (date.toDateString() === today.toDateString()) {
        return '今天'
      } else if (date.toDateString() === yesterday.toDateString()) {
        return '昨天'
      } else {
        return date.toLocaleDateString('zh-CN', {
          month: 'long',
          day: 'numeric'
        })
      }
    }
    
    // 格式化完整日期
    const formatDateFull = (dateStr) => {
      const date = new Date(dateStr)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      })
    }
    
    // 格式化时间
    const formatTime = (dateTimeStr) => {
      const date = new Date(dateTimeStr)
      return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }
    
    // 格式化日期时间
    const formatDateTime = (dateTimeStr) => {
      const date = new Date(dateTimeStr)
      return date.toLocaleString('zh-CN')
    }
    
    // 组件挂载时加载数据
    onMounted(() => {
      loadMediaFiles()
    })
    
    return {
      mediaFiles,
      timelineData,
      dateRange,
      selectedMediaType,
      previewDialogVisible,
      selectedMedia,
      loading,
      loadMediaFiles,
      handleDateRangeChange,
      handleTypeChange,
      handleMediaClick,
      setPrimaryPhoto,
      deleteMedia,
      editMedia,
      formatFileName,
      formatFileSize,
      formatMediaType,
      formatDate,
      formatDateFull,
      formatTime,
      formatDateTime
    }
  }
}
</script>

<style scoped>
.media-timeline {
  width: 100%;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.timeline-header h3 {
  margin: 0;
  color: #303133;
}

.timeline-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.timeline-content {
  margin-top: 20px;
}

.timeline-group {
  margin-bottom: 20px;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.group-date {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.group-count {
  font-size: 14px;
  color: #909399;
}

.media-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 15px;
}

.media-item {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;
  background: white;
  position: relative;
}

.media-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.media-item:hover .media-overlay {
  opacity: 1;
}

.media-item.is-primary {
  border-color: #67c23a;
  box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.2);
}

.media-thumbnail {
  position: relative;
  width: 100%;
  height: 120px;
  overflow: hidden;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.media-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-thumbnail {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-icon {
  position: absolute;
  font-size: 24px;
  color: white;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  padding: 6px;
}

.default-thumbnail {
  font-size: 36px;
  color: #c0c4cc;
}

.media-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.media-actions {
  display: flex;
  gap: 8px;
}

.media-badges {
  position: absolute;
  top: 8px;
  right: 8px;
}

.media-info {
  padding: 10px;
}

.media-title {
  font-size: 13px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.media-meta {
  font-size: 11px;
  color: #909399;
  display: flex;
  justify-content: space-between;
}

.media-preview {
  text-align: center;
}

.photo-preview img {
  max-width: 100%;
  max-height: 500px;
  border-radius: 8px;
}

.video-preview video {
  max-width: 100%;
  max-height: 500px;
  border-radius: 8px;
}

.media-details {
  margin-top: 20px;
  text-align: left;
}

.detail-item {
  display: flex;
  margin-bottom: 10px;
}

.detail-item.description {
  flex-direction: column;
}

.detail-label {
  font-weight: 500;
  color: #606266;
  width: 100px;
  flex-shrink: 0;
}

.detail-value {
  color: #303133;
  flex: 1;
}

.detail-item.description .detail-value {
  margin-top: 5px;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
}

.media-actions {
  margin-top: 20px;
  text-align: center;
}

/* 时间线样式覆盖 */
:deep(.el-timeline-item__timestamp) {
  font-weight: 500;
  color: #409eff;
}

:deep(.el-timeline-item__node) {
  background-color: #409eff;
}
</style>