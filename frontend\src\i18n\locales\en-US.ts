export default {
  // Application basic information
  app: {
    title: 'Cattery Management System',
    description: 'Professional Cattery Management System',
    version: 'Version 1.0.0'
  },

  // Common text
  common: {
    // Actions
    add: 'Add',
    edit: 'Edit',
    delete: 'Delete',
    save: 'Save',
    cancel: 'Cancel',
    confirm: 'Confirm',
    submit: 'Submit',
    reset: 'Reset',
    search: 'Search',
    filter: 'Filter',
    export: 'Export',
    import: 'Import',
    upload: 'Upload',
    download: 'Download',
    view: 'View',
    detail: 'Detail',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    finish: 'Finish',
    close: 'Close',
    refresh: 'Refresh',
    retry: 'Retry',
    loading: 'Loading...',
    noData: 'No Data',
    
    // Status
    success: 'Success',
    error: 'Error',
    warning: 'Warning',
    info: 'Info',
    pending: 'Pending',
    processing: 'Processing',
    completed: 'Completed',
    failed: 'Failed',
    active: 'Active',
    inactive: 'Inactive',
    enabled: 'Enabled',
    disabled: 'Disabled',
    
    // Time
    today: 'Today',
    yesterday: 'Yesterday',
    tomorrow: 'Tomorrow',
    thisWeek: 'This Week',
    thisMonth: 'This Month',
    thisYear: 'This Year',
    date: 'Date',
    time: 'Time',
    datetime: 'Date Time',
    year: 'Year',
    month: 'Month',
    day: 'Day',
    hour: 'Hour',
    minute: 'Minute',
    second: 'Second',
    years: 'Years',
    months: 'Months',
    days: 'Days',
    hours: 'Hours',
    minutes: 'Minutes',
    seconds: 'Seconds',
    
    // Quantity units
    total: 'Total',
    count: 'Count',
    amount: 'Amount',
    price: 'Price',
    weight: 'Weight',
    height: 'Height',
    length: 'Length',
    age: 'Age',
    
    // Confirmation dialogs
    confirmDelete: 'Are you sure you want to delete?',
    confirmSave: 'Are you sure you want to save?',
    confirmCancel: 'Are you sure you want to cancel?',
    deleteSuccess: 'Deleted successfully',
    saveSuccess: 'Saved successfully',
    updateSuccess: 'Updated successfully',
    createSuccess: 'Created successfully',
    operationSuccess: 'Operation successful',
    operationFailed: 'Operation failed',
    
    // Language related
    language: 'Language',
    languageChanged: 'Language changed successfully',
    languageChangeFailed: 'Failed to change language',
    
    // Others
    all: 'All',
    none: 'None',
    unknown: 'Unknown',
    other: 'Other',
    more: 'More',
    less: 'Less',
    expand: 'Expand',
    collapse: 'Collapse',
    required: 'Required',
    optional: 'Optional',
    placeholder: 'Please enter...',
    selectPlaceholder: 'Please select...',
    clearFilters: 'Clear Filters'
  },

  // Navigation menu
  navigation: {
    dashboard: 'Dashboard',
    cats: 'Cat Management',
    health: 'Health Management',
    breeding: 'Breeding Management',
    customers: 'Customer Management',
    inventory: 'Inventory Management',
    finance: 'Finance Management',
    reports: 'Reports',
    ai: 'AI Features',
    settings: 'Settings',
    profile: 'Profile',
    logout: 'Logout',
    home: 'Home',
    more: 'More'
  },

  // User authentication
  auth: {
    login: 'Login',
    logout: 'Logout',
    register: 'Register',
    forgotPassword: 'Forgot Password',
    resetPassword: 'Reset Password',
    changePassword: 'Change Password',
    username: 'Username',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    email: 'Email',
    phone: 'Phone',
    rememberMe: 'Remember Me',
    loginSuccess: 'Login successful',
    loginFailed: 'Login failed',
    logoutSuccess: 'Logout successful',
    registerSuccess: 'Registration successful',
    passwordChanged: 'Password changed successfully',
    invalidCredentials: 'Invalid username or password',
    accountLocked: 'Account is locked',
    sessionExpired: 'Session expired, please login again'
  },

  // Cat management
  cat: {
    // Basic information
    name: 'Name',
    breed: 'Breed',
    gender: 'Gender',
    dateOfBirth: 'Date of Birth',
    color: 'Color',
    pattern: 'Pattern',
    weight: 'Weight',
    microchipId: 'Microchip ID',
    registrationNumber: 'Registration Number',
    status: 'Status',
    description: 'Description',
    notes: 'Notes',
    
    // Gender
    gender: {
      male: 'Male',
      female: 'Female',
      unknown: 'Unknown'
    },
    
    // Status
    status: {
      available: 'Available',
      adopted: 'Adopted',
      reserved: 'Reserved',
      breeding: 'Breeding',
      medical: 'Medical',
      quarantine: 'Quarantine'
    },
    
    // Actions
    add: 'Add Cat',
    edit: 'Edit Cat',
    delete: 'Delete Cat',
    view: 'View Details',
    addFirst: 'Add First Cat',
    
    // List
    list: 'Cat List',
    noCats: 'No Cats',
    noCatsDescription: 'No cats have been added yet. Click the button below to add your first cat.',
    searchPlaceholder: 'Search cat name or breed...',
    
    // Detail page
    basicInfo: 'Basic Information',
    healthInfo: 'Health Information',
    breedingInfo: 'Breeding Information',
    photos: 'Photos',
    pedigree: 'Pedigree',
    
    // Photo management
    uploadPhoto: 'Upload Photo',
    deletePhoto: 'Delete Photo',
    setPrimaryPhoto: 'Set as Primary Photo',
    photoUploaded: 'Photo uploaded successfully',
    photoDeleted: 'Photo deleted successfully',
    
    // Pedigree management
    father: 'Father',
    mother: 'Mother',
    offspring: 'Offspring',
    siblings: 'Siblings',
    pedigreeTree: 'Pedigree Tree',
    
    // Validation messages
    nameRequired: 'Please enter cat name',
    breedRequired: 'Please select breed',
    genderRequired: 'Please select gender',
    dateOfBirthRequired: 'Please select date of birth',
    
    // Error messages
    loadError: 'Failed to load cat information',
    saveError: 'Failed to save cat information',
    deleteError: 'Failed to delete cat'
  },

  // Health management
  health: {
    // Record types
    recordType: {
      vaccination: 'Vaccination',
      checkup: 'Checkup',
      treatment: 'Treatment',
      surgery: 'Surgery',
      dental: 'Dental',
      grooming: 'Grooming',
      geneticTest: 'Genetic Test',
      weightCheck: 'Weight Check'
    },
    
    // Basic information
    record: 'Health Record',
    recordDate: 'Record Date',
    recordType: 'Record Type',
    veterinarian: 'Veterinarian',
    clinic: 'Clinic',
    notes: 'Notes',
    nextAppointment: 'Next Appointment',
    
    // Vaccine management
    vaccine: 'Vaccine',
    vaccineName: 'Vaccine Name',
    vaccineDate: 'Vaccination Date',
    nextVaccineDate: 'Next Vaccination Date',
    vaccineStatus: 'Vaccine Status',
    upToDate: 'Up to Date',
    overdue: 'Overdue',
    dueSoon: 'Due Soon',
    
    // Checkup
    checkup: 'Checkup',
    checkupDate: 'Checkup Date',
    checkupResult: 'Checkup Result',
    normal: 'Normal',
    abnormal: 'Abnormal',
    
    // Treatment
    treatment: 'Treatment',
    treatmentDate: 'Treatment Date',
    diagnosis: 'Diagnosis',
    medication: 'Medication',
    dosage: 'Dosage',
    
    // Statistics
    healthScore: 'Health Score',
    lastCheckup: 'Last Checkup',
    vaccinesDue: 'Vaccines Due',
    checkupsDue: 'Checkups Due',
    overallHealth: 'Overall Health'
  },

  // Breeding management
  breeding: {
    // Basic information
    mating: 'Mating',
    pregnancy: 'Pregnancy',
    birth: 'Birth',
    kitten: 'Kitten',
    
    // Mating
    matingDate: 'Mating Date',
    maleParent: 'Male Parent',
    femaleParent: 'Female Parent',
    matingType: 'Mating Type',
    matingResult: 'Mating Result',
    
    // Pregnancy
    pregnancyDate: 'Pregnancy Date',
    dueDate: 'Due Date',
    pregnancyStatus: 'Pregnancy Status',
    ultrasoundDate: 'Ultrasound Date',
    expectedKittens: 'Expected Kittens',
    
    // Birth
    birthDate: 'Birth Date',
    litterSize: 'Litter Size',
    birthWeight: 'Birth Weight',
    complications: 'Complications',
    
    // Kitten
    kittenName: 'Kitten Name',
    kittenGender: 'Kitten Gender',
    kittenWeight: 'Kitten Weight',
    kittenStatus: 'Kitten Status',
    
    // Status
    status: {
      planned: 'Planned',
      mated: 'Mated',
      pregnant: 'Pregnant',
      born: 'Born',
      weaned: 'Weaned'
    }
  },

  // Customer management
  customer: {
    // Basic information
    name: 'Customer Name',
    email: 'Email',
    phone: 'Phone',
    address: 'Address',
    customerType: 'Customer Type',
    registrationDate: 'Registration Date',
    
    // Customer types
    type: {
      potential: 'Potential',
      active: 'Active',
      adopted: 'Adopted',
      breeder: 'Breeder'
    },
    
    // Inquiry management
    inquiry: 'Inquiry',
    inquiryDate: 'Inquiry Date',
    inquiryType: 'Inquiry Type',
    inquiryStatus: 'Inquiry Status',
    response: 'Response',
    
    // Follow-up records
    followUp: 'Follow Up',
    followUpDate: 'Follow Up Date',
    followUpType: 'Follow Up Type',
    followUpResult: 'Follow Up Result'
  },

  // Inventory management
  inventory: {
    // Basic information
    item: 'Item',
    itemName: 'Item Name',
    category: 'Category',
    quantity: 'Quantity',
    unit: 'Unit',
    price: 'Price',
    supplier: 'Supplier',
    
    // Stock operations
    stockIn: 'Stock In',
    stockOut: 'Stock Out',
    stockAdjustment: 'Stock Adjustment',
    stockCheck: 'Stock Check',
    
    // Alerts
    lowStock: 'Low Stock',
    outOfStock: 'Out of Stock',
    minStock: 'Min Stock',
    safeStock: 'Safe Stock'
  },

  // Finance management
  finance: {
    // Basic information
    transaction: 'Transaction',
    transactionDate: 'Transaction Date',
    transactionType: 'Transaction Type',
    amount: 'Amount',
    description: 'Description',
    category: 'Category',
    
    // Transaction types
    type: {
      income: 'Income',
      expense: 'Expense',
      transfer: 'Transfer'
    },
    
    // Income categories
    incomeCategory: {
      adoption: 'Adoption Fee',
      breeding: 'Breeding Fee',
      boarding: 'Boarding Fee',
      grooming: 'Grooming Fee',
      other: 'Other Income'
    },
    
    // Expense categories
    expenseCategory: {
      food: 'Food',
      medical: 'Medical',
      supplies: 'Supplies',
      utilities: 'Utilities',
      rent: 'Rent',
      other: 'Other Expense'
    },
    
    // Statistics
    totalIncome: 'Total Income',
    totalExpense: 'Total Expense',
    netIncome: 'Net Income',
    monthlyIncome: 'Monthly Income',
    monthlyExpense: 'Monthly Expense'
  },

  // AI features
  ai: {
    // Cat recognition
    catRecognition: 'Cat Recognition',
    breedRecognition: 'Breed Recognition',
    uploadImage: 'Upload Image',
    takePhoto: 'Take Photo',
    recognizing: 'Recognizing...',
    recognitionResult: 'Recognition Result',
    confidence: 'Confidence',
    
    // Health prediction
    healthPrediction: 'Health Prediction',
    healthScore: 'Health Score',
    riskLevel: 'Risk Level',
    riskFactors: 'Risk Factors',
    recommendations: 'Recommendations',
    
    // Behavior analysis
    behaviorAnalysis: 'Behavior Analysis',
    behaviorScore: 'Behavior Score',
    behaviorPattern: 'Behavior Pattern',
    abnormalBehavior: 'Abnormal Behavior',
    
    // Service status
    serviceStatus: 'Service Status',
    available: 'Available',
    unavailable: 'Unavailable',
    modelVersion: 'Model Version',
    accuracy: 'Accuracy'
  },

  // Reports
  reports: {
    // Basic information
    report: 'Report',
    reportType: 'Report Type',
    reportPeriod: 'Report Period',
    generateReport: 'Generate Report',
    exportReport: 'Export Report',
    
    // Report types
    type: {
      catSummary: 'Cat Summary',
      healthSummary: 'Health Summary',
      breedingSummary: 'Breeding Summary',
      financeSummary: 'Finance Summary',
      customerSummary: 'Customer Summary'
    },
    
    // Time periods
    period: {
      daily: 'Daily',
      weekly: 'Weekly',
      monthly: 'Monthly',
      quarterly: 'Quarterly',
      yearly: 'Yearly'
    }
  },

  // Settings
  settings: {
    // Basic settings
    general: 'General',
    appearance: 'Appearance',
    language: 'Language',
    notification: 'Notification',
    security: 'Security',
    backup: 'Backup',
    
    // Theme
    theme: 'Theme',
    lightTheme: 'Light Theme',
    darkTheme: 'Dark Theme',
    autoTheme: 'Auto Theme',
    
    // Notification
    emailNotification: 'Email Notification',
    smsNotification: 'SMS Notification',
    pushNotification: 'Push Notification',
    
    // Security
    changePassword: 'Change Password',
    twoFactorAuth: 'Two-Factor Authentication',
    loginHistory: 'Login History',
    
    // Backup
    autoBackup: 'Auto Backup',
    backupFrequency: 'Backup Frequency',
    backupLocation: 'Backup Location'
  },

  // Dashboard
  dashboard: {
    welcome: 'Welcome Back',
    todayOverview: 'Today\'s Overview',
    quickActions: 'Quick Actions',
    recentActivities: 'Recent Activities',
    upcomingEvents: 'Upcoming Events',
    healthOverview: 'Health Overview',
    breedingOverview: 'Breeding Overview',
    
    // Statistics cards
    totalCats: 'Total Cats',
    healthAlerts: 'Health Alerts',
    breeding: 'Breeding',
    revenue: 'Monthly Revenue',
    
    // Quick actions
    addCat: 'Add Cat',
    healthCheck: 'Health Check',
    
    // Health overview
    vaccinesDue: 'Vaccines Due',
    checkupsDue: 'Checkups Due',
    
    // Breeding overview
    pregnant: 'Pregnant',
    dueSoon: 'Due Soon',
    
    // Alerts
    alerts: 'Alerts',
    viewAllAlerts: 'View All Alerts'
  },

  // Error messages
  error: {
    // Network errors
    networkError: 'Network connection failed',
    serverError: 'Server error',
    timeoutError: 'Request timeout',
    
    // Permission errors
    unauthorized: 'Unauthorized access',
    forbidden: 'Insufficient permissions',
    
    // Data errors
    dataNotFound: 'Data not found',
    dataInvalid: 'Invalid data format',
    
    // File errors
    fileUploadError: 'File upload failed',
    fileSizeError: 'File size exceeds limit',
    fileTypeError: 'File type not supported',
    
    // General errors
    unknownError: 'Unknown error',
    operationFailed: 'Operation failed'
  }
}
