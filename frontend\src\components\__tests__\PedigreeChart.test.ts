import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { ElButton, ElDialog, ElDescriptions } from 'element-plus'
import PedigreeChart from '../PedigreeChart.vue'

// Mock API modules
vi.mock('@/api', () => ({
  catApi: {
    getPedigree: vi.fn(() => Promise.resolve({
      id: 1,
      name: '测试猫咪',
      breedName: '英国短毛猫',
      gender: 'FEMALE',
      dateOfBirth: '2023-01-01',
      color: '银渐层',
      primaryPhoto: 'http://example.com/photo.jpg',
      father: {
        id: 2,
        name: '父猫',
        breedName: '英国短毛猫',
        gender: 'MALE',
        dateOfBirth: '2021-01-01',
        color: '蓝色',
        father: {
          id: 4,
          name: '爷爷',
          breedName: '英国短毛猫',
          gender: 'MALE',
          dateOfBirth: '2019-01-01',
          color: '蓝色'
        },
        mother: {
          id: 5,
          name: '奶奶',
          breedName: '英国短毛猫',
          gender: 'FEMALE',
          dateOfBirth: '2019-01-01',
          color: '银渐层'
        }
      },
      mother: {
        id: 3,
        name: '母猫',
        breedName: '英国短毛猫',
        gender: 'FEMALE',
        dateOfBirth: '2021-01-01',
        color: '银渐层',
        father: {
          id: 6,
          name: '外公',
          breedName: '英国短毛猫',
          gender: 'MALE',
          dateOfBirth: '2019-01-01',
          color: '蓝色'
        },
        mother: {
          id: 7,
          name: '外婆',
          breedName: '英国短毛猫',
          gender: 'FEMALE',
          dateOfBirth: '2019-01-01',
          color: '银渐层'
        }
      }
    }))
  }
}))

// Mock router
const mockPush = vi.fn()
vi.mock('vue-router', () => ({
  useRouter: () => ({
    push: mockPush
  })
}))

describe('PedigreeChart', () => {
  let wrapper: any

  beforeEach(() => {
    wrapper = mount(PedigreeChart, {
      props: {
        catId: 1
      },
      global: {
        components: {
          ElButton,
          ElDialog,
          ElDescriptions
        },
        stubs: {
          'el-button-group': true,
          'el-skeleton': true,
          'el-empty': true,
          'el-descriptions-item': true
        }
      }
    })
  })

  it('renders properly', () => {
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.pedigree-chart').exists()).toBe(true)
  })

  it('displays loading state initially', () => {
    expect(wrapper.vm.loading).toBe(true)
    expect(wrapper.find('.loading').exists()).toBe(true)
  })

  it('displays toolbar with zoom controls', async () => {
    wrapper.vm.loading = false
    wrapper.vm.pedigreeData = { id: 1, name: '测试猫咪' }
    await wrapper.vm.$nextTick()

    const toolbar = wrapper.find('.toolbar')
    expect(toolbar.exists()).toBe(true)
    
    const zoomButtons = toolbar.findAll('.el-button')
    expect(zoomButtons.length).toBeGreaterThanOrEqual(3) // 放大、缩小、重置按钮
  })

  it('displays SVG pedigree chart', async () => {
    wrapper.vm.loading = false
    wrapper.vm.pedigreeData = { id: 1, name: '测试猫咪' }
    await wrapper.vm.$nextTick()

    const svg = wrapper.find('.pedigree-svg')
    expect(svg.exists()).toBe(true)
  })

  it('displays legend', async () => {
    wrapper.vm.loading = false
    wrapper.vm.pedigreeData = { id: 1, name: '测试猫咪' }
    await wrapper.vm.$nextTick()

    const legend = wrapper.find('.legend')
    expect(legend.exists()).toBe(true)
    
    const legendItems = legend.findAll('.legend-item')
    expect(legendItems.length).toBe(3) // 雄性、雌性、当前猫咪
  })

  it('handles zoom in correctly', async () => {
    wrapper.vm.loading = false
    wrapper.vm.pedigreeData = { id: 1, name: '测试猫咪' }
    await wrapper.vm.$nextTick()

    const initialZoom = wrapper.vm.zoomLevel
    wrapper.vm.zoomIn()
    
    expect(wrapper.vm.zoomLevel).toBeGreaterThan(initialZoom)
    expect(wrapper.vm.zoomLevel).toBeLessThanOrEqual(3) // 最大缩放限制
  })

  it('handles zoom out correctly', async () => {
    wrapper.vm.loading = false
    wrapper.vm.pedigreeData = { id: 1, name: '测试猫咪' }
    wrapper.vm.zoomLevel = 2
    await wrapper.vm.$nextTick()

    const initialZoom = wrapper.vm.zoomLevel
    wrapper.vm.zoomOut()
    
    expect(wrapper.vm.zoomLevel).toBeLessThan(initialZoom)
    expect(wrapper.vm.zoomLevel).toBeGreaterThanOrEqual(0.5) // 最小缩放限制
  })

  it('resets zoom correctly', async () => {
    wrapper.vm.loading = false
    wrapper.vm.pedigreeData = { id: 1, name: '测试猫咪' }
    wrapper.vm.zoomLevel = 2
    await wrapper.vm.$nextTick()

    wrapper.vm.resetZoom()
    
    expect(wrapper.vm.zoomLevel).toBe(1)
  })

  it('toggles between simple and full pedigree view', async () => {
    wrapper.vm.loading = false
    wrapper.vm.pedigreeData = { id: 1, name: '测试猫咪' }
    await wrapper.vm.$nextTick()

    const initialShowFull = wrapper.vm.showFullPedigree
    
    // 找到切换按钮并点击
    const toggleButton = wrapper.find('.toolbar .el-button[type="primary"]')
    await toggleButton.trigger('click')
    
    expect(wrapper.vm.showFullPedigree).toBe(!initialShowFull)
  })

  it('builds nodes correctly from pedigree data', async () => {
    const mockPedigreeData = {
      id: 1,
      name: '测试猫咪',
      breedName: '英国短毛猫',
      gender: 'FEMALE',
      dateOfBirth: '2023-01-01',
      color: '银渐层',
      father: {
        id: 2,
        name: '父猫',
        breedName: '英国短毛猫',
        gender: 'MALE',
        dateOfBirth: '2021-01-01',
        color: '蓝色'
      },
      mother: {
        id: 3,
        name: '母猫',
        breedName: '英国短毛猫',
        gender: 'FEMALE',
        dateOfBirth: '2021-01-01',
        color: '银渐层'
      }
    }

    wrapper.vm.loading = false
    wrapper.vm.pedigreeData = mockPedigreeData
    await wrapper.vm.$nextTick()

    const nodes = wrapper.vm.nodes
    expect(nodes.length).toBeGreaterThan(0)
    
    // 检查根节点
    const rootNode = nodes.find((node: any) => node.generation === 0)
    expect(rootNode).toBeDefined()
    expect(rootNode.name).toBe('测试猫咪')
    expect(rootNode.relationship).toBe('本猫')
  })

  it('calculates connections correctly', async () => {
    const mockPedigreeData = {
      id: 1,
      name: '测试猫咪',
      father: {
        id: 2,
        name: '父猫',
        gender: 'MALE'
      },
      mother: {
        id: 3,
        name: '母猫',
        gender: 'FEMALE'
      }
    }

    wrapper.vm.loading = false
    wrapper.vm.pedigreeData = mockPedigreeData
    await wrapper.vm.$nextTick()

    const connections = wrapper.vm.connections
    expect(connections.length).toBeGreaterThan(0)
    
    // 检查连接线是否有正确的坐标
    connections.forEach((connection: any) => {
      expect(connection.x1).toBeTypeOf('number')
      expect(connection.y1).toBeTypeOf('number')
      expect(connection.x2).toBeTypeOf('number')
      expect(connection.y2).toBeTypeOf('number')
    })
  })

  it('handles node click correctly', async () => {
    wrapper.vm.loading = false
    wrapper.vm.pedigreeData = { id: 1, name: '测试猫咪' }
    await wrapper.vm.$nextTick()

    const mockNode = {
      id: 1,
      name: '测试猫咪',
      breedName: '英国短毛猫',
      gender: 'FEMALE',
      dateOfBirth: '2023-01-01',
      color: '银渐层',
      relationship: '本猫'
    }

    wrapper.vm.handleNodeClick(mockNode)

    expect(wrapper.vm.selectedNode).toEqual(mockNode)
    expect(wrapper.vm.showNodeDialog).toBe(true)
  })

  it('navigates to cat detail correctly', async () => {
    wrapper.vm.loading = false
    wrapper.vm.pedigreeData = { id: 1, name: '测试猫咪' }
    await wrapper.vm.$nextTick()

    wrapper.vm.viewCatDetail(123)

    expect(mockPush).toHaveBeenCalledWith('/cats/123')
    expect(wrapper.vm.showNodeDialog).toBe(false)
  })

  it('applies correct node classes based on cat properties', () => {
    const currentCatNode = { generation: 0, gender: 'FEMALE' }
    const maleCatNode = { generation: 1, gender: 'MALE' }
    const femaleCatNode = { generation: 1, gender: 'FEMALE' }

    expect(wrapper.vm.getNodeClass(currentCatNode)).toContain('current-cat')
    expect(wrapper.vm.getNodeClass(maleCatNode)).toContain('male-cat')
    expect(wrapper.vm.getNodeClass(femaleCatNode)).toContain('female-cat')
  })

  it('displays empty state when no pedigree data', async () => {
    wrapper.vm.loading = false
    wrapper.vm.pedigreeData = null
    await wrapper.vm.$nextTick()

    const emptyState = wrapper.find('.el-empty')
    expect(emptyState.exists()).toBe(true)
  })

  it('handles API errors gracefully', async () => {
    // 模拟API错误
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    
    // 这里可以添加错误处理的测试逻辑
    
    consoleSpy.mockRestore()
  })

  it('limits generations correctly in simple view', async () => {
    wrapper.vm.loading = false
    wrapper.vm.showFullPedigree = false
    wrapper.vm.pedigreeData = {
      id: 1,
      name: '测试猫咪',
      father: {
        id: 2,
        name: '父猫',
        father: {
          id: 4,
          name: '爷爷',
          father: { id: 8, name: '曾祖父' }
        }
      }
    }
    await wrapper.vm.$nextTick()

    const nodes = wrapper.vm.nodes
    const maxGeneration = Math.max(...nodes.map((node: any) => node.generation))
    expect(maxGeneration).toBeLessThanOrEqual(3) // 简化视图最多3代
  })

  it('shows more generations in full pedigree view', async () => {
    wrapper.vm.loading = false
    wrapper.vm.showFullPedigree = true
    wrapper.vm.pedigreeData = {
      id: 1,
      name: '测试猫咪',
      father: {
        id: 2,
        name: '父猫',
        father: {
          id: 4,
          name: '爷爷',
          father: { id: 8, name: '曾祖父' }
        }
      }
    }
    await wrapper.vm.$nextTick()

    const nodes = wrapper.vm.nodes
    const maxGeneration = Math.max(...nodes.map((node: any) => node.generation))
    expect(maxGeneration).toBeLessThanOrEqual(4) // 完整视图最多4代
  })
})
