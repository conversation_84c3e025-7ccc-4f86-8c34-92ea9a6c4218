package com.cattery.repository;

import com.cattery.entity.Cat;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Repository
public interface CatRepository extends JpaRepository<Cat, Long> {
    
    // 根据状态查找
    List<Cat> findByStatus(Cat.Status status);
    
    // 根据状态统计数量
    long countByStatus(Cat.Status status);
    
    // 根据品种查找
    List<Cat> findByBreed(String breed);
    
    // 根据性别查找
    List<Cat> findByGender(Cat.Gender gender);
    
    // 根据芯片ID查找
    Optional<Cat> findByMicrochipId(String microchipId);
    
    // 检查芯片ID是否存在
    boolean existsByMicrochipId(String microchipId);
    
    // 关键词搜索（名称、品种、颜色）
    @Query("SELECT c FROM Cat c WHERE " +
           "LOWER(c.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(c.breed) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(c.color) LIKE LOWER(CONCAT('%', :keyword, '%'))")
    List<Cat> searchByKeyword(@Param("keyword") String keyword);
    
    // 分页搜索
    @Query("SELECT c FROM Cat c WHERE " +
           "LOWER(c.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(c.breed) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(c.color) LIKE LOWER(CONCAT('%', :keyword, '%'))")
    Page<Cat> searchByKeyword(@Param("keyword") String keyword, Pageable pageable);
    
    // 获取品种统计
    @Query("SELECT c.breed as breed, COUNT(c) as count FROM Cat c " +
           "WHERE c.breed IS NOT NULL " +
           "GROUP BY c.breed " +
           "ORDER BY COUNT(c) DESC")
    List<Map<String, Object>> getBreedStatistics();
    
    // 获取状态统计
    @Query("SELECT c.status as status, COUNT(c) as count FROM Cat c " +
           "GROUP BY c.status")
    List<Map<String, Object>> getStatusStatistics();
    
    // 根据价格范围查找
    @Query("SELECT c FROM Cat c WHERE c.price BETWEEN :minPrice AND :maxPrice")
    List<Cat> findByPriceRange(@Param("minPrice") java.math.BigDecimal minPrice, 
                               @Param("maxPrice") java.math.BigDecimal maxPrice);
    
    // 查找可售猫咪
    @Query("SELECT c FROM Cat c WHERE c.status = 'AVAILABLE' ORDER BY c.createdTime DESC")
    List<Cat> findAvailableCats();
    
    // 查找可售猫咪（分页）
    @Query("SELECT c FROM Cat c WHERE c.status = 'AVAILABLE' ORDER BY c.createdTime DESC")
    Page<Cat> findAvailableCats(Pageable pageable);
    
    // 为AI服务添加的方法 - 根据品种和颜色查找
    List<Cat> findByBreedAndColorContaining(String breed, String color);
    
    // 为报表服务添加的方法 - 根据创建时间范围统计
    long countByCreatedTimeBetween(java.time.LocalDateTime startDate, java.time.LocalDateTime endDate);

    // 为健康服务添加的方法 - 查找需要体检的猫咪
    @Query("SELECT c FROM Cat c WHERE c.id NOT IN " +
           "(SELECT chr.cat.id FROM CatHealthRecord chr " +
           "WHERE chr.recordType = 'CHECKUP' AND chr.recordDate > :cutoffDate)")
    List<Cat> findCatsNeedingCheckup(@Param("cutoffDate") java.time.LocalDateTime cutoffDate);

    // 为繁育服务添加的方法 - 查找可繁育的母猫
    @Query("SELECT c FROM Cat c WHERE c.gender = 'FEMALE' AND c.status = 'AVAILABLE' " +
           "AND c.birthDate IS NOT NULL " +
           "AND c.birthDate <= :maxBirthDate AND c.birthDate >= :minBirthDate") // 12个月到8年
    List<Cat> findBreedableFemaleCats(@Param("minBirthDate") java.time.LocalDate minBirthDate,
                                     @Param("maxBirthDate") java.time.LocalDate maxBirthDate);

    // 为繁育服务添加的方法 - 查找可繁育的公猫
    @Query("SELECT c FROM Cat c WHERE c.gender = 'MALE' AND c.status = 'AVAILABLE' " +
           "AND c.birthDate IS NOT NULL " +
           "AND c.birthDate <= :maxBirthDate AND c.birthDate >= :minBirthDate") // 12个月到10年
    List<Cat> findBreedableMaleCats(@Param("minBirthDate") java.time.LocalDate minBirthDate,
                                   @Param("maxBirthDate") java.time.LocalDate maxBirthDate);
}





