package com.cattery.repository;

import com.cattery.entity.InventoryItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * 库存物品仓库接口
 */
@Repository
public interface InventoryItemRepository extends JpaRepository<InventoryItem, Long>, JpaSpecificationExecutor<InventoryItem> {

    /**
     * 根据物品名称查找
     */
    List<InventoryItem> findByItemNameContaining(String itemName);

    /**
     * 根据分类查找
     */
    List<InventoryItem> findByCategory(String category);

    /**
     * 根据供应商查找
     */
    List<InventoryItem> findBySupplier(String supplier);

    /**
     * 查找库存不足的物品
     */
    @Query("SELECT i FROM InventoryItem i WHERE i.currentStock <= i.minimumStock")
    List<InventoryItem> findLowStockItems();

    /**
     * 查找即将过期的物品
     */
    @Query("SELECT i FROM InventoryItem i WHERE i.expiryDate IS NOT NULL " +
           "AND i.expiryDate BETWEEN CURRENT_DATE AND :endDate")
    List<InventoryItem> findItemsExpiringBefore(@Param("endDate") LocalDateTime endDate);

    /**
     * 查找已过期的物品
     */
    @Query("SELECT i FROM InventoryItem i WHERE i.expiryDate IS NOT NULL " +
           "AND i.expiryDate < CURRENT_DATE")
    List<InventoryItem> findExpiredItems();

    /**
     * 按分类统计数量
     */
    @Query("SELECT i.category, COUNT(i) FROM InventoryItem i GROUP BY i.category")
    List<Object[]> countByCategoryGrouped();

    /**
     * 按供应商统计数量
     */
    @Query("SELECT i.supplier, COUNT(i) FROM InventoryItem i GROUP BY i.supplier")
    List<Object[]> countBySupplierGrouped();

    /**
     * 计算总库存价值
     */
    @Query("SELECT COALESCE(SUM(i.currentStock * i.unitPrice), 0) FROM InventoryItem i")
    BigDecimal calculateTotalInventoryValue();

    /**
     * 检查物品名称是否存在
     */
    boolean existsByItemName(String itemName);

    /**
     * 根据分类分页查询
     */
    Page<InventoryItem> findByCategory(String category, Pageable pageable);

    /**
     * 搜索物品（名称或描述）
     */
    @Query("SELECT i FROM InventoryItem i WHERE " +
           "LOWER(i.itemName) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(i.description) LIKE LOWER(CONCAT('%', :keyword, '%'))")
    Page<InventoryItem> findByItemNameContainingIgnoreCaseOrDescriptionContainingIgnoreCase(
        @Param("keyword") String itemName, @Param("keyword") String description, Pageable pageable);

    /**
     * 查找即将过期的物品（修正方法名）
     */
    @Query("SELECT i FROM InventoryItem i WHERE i.expiryDate IS NOT NULL " +
           "AND i.expiryDate <= :cutoffDate")
    List<InventoryItem> findExpiringItems(@Param("cutoffDate") LocalDateTime cutoffDate);

    /**
     * 获取分类统计
     */
    @Query("SELECT i.category, COUNT(i), SUM(i.currentStock) FROM InventoryItem i " +
           "GROUP BY i.category")
    List<Object[]> getCategoryStatistics();
}
