@echo off
echo ========================================
echo Starting Backend Service
echo ========================================
echo.

echo Step 1: Check Java installation
java -version
if %errorlevel% neq 0 (
    echo ERROR: Java not found
    pause
    exit /b 1
)

echo.
echo Step 2: Find Java installation path
for /f "tokens=*" %%i in ('where java') do set JAVA_EXE=%%i
for %%i in ("%JAVA_EXE%") do set JAVA_DIR=%%~dpi
for %%i in ("%JAVA_DIR%..") do set JAVA_HOME=%%~fi

echo Java executable: %JAVA_EXE%
echo Java directory: %JAVA_DIR%
echo Setting JAVA_HOME to: %JAVA_HOME%

echo.
echo Step 3: Set JAVA_HOME and start backend
set JAVA_HOME=%JAVA_HOME%
cd /d "D:\噔噔\backend"

echo Current directory: %CD%
echo JAVA_HOME: %JAVA_HOME%

echo.
echo Starting Spring Boot application...
echo This may take a few minutes...
echo.

.\mvnw.cmd spring-boot:run

pause
