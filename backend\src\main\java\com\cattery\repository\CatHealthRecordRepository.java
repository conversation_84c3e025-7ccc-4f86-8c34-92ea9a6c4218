package com.cattery.repository;

import com.cattery.entity.Cat;
import com.cattery.entity.CatHealthRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 猫咪健康记录仓库接口
 */
@Repository
public interface CatHealthRecordRepository extends JpaRepository<CatHealthRecord, Long>, JpaSpecificationExecutor<CatHealthRecord> {

    /**
     * 根据猫咪查找健康记录
     */
    List<CatHealthRecord> findByCat(Cat cat);

    /**
     * 根据猫咪ID查找健康记录
     */
    List<CatHealthRecord> findByCatId(Long catId);

    /**
     * 根据记录类型查找健康记录
     */
    List<CatHealthRecord> findByRecordType(CatHealthRecord.RecordType recordType);

    /**
     * 根据猫咪和记录类型查找健康记录
     */
    List<CatHealthRecord> findByCatAndRecordType(Cat cat, CatHealthRecord.RecordType recordType);

    /**
     * 根据猫咪ID和记录类型查找健康记录
     */
    List<CatHealthRecord> findByCatIdAndRecordType(Long catId, CatHealthRecord.RecordType recordType);

    /**
     * 根据记录日期范围查找健康记录
     */
    List<CatHealthRecord> findByRecordDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 根据猫咪和记录日期范围查找健康记录
     */
    List<CatHealthRecord> findByCatAndRecordDateBetween(Cat cat, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 根据兽医查找健康记录
     */
    List<CatHealthRecord> findByVeterinarian(String veterinarian);

    /**
     * 根据诊所查找健康记录
     */
    List<CatHealthRecord> findByClinic(String clinic);

    /**
     * 查找最近的健康记录
     */
    List<CatHealthRecord> findByCatOrderByRecordDateDesc(Cat cat);

    /**
     * 查找猫咪最近的健康记录（限制数量）
     */
    List<CatHealthRecord> findTop10ByCatOrderByRecordDateDesc(Cat cat);

    /**
     * 查找猫咪最近的特定类型记录
     */
    Optional<CatHealthRecord> findFirstByCatAndRecordTypeOrderByRecordDateDesc(Cat cat, CatHealthRecord.RecordType recordType);

    /**
     * 查找猫咪最近的疫苗记录
     */
    @Query("SELECT hr FROM CatHealthRecord hr WHERE hr.cat = :cat AND hr.recordType = 'VACCINATION' " +
           "ORDER BY hr.recordDate DESC")
    List<CatHealthRecord> findLatestVaccinationRecords(@Param("cat") Cat cat);

    /**
     * 查找猫咪最近的体检记录
     */
    @Query("SELECT hr FROM CatHealthRecord hr WHERE hr.cat = :cat AND hr.recordType = 'CHECKUP' " +
           "ORDER BY hr.recordDate DESC")
    List<CatHealthRecord> findLatestCheckupRecords(@Param("cat") Cat cat);

    /**
     * 查找即将到期的疫苗
     */
    @Query("SELECT hr FROM CatHealthRecord hr WHERE hr.recordType = 'VACCINATION' AND " +
           "hr.nextVaccineDate BETWEEN CURRENT_DATE AND :endDate")
    List<CatHealthRecord> findUpcomingVaccinations(@Param("endDate") LocalDateTime endDate);

    /**
     * 查找过期的疫苗
     */
    @Query("SELECT hr FROM CatHealthRecord hr WHERE hr.recordType = 'VACCINATION' AND " +
           "hr.nextVaccineDate < CURRENT_DATE")
    List<CatHealthRecord> findOverdueVaccinations();

    /**
     * 统计记录类型数量
     */
    long countByRecordType(CatHealthRecord.RecordType recordType);

    /**
     * 统计时间范围内的记录数量
     */
    long countByRecordDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 统计猫咪的记录数量
     */
    long countByCat(Cat cat);

    /**
     * 根据兽医统计记录数量
     */
    long countByVeterinarian(String veterinarian);

    /**
     * 查找需要复查的记录
     */
    @Query("SELECT hr FROM CatHealthRecord hr WHERE hr.nextAppointment IS NOT NULL AND " +
           "hr.nextAppointment BETWEEN CURRENT_DATE AND :endDate")
    List<CatHealthRecord> findUpcomingAppointments(@Param("endDate") LocalDateTime endDate);

    /**
     * 查找过期的复查
     */
    @Query("SELECT hr FROM CatHealthRecord hr WHERE hr.nextAppointment IS NOT NULL AND " +
           "hr.nextAppointment < CURRENT_DATE")
    List<CatHealthRecord> findOverdueAppointments();

    /**
     * 根据费用范围查找记录
     */
    @Query("SELECT hr FROM CatHealthRecord hr WHERE hr.cost BETWEEN :minCost AND :maxCost")
    List<CatHealthRecord> findByCostRange(@Param("minCost") Double minCost, @Param("maxCost") Double maxCost);

    /**
     * 查找有附件的记录
     */
    @Query("SELECT hr FROM CatHealthRecord hr WHERE hr.attachmentPath IS NOT NULL")
    List<CatHealthRecord> findRecordsWithAttachments();

    /**
     * 根据关键词搜索健康记录
     */
    @Query("SELECT hr FROM CatHealthRecord hr WHERE " +
           "LOWER(hr.diagnosis) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(hr.treatment) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(hr.medication) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(hr.notes) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(hr.veterinarian) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(hr.clinic) LIKE LOWER(CONCAT('%', :keyword, '%'))")
    Page<CatHealthRecord> searchHealthRecords(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 统计各记录类型的数量
     */
    @Query("SELECT hr.recordType, COUNT(hr) FROM CatHealthRecord hr GROUP BY hr.recordType")
    List<Object[]> countByRecordTypeGrouped();

    /**
     * 统计各兽医的记录数量
     */
    @Query("SELECT hr.veterinarian, COUNT(hr) FROM CatHealthRecord hr WHERE hr.veterinarian IS NOT NULL " +
           "GROUP BY hr.veterinarian ORDER BY COUNT(hr) DESC")
    List<Object[]> countByVeterinarianGrouped();

    /**
     * 统计各诊所的记录数量
     */
    @Query("SELECT hr.clinic, COUNT(hr) FROM CatHealthRecord hr WHERE hr.clinic IS NOT NULL " +
           "GROUP BY hr.clinic ORDER BY COUNT(hr) DESC")
    List<Object[]> countByClinicGrouped();

    /**
     * 查找最近创建的记录
     */
    List<CatHealthRecord> findTop10ByOrderByCreatedAtDesc();

    /**
     * 查找最近更新的记录
     */
    List<CatHealthRecord> findTop10ByOrderByUpdatedAtDesc();

    /**
     * 根据猫咪查找分页健康记录
     */
    Page<CatHealthRecord> findByCatOrderByRecordDateDesc(Cat cat, Pageable pageable);

    /**
     * 根据猫咪ID查找分页健康记录
     */
    Page<CatHealthRecord> findByCatIdOrderByRecordDateDesc(Long catId, Pageable pageable);

    /**
     * 根据猫咪ID查找健康记录（按日期倒序）
     */
    List<CatHealthRecord> findByCatIdOrderByRecordDateDesc(Long catId);

    /**
     * 根据猫咪ID和记录类型查找健康记录（按日期倒序）
     */
    List<CatHealthRecord> findByCatIdAndRecordTypeOrderByRecordDateDesc(Long catId, CatHealthRecord.RecordType recordType);

    /**
     * 统计指定类型和时间范围内的记录数量
     */
    long countByRecordTypeAndRecordDateBetween(CatHealthRecord.RecordType recordType,
                                              LocalDateTime startDate,
                                              LocalDateTime endDate);
}
