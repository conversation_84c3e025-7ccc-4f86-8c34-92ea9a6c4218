package com.cattery.dto.ai;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 健康预测结果DTO
 */
@Data
public class HealthPredictionResultDTO {
    
    /**
     * 猫咪ID
     */
    private Long catId;
    
    /**
     * 预测时间
     */
    private LocalDateTime predictionTime;
    
    /**
     * 整体风险等级 (LOW, MEDIUM, HIGH)
     */
    private String riskLevel;
    
    /**
     * 整体健康评分 (0-100)
     */
    private Integer healthScore;
    
    /**
     * 疾病风险列表
     */
    private List<DiseaseRiskDTO> diseaseRisks;
    
    /**
     * 健康建议列表
     */
    private List<HealthRecommendationDTO> recommendations;
    
    /**
     * 预测准确度
     */
    private Double accuracy;
    
    /**
     * 预测依据
     */
    private List<String> predictionBasis;
    
    /**
     * 下次检查建议时间
     */
    private LocalDateTime nextCheckupRecommendation;
    
    /**
     * 紧急程度
     */
    private String urgencyLevel;
    
    /**
     * 预测模型版本
     */
    private String modelVersion;

    /**
     * 整体健康评分
     */
    private Double overallHealthScore;

    /**
     * 预测置信度
     */
    private Double predictionConfidence;

    /**
     * 健康趋势
     */
    private HealthTrendDTO healthTrend;
}
