<template>
  <div class="cat-statistics-report">
    <!-- 报表头部 -->
    <el-card class="report-header">
      <div class="report-title">
        <h2>猫咪统计报表</h2>
        <div class="report-meta">
          <span>报表周期: {{ reportData.reportPeriod }}</span>
          <span>生成时间: {{ formatDateTime(reportData.generatedAt) }}</span>
        </div>
      </div>
    </el-card>

    <!-- 核心指标卡片 -->
    <div class="metrics-grid">
      <el-card class="metric-card">
        <div class="metric-content">
          <div class="metric-icon total">
            <el-icon><DataAnalysis /></el-icon>
          </div>
          <div class="metric-info">
            <div class="metric-value">{{ reportData.totalCats }}</div>
            <div class="metric-label">总猫咪数</div>
          </div>
        </div>
      </el-card>

      <el-card class="metric-card">
        <div class="metric-content">
          <div class="metric-icon new">
            <el-icon><Plus /></el-icon>
          </div>
          <div class="metric-info">
            <div class="metric-value">{{ reportData.newCatsInPeriod }}</div>
            <div class="metric-label">新增猫咪</div>
          </div>
        </div>
      </el-card>

      <el-card class="metric-card">
        <div class="metric-content">
          <div class="metric-icon health">
            <el-icon><CircleCheck /></el-icon>
          </div>
          <div class="metric-info">
            <div class="metric-value">{{ reportData.healthStatistics?.healthyCats || 0 }}</div>
            <div class="metric-label">健康猫咪</div>
          </div>
        </div>
      </el-card>

      <el-card class="metric-card">
        <div class="metric-content">
          <div class="metric-icon vaccination">
            <el-icon><MagicStick /></el-icon>
          </div>
          <div class="metric-info">
            <div class="metric-value">{{ formatPercentage(reportData.healthStatistics?.vaccinationRate) }}</div>
            <div class="metric-label">疫苗接种率</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 图表区域 -->
    <div class="charts-grid">
      <!-- 性别分布饼图 -->
      <el-card class="chart-card">
        <template #header>
          <span>性别分布</span>
        </template>
        <div class="chart-container">
          <pie-chart
            :data="genderChartData"
            :options="pieChartOptions"
            height="300px"
          />
        </div>
      </el-card>

      <!-- 品种分布柱状图 -->
      <el-card class="chart-card">
        <template #header>
          <span>品种分布 (Top 10)</span>
        </template>
        <div class="chart-container">
          <bar-chart
            :data="breedChartData"
            :options="barChartOptions"
            height="300px"
          />
        </div>
      </el-card>

      <!-- 状态分布环形图 -->
      <el-card class="chart-card">
        <template #header>
          <span>状态分布</span>
        </template>
        <div class="chart-container">
          <doughnut-chart
            :data="statusChartData"
            :options="doughnutChartOptions"
            height="300px"
          />
        </div>
      </el-card>

      <!-- 年龄分布柱状图 -->
      <el-card class="chart-card">
        <template #header>
          <span>年龄分布</span>
        </template>
        <div class="chart-container">
          <bar-chart
            :data="ageChartData"
            :options="ageBarChartOptions"
            height="300px"
          />
        </div>
      </el-card>
    </div>

    <!-- 详细数据表格 -->
    <el-card class="data-table-card">
      <template #header>
        <div class="table-header">
          <span>详细统计数据</span>
          <el-button-group>
            <el-button 
              :type="activeTab === 'gender' ? 'primary' : 'default'"
              @click="activeTab = 'gender'"
            >
              性别
            </el-button>
            <el-button 
              :type="activeTab === 'breed' ? 'primary' : 'default'"
              @click="activeTab = 'breed'"
            >
              品种
            </el-button>
            <el-button 
              :type="activeTab === 'status' ? 'primary' : 'default'"
              @click="activeTab = 'status'"
            >
              状态
            </el-button>
            <el-button 
              :type="activeTab === 'age' ? 'primary' : 'default'"
              @click="activeTab = 'age'"
            >
              年龄
            </el-button>
          </el-button-group>
        </div>
      </template>

      <!-- 性别统计表 -->
      <el-table 
        v-if="activeTab === 'gender'"
        :data="genderTableData" 
        style="width: 100%"
      >
        <el-table-column prop="gender" label="性别" width="200">
          <template #default="scope">
            <el-tag :type="getGenderTagType(scope.row.gender)">
              {{ getGenderLabel(scope.row.gender) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="count" label="数量" width="150" />
        <el-table-column prop="percentage" label="占比" width="150">
          <template #default="scope">
            {{ formatPercentage(scope.row.percentage) }}
          </template>
        </el-table-column>
        <el-table-column label="进度条">
          <template #default="scope">
            <el-progress 
              :percentage="scope.row.percentage" 
              :color="getGenderColor(scope.row.gender)"
            />
          </template>
        </el-table-column>
      </el-table>

      <!-- 品种统计表 -->
      <el-table 
        v-else-if="activeTab === 'breed'"
        :data="breedTableData" 
        style="width: 100%"
      >
        <el-table-column prop="breed" label="品种" width="200" />
        <el-table-column prop="count" label="数量" width="150" />
        <el-table-column prop="percentage" label="占比" width="150">
          <template #default="scope">
            {{ formatPercentage(scope.row.percentage) }}
          </template>
        </el-table-column>
        <el-table-column label="进度条">
          <template #default="scope">
            <el-progress 
              :percentage="scope.row.percentage" 
              color="#409eff"
            />
          </template>
        </el-table-column>
      </el-table>

      <!-- 状态统计表 -->
      <el-table 
        v-else-if="activeTab === 'status'"
        :data="statusTableData" 
        style="width: 100%"
      >
        <el-table-column prop="status" label="状态" width="200">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ getStatusLabel(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="count" label="数量" width="150" />
        <el-table-column prop="percentage" label="占比" width="150">
          <template #default="scope">
            {{ formatPercentage(scope.row.percentage) }}
          </template>
        </el-table-column>
        <el-table-column label="进度条">
          <template #default="scope">
            <el-progress 
              :percentage="scope.row.percentage" 
              :color="getStatusColor(scope.row.status)"
            />
          </template>
        </el-table-column>
      </el-table>

      <!-- 年龄统计表 -->
      <el-table 
        v-else-if="activeTab === 'age'"
        :data="ageTableData" 
        style="width: 100%"
      >
        <el-table-column prop="ageGroup" label="年龄组" width="200" />
        <el-table-column prop="count" label="数量" width="150" />
        <el-table-column prop="percentage" label="占比" width="150">
          <template #default="scope">
            {{ formatPercentage(scope.row.percentage) }}
          </template>
        </el-table-column>
        <el-table-column label="进度条">
          <template #default="scope">
            <el-progress 
              :percentage="scope.row.percentage" 
              color="#67c23a"
            />
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 健康统计详情 -->
    <el-card v-if="reportData.healthStatistics" class="health-details-card">
      <template #header>
        <span>健康统计详情</span>
      </template>
      
      <div class="health-metrics">
        <div class="health-metric">
          <div class="health-metric-label">平均健康评分</div>
          <div class="health-metric-value">
            {{ reportData.healthStatistics.averageHealthScore?.toFixed(1) || 'N/A' }}
          </div>
          <el-progress 
            :percentage="reportData.healthStatistics.averageHealthScore || 0"
            :color="getHealthScoreColor(reportData.healthStatistics.averageHealthScore)"
          />
        </div>
        
        <div class="health-metric">
          <div class="health-metric-label">体检覆盖率</div>
          <div class="health-metric-value">
            {{ formatPercentage(reportData.healthStatistics.checkupCoverage) }}
          </div>
          <el-progress 
            :percentage="reportData.healthStatistics.checkupCoverage || 0"
            color="#e6a23c"
          />
        </div>
        
        <div class="health-metric">
          <div class="health-metric-label">需要关注的猫咪</div>
          <div class="health-metric-value">
            {{ reportData.healthStatistics.catsNeedingAttention || 0 }}
          </div>
        </div>
      </div>

      <!-- 常见健康问题 -->
      <div v-if="reportData.healthStatistics.commonHealthIssues" class="health-issues">
        <h4>常见健康问题</h4>
        <div class="health-issues-list">
          <el-tag 
            v-for="(count, issue) in reportData.healthStatistics.commonHealthIssues"
            :key="issue"
            class="health-issue-tag"
            type="warning"
          >
            {{ issue }}: {{ count }}次
          </el-tag>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  DataAnalysis, 
  Plus, 
  CircleCheck, 
  MagicStick 
} from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils/date'
import PieChart from '@/components/charts/PieChart.vue'
import BarChart from '@/components/charts/BarChart.vue'
import DoughnutChart from '@/components/charts/DoughnutChart.vue'
import type { CatStatisticsReport } from '@/types/reports'

// 组件属性
interface Props {
  reportData: CatStatisticsReport
}

const props = defineProps<Props>()

// 发射事件
const emit = defineEmits<{
  export: [format: 'pdf' | 'excel' | 'csv']
}>()

// 响应式数据
const activeTab = ref('gender')

// 计算属性 - 图表数据
const genderChartData = computed(() => {
  const data = props.reportData.genderDistribution || {}
  return {
    labels: Object.keys(data).map(key => getGenderLabel(key)),
    datasets: [{
      data: Object.values(data),
      backgroundColor: ['#409eff', '#f56c6c', '#909399'],
      borderWidth: 2,
      borderColor: '#fff'
    }]
  }
})

const breedChartData = computed(() => {
  const data = props.reportData.breedDistribution || {}
  const sortedEntries = Object.entries(data)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10)
  
  return {
    labels: sortedEntries.map(([breed]) => breed),
    datasets: [{
      label: '数量',
      data: sortedEntries.map(([,count]) => count),
      backgroundColor: '#67c23a',
      borderColor: '#67c23a',
      borderWidth: 1
    }]
  }
})

const statusChartData = computed(() => {
  const data = props.reportData.statusDistribution || {}
  return {
    labels: Object.keys(data).map(key => getStatusLabel(key)),
    datasets: [{
      data: Object.values(data),
      backgroundColor: ['#67c23a', '#409eff', '#e6a23c', '#f56c6c', '#909399', '#b37feb'],
      borderWidth: 2,
      borderColor: '#fff'
    }]
  }
})

const ageChartData = computed(() => {
  const data = props.reportData.ageDistribution || {}
  return {
    labels: Object.keys(data),
    datasets: [{
      label: '数量',
      data: Object.values(data),
      backgroundColor: '#e6a23c',
      borderColor: '#e6a23c',
      borderWidth: 1
    }]
  }
})

// 表格数据
const genderTableData = computed(() => {
  const data = props.reportData.genderDistribution || {}
  const total = Object.values(data).reduce((sum, count) => sum + count, 0)
  
  return Object.entries(data).map(([gender, count]) => ({
    gender,
    count,
    percentage: total > 0 ? (count / total) * 100 : 0
  }))
})

const breedTableData = computed(() => {
  const data = props.reportData.breedDistribution || {}
  const total = Object.values(data).reduce((sum, count) => sum + count, 0)
  
  return Object.entries(data)
    .map(([breed, count]) => ({
      breed,
      count,
      percentage: total > 0 ? (count / total) * 100 : 0
    }))
    .sort((a, b) => b.count - a.count)
})

const statusTableData = computed(() => {
  const data = props.reportData.statusDistribution || {}
  const total = Object.values(data).reduce((sum, count) => sum + count, 0)
  
  return Object.entries(data).map(([status, count]) => ({
    status,
    count,
    percentage: total > 0 ? (count / total) * 100 : 0
  }))
})

const ageTableData = computed(() => {
  const data = props.reportData.ageDistribution || {}
  const total = Object.values(data).reduce((sum, count) => sum + count, 0)
  
  return Object.entries(data).map(([ageGroup, count]) => ({
    ageGroup,
    count,
    percentage: total > 0 ? (count / total) * 100 : 0
  }))
})

// 图表配置
const pieChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom' as const
    }
  }
}

const barChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false
    }
  },
  scales: {
    y: {
      beginAtZero: true
    }
  }
}

const doughnutChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom' as const
    }
  }
}

const ageBarChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false
    }
  },
  scales: {
    y: {
      beginAtZero: true
    }
  }
}

// 工具函数
const formatPercentage = (value: number | undefined) => {
  return value ? `${value.toFixed(1)}%` : '0%'
}

const getGenderLabel = (gender: string) => {
  const labels: Record<string, string> = {
    'MALE': '公猫',
    'FEMALE': '母猫',
    'UNKNOWN': '未知'
  }
  return labels[gender] || gender
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    'AVAILABLE': '可领养',
    'ADOPTED': '已领养',
    'RESERVED': '已预定',
    'BREEDING': '繁育中',
    'MEDICAL': '医疗中',
    'QUARANTINE': '隔离中'
  }
  return labels[status] || status
}

const getGenderTagType = (gender: string) => {
  const types: Record<string, string> = {
    'MALE': 'primary',
    'FEMALE': 'danger',
    'UNKNOWN': 'info'
  }
  return types[gender] || 'info'
}

const getStatusTagType = (status: string) => {
  const types: Record<string, string> = {
    'AVAILABLE': 'success',
    'ADOPTED': 'primary',
    'RESERVED': 'warning',
    'BREEDING': 'info',
    'MEDICAL': 'danger',
    'QUARANTINE': 'warning'
  }
  return types[status] || 'info'
}

const getGenderColor = (gender: string) => {
  const colors: Record<string, string> = {
    'MALE': '#409eff',
    'FEMALE': '#f56c6c',
    'UNKNOWN': '#909399'
  }
  return colors[gender] || '#909399'
}

const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    'AVAILABLE': '#67c23a',
    'ADOPTED': '#409eff',
    'RESERVED': '#e6a23c',
    'BREEDING': '#909399',
    'MEDICAL': '#f56c6c',
    'QUARANTINE': '#e6a23c'
  }
  return colors[status] || '#909399'
}

const getHealthScoreColor = (score: number | undefined) => {
  if (!score) return '#909399'
  if (score >= 80) return '#67c23a'
  if (score >= 60) return '#e6a23c'
  return '#f56c6c'
}
</script>

<style scoped>
.cat-statistics-report {
  space-y: 20px;
}

.report-header {
  margin-bottom: 20px;
}

.report-title h2 {
  margin: 0 0 10px 0;
  color: #303133;
}

.report-meta {
  display: flex;
  gap: 20px;
  color: #606266;
  font-size: 14px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.metric-card {
  cursor: pointer;
  transition: transform 0.2s;
}

.metric-card:hover {
  transform: translateY(-2px);
}

.metric-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.metric-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.metric-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.metric-icon.new {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.metric-icon.health {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.metric-icon.vaccination {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.metric-info {
  flex: 1;
}

.metric-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.metric-label {
  font-size: 14px;
  color: #606266;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.chart-card {
  min-height: 400px;
}

.chart-container {
  height: 300px;
  position: relative;
}

.data-table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.health-details-card {
  margin-bottom: 20px;
}

.health-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.health-metric {
  text-align: center;
}

.health-metric-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
}

.health-metric-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 10px;
}

.health-issues h4 {
  margin-bottom: 15px;
  color: #303133;
}

.health-issues-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.health-issue-tag {
  margin-bottom: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .charts-grid {
    grid-template-columns: 1fr;
  }
  
  .report-meta {
    flex-direction: column;
    gap: 5px;
  }
  
  .table-header {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
}
</style>
