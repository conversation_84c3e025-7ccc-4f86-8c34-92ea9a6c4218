package com.cattery.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 库存物品实体类
 */
@Entity
@Table(name = "inventory_items")
@Data
@EqualsAndHashCode(callSuper = false)
public class InventoryItem {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 物品名称
     */
    @Column(name = "item_name", nullable = false)
    private String itemName;

    /**
     * 物品描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    /**
     * 分类
     */
    @Column(name = "category", nullable = false)
    private String category;

    /**
     * 品牌
     */
    @Column(name = "brand")
    private String brand;

    /**
     * 型号/规格
     */
    @Column(name = "model")
    private String model;

    /**
     * 单位
     */
    @Column(name = "unit", nullable = false)
    private String unit;

    /**
     * 当前库存数量
     */
    @Column(name = "current_stock", nullable = false)
    private Integer currentStock = 0;

    /**
     * 最小库存数量
     */
    @Column(name = "minimum_stock", nullable = false)
    private Integer minimumStock = 0;

    /**
     * 最大库存数量
     */
    @Column(name = "maximum_stock")
    private Integer maximumStock;

    /**
     * 单价
     */
    @Column(name = "unit_price", precision = 10, scale = 2)
    private BigDecimal unitPrice;

    /**
     * 供应商
     */
    @Column(name = "supplier")
    private String supplier;

    /**
     * 供应商联系方式
     */
    @Column(name = "supplier_contact")
    private String supplierContact;

    /**
     * 采购日期
     */
    @Column(name = "purchase_date")
    private LocalDateTime purchaseDate;

    /**
     * 过期日期
     */
    @Column(name = "expiry_date")
    private LocalDateTime expiryDate;

    /**
     * 存储位置
     */
    @Column(name = "storage_location")
    private String storageLocation;

    /**
     * 条形码
     */
    @Column(name = "barcode")
    private String barcode;

    /**
     * 状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private Status status = Status.ACTIVE;

    /**
     * 备注
     */
    @Column(name = "notes", columnDefinition = "TEXT")
    private String notes;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 库存状态枚举
     */
    public enum Status {
        ACTIVE("正常"),
        LOW_STOCK("库存不足"),
        OUT_OF_STOCK("缺货"),
        EXPIRED("已过期"),
        DISCONTINUED("停用");

        private final String description;

        Status(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
