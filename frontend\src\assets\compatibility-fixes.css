/* CSS 兼容性修复 - 根据最佳实践 */

/* 1. appearance 属性修复 */
/* 修复前 */
/*
.element {
  -moz-appearance: none;
}
*/

/* 修复后 */
.element {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none; /* 添加标准属性支持现代浏览器 */
}

/* 2. image-rendering 属性修复 */
/* 修复前 */
/*
.image {
  image-rendering: crisp-edges;
}
*/

/* 修复后 */
.image {
  image-rendering: -webkit-optimize-contrast; /* Edge 支持 */
  image-rendering: crisp-edges;
}

/* 3. mask 相关属性修复（注意顺序） */
/* 修复前 - 顺序错误 */
/*
.masked-element {
  mask-size: cover;
  mask: url(mask.svg);
}
*/

/* 修复后 - 正确顺序：前缀版本在前 */
.masked-element {
  -webkit-mask: url(mask.svg);
  mask: url(mask.svg);
  -webkit-mask-size: cover;
  mask-size: cover;
}

/* 4. text-size-adjust 属性修复 */
/* 修复前 */
/*
.text {
  -webkit-text-size-adjust: none;
  text-size-adjust: none;
}
*/

/* 修复后 - 移除不支持的浏览器警告 */
.text {
  -webkit-text-size-adjust: none; /* 仅 WebKit 内核支持 */
  text-size-adjust: none; /* 标准属性 */
  /* Firefox 和 Safari 不支持此属性，但不会报错 */
}

/* 5. scrollbar-width 属性修复（Safari 兼容） */
/* 修复前 */
/*
.scrollable {
  scrollbar-width: thin;
}
*/

/* 修复后 - 添加 Safari 兼容方案 */
.scrollable {
  scrollbar-width: thin; /* Firefox 支持 */
}

/* Safari 使用 webkit 前缀方案 */
.scrollable::-webkit-scrollbar {
  width: 8px;
}

.scrollable::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.scrollable::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

/* 6. 性能优化 - 避免动画中触发布局 */

/* ❌ 性能差 - 触发布局重排 */
/*
@keyframes slideInBad {
  from { left: -100px; }
  to { left: 0; }
}
*/

/* ✅ 性能好 - 只触发合成层 */
@keyframes slideIn {
  from { transform: translateX(-100px); }
  to { transform: translateX(0); }
}

.animated-element {
  animation: slideIn 0.3s ease-out;
  /* 提升到合成层，避免重排 */
  will-change: transform;
}

/* 7. 高性能动画最佳实践 */

/* ✅ 推荐动画属性（只触发合成） */
.performance-friendly {
  transform: translateX(0); /* 位移 */
  opacity: 1; /* 透明度 */
  filter: blur(0); /* 滤镜 */
  
  will-change: transform, opacity; /* 提前告知浏览器 */
  transition: transform 0.3s ease, opacity 0.3s ease;
}

/* ❌ 避免动画这些属性 */
.avoid-animating {
  /* 触发布局：left, top, right, bottom, width, height, margin, padding */
  /* 触发绘制：color, background-color, border, box-shadow */
}

/* 8. 实际应用示例 */

/* 表单元素 */
.form-input {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 12px;
  transition: border-color 0.2s ease;
}

.form-input:focus {
  border-color: #409eff;
  outline: none;
}

/* 按钮元素 */
.button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  
  /* 高性能动画 */
  will-change: transform, opacity;
  transition: transform 0.2s ease, opacity 0.2s ease;
}

.button:hover {
  transform: translateY(-1px);
}

.button:active {
  transform: translateY(0);
}

/* 图片元素 */
.high-res-image {
  image-rendering: -webkit-optimize-contrast; /* Edge 支持 */
  image-rendering: crisp-edges;
  max-width: 100%;
  height: auto;
}

/* 滚动容器 */
.scroll-container {
  max-height: 300px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #888 #f1f1f1;
}

.scroll-container::-webkit-scrollbar {
  width: 8px;
}

.scroll-container::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.scroll-container::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

/* 遮罩元素 */
.icon-mask {
  -webkit-mask: url('/icons/star.svg') no-repeat center;
  mask: url('/icons/star.svg') no-repeat center;
  -webkit-mask-size: contain;
  mask-size: contain;
  background: linear-gradient(45deg, #409eff, #67c23a);
  width: 24px;
  height: 24px;
}

/* 动画卡片 */
.card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  /* 高性能动画 */
  will-change: transform, box-shadow;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* 响应式文本 */
.responsive-text {
  -webkit-text-size-adjust: 100%;
  text-size-adjust: 100%;
  font-size: 16px;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .responsive-text {
    font-size: 14px;
  }
}

/* 用户偏好支持 */
@media (prefers-reduced-motion: reduce) {
  .animated-element,
  .button,
  .card {
    animation: none !important;
    transition: none !important;
    transform: none !important;
  }
}

@media (prefers-color-scheme: dark) {
  .card {
    background: #2d2d2d;
    color: white;
  }
  
  .form-input {
    background: #3d3d3d;
    border-color: #555;
    color: white;
  }
}
