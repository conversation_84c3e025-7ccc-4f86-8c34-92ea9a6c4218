package com.cattery.dto.ai;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 行为建议DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BehaviorRecommendationDTO {

    /**
     * 三参数构造函数
     */
    public BehaviorRecommendationDTO(String title, String content, String type) {
        this.title = title;
        this.content = content;
        this.recommendationType = type;
    }

    /**
     * 标题
     */
    private String title;
    
    /**
     * 建议类型
     */
    private String recommendationType;
    
    /**
     * 优先级
     */
    private String priority;
    
    /**
     * 建议内容
     */
    private String content;
    
    /**
     * 预期效果
     */
    private String expectedOutcome;
    
    /**
     * 实施难度
     */
    private String difficulty;
    
    /**
     * 预计时间
     */
    private String estimatedTime;
    
    /**
     * 相关资源
     */
    private String resources;
}
