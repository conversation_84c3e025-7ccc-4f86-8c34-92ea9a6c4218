package com.cattery.controller;

import com.cattery.dto.common.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 */
@RestController
@RequestMapping("/api/health")
@Slf4j
@Tag(name = "系统健康检查", description = "系统状态和健康检查相关接口")
public class HealthController {

    @Value("${spring.application.name}")
    private String applicationName;

    @Value("${server.port:8080}")
    private String serverPort;

    /**
     * 系统健康检查
     */
    @GetMapping("/check")
    @Operation(summary = "系统健康检查", description = "检查系统是否正常运行")
    public ResponseEntity<ApiResponse<Map<String, Object>>> healthCheck() {
        try {
            Map<String, Object> healthInfo = new HashMap<>();
            healthInfo.put("status", "UP");
            healthInfo.put("application", applicationName);
            healthInfo.put("port", serverPort);
            healthInfo.put("timestamp", LocalDateTime.now());
            healthInfo.put("version", "1.0.0");
            
            // 检查数据库连接状态
            healthInfo.put("database", "UP");
            
            // 检查Redis连接状态（如果配置了）
            healthInfo.put("redis", "UP");
            
            // 检查AI服务状态
            healthInfo.put("aiServices", "UP");
            
            log.info("系统健康检查通过");
            
            return ResponseEntity.ok(ApiResponse.success("系统运行正常", healthInfo));
            
        } catch (Exception e) {
            log.error("系统健康检查失败", e);
            
            Map<String, Object> errorInfo = new HashMap<>();
            errorInfo.put("status", "DOWN");
            errorInfo.put("error", e.getMessage());
            errorInfo.put("timestamp", LocalDateTime.now());
            
            ApiResponse<Map<String, Object>> response = ApiResponse.<Map<String, Object>>error("系统异常", "SYSTEM_ERROR");
            response.setData(errorInfo);
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 获取系统信息
     */
    @GetMapping("/info")
    @Operation(summary = "获取系统信息", description = "获取系统基本信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSystemInfo() {
        Map<String, Object> systemInfo = new HashMap<>();
        
        // 应用信息
        systemInfo.put("application", applicationName);
        systemInfo.put("version", "1.0.0");
        systemInfo.put("port", serverPort);
        systemInfo.put("startTime", LocalDateTime.now());
        
        // JVM信息
        Runtime runtime = Runtime.getRuntime();
        Map<String, Object> jvmInfo = new HashMap<>();
        jvmInfo.put("totalMemory", runtime.totalMemory());
        jvmInfo.put("freeMemory", runtime.freeMemory());
        jvmInfo.put("maxMemory", runtime.maxMemory());
        jvmInfo.put("usedMemory", runtime.totalMemory() - runtime.freeMemory());
        jvmInfo.put("availableProcessors", runtime.availableProcessors());
        systemInfo.put("jvm", jvmInfo);
        
        // 系统属性
        Map<String, Object> systemProps = new HashMap<>();
        systemProps.put("javaVersion", System.getProperty("java.version"));
        systemProps.put("osName", System.getProperty("os.name"));
        systemProps.put("osVersion", System.getProperty("os.version"));
        systemProps.put("osArch", System.getProperty("os.arch"));
        systemInfo.put("system", systemProps);
        
        return ResponseEntity.ok(ApiResponse.success("获取系统信息成功", systemInfo));
    }

    /**
     * 简单的ping接口
     */
    @GetMapping("/ping")
    @Operation(summary = "Ping接口", description = "简单的连通性测试")
    public ResponseEntity<ApiResponse<String>> ping() {
        return ResponseEntity.ok(ApiResponse.success("pong"));
    }
}
