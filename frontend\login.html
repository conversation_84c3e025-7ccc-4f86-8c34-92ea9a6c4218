<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>猫舍管理系统 - 登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 400px;
            max-width: 90vw;
        }

        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }

        .login-header h1 {
            font-size: 28px;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .login-header .subtitle {
            font-size: 14px;
            opacity: 0.9;
        }

        .cat-icon {
            font-size: 48px;
            margin-bottom: 20px;
            display: block;
        }

        .login-form {
            padding: 40px 30px;
        }

        .form-group {
            margin-bottom: 25px;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
            font-size: 14px;
        }

        .form-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .login-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .demo-accounts {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }

        .demo-accounts h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .demo-account {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: white;
            border-radius: 8px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .demo-account:hover {
            background: #e9ecef;
        }

        .demo-account:last-child {
            margin-bottom: 0;
        }

        .demo-account .role {
            font-weight: 600;
            color: #667eea;
        }

        .demo-account .credentials {
            font-size: 12px;
            color: #666;
        }

        .error-message {
            background: #fee;
            color: #c33;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #c33;
            display: none;
        }

        .success-message {
            background: #efe;
            color: #3c3;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #3c3;
            display: none;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .footer {
            text-align: center;
            padding: 20px;
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <span class="cat-icon">🐱</span>
            <h1>猫舍管理系统</h1>
            <p class="subtitle">专业的猫咪繁育和健康管理平台</p>
        </div>

        <div class="login-form">
            <div id="error-message" class="error-message"></div>
            <div id="success-message" class="success-message"></div>
            
            <form id="login-form">
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" name="username" required autocomplete="username">
                </div>
                
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password" required autocomplete="current-password">
                </div>
                
                <button type="submit" class="login-btn" id="login-btn">
                    登录
                </button>
            </form>

            <div id="loading" class="loading">
                <div class="loading-spinner"></div>
                <p>正在登录...</p>
            </div>

            <div class="demo-accounts">
                <h3>演示账户</h3>
                <div class="demo-account" onclick="fillCredentials('admin', 'admin123')">
                    <div>
                        <div class="role">管理员</div>
                        <div class="credentials">admin / admin123</div>
                    </div>
                    <div>👑</div>
                </div>
                <div class="demo-account" onclick="fillCredentials('user', 'user123')">
                    <div>
                        <div class="role">普通用户</div>
                        <div class="credentials">user / user123</div>
                    </div>
                    <div>👤</div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>&copy; 2024 猫舍管理系统. 保留所有权利.</p>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080/api';
        
        // 填充演示账户信息
        function fillCredentials(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
        }

        // 显示错误消息
        function showError(message) {
            const errorDiv = document.getElementById('error-message');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            document.getElementById('success-message').style.display = 'none';
        }

        // 显示成功消息
        function showSuccess(message) {
            const successDiv = document.getElementById('success-message');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            document.getElementById('error-message').style.display = 'none';
        }

        // 隐藏消息
        function hideMessages() {
            document.getElementById('error-message').style.display = 'none';
            document.getElementById('success-message').style.display = 'none';
        }

        // 显示/隐藏加载状态
        function setLoading(loading) {
            const loadingDiv = document.getElementById('loading');
            const loginForm = document.getElementById('login-form');
            const loginBtn = document.getElementById('login-btn');
            
            if (loading) {
                loadingDiv.style.display = 'block';
                loginForm.style.display = 'none';
                loginBtn.disabled = true;
            } else {
                loadingDiv.style.display = 'none';
                loginForm.style.display = 'block';
                loginBtn.disabled = false;
            }
        }

        // 登录处理
        async function handleLogin(event) {
            event.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                showError('请输入用户名和密码');
                return;
            }

            setLoading(true);
            hideMessages();

            try {
                const response = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    // 保存token到localStorage
                    localStorage.setItem('authToken', data.data.token);
                    localStorage.setItem('userInfo', JSON.stringify(data.data.userInfo));
                    
                    showSuccess('登录成功！正在跳转...');
                    
                    // 延迟跳转到主页面
                    setTimeout(() => {
                        window.location.href = 'dashboard.html';
                    }, 1500);
                } else {
                    showError(data.message || '登录失败，请检查用户名和密码');
                }
            } catch (error) {
                console.error('登录错误:', error);
                showError('网络连接失败，请检查后端服务是否启动');
            } finally {
                setLoading(false);
            }
        }

        // 检查是否已登录
        function checkAuthStatus() {
            const token = localStorage.getItem('authToken');
            if (token) {
                // 如果已有token，直接跳转到主页面
                window.location.href = 'dashboard.html';
            }
        }

        // 页面加载时检查登录状态
        document.addEventListener('DOMContentLoaded', function() {
            checkAuthStatus();
            
            // 绑定表单提交事件
            document.getElementById('login-form').addEventListener('submit', handleLogin);
            
            // 测试后端连接
            testBackendConnection();
        });

        // 测试后端连接
        async function testBackendConnection() {
            try {
                const response = await fetch(`${API_BASE_URL}/test/health`);
                if (response.ok) {
                    console.log('后端连接正常');
                } else {
                    console.warn('后端连接异常');
                }
            } catch (error) {
                console.error('无法连接到后端服务:', error);
                showError('无法连接到后端服务，请确保服务已启动');
            }
        }
    </script>
</body>
</html>
