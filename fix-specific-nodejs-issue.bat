@echo off
chcp 65001 >nul
echo ========================================
echo 针对您的Node.js问题的专门修复脚本
echo ========================================
echo.

echo 🔍 已识别的问题:
echo   ❌ npm核心文件缺失: npm-prefix.js
echo   ❌ 安装路径异常: D:\软件\node_modules\
echo   ✅ Node.js版本正常: v22.17.1
echo.

echo ========================================
echo 修复方案选择
echo ========================================
echo.
echo 请选择修复方案:
echo   1. 快速修复 - 尝试修复当前安装
echo   2. 完全重装 - 卸载后重新安装 (推荐)
echo   3. 使用Yarn - 安装yarn替代npm
echo   4. 跳过修复 - 继续使用静态版本
echo.

set /p choice="请输入选择 (1-4): "

if "%choice%"=="1" goto :quick_fix
if "%choice%"=="2" goto :complete_reinstall
if "%choice%"=="3" goto :install_yarn
if "%choice%"=="4" goto :skip_fix
echo 无效选择，默认使用快速修复...
goto :quick_fix

:quick_fix
echo.
echo ========================================
echo 方案1: 快速修复当前安装
echo ========================================
echo.

echo 步骤1: 尝试修复npm配置...
npm config set prefix "%APPDATA%\npm" 2>nul
npm config set cache "%APPDATA%\npm-cache" 2>nul
echo 配置已更新

echo.
echo 步骤2: 清理npm缓存...
npm cache clean --force 2>nul
if %errorlevel% equ 0 (
    echo ✅ 缓存清理成功
) else (
    echo ❌ 缓存清理失败，继续下一步...
)

echo.
echo 步骤3: 手动下载npm...
echo 正在打开npm下载页面...
start https://github.com/npm/cli/releases/latest
echo.
echo 请手动下载npm-cli包并解压到:
echo   %APPDATA%\npm\node_modules\npm\
echo.
pause

echo.
echo 步骤4: 测试修复结果...
npm --version 2>nul
if %errorlevel% equ 0 (
    echo ✅ npm修复成功!
    goto :install_deps
) else (
    echo ❌ 快速修复失败，建议选择完全重装
    pause
    goto :end
)

:complete_reinstall
echo.
echo ========================================
echo 方案2: 完全重新安装
echo ========================================
echo.

echo 步骤1: 卸载指导
echo.
echo 请手动执行以下步骤:
echo   1. Win+R 输入 appwiz.cpl
echo   2. 找到并卸载 Node.js
echo   3. 删除目录: D:\软件\node_modules\
echo   4. 删除目录: %APPDATA%\npm
echo.
pause

echo.
echo 步骤2: 下载新版本
echo 正在打开Node.js官网...
start https://nodejs.org/zh-cn/
echo.
echo 请下载并安装:
echo   - 版本: LTS (推荐v20.x)
echo   - 类型: Windows Installer (.msi)
echo   - 位数: 64-bit
echo   - 安装路径: C:\Program Files\nodejs\
echo   - 勾选: Add to PATH
echo.
pause

echo.
echo 步骤3: 验证安装
echo 安装完成后，请打开新的命令行窗口测试:
echo   node --version
echo   npm --version
echo.
echo 如果显示版本号，说明安装成功!
echo.
pause
goto :install_deps

:install_yarn
echo.
echo ========================================
echo 方案3: 安装Yarn替代npm
echo ========================================
echo.

echo 步骤1: 下载Yarn
echo 正在打开Yarn官网...
start https://yarnpkg.com/getting-started/install
echo.
echo 请下载并安装Yarn
echo.
pause

echo.
echo 步骤2: 使用Yarn安装依赖
cd /d "%~dp0frontend"
if exist package.json (
    echo 使用Yarn安装项目依赖...
    yarn install
    if %errorlevel% equ 0 (
        echo ✅ Yarn安装依赖成功!
        echo.
        echo 启动开发服务器...
        yarn dev
    ) else (
        echo ❌ Yarn安装失败
    )
) else (
    echo ❌ 未找到package.json文件
)
goto :end

:skip_fix
echo.
echo ========================================
echo 方案4: 跳过修复，使用静态版本
echo ========================================
echo.

echo 您选择跳过Node.js修复，这是明智的选择!
echo 静态版本功能完全正常，无需Node.js环境。
echo.
echo 正在启动静态版本...
cd /d "%~dp0"
call start-simple.bat
goto :end

:install_deps
echo.
echo ========================================
echo 安装项目依赖
echo ========================================
echo.

cd /d "%~dp0frontend"
if not exist package.json (
    echo ❌ 未找到package.json文件
    goto :end
)

echo 清理旧依赖...
if exist node_modules rmdir /s /q node_modules 2>nul
if exist package-lock.json del package-lock.json 2>nul

echo.
echo 安装依赖包...
npm install
if %errorlevel% equ 0 (
    echo ✅ 依赖安装成功!
    echo.
    echo 启动开发服务器...
    echo 访问地址: http://localhost:3000
    echo.
    npm run dev
) else (
    echo ❌ 依赖安装失败
    echo.
    echo 建议:
    echo 1. 检查网络连接
    echo 2. 尝试使用国内镜像: npm config set registry https://registry.npmmirror.com/
    echo 3. 或者使用静态版本: start-simple.bat
)

:end
echo.
echo ========================================
echo 脚本执行完成
echo ========================================
pause
