@echo off
chcp 65001 >nul
echo ========================================
echo Node.js 卸载验证
echo ========================================
echo.

echo 🔍 验证卸载是否完成...
echo.

echo 1. 测试Node.js命令:
node --version 2>nul
if %errorlevel% equ 0 (
    echo ❌ Node.js仍然可以执行
    echo 当前版本: 
    node --version
    echo.
    echo 可能原因:
    echo   - 程序卸载不完整
    echo   - 环境变量未清理
    echo   - 存在多个安装版本
) else (
    echo ✅ Node.js已无法执行 (卸载成功)
)
echo.

echo 2. 测试npm命令:
npm --version 2>nul
if %errorlevel% equ 0 (
    echo ❌ npm仍然可以执行
    echo 当前版本:
    npm --version
) else (
    echo ✅ npm已无法执行 (卸载成功)
)
echo.

echo 3. 检查PATH环境变量:
echo 当前PATH中的Node.js相关路径:
echo %PATH% | findstr /i node
if %errorlevel% equ 0 (
    echo ⚠️ PATH中仍包含Node.js路径，需要清理
) else (
    echo ✅ PATH中已无Node.js路径
)
echo.

echo 4. 检查残留目录:
echo.

if exist "C:\Program Files\nodejs\" (
    echo ❌ 标准安装目录仍存在: C:\Program Files\nodejs\
) else (
    echo ✅ 标准安装目录已清理
)

if exist "C:\Program Files (x86)\nodejs\" (
    echo ❌ 32位安装目录仍存在: C:\Program Files (x86)\nodejs\
) else (
    echo ✅ 32位安装目录已清理
)

if exist "D:\软件\nodejs\" (
    echo ❌ 自定义安装目录仍存在: D:\软件\nodejs\
) else (
    echo ✅ 自定义安装目录已清理
)

if exist "D:\软件\node_modules\" (
    echo ❌ 问题目录仍存在: D:\软件\node_modules\
) else (
    echo ✅ 问题目录已清理
)

if exist "%APPDATA%\npm" (
    echo ❌ 用户npm目录仍存在: %APPDATA%\npm
) else (
    echo ✅ 用户npm目录已清理
)
echo.

echo ========================================
echo 卸载验证结果
echo ========================================
echo.

node --version 2>nul
npm --version 2>nul
if %errorlevel% neq 0 (
    echo 🎉 恭喜！Node.js完全卸载成功！
    echo.
    echo ✅ 所有检查项目都通过
    echo ✅ 可以开始安装新版本
    echo.
    echo 下一步:
    echo 1. 运行: install-new-nodejs.bat
    echo 2. 或访问: https://nodejs.org 手动下载
    echo.
) else (
    echo ⚠️ 卸载不完整，仍需要进一步清理
    echo.
    echo 建议:
    echo 1. 重新检查"程序和功能"
    echo 2. 手动清理环境变量
    echo 3. 重启计算机后再次验证
    echo.
)

pause
