package com.cattery.dto.ai;

import lombok.Data;
import java.util.List;

/**
 * 疾病风险DTO
 */
@Data
public class DiseaseRiskDTO {
    
    /**
     * 疾病名称
     */
    private String diseaseName;
    
    /**
     * 风险评分 (0.0 - 1.0)
     */
    private Double riskScore;
    
    /**
     * 风险等级 (LOW, MEDIUM, HIGH, CRITICAL)
     */
    private String riskLevel;
    
    /**
     * 风险因素
     */
    private List<String> riskFactors;
    
    /**
     * 症状指标
     */
    private List<String> symptoms;
    
    /**
     * 预防措施
     */
    private List<String> preventiveMeasures;
    
    /**
     * 建议检查项目
     */
    private List<String> recommendedTests;
    
    /**
     * 紧急程度
     */
    private String urgency;
    
    /**
     * 疾病描述
     */
    private String description;
    
    /**
     * 治疗建议
     */
    private String treatmentAdvice;
}
