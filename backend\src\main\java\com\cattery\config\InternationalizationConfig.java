package com.cattery.config;

import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.i18n.AcceptHeaderLocaleResolver;
import org.springframework.web.servlet.i18n.LocaleChangeInterceptor;
import org.springframework.lang.NonNull;

import java.util.Arrays;
import java.util.Locale;

/**
 * 国际化配置类
 */
@Configuration
public class InternationalizationConfig implements WebMvcConfigurer {

    /**
     * 配置消息源
     */
    @Bean
    public MessageSource messageSource() {
        ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
        
        // 设置消息文件的基础名称
        messageSource.setBasenames(
            "i18n/messages",           // 通用消息
            "i18n/validation",         // 验证消息
            "i18n/error",             // 错误消息
            "i18n/business"           // 业务消息
        );
        
        // 设置默认编码
        messageSource.setDefaultEncoding("UTF-8");
        
        // 设置缓存时间（秒）
        messageSource.setCacheSeconds(3600);
        
        // 设置是否使用代码作为默认消息
        messageSource.setUseCodeAsDefaultMessage(true);
        
        // 设置是否总是应用MessageFormat规则
        messageSource.setAlwaysUseMessageFormat(false);
        
        return messageSource;
    }

    /**
     * 配置区域解析器
     */
    @Bean
    public LocaleResolver localeResolver() {
        AcceptHeaderLocaleResolver localeResolver = new AcceptHeaderLocaleResolver();
        
        // 设置默认区域
        localeResolver.setDefaultLocale(Locale.SIMPLIFIED_CHINESE);
        
        // 设置支持的区域列表
        localeResolver.setSupportedLocales(Arrays.asList(
            Locale.SIMPLIFIED_CHINESE,    // zh-CN
            Locale.US,                    // en-US
            Locale.JAPAN,                 // ja-JP
            Locale.KOREA                  // ko-KR
        ));
        
        return localeResolver;
    }

    /**
     * 配置区域变更拦截器
     */
    @Bean
    public LocaleChangeInterceptor localeChangeInterceptor() {
        LocaleChangeInterceptor interceptor = new LocaleChangeInterceptor();
        
        // 设置请求参数名
        interceptor.setParamName("lang");
        
        // 设置是否忽略无效的区域
        interceptor.setIgnoreInvalidLocale(true);
        
        return interceptor;
    }

    /**
     * 注册拦截器
     */
    @Override
    public void addInterceptors(@NonNull InterceptorRegistry registry) {
        registry.addInterceptor(localeChangeInterceptor());
    }
}
