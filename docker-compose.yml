version: '3.8'

services:
  # 数据库服务
  mysql:
    image: mysql:8.0
    container_name: cat-shelter-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-rootpassword}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-dengdeng}
      MYSQL_USER: ${MYSQL_USER:-catuser}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-catpassword}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./backend/database_schema.sql:/docker-entrypoint-initdb.d/01-schema.sql:ro
    networks:
      - cat-shelter-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: cat-shelter-backend
    restart: unless-stopped
    environment:
      SPRING_PROFILES_ACTIVE: ${SPRING_PROFILES_ACTIVE:-prod}
      DB_URL: ***********************/${MYSQL_DATABASE:-dengdeng}?useSSL=false&serverTimezone=UTC&allowPublicKeyRetrieval=true
      DB_USERNAME: ${MYSQL_USER:-catuser}
      DB_PASSWORD: ${MYSQL_PASSWORD:-catpassword}
      JWT_SECRET: ${JWT_SECRET:-mySecretKey123456789012345678901234567890}
      JWT_EXPIRATION: ${JWT_EXPIRATION:-86400000}
      CORS_ORIGINS: ${CORS_ORIGINS:-http://localhost}
    ports:
      - "8080:8080"
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - cat-shelter-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/actuator/health"]
      timeout: 10s
      retries: 5
      start_period: 30s

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: cat-shelter-frontend
    restart: unless-stopped
    ports:
      - "80:80"
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - cat-shelter-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      timeout: 10s
      retries: 3

  # Redis 缓存服务（可选）
  redis:
    image: redis:7-alpine
    container_name: cat-shelter-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - cat-shelter-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 10s
      retries: 3

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  cat-shelter-network:
    driver: bridge
