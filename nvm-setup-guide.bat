@echo off
echo ========================================
echo NVM for Windows Setup Guide
echo ========================================
echo.

echo Step 1: Install NVM
echo.
echo If you haven't installed NVM yet:
echo 1. Double-click the downloaded nvm-setup.exe
echo 2. Follow the installation wizard
echo 3. Use default installation paths
echo 4. Complete the installation
echo.

echo Step 2: Verify NVM Installation
echo.
echo Testing NVM installation...
nvm version 2>nul
if %errorlevel% equ 0 (
    echo ✅ NVM is installed and working!
    nvm version
    goto :install_node
) else (
    echo ❌ NVM is not installed or not in PATH
    echo.
    echo Please:
    echo 1. Make sure you've run nvm-setup.exe
    echo 2. Restart this command prompt
    echo 3. Or restart your computer
    echo.
    echo If NVM is installed but not working:
    echo - Check if C:\Users\<USER>\AppData\Roaming\nvm exists
    echo - Check if PATH contains NVM directory
    echo.
    goto :troubleshoot
)

:install_node
echo.
echo Step 3: Install Node.js using NVM
echo.

echo Available Node.js versions:
nvm list available

echo.
echo Installing Node.js LTS version...
echo.

echo Recommended: Install Node.js 20.11.0 (LTS)
set /p install_version="Enter version to install (or press Enter for 20.11.0): "
if "%install_version%"=="" set install_version=20.11.0

echo.
echo Installing Node.js %install_version%...
nvm install %install_version%

if %errorlevel% equ 0 (
    echo ✅ Node.js %install_version% installed successfully!
    echo.
    echo Setting Node.js %install_version% as active version...
    nvm use %install_version%
    
    if %errorlevel% equ 0 (
        echo ✅ Node.js %install_version% is now active!
        goto :verify_installation
    ) else (
        echo ❌ Failed to activate Node.js %install_version%
        goto :troubleshoot
    )
) else (
    echo ❌ Failed to install Node.js %install_version%
    goto :troubleshoot
)

:verify_installation
echo.
echo Step 4: Verify Node.js Installation
echo.

echo Testing Node.js...
node --version
if %errorlevel% equ 0 (
    echo ✅ Node.js is working!
    echo Version: 
    node --version
) else (
    echo ❌ Node.js is not working
    goto :troubleshoot
)

echo.
echo Testing npm...
npm --version
if %errorlevel% equ 0 (
    echo ✅ npm is working!
    echo Version: 
    npm --version
) else (
    echo ❌ npm is not working
    goto :troubleshoot
)

echo.
echo ========================================
echo SUCCESS! Node.js is ready to use
echo ========================================
echo.

echo Current Node.js version:
node --version

echo Current npm version:
npm --version

echo.
echo NVM Commands Reference:
echo   nvm list                 - Show installed versions
echo   nvm list available       - Show available versions
echo   nvm install [version]    - Install a version
echo   nvm use [version]        - Switch to a version
echo   nvm uninstall [version]  - Remove a version
echo.

echo Next Steps:
echo 1. Navigate to your project: cd D:\噔噔\frontend
echo 2. Install dependencies: npm install
echo 3. Start development server: npm run dev
echo.

goto :install_project_deps

:troubleshoot
echo.
echo ========================================
echo Troubleshooting
echo ========================================
echo.

echo If NVM is not working:
echo.

echo 1. Check if NVM is installed:
if exist "C:\Users\<USER>\AppData\Roaming\nvm\nvm.exe" (
    echo ✅ NVM executable found
) else (
    echo ❌ NVM executable not found
    echo Please reinstall NVM from: https://github.com/coreybutler/nvm-windows/releases
)

echo.
echo 2. Check PATH environment variable:
echo %PATH% | findstr /i nvm
if %errorlevel% equ 0 (
    echo ✅ NVM found in PATH
) else (
    echo ❌ NVM not found in PATH
    echo You may need to:
    echo - Restart command prompt
    echo - Restart computer
    echo - Manually add NVM to PATH
)

echo.
echo 3. Manual PATH setup (if needed):
echo Add these paths to your PATH environment variable:
echo   C:\Users\<USER>\AppData\Roaming\nvm
echo   C:\Program Files\nodejs
echo.

echo 4. Alternative: Restart and try again
echo Sometimes a simple restart fixes PATH issues
echo.

goto :end

:install_project_deps
echo.
echo ========================================
echo Install Project Dependencies
echo ========================================
echo.

set /p install_deps="Install project dependencies now? (Y/N): "
if /i "%install_deps%"=="Y" (
    echo.
    echo Navigating to frontend directory...
    cd /d "D:\噔噔\frontend"
    
    if exist package.json (
        echo Found package.json, installing dependencies...
        echo.
        npm install
        
        if %errorlevel% equ 0 (
            echo.
            echo ✅ Dependencies installed successfully!
            echo.
            echo Starting development server...
            echo Access the application at: http://localhost:3000
            echo Press Ctrl+C to stop the server
            echo.
            npm run dev
        ) else (
            echo ❌ Failed to install dependencies
            echo.
            echo Try these solutions:
            echo 1. npm cache clean --force
            echo 2. Delete node_modules and package-lock.json
            echo 3. Run npm install again
        )
    ) else (
        echo ❌ package.json not found in D:\噔噔\frontend
        echo Please check the project directory
    )
) else (
    echo.
    echo You can install dependencies later with:
    echo   cd D:\噔噔\frontend
    echo   npm install
    echo   npm run dev
)

:end
echo.
pause
