import * as axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'

// 获取axios的默认导出
const axiosDefault = axios.default || axios
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import router from '@/router'

// API基础配置
export const API_CONFIG = {
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api',
  timeout: 30000,
  withCredentials: false
}

// 创建axios实例
const apiClient: AxiosInstance = axiosDefault.create(API_CONFIG)

// 请求拦截器
apiClient.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // 添加认证token
    const authStore = useAuthStore()
    if (authStore.token) {
      config.headers = config.headers || {}
      config.headers.Authorization = `Bearer ${authStore.token}`
    }

    // 添加请求时间戳（防止缓存）
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }

    // 设置Content-Type
    if (config.method === 'post' || config.method === 'put' || config.method === 'patch') {
      if (!config.headers['Content-Type']) {
        config.headers['Content-Type'] = 'application/json'
      }
    }

    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    // 检查业务状态码
    const { data } = response
    
    // 如果是文件下载，直接返回
    if (response.config.responseType === 'blob') {
      return response
    }

    // 统一的响应格式处理
    if (data && typeof data === 'object') {
      // 如果有success字段，检查业务状态
      if ('success' in data) {
        if (!data.success) {
          ElMessage.error(data.message || '操作失败')
          return Promise.reject(new Error(data.message || '操作失败'))
        }
        return data
      }
      
      // 如果有code字段，检查状态码
      if ('code' in data) {
        if (data.code !== 200 && data.code !== 0) {
          ElMessage.error(data.message || '操作失败')
          return Promise.reject(new Error(data.message || '操作失败'))
        }
        return data
      }
    }

    return data
  },
  async (error) => {
    const { response, message } = error

    // 网络错误
    if (!response) {
      ElMessage.error('网络连接失败，请检查网络设置')
      return Promise.reject(error)
    }

    const { status, data } = response

    switch (status) {
      case 400:
        ElMessage.error(data?.message || '请求参数错误')
        break
      
      case 401:
        // 未授权，清除token并跳转到登录页
        const authStore = useAuthStore()
        authStore.logout()
        
        ElMessageBox.alert('登录已过期，请重新登录', '提示', {
          confirmButtonText: '确定',
          type: 'warning'
        }).then(() => {
          router.push('/login')
        })
        break
      
      case 403:
        ElMessage.error('没有权限访问该资源')
        break
      
      case 404:
        ElMessage.error('请求的资源不存在')
        break
      
      case 422:
        // 表单验证错误
        if (data?.errors) {
          const errorMessages = Object.values(data.errors).flat()
          ElMessage.error(errorMessages.join(', '))
        } else {
          ElMessage.error(data?.message || '数据验证失败')
        }
        break
      
      case 429:
        ElMessage.error('请求过于频繁，请稍后再试')
        break
      
      case 500:
        ElMessage.error('服务器内部错误')
        break
      
      case 502:
        ElMessage.error('网关错误')
        break
      
      case 503:
        ElMessage.error('服务暂时不可用')
        break
      
      case 504:
        ElMessage.error('请求超时')
        break
      
      default:
        ElMessage.error(data?.message || message || '请求失败')
    }

    return Promise.reject(error)
  }
)

// 导出配置和实例
export default apiClient

// 通用API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  code: number
  message: string
  data: T
  timestamp?: number
}

// 分页响应类型
export interface PageResponse<T = any> {
  content: T[]
  totalElements: number
  totalPages: number
  size: number
  number: number
  first: boolean
  last: boolean
  empty: boolean
}

// 请求配置类型
export interface RequestConfig extends AxiosRequestConfig {
  showLoading?: boolean
  showError?: boolean
  showSuccess?: boolean
}

// 封装常用的HTTP方法
export const http = {
  get<T = any>(url: string, config?: RequestConfig): Promise<T> {
    return apiClient.get(url, config)
  },

  post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return apiClient.post(url, data, config)
  },

  put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return apiClient.put(url, data, config)
  },

  patch<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return apiClient.patch(url, data, config)
  },

  delete<T = any>(url: string, config?: RequestConfig): Promise<T> {
    return apiClient.delete(url, config)
  },

  upload<T = any>(url: string, formData: FormData, config?: RequestConfig): Promise<T> {
    return apiClient.post(url, formData, {
      ...config,
      headers: {
        ...config?.headers,
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  download(url: string, config?: RequestConfig): Promise<AxiosResponse> {
    return apiClient.get(url, {
      ...config,
      responseType: 'blob'
    })
  }
}

// 导出类型
export type { AxiosRequestConfig, AxiosResponse }
