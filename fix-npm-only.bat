@echo off
chcp 65001 >nul
echo ========================================
echo 精准修复npm (保留Node.js)
echo ========================================
echo.

echo 🔍 当前状态:
echo   ✅ Node.js v22.17.1 正常工作
echo   ❌ npm 损坏无法使用
echo   🎯 只需修复npm，无需重装Node.js
echo.

echo ========================================
echo 方法1: 重新安装npm
echo ========================================
echo.

echo 步骤1: 使用Node.js内置的npm安装器...
echo.

echo 尝试重新安装npm到全局位置...
node -e "console.log('Node.js工作正常，版本:', process.version)"
echo.

echo 下载并安装最新npm...
echo 这可能需要几分钟，请耐心等待...
echo.

REM 使用Node.js直接安装npm
node -e "
const https = require('https');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('正在下载npm安装脚本...');

const options = {
  hostname: 'registry.npmjs.org',
  port: 443,
  path: '/npm/latest',
  method: 'GET'
};

const req = https.request(options, (res) => {
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  res.on('end', () => {
    try {
      const npmInfo = JSON.parse(data);
      console.log('最新npm版本:', npmInfo.version);
      console.log('正在安装npm...');
      
      // 使用curl下载npm
      execSync('curl -L https://www.npmjs.com/install.sh | sh', {stdio: 'inherit'});
      
    } catch (error) {
      console.log('自动安装失败，请手动安装');
    }
  });
});

req.on('error', (error) => {
  console.log('网络请求失败，尝试备用方案...');
});

req.end();
" 2>nul

echo.
echo ========================================
echo 方法2: 手动下载npm
echo ========================================
echo.

echo 如果自动安装失败，请手动下载:
echo.
echo 1. 访问: https://github.com/npm/cli/releases/latest
echo 2. 下载: npm-cli.zip
echo 3. 解压到Node.js安装目录
echo.

echo 正在打开npm下载页面...
start https://github.com/npm/cli/releases/latest
echo.

echo ========================================
echo 方法3: 使用yarn替代npm
echo ========================================
echo.

echo 如果npm修复困难，可以安装yarn:
echo.
echo 1. 访问: https://yarnpkg.com/getting-started/install
echo 2. 下载并安装yarn
echo 3. 使用yarn代替npm命令
echo.

echo 正在打开yarn官网...
start https://yarnpkg.com/getting-started/install
echo.

echo ========================================
echo 验证修复结果
echo ========================================
echo.

echo 请在修复完成后测试:
echo.

echo 测试npm:
npm --version 2>nul
if %errorlevel% equ 0 (
    echo ✅ npm修复成功!
    echo npm版本:
    npm --version
    goto :install_deps
) else (
    echo ❌ npm仍然无法工作
    echo.
    echo 建议:
    echo 1. 尝试重启命令行
    echo 2. 检查环境变量设置
    echo 3. 使用yarn替代npm
    echo 4. 或选择完全重装Node.js
)

echo.
pause
goto :end

:install_deps
echo.
echo ========================================
echo 安装项目依赖
echo ========================================
echo.

cd /d "%~dp0frontend"
if exist package.json (
    echo 找到项目配置文件，开始安装依赖...
    echo.
    
    REM 清理旧依赖
    if exist node_modules (
        echo 清理旧的node_modules...
        rmdir /s /q node_modules 2>nul
    )
    if exist package-lock.json (
        echo 清理package-lock.json...
        del package-lock.json 2>nul
    )
    
    echo 安装项目依赖...
    npm install
    if %errorlevel% equ 0 (
        echo.
        echo ✅ 项目依赖安装成功!
        echo.
        echo 🚀 启动开发服务器...
        echo 访问地址: http://localhost:3000
        echo 按 Ctrl+C 停止服务器
        echo.
        npm run dev
    ) else (
        echo ❌ 项目依赖安装失败
        echo.
        echo 可能的解决方案:
        echo 1. 检查网络连接
        echo 2. 设置npm镜像: npm config set registry https://registry.npmmirror.com/
        echo 3. 清理npm缓存: npm cache clean --force
        echo 4. 使用yarn: yarn install
    )
) else (
    echo ❌ 未找到package.json文件
    echo 请确保在正确的项目目录中
)

:end
echo.
echo ========================================
echo 修复完成
echo ========================================
echo.

echo 如果npm修复成功:
echo ✅ 可以正常使用npm命令
echo ✅ 可以安装项目依赖
echo ✅ 可以启动开发服务器
echo.

echo 如果npm仍有问题:
echo 🔄 可以尝试使用yarn
echo 🔄 或选择完全重装Node.js
echo 🔄 或继续使用静态版本
echo.

pause
