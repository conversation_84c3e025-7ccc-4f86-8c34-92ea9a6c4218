import request from '@/utils/request'

export interface LoginParams {
  username: string
  password: string
}

export interface LoginResponse {
  token: string
  userInfo: {
    id: number
    username: string
    realName: string
    email: string
    roles: string[]
    permissions: string[]
  }
}

export interface UserInfo {
  id: number
  username: string
  realName: string
  email: string
  phone?: string
  avatar?: string
  roles: string[]
  permissions: string[]
  enabled: boolean
  accountNonExpired: boolean
  accountNonLocked: boolean
  credentialsNonExpired: boolean
}

// 登录
export const login = (data: LoginParams) => {
  return request.post<LoginResponse>('/auth/login', data)
}

// 登出
export const logout = () => {
  return request.post('/auth/logout')
}

// 获取用户信息
export const getUserInfo = () => {
  return request.get<UserInfo>('/auth/userinfo')
}

// 刷新token
export const refreshToken = () => {
  return request.post('/auth/refresh')
}

// 修改密码
export const changePassword = (data: { oldPassword: string; newPassword: string }) => {
  return request.post('/auth/change-password', data)
}
