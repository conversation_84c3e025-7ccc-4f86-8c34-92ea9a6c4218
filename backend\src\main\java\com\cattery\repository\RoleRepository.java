package com.cattery.repository;

import com.cattery.entity.Role;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 角色仓库接口
 */
@Repository
public interface RoleRepository extends JpaRepository<Role, Long>, JpaSpecificationExecutor<Role> {

    /**
     * 根据角色名称查找角色
     */
    Optional<Role> findByName(String name);

    /**
     * 根据显示名称查找角色
     */
    Optional<Role> findByDisplayName(String displayName);

    /**
     * 检查角色名称是否存在
     */
    boolean existsByName(String name);

    /**
     * 检查显示名称是否存在
     */
    boolean existsByDisplayName(String displayName);

    /**
     * 根据启用状态查找角色
     */
    List<Role> findByEnabled(Boolean enabled);

    /**
     * 查找所有启用的角色
     */
    List<Role> findByEnabledTrueOrderByName();

    /**
     * 根据权限查找角色
     */
    @Query("SELECT DISTINCT r FROM Role r JOIN r.permissions p WHERE p.name = :permissionName")
    List<Role> findByPermissionName(@Param("permissionName") String permissionName);

    /**
     * 根据用户查找角色
     */
    @Query("SELECT r FROM Role r JOIN r.users u WHERE u.id = :userId")
    List<Role> findByUserId(@Param("userId") Long userId);

    /**
     * 根据用户名查找角色
     */
    @Query("SELECT r FROM Role r JOIN r.users u WHERE u.username = :username")
    List<Role> findByUsername(@Param("username") String username);

    /**
     * 统计启用的角色数量
     */
    long countByEnabled(Boolean enabled);

    /**
     * 根据关键词搜索角色
     */
    @Query("SELECT r FROM Role r WHERE " +
           "LOWER(r.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(r.displayName) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(r.description) LIKE LOWER(CONCAT('%', :keyword, '%'))")
    Page<Role> searchRoles(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 查找有用户的角色
     */
    @Query("SELECT DISTINCT r FROM Role r WHERE SIZE(r.users) > 0")
    List<Role> findRolesWithUsers();

    /**
     * 查找没有用户的角色
     */
    @Query("SELECT r FROM Role r WHERE SIZE(r.users) = 0")
    List<Role> findRolesWithoutUsers();

    /**
     * 查找有权限的角色
     */
    @Query("SELECT DISTINCT r FROM Role r WHERE SIZE(r.permissions) > 0")
    List<Role> findRolesWithPermissions();

    /**
     * 查找没有权限的角色
     */
    @Query("SELECT r FROM Role r WHERE SIZE(r.permissions) = 0")
    List<Role> findRolesWithoutPermissions();

    /**
     * 统计各角色的用户数量
     */
    @Query("SELECT r.name, SIZE(r.users) FROM Role r GROUP BY r.name ORDER BY SIZE(r.users) DESC")
    List<Object[]> countUsersByRole();

    /**
     * 统计各角色的权限数量
     */
    @Query("SELECT r.name, SIZE(r.permissions) FROM Role r GROUP BY r.name ORDER BY SIZE(r.permissions) DESC")
    List<Object[]> countPermissionsByRole();

    /**
     * 查找系统默认角色
     */
    @Query("SELECT r FROM Role r WHERE r.name IN ('ROLE_ADMIN', 'ROLE_STAFF', 'ROLE_USER')")
    List<Role> findSystemRoles();

    /**
     * 查找自定义角色
     */
    @Query("SELECT r FROM Role r WHERE r.name NOT IN ('ROLE_ADMIN', 'ROLE_STAFF', 'ROLE_USER')")
    List<Role> findCustomRoles();
}
