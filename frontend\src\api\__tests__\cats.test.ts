import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { catApi } from '../cats'
import type { Cat } from '@/types'

// Mock fetch
const mockFetch = vi.fn()
global.fetch = mockFetch

// Mock ElMessage
vi.mock('element-plus', () => ({
  ElMessage: {
    error: vi.fn()
  }
}))

describe('catApi', () => {
  const mockCat: Cat = {
    id: 1,
    name: '测试猫咪',
    breedId: 1,
    breedName: '波斯猫',
    gender: 'FEMALE',
    dateOfBirth: '2023-01-01',
    color: '白色',
    status: 'PENDING_ADOPTION',
    description: '一只可爱的测试猫咪'
  }

  const mockApiResponse = {
    success: true,
    message: '操作成功',
    data: mockCat,
    timestamp: '2024-01-01T00:00:00Z'
  }

  beforeEach(() => {
    mockFetch.mockClear()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('getAll', () => {
    it('should fetch all cats successfully', async () => {
      const mockCats = [mockCat]
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ ...mockApiResponse, data: mockCats })
      })

      const result = await catApi.getAll()

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/cats'),
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          })
        })
      )
      expect(result).toEqual(mockCats)
    })

    it('should handle API error response', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: false,
          message: '获取失败'
        })
      })

      await expect(catApi.getAll()).rejects.toThrow('获取失败')
    })

    it('should handle network error', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error'
      })

      await expect(catApi.getAll()).rejects.toThrow('HTTP 500: Internal Server Error')
    })
  })

  describe('getById', () => {
    it('should fetch cat by id successfully', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponse
      })

      const result = await catApi.getById(1)

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/cats/1'),
        expect.objectContaining({
          method: 'GET'
        })
      )
      expect(result).toEqual(mockCat)
    })
  })

  describe('create', () => {
    it('should create cat successfully', async () => {
      const newCat: Omit<Cat, 'id'> = {
        name: '新猫咪',
        breedId: 1,
        gender: 'MALE',
        status: 'PENDING_ADOPTION'
      }

      const createdCat = { ...newCat, id: 2 }
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ ...mockApiResponse, data: createdCat })
      })

      const result = await catApi.create(newCat)

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/cats'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          }),
          body: JSON.stringify(newCat)
        })
      )
      expect(result).toEqual(createdCat)
    })
  })

  describe('update', () => {
    it('should update cat successfully', async () => {
      const updateData: Partial<Cat> = {
        name: '更新后的猫咪',
        status: 'ADOPTED'
      }

      const updatedCat = { ...mockCat, ...updateData }
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ ...mockApiResponse, data: updatedCat })
      })

      const result = await catApi.update(1, updateData)

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/cats/1'),
        expect.objectContaining({
          method: 'PUT',
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          }),
          body: JSON.stringify(updateData)
        })
      )
      expect(result).toEqual(updatedCat)
    })
  })

  describe('delete', () => {
    it('should delete cat successfully', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          message: '删除成功'
        })
      })

      await catApi.delete(1)

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/cats/1'),
        expect.objectContaining({
          method: 'DELETE'
        })
      )
    })
  })

  describe('timeout handling', () => {
    it('should handle request timeout', async () => {
      // Mock a timeout scenario
      mockFetch.mockImplementationOnce(() => 
        new Promise((_, reject) => {
          setTimeout(() => {
            const error = new Error('The operation was aborted')
            error.name = 'AbortError'
            reject(error)
          }, 100)
        })
      )

      await expect(catApi.getAll()).rejects.toThrow('请求超时')
    })
  })
})
