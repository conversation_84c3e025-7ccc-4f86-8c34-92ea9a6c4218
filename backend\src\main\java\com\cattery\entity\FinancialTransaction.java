package com.cattery.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 财务交易实体类
 */
@Entity
@Table(name = "financial_transactions")
@Data
@EqualsAndHashCode(callSuper = false)
public class FinancialTransaction {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 交易类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "transaction_type", nullable = false)
    private TransactionType transactionType;

    /**
     * 交易金额
     */
    @Column(nullable = false, precision = 12, scale = 2)
    private BigDecimal amount;

    /**
     * 交易日期
     */
    @Column(name = "transaction_date", nullable = false)
    private LocalDateTime transactionDate;

    /**
     * 交易描述
     */
    @Column(columnDefinition = "TEXT")
    private String description;

    /**
     * 交易分类
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "category_id")
    private FinancialCategory category;

    /**
     * 支付方式
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "payment_method")
    private PaymentMethod paymentMethod;

    /**
     * 关联的猫咪（如果适用）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_id")
    private Cat cat;

    /**
     * 关联的客户（如果适用）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "customer_id")
    private Customer customer;

    /**
     * 发票号码
     */
    @Column(name = "invoice_number", length = 100)
    private String invoiceNumber;

    /**
     * 收据路径
     */
    @Column(name = "receipt_path", length = 500)
    private String receiptPath;

    /**
     * 备注
     */
    @Column(columnDefinition = "TEXT")
    private String notes;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 创建者ID
     */
    @Column(name = "created_by")
    private Long createdBy;

    /**
     * 更新者ID
     */
    @Column(name = "updated_by")
    private Long updatedBy;

    /**
     * 交易类型枚举
     */
    public enum TransactionType {
        INCOME("收入"),
        EXPENSE("支出"),
        TRANSFER("转账");

        private final String description;

        TransactionType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 支付方式枚举
     */
    public enum PaymentMethod {
        CASH("现金"),
        BANK_TRANSFER("银行转账"),
        CREDIT_CARD("信用卡"),
        DEBIT_CARD("借记卡"),
        ALIPAY("支付宝"),
        WECHAT_PAY("微信支付"),
        CHECK("支票"),
        OTHER("其他");

        private final String description;

        PaymentMethod(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
