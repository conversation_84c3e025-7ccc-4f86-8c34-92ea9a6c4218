package com.cattery.service;

import com.cattery.entity.FinancialTransaction;
import com.cattery.entity.FinancialCategory;
import com.cattery.repository.FinancialTransactionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 财务管理服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class FinancialService {
    
    private final FinancialTransactionRepository financialTransactionRepository;
    
    /**
     * 记录财务交易
     */
    public FinancialTransaction recordTransaction(FinancialTransaction transaction) {
        log.info("记录财务交易: type={}, amount={}, description={}", 
            transaction.getTransactionType(), transaction.getAmount(), transaction.getDescription());
        
        // 验证金额
        if (transaction.getAmount() == null || transaction.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("交易金额必须大于0");
        }
        
        transaction.setTransactionDate(LocalDateTime.now());
        transaction.setCreatedAt(LocalDateTime.now());
        
        return financialTransactionRepository.save(transaction);
    }
    
    /**
     * 更新财务交易
     */
    public FinancialTransaction updateTransaction(Long transactionId, FinancialTransaction updatedTransaction) {
        log.info("更新财务交易: transactionId={}", transactionId);
        
        FinancialTransaction existingTransaction = financialTransactionRepository.findById(transactionId)
            .orElseThrow(() -> new IllegalArgumentException("财务交易不存在"));
        
        // 验证金额
        if (updatedTransaction.getAmount() == null || updatedTransaction.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("交易金额必须大于0");
        }
        
        // 更新字段
        existingTransaction.setTransactionType(updatedTransaction.getTransactionType());
        existingTransaction.setAmount(updatedTransaction.getAmount());
        existingTransaction.setDescription(updatedTransaction.getDescription());
        existingTransaction.setCategory(updatedTransaction.getCategory());
        existingTransaction.setPaymentMethod(updatedTransaction.getPaymentMethod());
        existingTransaction.setInvoiceNumber(updatedTransaction.getInvoiceNumber());
        existingTransaction.setNotes(updatedTransaction.getNotes());
        
        return financialTransactionRepository.save(existingTransaction);
    }
    
    /**
     * 获取所有财务交易
     */
    @Transactional(readOnly = true)
    public Page<FinancialTransaction> getAllTransactions(Pageable pageable) {
        log.debug("获取所有财务交易: pageable={}", pageable);
        return financialTransactionRepository.findAll(pageable);
    }
    
    /**
     * 根据ID获取财务交易
     */
    @Transactional(readOnly = true)
    public Optional<FinancialTransaction> getTransactionById(Long transactionId) {
        log.debug("根据ID获取财务交易: transactionId={}", transactionId);
        return financialTransactionRepository.findById(transactionId);
    }
    
    /**
     * 根据类型获取财务交易
     */
    @Transactional(readOnly = true)
    public Page<FinancialTransaction> getTransactionsByType(
            FinancialTransaction.TransactionType type, Pageable pageable) {
        log.debug("根据类型获取财务交易: type={}, pageable={}", type, pageable);
        return financialTransactionRepository.findByTransactionType(type, pageable);
    }
    
    /**
     * 根据日期范围获取财务交易
     */
    @Transactional(readOnly = true)
    public Page<FinancialTransaction> getTransactionsByDateRange(
            LocalDateTime startDate, LocalDateTime endDate, Pageable pageable) {
        log.debug("根据日期范围获取财务交易: {} - {}, pageable={}", startDate, endDate, pageable);
        return financialTransactionRepository.findByTransactionDateBetween(startDate, endDate, pageable);
    }
    
    /**
     * 根据分类获取财务交易
     */
    @Transactional(readOnly = true)
    public Page<FinancialTransaction> getTransactionsByCategory(String category, Pageable pageable) {
        log.debug("根据分类获取财务交易: category={}, pageable={}", category, pageable);
        return financialTransactionRepository.findByCategory(category, pageable);
    }
    
    /**
     * 搜索财务交易
     */
    @Transactional(readOnly = true)
    public Page<FinancialTransaction> searchTransactions(String keyword, Pageable pageable) {
        log.debug("搜索财务交易: keyword={}, pageable={}", keyword, pageable);
        return financialTransactionRepository.findByDescriptionContainingIgnoreCaseOrNotesContainingIgnoreCase(
            keyword, keyword, pageable);
    }
    
    /**
     * 删除财务交易
     */
    public void deleteTransaction(Long transactionId) {
        log.info("删除财务交易: transactionId={}", transactionId);
        
        if (!financialTransactionRepository.existsById(transactionId)) {
            throw new IllegalArgumentException("财务交易不存在");
        }
        
        financialTransactionRepository.deleteById(transactionId);
    }
    
    /**
     * 计算总收入
     */
    @Transactional(readOnly = true)
    public BigDecimal getTotalIncome(LocalDateTime startDate, LocalDateTime endDate) {
        log.debug("计算总收入: {} - {}", startDate, endDate);
        BigDecimal total = financialTransactionRepository.sumByTransactionTypeAndDateRange(
            FinancialTransaction.TransactionType.INCOME, startDate, endDate);
        return total != null ? total : BigDecimal.ZERO;
    }
    
    /**
     * 计算总支出
     */
    @Transactional(readOnly = true)
    public BigDecimal getTotalExpense(LocalDateTime startDate, LocalDateTime endDate) {
        log.debug("计算总支出: {} - {}", startDate, endDate);
        BigDecimal total = financialTransactionRepository.sumByTransactionTypeAndDateRange(
            FinancialTransaction.TransactionType.EXPENSE, startDate, endDate);
        return total != null ? total : BigDecimal.ZERO;
    }
    
    /**
     * 计算净利润
     */
    @Transactional(readOnly = true)
    public BigDecimal getNetProfit(LocalDateTime startDate, LocalDateTime endDate) {
        log.debug("计算净利润: {} - {}", startDate, endDate);
        BigDecimal income = getTotalIncome(startDate, endDate);
        BigDecimal expense = getTotalExpense(startDate, endDate);
        return income.subtract(expense);
    }
    
    /**
     * 获取收入分类统计
     */
    @Transactional(readOnly = true)
    public List<Object[]> getIncomeByCategory(LocalDateTime startDate, LocalDateTime endDate) {
        log.debug("获取收入分类统计: {} - {}", startDate, endDate);
        return financialTransactionRepository.sumByCategoryAndTypeAndDateRange(
            FinancialTransaction.TransactionType.INCOME, startDate, endDate);
    }
    
    /**
     * 获取支出分类统计
     */
    @Transactional(readOnly = true)
    public List<Object[]> getExpenseByCategory(LocalDateTime startDate, LocalDateTime endDate) {
        log.debug("获取支出分类统计: {} - {}", startDate, endDate);
        return financialTransactionRepository.sumByCategoryAndTypeAndDateRange(
            FinancialTransaction.TransactionType.EXPENSE, startDate, endDate);
    }
    
    /**
     * 获取月度财务统计
     */
    @Transactional(readOnly = true)
    public List<Object[]> getMonthlyFinancialStats(int year) {
        log.debug("获取月度财务统计: year={}", year);
        return financialTransactionRepository.getMonthlyStats(year);
    }
    
    /**
     * 获取支付方式统计
     */
    @Transactional(readOnly = true)
    public List<Object[]> getPaymentMethodStats(LocalDateTime startDate, LocalDateTime endDate) {
        log.debug("获取支付方式统计: {} - {}", startDate, endDate);
        return financialTransactionRepository.getPaymentMethodStats(startDate, endDate);
    }
    
    /**
     * 获取最近的交易
     */
    @Transactional(readOnly = true)
    public List<FinancialTransaction> getRecentTransactions(int limit) {
        log.debug("获取最近的交易: limit={}", limit);
        return financialTransactionRepository.findRecentTransactions(limit);
    }
}
