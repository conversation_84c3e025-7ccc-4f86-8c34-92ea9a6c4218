package com.cattery.service;

import com.cattery.exception.BusinessException;
import com.cattery.util.ValidationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * 文件存储服务
 */
@Slf4j
@Service
public class FileStorageService {

    @Value("${file.storage.upload-dir:uploads}")
    private String uploadDir;

    @Value("${file.storage.max-file-size:10485760}") // 10MB
    private long maxFileSize;

    /**
     * 初始化存储目录
     */
    public void initializeStorageDirectories() {
        try {
            // 创建主要目录
            Files.createDirectories(getUploadPath());
            Files.createDirectories(getCatMediaPath());
            Files.createDirectories(getThumbnailPath());
            Files.createDirectories(getTempPath());
            Files.createDirectories(getDocumentPath());
            
            log.info("文件存储目录初始化完成: {}", uploadDir);
        } catch (IOException e) {
            log.error("无法创建文件存储目录", e);
            throw new BusinessException("文件存储初始化失败");
        }
    }

    /**
     * 存储文件
     */
    public String storeFile(MultipartFile file, String category) {
        // 验证文件
        ValidationUtils.validateFileNotEmpty(file);
        ValidationUtils.validateFileSize(file, maxFileSize);

        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            throw new BusinessException("文件名不能为空");
        }
        originalFilename = StringUtils.cleanPath(originalFilename);
        ValidationUtils.validateFilename(originalFilename);

        try {
            // 生成唯一文件名
            String filename = generateUniqueFilename(originalFilename);
            
            // 根据分类确定存储路径
            Path targetLocation = getStoragePath(category).resolve(filename);
            
            // 确保目录存在
            Files.createDirectories(targetLocation.getParent());
            
            // 复制文件
            Files.copy(file.getInputStream(), targetLocation, StandardCopyOption.REPLACE_EXISTING);
            
            log.info("文件存储成功: {}", filename);
            return filename;
            
        } catch (IOException e) {
            log.error("文件存储失败: {}", originalFilename, e);
            throw new BusinessException("文件存储失败: " + e.getMessage());
        }
    }

    /**
     * 存储猫咪照片
     */
    public String storeCatPhoto(MultipartFile file) {
        ValidationUtils.validateImageFile(file);
        return storeFile(file, "cat-media");
    }

    /**
     * 存储文档文件
     */
    public String storeDocument(MultipartFile file) {
        ValidationUtils.validateDocumentFile(file);
        return storeFile(file, "documents");
    }

    /**
     * 加载文件作为资源
     */
    public Resource loadFileAsResource(String filename, String category) {
        try {
            Path filePath = getStoragePath(category).resolve(filename).normalize();
            Resource resource = new UrlResource(filePath.toUri());
            
            if (resource.exists() && resource.isReadable()) {
                return resource;
            } else {
                throw new BusinessException("文件不存在或不可读: " + filename);
            }
        } catch (MalformedURLException e) {
            log.error("文件路径格式错误: {}", filename, e);
            throw new BusinessException("文件路径格式错误");
        }
    }

    /**
     * 删除文件
     */
    public void deleteFile(String filename, String category) {
        try {
            Path filePath = getStoragePath(category).resolve(filename).normalize();
            Files.deleteIfExists(filePath);
            log.info("文件删除成功: {}", filename);
        } catch (IOException e) {
            log.error("文件删除失败: {}", filename, e);
            throw new BusinessException("文件删除失败");
        }
    }

    /**
     * 检查文件是否存在
     */
    public boolean fileExists(String filename, String category) {
        Path filePath = getStoragePath(category).resolve(filename).normalize();
        return Files.exists(filePath);
    }

    /**
     * 获取文件大小
     */
    public long getFileSize(String filename, String category) {
        try {
            Path filePath = getStoragePath(category).resolve(filename).normalize();
            return Files.size(filePath);
        } catch (IOException e) {
            log.error("获取文件大小失败: {}", filename, e);
            return 0;
        }
    }

    /**
     * 移动文件
     */
    public String moveFile(String filename, String fromCategory, String toCategory) {
        try {
            Path sourcePath = getStoragePath(fromCategory).resolve(filename);
            Path targetPath = getStoragePath(toCategory).resolve(filename);
            
            // 确保目标目录存在
            Files.createDirectories(targetPath.getParent());
            
            Files.move(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
            
            log.info("文件移动成功: {} -> {}", fromCategory, toCategory);
            return filename;
            
        } catch (IOException e) {
            log.error("文件移动失败: {}", filename, e);
            throw new BusinessException("文件移动失败");
        }
    }

    /**
     * 复制文件
     */
    public String copyFile(String filename, String fromCategory, String toCategory, String newFilename) {
        try {
            Path sourcePath = getStoragePath(fromCategory).resolve(filename);
            Path targetPath = getStoragePath(toCategory).resolve(newFilename);
            
            // 确保目标目录存在
            Files.createDirectories(targetPath.getParent());
            
            Files.copy(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
            
            log.info("文件复制成功: {} -> {}", filename, newFilename);
            return newFilename;
            
        } catch (IOException e) {
            log.error("文件复制失败: {}", filename, e);
            throw new BusinessException("文件复制失败");
        }
    }

    // 私有辅助方法

    private Path getUploadPath() {
        return Paths.get(uploadDir);
    }

    private Path getCatMediaPath() {
        return getUploadPath().resolve("cat-media");
    }

    private Path getThumbnailPath() {
        return getUploadPath().resolve("thumbnails");
    }

    private Path getTempPath() {
        return getUploadPath().resolve("temp");
    }

    private Path getDocumentPath() {
        return getUploadPath().resolve("documents");
    }

    private Path getStoragePath(String category) {
        switch (category.toLowerCase()) {
            case "cat-media":
                return getCatMediaPath();
            case "thumbnails":
                return getThumbnailPath();
            case "temp":
                return getTempPath();
            case "documents":
                return getDocumentPath();
            default:
                return getUploadPath().resolve(category);
        }
    }

    private String generateUniqueFilename(String originalFilename) {
        String extension = ValidationUtils.getFileExtension(originalFilename);
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String uuid = UUID.randomUUID().toString().substring(0, 8);
        
        return String.format("%s_%s.%s", timestamp, uuid, extension);
    }
}
