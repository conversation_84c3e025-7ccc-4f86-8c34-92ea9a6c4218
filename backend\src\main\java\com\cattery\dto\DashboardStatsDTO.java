package com.cattery.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 仪表盘统计数据DTO
 */
@Data
public class DashboardStatsDTO {

    /**
     * 猫咪统计
     */
    private CatStats catStats;

    /**
     * 客户统计
     */
    private CustomerStats customerStats;

    /**
     * 财务统计
     */
    private FinanceStats financeStats;

    /**
     * 健康统计
     */
    private HealthStats healthStats;

    /**
     * 繁育统计
     */
    private BreedingStats breedingStats;

    /**
     * 最近活动
     */
    private List<RecentActivity> recentActivities;

    /**
     * 猫咪统计
     */
    @Data
    public static class CatStats {
        private Long totalCats;
        private Long availableCats;
        private Long soldCats;
        private Long breedingCats;
        private Long retiredCats;
        private Map<String, Long> catsByBreed;
        private Map<String, Long> catsByStatus;
    }

    /**
     * 客户统计
     */
    @Data
    public static class CustomerStats {
        private Long totalCustomers;
        private Long activeCustomers;
        private Long potentialCustomers;
        private Long vipCustomers;
        private Long newCustomersThisMonth;
        private Map<String, Long> customersByType;
    }

    /**
     * 财务统计
     */
    @Data
    public static class FinanceStats {
        private BigDecimal totalIncome;
        private BigDecimal totalExpense;
        private BigDecimal netProfit;
        private BigDecimal monthlyIncome;
        private BigDecimal monthlyExpense;
        private BigDecimal monthlyProfit;
        private List<MonthlyTrend> monthlyTrends;
        private Map<String, BigDecimal> incomeByCategory;
        private Map<String, BigDecimal> expenseByCategory;
    }

    /**
     * 健康统计
     */
    @Data
    public static class HealthStats {
        private Long totalHealthRecords;
        private Long vaccinationRecords;
        private Long checkupRecords;
        private Long treatmentRecords;
        private Long upcomingVaccinations;
        private Long overdueVaccinations;
        private Map<String, Long> recordsByType;
    }

    /**
     * 繁育统计
     */
    @Data
    public static class BreedingStats {
        private Long totalBreedingRecords;
        private Long activeBreeding;
        private Long pregnantCats;
        private Long newbornThisMonth;
        private BigDecimal successRate;
        private Long upcomingBirths;
        private Map<String, Long> breedingByStatus;
    }

    /**
     * 月度趋势
     */
    @Data
    public static class MonthlyTrend {
        private String month;
        private BigDecimal income;
        private BigDecimal expense;
        private BigDecimal profit;
    }

    /**
     * 最近活动
     */
    @Data
    public static class RecentActivity {
        private String type;
        private String title;
        private String description;
        private String timestamp;
        private String icon;
        private String color;
        private Map<String, Object> metadata;
    }

    /**
     * 提醒信息
     */
    @Data
    public static class Reminder {
        private String type;
        private String title;
        private String message;
        private String dueDate;
        private String priority;
        private Long relatedId;
        private String relatedType;
    }

    /**
     * 快速统计
     */
    @Data
    public static class QuickStats {
        private String label;
        private String value;
        private String trend;
        private String trendType; // up, down, flat
        private String icon;
        private String color;
    }
}
