# Node.js 环境修复指南

## 🚨 问题诊断

根据错误信息分析，您的Node.js环境存在以下问题：

```
Error: Cannot find module 'D:\软件\node_modules\npm\bin\npm-prefix.js'
```

**问题原因：**
1. Node.js安装路径异常（安装在`D:\软件\`目录）
2. npm核心文件缺失或损坏
3. 环境变量配置可能有问题

## 🛠️ 解决方案

### 方案1: 完全重新安装Node.js（推荐）

#### 步骤1: 卸载当前Node.js
1. 打开"控制面板" → "程序和功能"
2. 找到并卸载所有Node.js相关程序
3. 删除以下目录（如果存在）：
   - `C:\Program Files\nodejs\`
   - `C:\Program Files (x86)\nodejs\`
   - `D:\软件\node_modules\`（您当前的安装路径）

#### 步骤2: 清理环境变量
1. 右键"此电脑" → "属性" → "高级系统设置" → "环境变量"
2. 在"系统变量"中找到"Path"，编辑并删除所有Node.js相关路径
3. 删除`NODE_PATH`变量（如果存在）

#### 步骤3: 下载并安装新版本
1. 访问 [Node.js官网](https://nodejs.org/)
2. 下载LTS版本（推荐v18.x或v20.x）
3. 安装到默认路径：`C:\Program Files\nodejs\`
4. 安装时勾选"Add to PATH"选项

#### 步骤4: 验证安装
打开新的命令行窗口，执行：
```bash
node --version
npm --version
```

### 方案2: 修复当前安装

如果不想重新安装，可以尝试修复：

#### 步骤1: 以管理员身份运行PowerShell
```powershell
# 清理npm缓存
npm cache clean --force

# 重新安装npm
npm install -g npm@latest

# 如果上述命令失败，尝试手动下载npm
```

#### 步骤2: 手动修复npm
如果npm完全无法使用：
1. 下载最新的npm包：https://github.com/npm/cli/releases
2. 解压到Node.js安装目录
3. 重新配置环境变量

### 方案3: 使用替代方案

#### 使用Yarn替代npm
```bash
# 如果npm无法修复，可以安装yarn
# 下载yarn安装包：https://yarnpkg.com/getting-started/install
```

#### 使用pnpm替代npm
```bash
# 使用PowerShell安装pnpm
iwr https://get.pnpm.io/install.ps1 -useb | iex
```

## 🔧 项目启动指南

### Node.js修复后的启动步骤

1. **进入前端目录**
   ```bash
   cd frontend
   ```

2. **安装依赖**
   ```bash
   npm install
   # 或者使用yarn
   yarn install
   ```

3. **启动开发服务器**
   ```bash
   npm run dev
   # 或者使用yarn
   yarn dev
   ```

4. **访问应用**
   - 前端开发服务器：http://localhost:3000
   - 后端API服务：http://localhost:8080

### 临时解决方案（无需Node.js）

如果暂时无法修复Node.js，可以：

1. **使用简化启动脚本**
   ```bash
   # 运行简化启动脚本
   start-simple.bat
   ```

2. **直接打开HTML文件**
   - 双击`login-test.html`开始使用
   - 确保后端服务已启动

## 📋 常见问题解决

### 问题1: 权限错误
**解决方案：**
- 以管理员身份运行命令行
- 或者配置npm使用用户目录：
  ```bash
  npm config set prefix %APPDATA%\npm
  ```

### 问题2: 网络问题
**解决方案：**
```bash
# 设置国内镜像源
npm config set registry https://registry.npmmirror.com/

# 或者使用淘宝镜像
npm config set registry https://registry.npm.taobao.org/
```

### 问题3: 端口冲突
**解决方案：**
```bash
# 检查端口占用
netstat -ano | findstr :3000
netstat -ano | findstr :8080

# 修改端口配置
# 编辑 vite.config.ts 中的 server.port
```

### 问题4: 依赖安装失败
**解决方案：**
```bash
# 删除node_modules和package-lock.json
rmdir /s node_modules
del package-lock.json

# 重新安装
npm install
```

## 🎯 推荐配置

### 推荐的Node.js版本
- **Node.js**: v18.x LTS 或 v20.x LTS
- **npm**: 最新版本（通常随Node.js一起安装）

### 推荐的开发工具
- **VS Code**: 最佳的前端开发IDE
- **Git**: 版本控制工具
- **Chrome DevTools**: 调试工具

## 📞 技术支持

如果按照上述步骤仍无法解决问题，请：

1. 运行`fix-nodejs.bat`脚本获取详细诊断信息
2. 检查系统环境变量配置
3. 确认防火墙和杀毒软件设置
4. 考虑使用Docker容器化部署

## 🚀 成功标志

修复成功后，您应该能够：
- ✅ 正常执行`node --version`和`npm --version`
- ✅ 在frontend目录成功运行`npm install`
- ✅ 启动开发服务器`npm run dev`
- ✅ 访问http://localhost:3000看到Vue应用
- ✅ 前端能够正常调用后端API

---

**注意：** 如果您不熟悉命令行操作，建议寻求技术人员协助，或者使用提供的简化启动脚本临时解决问题。
