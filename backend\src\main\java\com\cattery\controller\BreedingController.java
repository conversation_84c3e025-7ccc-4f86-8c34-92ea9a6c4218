package com.cattery.controller;

import com.cattery.dto.ApiResponse;
import com.cattery.entity.Cat;
import com.cattery.entity.Mating;
import com.cattery.entity.Pregnancy;
import com.cattery.service.BreedingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 繁育管理控制器
 */
@RestController
@RequestMapping("/api/breeding")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "繁育管理", description = "配种记录、怀孕跟踪和繁育统计")
public class BreedingController {
    
    private final BreedingService breedingService;
    
    /**
     * 获取繁育统计
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取繁育统计", description = "获取繁育相关的统计数据")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getBreedingStatistics(
            @Parameter(description = "开始日期") 
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @Parameter(description = "结束日期") 
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        
        log.info("获取繁育统计: startDate={}, endDate={}", startDate, endDate);
        
        // 默认时间范围：当年
        if (startDate == null) {
            startDate = LocalDateTime.now().withDayOfYear(1).withHour(0).withMinute(0).withSecond(0);
        }
        if (endDate == null) {
            endDate = LocalDateTime.now();
        }
        
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalMatings", breedingService.getMatingCount(startDate, endDate));
        statistics.put("totalDeliveries", breedingService.getDeliveryCount(startDate, endDate));
        statistics.put("currentPregnancies", breedingService.getCurrentPregnancies().size());
        statistics.put("upcomingDeliveries", breedingService.getUpcomingDeliveries(30).size()); // 30天内
        
        return ResponseEntity.ok(ApiResponse.success("获取繁育统计成功", statistics));
    }
    
    /**
     * 获取所有配种记录
     */
    @GetMapping("/matings")
    @Operation(summary = "获取配种记录列表", description = "分页获取配种记录列表")
    public ResponseEntity<ApiResponse<Page<Mating>>> getAllMatings(
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        
        log.info("获取配种记录列表: page={}, size={}", page, size);
        
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "matingDate"));
        Page<Mating> matings = breedingService.getAllMatings(pageable);
        
        return ResponseEntity.ok(ApiResponse.success("获取配种记录列表成功", matings));
    }
    
    /**
     * 记录配种信息
     */
    @PostMapping("/matings")
    @Operation(summary = "记录配种信息", description = "创建新的配种记录")
    public ResponseEntity<ApiResponse<Mating>> recordMating(
            @Parameter(description = "配种信息") @Valid @RequestBody Mating mating) {

        log.info("记录配种信息: femaleCatId={}, maleCatId={}",
            mating.getFemaleParent().getId(), mating.getMaleParent().getId());

        Mating recordedMating = breedingService.recordMating(mating);
        return ResponseEntity.ok(ApiResponse.success("记录配种信息成功", recordedMating));
    }
    
    /**
     * 根据母猫获取配种记录
     */
    @GetMapping("/matings/by-female/{femaleCatId}")
    @Operation(summary = "根据母猫获取配种记录", description = "获取指定母猫的所有配种记录")
    public ResponseEntity<ApiResponse<List<Mating>>> getMatingsByFemaleCat(
            @Parameter(description = "母猫ID") @PathVariable Long femaleCatId) {
        
        log.info("根据母猫获取配种记录: femaleCatId={}", femaleCatId);
        
        List<Mating> matings = breedingService.getMatingsByFemaleCat(femaleCatId);
        return ResponseEntity.ok(ApiResponse.success("获取母猫配种记录成功", matings));
    }
    
    /**
     * 根据公猫获取配种记录
     */
    @GetMapping("/matings/by-male/{maleCatId}")
    @Operation(summary = "根据公猫获取配种记录", description = "获取指定公猫的所有配种记录")
    public ResponseEntity<ApiResponse<List<Mating>>> getMatingsByMaleCat(
            @Parameter(description = "公猫ID") @PathVariable Long maleCatId) {
        
        log.info("根据公猫获取配种记录: maleCatId={}", maleCatId);
        
        List<Mating> matings = breedingService.getMatingsByMaleCat(maleCatId);
        return ResponseEntity.ok(ApiResponse.success("获取公猫配种记录成功", matings));
    }
    
    /**
     * 获取所有怀孕记录
     */
    @GetMapping("/pregnancies")
    @Operation(summary = "获取怀孕记录列表", description = "分页获取怀孕记录列表")
    public ResponseEntity<ApiResponse<Page<Pregnancy>>> getAllPregnancies(
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "怀孕状态") @RequestParam(required = false) Pregnancy.Status status) {

        log.info("获取怀孕记录列表: page={}, size={}, status={}", page, size, status);

        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "expectedBirthDate"));
        Page<Pregnancy> pregnancies;

        if (status != null) {
            // 这里需要在服务中添加按状态分页查询的方法
            pregnancies = breedingService.getAllPregnancies(pageable);
        } else {
            pregnancies = breedingService.getAllPregnancies(pageable);
        }

        return ResponseEntity.ok(ApiResponse.success("获取怀孕记录列表成功", pregnancies));
    }
    
    /**
     * 记录怀孕信息
     */
    @PostMapping("/pregnancies")
    @Operation(summary = "记录怀孕信息", description = "创建新的怀孕记录")
    public ResponseEntity<ApiResponse<Pregnancy>> recordPregnancy(
            @Parameter(description = "怀孕信息") @Valid @RequestBody Pregnancy pregnancy) {

        log.info("记录怀孕信息: motherCatId={}", pregnancy.getMother().getId());

        Pregnancy recordedPregnancy = breedingService.recordPregnancy(pregnancy);
        return ResponseEntity.ok(ApiResponse.success("记录怀孕信息成功", recordedPregnancy));
    }
    
    /**
     * 更新怀孕状态
     */
    @PatchMapping("/pregnancies/{id}/status")
    @Operation(summary = "更新怀孕状态", description = "更新怀孕记录的状态")
    public ResponseEntity<ApiResponse<Pregnancy>> updatePregnancyStatus(
            @Parameter(description = "怀孕记录ID") @PathVariable Long id,
            @Parameter(description = "新状态") @RequestParam Pregnancy.Status status) {

        log.info("更新怀孕状态: id={}, status={}", id, status);

        Pregnancy updatedPregnancy = breedingService.updatePregnancyStatus(id, status);
        return ResponseEntity.ok(ApiResponse.success("更新怀孕状态成功", updatedPregnancy));
    }
    
    /**
     * 获取当前怀孕的猫咪
     */
    @GetMapping("/pregnancies/current")
    @Operation(summary = "获取当前怀孕的猫咪", description = "获取所有当前怀孕状态的猫咪")
    public ResponseEntity<ApiResponse<List<Pregnancy>>> getCurrentPregnancies() {
        log.info("获取当前怀孕的猫咪");
        
        List<Pregnancy> currentPregnancies = breedingService.getCurrentPregnancies();
        return ResponseEntity.ok(ApiResponse.success("获取当前怀孕猫咪成功", currentPregnancies));
    }
    
    /**
     * 获取即将分娩的猫咪
     */
    @GetMapping("/pregnancies/upcoming-deliveries")
    @Operation(summary = "获取即将分娩的猫咪", description = "获取即将分娩的猫咪列表")
    public ResponseEntity<ApiResponse<List<Pregnancy>>> getUpcomingDeliveries(
            @Parameter(description = "提前天数") @RequestParam(defaultValue = "30") int daysAhead) {
        
        log.info("获取即将分娩的猫咪: daysAhead={}", daysAhead);
        
        List<Pregnancy> upcomingDeliveries = breedingService.getUpcomingDeliveries(daysAhead);
        return ResponseEntity.ok(ApiResponse.success("获取即将分娩猫咪成功", upcomingDeliveries));
    }
    
    /**
     * 获取可繁育的母猫
     */
    @GetMapping("/breedable-females")
    @Operation(summary = "获取可繁育的母猫", description = "获取所有可用于繁育的母猫")
    public ResponseEntity<ApiResponse<List<Cat>>> getBreedableFemaleCats() {
        log.info("获取可繁育的母猫");
        
        List<Cat> breedableFemales = breedingService.getBreedableFemaleCats();
        return ResponseEntity.ok(ApiResponse.success("获取可繁育母猫成功", breedableFemales));
    }
    
    /**
     * 获取可繁育的公猫
     */
    @GetMapping("/breedable-males")
    @Operation(summary = "获取可繁育的公猫", description = "获取所有可用于繁育的公猫")
    public ResponseEntity<ApiResponse<List<Cat>>> getBreedableMaleCats() {
        log.info("获取可繁育的公猫");
        
        List<Cat> breedableMales = breedingService.getBreedableMaleCats();
        return ResponseEntity.ok(ApiResponse.success("获取可繁育公猫成功", breedableMales));
    }
}
