package com.catshelter.managementsystem.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 文件存储配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "file.storage")
public class FileStorageConfig {

    private String uploadDir = "uploads";
    private String catMediaDir = "cat-media";
    private String thumbnailDir = "thumbnails";
    private String tempDir = "temp";
    private long maxFileSize = 10485760L; // 10MB
    private long maxRequestSize = 52428800L; // 50MB
    private int thumbnailWidth = 200;
    private int thumbnailHeight = 200;
    private int previewWidth = 800;
    private int previewHeight = 600;
    private String[] allowedImageTypes = {"image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"};
    private String[] allowedVideoTypes = {"video/mp4", "video/avi", "video/mov", "video/wmv", "video/flv"};
    private String[] allowedDocumentTypes = {"application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"};

    public String getFullUploadPath() {
        return System.getProperty("user.dir") + "/" + uploadDir;
    }

    public String getFullCatMediaPath() {
        return getFullUploadPath() + "/" + catMediaDir;
    }

    public String getFullThumbnailPath() {
        return getFullUploadPath() + "/" + thumbnailDir;
    }

    public String getFullTempPath() {
        return getFullUploadPath() + "/" + tempDir;
    }
}