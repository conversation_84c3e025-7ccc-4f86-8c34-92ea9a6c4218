import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import 'dayjs/locale/zh-cn'

// 扩展dayjs插件
dayjs.extend(relativeTime)
dayjs.extend(utc)
dayjs.extend(timezone)

// 设置默认语言
dayjs.locale('zh-cn')

/**
 * 日期工具函数
 */

/**
 * 格式化日期时间
 */
export function formatDateTime(
  date: string | Date | dayjs.Dayjs,
  format: string = 'YYYY-MM-DD HH:mm:ss'
): string {
  if (!date) return ''
  return dayjs(date).format(format)
}

/**
 * 格式化日期
 */
export function formatDate(
  date: string | Date | dayjs.Dayjs,
  format: string = 'YYYY-MM-DD'
): string {
  if (!date) return ''
  return dayjs(date).format(format)
}

/**
 * 格式化时间
 */
export function formatTime(
  date: string | Date | dayjs.Dayjs,
  format: string = 'HH:mm:ss'
): string {
  if (!date) return ''
  return dayjs(date).format(format)
}

/**
 * 相对时间
 */
export function fromNow(date: string | Date | dayjs.Dayjs): string {
  if (!date) return ''
  return dayjs(date).fromNow()
}

/**
 * 计算年龄
 */
export function calculateAge(birthDate: string | Date | dayjs.Dayjs): string {
  if (!birthDate) return ''
  
  const birth = dayjs(birthDate)
  const now = dayjs()
  
  const years = now.diff(birth, 'year')
  const months = now.diff(birth.add(years, 'year'), 'month')
  
  if (years === 0) {
    return `${months}个月`
  } else if (months === 0) {
    return `${years}岁`
  } else {
    return `${years}岁${months}个月`
  }
}

/**
 * 计算天数差
 */
export function daysDiff(
  date1: string | Date | dayjs.Dayjs,
  date2: string | Date | dayjs.Dayjs = dayjs()
): number {
  return dayjs(date2).diff(dayjs(date1), 'day')
}

/**
 * 是否是今天
 */
export function isToday(date: string | Date | dayjs.Dayjs): boolean {
  return dayjs(date).isSame(dayjs(), 'day')
}

/**
 * 是否是昨天
 */
export function isYesterday(date: string | Date | dayjs.Dayjs): boolean {
  return dayjs(date).isSame(dayjs().subtract(1, 'day'), 'day')
}

/**
 * 是否是本周
 */
export function isThisWeek(date: string | Date | dayjs.Dayjs): boolean {
  return dayjs(date).isSame(dayjs(), 'week')
}

/**
 * 是否是本月
 */
export function isThisMonth(date: string | Date | dayjs.Dayjs): boolean {
  return dayjs(date).isSame(dayjs(), 'month')
}

/**
 * 是否是本年
 */
export function isThisYear(date: string | Date | dayjs.Dayjs): boolean {
  return dayjs(date).isSame(dayjs(), 'year')
}

/**
 * 获取日期范围
 */
export function getDateRange(type: string): [string, string] {
  const now = dayjs()
  
  switch (type) {
    case 'today':
      return [now.format('YYYY-MM-DD'), now.format('YYYY-MM-DD')]
    
    case 'yesterday':
      const yesterday = now.subtract(1, 'day')
      return [yesterday.format('YYYY-MM-DD'), yesterday.format('YYYY-MM-DD')]
    
    case 'thisWeek':
      return [now.startOf('week').format('YYYY-MM-DD'), now.format('YYYY-MM-DD')]
    
    case 'lastWeek':
      const lastWeekStart = now.subtract(1, 'week').startOf('week')
      const lastWeekEnd = now.subtract(1, 'week').endOf('week')
      return [lastWeekStart.format('YYYY-MM-DD'), lastWeekEnd.format('YYYY-MM-DD')]
    
    case 'thisMonth':
      return [now.startOf('month').format('YYYY-MM-DD'), now.format('YYYY-MM-DD')]
    
    case 'lastMonth':
      const lastMonthStart = now.subtract(1, 'month').startOf('month')
      const lastMonthEnd = now.subtract(1, 'month').endOf('month')
      return [lastMonthStart.format('YYYY-MM-DD'), lastMonthEnd.format('YYYY-MM-DD')]
    
    case 'thisQuarter':
      return [now.startOf('quarter').format('YYYY-MM-DD'), now.format('YYYY-MM-DD')]
    
    case 'lastQuarter':
      const lastQuarterStart = now.subtract(1, 'quarter').startOf('quarter')
      const lastQuarterEnd = now.subtract(1, 'quarter').endOf('quarter')
      return [lastQuarterStart.format('YYYY-MM-DD'), lastQuarterEnd.format('YYYY-MM-DD')]
    
    case 'thisYear':
      return [now.startOf('year').format('YYYY-MM-DD'), now.format('YYYY-MM-DD')]
    
    case 'lastYear':
      const lastYearStart = now.subtract(1, 'year').startOf('year')
      const lastYearEnd = now.subtract(1, 'year').endOf('year')
      return [lastYearStart.format('YYYY-MM-DD'), lastYearEnd.format('YYYY-MM-DD')]
    
    case 'last7Days':
      return [now.subtract(6, 'day').format('YYYY-MM-DD'), now.format('YYYY-MM-DD')]
    
    case 'last30Days':
      return [now.subtract(29, 'day').format('YYYY-MM-DD'), now.format('YYYY-MM-DD')]
    
    case 'last90Days':
      return [now.subtract(89, 'day').format('YYYY-MM-DD'), now.format('YYYY-MM-DD')]
    
    default:
      return [now.format('YYYY-MM-DD'), now.format('YYYY-MM-DD')]
  }
}

/**
 * 智能格式化日期时间
 */
export function smartFormatDateTime(date: string | Date | dayjs.Dayjs): string {
  if (!date) return ''
  
  const target = dayjs(date)
  const now = dayjs()
  
  if (isToday(target)) {
    return `今天 ${target.format('HH:mm')}`
  } else if (isYesterday(target)) {
    return `昨天 ${target.format('HH:mm')}`
  } else if (isThisWeek(target)) {
    return target.format('dddd HH:mm')
  } else if (isThisYear(target)) {
    return target.format('MM-DD HH:mm')
  } else {
    return target.format('YYYY-MM-DD HH:mm')
  }
}

/**
 * 验证日期格式
 */
export function isValidDate(date: any): boolean {
  return dayjs(date).isValid()
}

/**
 * 获取时区
 */
export function getTimezone(): string {
  return dayjs.tz.guess()
}

/**
 * 转换时区
 */
export function convertTimezone(
  date: string | Date | dayjs.Dayjs,
  timezone: string
): dayjs.Dayjs {
  return dayjs(date).tz(timezone)
}

/**
 * 获取月份天数
 */
export function getDaysInMonth(date: string | Date | dayjs.Dayjs): number {
  return dayjs(date).daysInMonth()
}

/**
 * 获取周的开始和结束日期
 */
export function getWeekRange(date: string | Date | dayjs.Dayjs): [dayjs.Dayjs, dayjs.Dayjs] {
  const target = dayjs(date)
  return [target.startOf('week'), target.endOf('week')]
}

/**
 * 获取月的开始和结束日期
 */
export function getMonthRange(date: string | Date | dayjs.Dayjs): [dayjs.Dayjs, dayjs.Dayjs] {
  const target = dayjs(date)
  return [target.startOf('month'), target.endOf('month')]
}

/**
 * 生成日期数组
 */
export function generateDateArray(
  start: string | Date | dayjs.Dayjs,
  end: string | Date | dayjs.Dayjs,
  unit: 'day' | 'week' | 'month' = 'day'
): dayjs.Dayjs[] {
  const startDate = dayjs(start)
  const endDate = dayjs(end)
  const dates: dayjs.Dayjs[] = []
  
  let current = startDate
  while (current.isBefore(endDate) || current.isSame(endDate)) {
    dates.push(current)
    current = current.add(1, unit)
  }
  
  return dates
}
