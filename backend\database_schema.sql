-- 猫舍管理系统数据库初始化脚本
-- 用于Docker MySQL容器初始化

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS dengdeng CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE dengdeng;

-- 创建用户（如果不存在）
CREATE USER IF NOT EXISTS 'catuser'@'%' IDENTIFIED BY 'catpassword';
GRANT ALL PRIVILEGES ON dengdeng.* TO 'catuser'@'%';
FLUSH PRIVILEGES;

-- 设置时区
SET time_zone = '+08:00';

-- 创建基础表结构（Spring Boot JPA会自动创建，这里只是备用）
-- 实际表结构由JPA实体类自动生成

-- 插入初始数据（可选）
-- 这些数据会在应用启动时由DataInitializer自动创建
-- 这里只是作为备用方案

-- 创建管理员用户（密码：password）
-- INSERT INTO users (username, password, email, real_name, enabled, account_non_expired, account_non_locked, credentials_non_expired, created_at, updated_at)
-- VALUES ('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9P8gDKOKKqmsHw2', '<EMAIL>', '系统管理员', true, true, true, true, NOW(), NOW());

-- 创建普通用户（密码：password）  
-- INSERT INTO users (username, password, email, real_name, enabled, account_non_expired, account_non_locked, credentials_non_expired, created_at, updated_at)
-- VALUES ('user', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9P8gDKOKKqmsHw2', '<EMAIL>', '普通用户', true, true, true, true, NOW(), NOW());

-- 注意：实际的数据初始化由Spring Boot应用的DataInitializer类处理
-- 这个文件主要用于Docker环境下的数据库初始化
