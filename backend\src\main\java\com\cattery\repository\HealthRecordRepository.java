package com.cattery.repository;

import com.cattery.entity.HealthRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface HealthRecordRepository extends JpaRepository<HealthRecord, Long> {
    
    List<HealthRecord> findByCatIdOrderByRecordDateDesc(Long catId);
    
    List<HealthRecord> findByRecordType(HealthRecord.RecordType recordType);
    
    long countByCatId(Long catId);
}



