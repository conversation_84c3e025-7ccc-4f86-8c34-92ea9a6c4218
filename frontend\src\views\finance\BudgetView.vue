<template>
  <div class="budget-view">
    <div class="page-header">
      <h1>预算管理</h1>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        新增预算
      </el-button>
    </div>

    <div class="search-bar">
      <el-form :model="searchForm" inline>
        <el-form-item label="预算年份">
          <el-date-picker
            v-model="searchForm.year"
            type="year"
            placeholder="选择年份"
            clearable
          />
        </el-form-item>
        <el-form-item label="预算类型">
          <el-select v-model="searchForm.budgetType" placeholder="选择预算类型" clearable>
            <el-option label="收入预算" value="INCOME" />
            <el-option label="支出预算" value="EXPENSE" />
            <el-option label="投资预算" value="INVESTMENT" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
            <el-option label="草稿" value="DRAFT" />
            <el-option label="已批准" value="APPROVED" />
            <el-option label="执行中" value="ACTIVE" />
            <el-option label="已完成" value="COMPLETED" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchBudgets">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table :data="budgets" v-loading="loading">
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="budgetName" label="预算名称" />
      <el-table-column prop="budgetYear" label="预算年份" width="100" />
      <el-table-column label="预算类型" width="100">
        <template #default="{ row }">
          <el-tag :type="getBudgetTypeColor(row.budgetType)">
            {{ getBudgetTypeText(row.budgetType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="预算金额" width="120">
        <template #default="{ row }">
          <span class="amount">¥{{ row.budgetAmount.toLocaleString() }}</span>
        </template>
      </el-table-column>
      <el-table-column label="已使用金额" width="120">
        <template #default="{ row }">
          <span class="amount">¥{{ row.usedAmount.toLocaleString() }}</span>
        </template>
      </el-table-column>
      <el-table-column label="使用率" width="100">
        <template #default="{ row }">
          <el-progress 
            :percentage="Math.round((row.usedAmount / row.budgetAmount) * 100)"
            :color="getProgressColor(row.usedAmount / row.budgetAmount)"
          />
        </template>
      </el-table-column>
      <el-table-column label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusColor(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button size="small" @click="viewBudget(row)">查看</el-button>
          <el-button size="small" type="primary" @click="editBudget(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="deleteBudget(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingBudget ? '编辑预算' : '新增预算'"
      width="600px"
    >
      <el-form :model="budgetForm" :rules="formRules" ref="formRef" label-width="100px">
        <el-form-item label="预算名称" prop="budgetName">
          <el-input v-model="budgetForm.budgetName" placeholder="输入预算名称" />
        </el-form-item>
        <el-form-item label="预算年份" prop="budgetYear">
          <el-date-picker
            v-model="budgetForm.budgetYear"
            type="year"
            placeholder="选择预算年份"
          />
        </el-form-item>
        <el-form-item label="预算类型" prop="budgetType">
          <el-select v-model="budgetForm.budgetType" placeholder="选择预算类型">
            <el-option label="收入预算" value="INCOME" />
            <el-option label="支出预算" value="EXPENSE" />
            <el-option label="投资预算" value="INVESTMENT" />
          </el-select>
        </el-form-item>
        <el-form-item label="预算金额" prop="budgetAmount">
          <el-input-number 
            v-model="budgetForm.budgetAmount" 
            :min="0" 
            :precision="2"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="budgetForm.status" placeholder="选择状态">
            <el-option label="草稿" value="DRAFT" />
            <el-option label="已批准" value="APPROVED" />
            <el-option label="执行中" value="ACTIVE" />
            <el-option label="已完成" value="COMPLETED" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="budgetForm.description"
            type="textarea"
            :rows="3"
            placeholder="输入预算描述"
          />
        </el-form-item>
        <el-form-item label="备注" prop="notes">
          <el-input
            v-model="budgetForm.notes"
            type="textarea"
            :rows="3"
            placeholder="输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveBudget">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const showCreateDialog = ref(false)
const editingBudget = ref(null)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

const budgets = ref([])

const searchForm = reactive({
  year: '',
  budgetType: '',
  status: ''
})

const budgetForm = reactive({
  budgetName: '',
  budgetYear: '',
  budgetType: '',
  budgetAmount: 0,
  status: 'DRAFT',
  description: '',
  notes: ''
})

const formRules = {
  budgetName: [{ required: true, message: '请输入预算名称', trigger: 'blur' }],
  budgetYear: [{ required: true, message: '请选择预算年份', trigger: 'change' }],
  budgetType: [{ required: true, message: '请选择预算类型', trigger: 'change' }],
  budgetAmount: [{ required: true, message: '请输入预算金额', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

// 方法
const loadBudgets = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    budgets.value = [
      {
        id: 1,
        budgetName: '2024年度运营预算',
        budgetYear: 2024,
        budgetType: 'EXPENSE',
        budgetAmount: 500000,
        usedAmount: 320000,
        status: 'ACTIVE',
        description: '2024年度猫舍运营支出预算',
        notes: '包括饲料、医疗、人工等费用'
      },
      {
        id: 2,
        budgetName: '2024年度收入预算',
        budgetYear: 2024,
        budgetType: 'INCOME',
        budgetAmount: 800000,
        usedAmount: 450000,
        status: 'ACTIVE',
        description: '2024年度猫咪销售收入预算',
        notes: '预计销售50只猫咪'
      }
    ]
    total.value = 2
  } catch (error) {
    ElMessage.error('加载预算数据失败')
  } finally {
    loading.value = false
  }
}

const searchBudgets = () => {
  currentPage.value = 1
  loadBudgets()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    year: '',
    budgetType: '',
    status: ''
  })
  searchBudgets()
}

const viewBudget = (budget: any) => {
  ElMessage.info('查看预算详情功能开发中')
}

const editBudget = (budget: any) => {
  editingBudget.value = budget
  Object.assign(budgetForm, budget)
  showCreateDialog.value = true
}

const deleteBudget = async (budget: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这个预算吗？', '确认删除', {
      type: 'warning'
    })
    ElMessage.success('删除成功')
    loadBudgets()
  } catch {
    // 用户取消删除
  }
}

const saveBudget = () => {
  ElMessage.success('保存成功')
  showCreateDialog.value = false
  loadBudgets()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  loadBudgets()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadBudgets()
}

const getBudgetTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    INCOME: 'success',
    EXPENSE: 'warning',
    INVESTMENT: 'primary'
  }
  return colors[type] || ''
}

const getBudgetTypeText = (type: string) => {
  const texts: Record<string, string> = {
    INCOME: '收入预算',
    EXPENSE: '支出预算',
    INVESTMENT: '投资预算'
  }
  return texts[type] || type
}

const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    DRAFT: 'info',
    APPROVED: 'success',
    ACTIVE: 'primary',
    COMPLETED: ''
  }
  return colors[status] || ''
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    DRAFT: '草稿',
    APPROVED: '已批准',
    ACTIVE: '执行中',
    COMPLETED: '已完成'
  }
  return texts[status] || status
}

const getProgressColor = (percentage: number) => {
  if (percentage < 0.5) return '#67c23a'
  if (percentage < 0.8) return '#e6a23c'
  return '#f56c6c'
}

onMounted(() => {
  loadBudgets()
})
</script>

<style scoped>
.budget-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-bar {
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 4px;
}

.amount {
  font-weight: bold;
  color: #333;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
