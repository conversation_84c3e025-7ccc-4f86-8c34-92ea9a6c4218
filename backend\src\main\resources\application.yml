# 猫舍管理系统配置文件
spring:
  application:
    name: cattery-management-system
  
  # 数据源配置
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  # H2数据库控制台配置
  h2:
    console:
      enabled: true
      path: /h2-console
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.H2Dialect
  
  # 文件上传配置
  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 50MB
      file-size-threshold: 2KB

# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /
    encoding:
      charset: UTF-8
      enabled: true
      force: true

# 日志配置
logging:
  level:
    root: INFO
    com.catshelter: DEBUG
    org.springframework.web: INFO
    org.hibernate: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 文件存储配置
file:
  storage:
    upload-dir: uploads
    cat-media-dir: cat-media
    thumbnail-dir: thumbnails
    temp-dir: temp
    max-file-size: 10485760 # 10MB
    max-request-size: 52428800 # 50MB
    thumbnail-width: 200
    thumbnail-height: 200
    preview-width: 800
    preview-height: 600
    allowed-image-types: image/jpeg,image/jpg,image/png,image/gif,image/webp
    allowed-video-types: video/mp4,video/avi,video/mov,video/wmv,video/flv
    allowed-document-types: application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

# JWT配置
jwt:
  secret: mySecretKey123456789012345678901234567890123456789012345678901234567890
  expiration: 86400000 # 24小时 (毫秒)

# OpenAPI文档配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    enabled: true