package com.cattery.controller;

import com.cattery.entity.Cat;
import com.cattery.service.CatService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/cats")
@RequiredArgsConstructor
public class CatController {
    
    private final CatService catService;
    
    @GetMapping
    public ResponseEntity<Map<String, Object>> getAllCats(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String keyword) {
        
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Cat> catPage;
            
            if (keyword != null && !keyword.trim().isEmpty()) {
                List<Cat> cats = catService.searchByKeyword(keyword.trim());
                catPage = catService.findAll(pageable); // 简化处理，实际应该支持分页搜索
            } else {
                catPage = catService.findAll(pageable);
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "获取成功");
            response.put("data", Map.of(
                "content", catPage.getContent(),
                "totalElements", catPage.getTotalElements(),
                "totalPages", catPage.getTotalPages(),
                "size", catPage.getSize(),
                "number", catPage.getNumber()
            ));
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            response.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getCatById(@PathVariable Long id) {
        try {
            Cat cat = catService.findById(id)
                    .orElseThrow(() -> new RuntimeException("猫咪不存在"));
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "获取成功");
            response.put("data", cat);
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            response.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getStatistics() {
        try {
            Map<String, Object> stats = new HashMap<>();
            stats.put("total", catService.findAll().size());
            stats.put("available", catService.countByStatus(Cat.Status.AVAILABLE));
            stats.put("sold", catService.countByStatus(Cat.Status.SOLD));
            stats.put("breeding", catService.countByStatus(Cat.Status.BREEDING));
            stats.put("breedStats", catService.getBreedStatistics());
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "获取统计信息成功");
            response.put("data", stats);
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取统计信息失败: " + e.getMessage());
            response.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.internalServerError().body(response);
        }
    }
}

