<template>
  <div class="breeding-list">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>繁育管理</h1>
      <p>猫咪繁育计划与记录管理</p>
    </div>

    <!-- 繁育统计卡片 -->
    <el-row :gutter="20" class="breeding-stats">
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card breeding">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon><Management /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ breedingStats.breedingCats || 0 }}</div>
              <div class="stat-label">繁育中</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card pregnant">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon><Female /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ breedingStats.pregnantCats || 0 }}</div>
              <div class="stat-label">怀孕中</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card kittens">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon><House /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ breedingStats.newbornKittens || 0 }}</div>
              <div class="stat-label">新生幼猫</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card plans">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ breedingStats.activePlans || 0 }}</div>
              <div class="stat-label">活跃计划</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索和操作栏 -->
    <el-card class="search-card">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索猫咪名称或编号"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
          <el-select
            v-model="searchForm.status"
            placeholder="繁育状态"
            clearable
            @change="handleSearch"
          >
            <el-option label="全部" value="" />
            <el-option label="可繁育" value="AVAILABLE" />
            <el-option label="繁育中" value="BREEDING" />
            <el-option label="怀孕中" value="PREGNANT" />
            <el-option label="哺乳期" value="NURSING" />
            <el-option label="休息期" value="RESTING" />
          </el-select>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
          <el-select
            v-model="searchForm.gender"
            placeholder="性别"
            clearable
            @change="handleSearch"
          >
            <el-option label="全部" value="" />
            <el-option label="公猫" value="MALE" />
            <el-option label="母猫" value="FEMALE" />
          </el-select>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
          <div class="action-buttons">
            <el-button type="primary" @click="showAddDialog = true">
              <el-icon><Plus /></el-icon>
              新增繁育记录
            </el-button>
            <el-button @click="resetSearch">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 繁育列表 -->
    <el-card class="list-card">
      <el-table
        v-loading="loading"
        :data="breedingList"
        stripe
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="catName" label="猫咪名称" min-width="120">
          <template #default="{ row }">
            <div class="cat-info">
              <el-avatar :size="40" :src="row.catAvatar">
                {{ row.catName?.charAt(0) }}
              </el-avatar>
              <div class="cat-details">
                <div class="cat-name">{{ row.catName }}</div>
                <div class="cat-code">{{ row.catCode }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="breed" label="品种" width="100" />
        
        <el-table-column prop="gender" label="性别" width="80">
          <template #default="{ row }">
            <el-tag :type="row.gender === 'MALE' ? 'primary' : 'danger'" size="small">
              {{ row.gender === 'MALE' ? '公' : '母' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="age" label="年龄" width="80">
          <template #default="{ row }">
            {{ calculateAge(row.birthDate) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="breedingStatus" label="繁育状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.breedingStatus)" size="small">
              {{ getStatusText(row.breedingStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="lastBreedingDate" label="最后繁育日期" width="120" sortable>
          <template #default="{ row }">
            {{ formatDate(row.lastBreedingDate) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="totalOffspring" label="后代数量" width="100" sortable />
        
        <el-table-column prop="healthScore" label="健康评分" width="100" sortable>
          <template #default="{ row }">
            <el-progress
              :percentage="row.healthScore || 0"
              :color="getHealthColor(row.healthScore)"
              :show-text="false"
              :stroke-width="8"
            />
            <span class="health-score">{{ row.healthScore || 0 }}%</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewDetails(row)">
              详情
            </el-button>
            <el-button size="small" type="primary" @click="editBreeding(row)">
              编辑
            </el-button>
            <el-dropdown @command="(command) => handleCommand(command, row)">
              <el-button size="small">
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="breeding">繁育记录</el-dropdown-item>
                  <el-dropdown-item command="pedigree">血统查看</el-dropdown-item>
                  <el-dropdown-item command="health">健康记录</el-dropdown-item>
                  <el-dropdown-item command="offspring">后代管理</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增繁育记录对话框 -->
    <el-dialog
      v-model="showAddDialog"
      title="新增繁育记录"
      width="600px"
      @close="resetAddForm"
    >
      <el-form
        ref="addFormRef"
        :model="addForm"
        :rules="addFormRules"
        label-width="100px"
      >
        <el-form-item label="母猫" prop="femaleCatId">
          <el-select
            v-model="addForm.femaleCatId"
            placeholder="选择母猫"
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="cat in femaleCats"
              :key="cat.id"
              :label="cat.name"
              :value="cat.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="公猫" prop="maleCatId">
          <el-select
            v-model="addForm.maleCatId"
            placeholder="选择公猫"
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="cat in maleCats"
              :key="cat.id"
              :label="cat.name"
              :value="cat.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="配种日期" prop="breedingDate">
          <el-date-picker
            v-model="addForm.breedingDate"
            type="date"
            placeholder="选择配种日期"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="配种方式" prop="breedingMethod">
          <el-select
            v-model="addForm.breedingMethod"
            placeholder="选择配种方式"
            style="width: 100%"
          >
            <el-option label="自然配种" value="NATURAL" />
            <el-option label="人工授精" value="ARTIFICIAL" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="备注" prop="notes">
          <el-input
            v-model="addForm.notes"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddDialog = false">取消</el-button>
          <el-button type="primary" @click="submitAdd">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import {
  Management,
  Female,
  House,
  Calendar,
  Search,
  Plus,
  Refresh,
  ArrowDown
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { breedingApi } from '@/api/breeding'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const showAddDialog = ref(false)

const breedingStats = reactive({
  breedingCats: 0,
  pregnantCats: 0,
  newbornKittens: 0,
  activePlans: 0
})

const searchForm = reactive({
  keyword: '',
  status: '',
  gender: ''
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

const breedingList = ref([])
const femaleCats = ref([])
const maleCats = ref([])

const addForm = reactive({
  femaleCatId: '',
  maleCatId: '',
  breedingDate: '',
  breedingMethod: '',
  notes: ''
})

const addFormRules = {
  femaleCatId: [{ required: true, message: '请选择母猫', trigger: 'change' }],
  maleCatId: [{ required: true, message: '请选择公猫', trigger: 'change' }],
  breedingDate: [{ required: true, message: '请选择配种日期', trigger: 'change' }],
  breedingMethod: [{ required: true, message: '请选择配种方式', trigger: 'change' }]
}

// 方法
const calculateAge = (birthDate: string) => {
  if (!birthDate) return '-'
  const years = dayjs().diff(dayjs(birthDate), 'year')
  const months = dayjs().diff(dayjs(birthDate), 'month') % 12
  return years > 0 ? `${years}岁${months}月` : `${months}月`
}

const formatDate = (date: string) => {
  return date ? dayjs(date).format('YYYY-MM-DD') : '-'
}

const getStatusType = (status: string) => {
  const statusMap = {
    'AVAILABLE': 'success',
    'BREEDING': 'warning',
    'PREGNANT': 'danger',
    'NURSING': 'info',
    'RESTING': ''
  }
  return statusMap[status] || ''
}

const getStatusText = (status: string) => {
  const statusMap = {
    'AVAILABLE': '可繁育',
    'BREEDING': '繁育中',
    'PREGNANT': '怀孕中',
    'NURSING': '哺乳期',
    'RESTING': '休息期'
  }
  return statusMap[status] || status
}

const getHealthColor = (score: number) => {
  if (score >= 90) return '#67c23a'
  if (score >= 70) return '#e6a23c'
  return '#f56c6c'
}

const loadBreedingStats = async () => {
  try {
    const response = await breedingApi.getBreedingStats()
    Object.assign(breedingStats, response.data)
  } catch (error) {
    console.error('加载繁育统计失败:', error)
  }
}

const loadBreedingList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.currentPage,
      size: pagination.pageSize,
      keyword: searchForm.keyword,
      status: searchForm.status,
      gender: searchForm.gender
    }
    
    const response = await breedingApi.getBreedingList(params)
    breedingList.value = response.data.content
    pagination.total = response.data.totalElements
  } catch (error) {
    console.error('加载繁育列表失败:', error)
    ElMessage.error('加载繁育列表失败')
  } finally {
    loading.value = false
  }
}

const loadCatsForBreeding = async () => {
  try {
    const response = await breedingApi.getCatsForBreeding()
    femaleCats.value = response.data.females
    maleCats.value = response.data.males
  } catch (error) {
    console.error('加载可繁育猫咪失败:', error)
  }
}

const handleSearch = () => {
  pagination.currentPage = 1
  loadBreedingList()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: '',
    gender: ''
  })
  handleSearch()
}

const handleSortChange = ({ prop, order }) => {
  // 处理排序
  loadBreedingList()
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  loadBreedingList()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadBreedingList()
}

const viewDetails = (row: any) => {
  // 查看详情
  console.log('查看详情:', row)
}

const editBreeding = (row: any) => {
  // 编辑繁育信息
  console.log('编辑繁育:', row)
}

const handleCommand = (command: string, row: any) => {
  switch (command) {
    case 'breeding':
      // 查看繁育记录
      break
    case 'pedigree':
      // 查看血统
      break
    case 'health':
      // 查看健康记录
      break
    case 'offspring':
      // 后代管理
      break
  }
}

const resetAddForm = () => {
  Object.assign(addForm, {
    femaleCatId: '',
    maleCatId: '',
    breedingDate: '',
    breedingMethod: '',
    notes: ''
  })
}

const submitAdd = async () => {
  try {
    await breedingApi.createBreedingRecord(addForm)
    ElMessage.success('繁育记录创建成功')
    showAddDialog.value = false
    resetAddForm()
    loadBreedingList()
    loadBreedingStats()
  } catch (error) {
    console.error('创建繁育记录失败:', error)
    ElMessage.error('创建繁育记录失败')
  }
}

// 初始化
onMounted(() => {
  loadBreedingStats()
  loadBreedingList()
  loadCatsForBreeding()
})
</script>

<style scoped lang="scss">
.breeding-list {
  padding: 20px;
  
  .page-header {
    margin-bottom: 20px;
    
    h1 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }
    
    p {
      margin: 0;
      color: #909399;
      font-size: 14px;
    }
  }
  
  .breeding-stats {
    margin-bottom: 20px;
    
    .stat-card {
      margin-bottom: 20px;
      
      &.breeding .stat-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
      
      &.pregnant .stat-icon {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }
      
      &.kittens .stat-icon {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }
      
      &.plans .stat-icon {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      }
      
      .stat-content {
        display: flex;
        align-items: center;
        
        .stat-icon {
          width: 60px;
          height: 60px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          font-size: 24px;
          color: white;
        }
        
        .stat-info {
          flex: 1;
          
          .stat-number {
            font-size: 28px;
            font-weight: 600;
            color: #303133;
            line-height: 1;
            margin-bottom: 4px;
          }
          
          .stat-label {
            font-size: 14px;
            color: #909399;
          }
        }
      }
    }
  }
  
  .search-card {
    margin-bottom: 20px;
    
    .action-buttons {
      display: flex;
      gap: 8px;
    }
  }
  
  .list-card {
    .cat-info {
      display: flex;
      align-items: center;
      
      .cat-details {
        margin-left: 12px;
        
        .cat-name {
          font-weight: 600;
          color: #303133;
          margin-bottom: 2px;
        }
        
        .cat-code {
          font-size: 12px;
          color: #909399;
        }
      }
    }
    
    .health-score {
      margin-left: 8px;
      font-size: 12px;
      color: #606266;
    }
    
    .pagination-container {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  }
}

@media (max-width: 768px) {
  .breeding-list {
    padding: 12px;
    
    .breeding-stats {
      .stat-card {
        .stat-content {
          .stat-icon {
            width: 50px;
            height: 50px;
            font-size: 20px;
            margin-right: 12px;
          }
          
          .stat-info {
            .stat-number {
              font-size: 24px;
            }
          }
        }
      }
    }
    
    .search-card {
      .action-buttons {
        margin-top: 12px;
        
        .el-button {
          width: 100%;
          margin-bottom: 8px;
        }
      }
    }
  }
}
</style>
