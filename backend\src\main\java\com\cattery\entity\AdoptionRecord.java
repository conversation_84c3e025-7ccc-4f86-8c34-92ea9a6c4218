package com.cattery.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 领养记录实体类
 */
@Entity
@Table(name = "adoption_records")
@Data
@EqualsAndHashCode(callSuper = false)
public class AdoptionRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 关联的猫咪
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_id", nullable = false)
    private Cat cat;

    /**
     * 关联的客户
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "customer_id", nullable = false)
    private Customer customer;

    /**
     * 领养状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "adoption_status", nullable = false)
    private AdoptionStatus adoptionStatus = AdoptionStatus.PENDING;

    /**
     * 申请日期
     */
    @Column(name = "application_date", nullable = false)
    private LocalDateTime applicationDate;

    /**
     * 审核日期
     */
    @Column(name = "review_date")
    private LocalDateTime reviewDate;

    /**
     * 领养日期
     */
    @Column(name = "adoption_date")
    private LocalDateTime adoptionDate;

    /**
     * 领养费用
     */
    @Column(name = "adoption_fee", precision = 10, scale = 2)
    private BigDecimal adoptionFee;

    /**
     * 审核员ID
     */
    @Column(name = "reviewer_id")
    private Long reviewerId;

    /**
     * 审核意见
     */
    @Column(name = "review_notes", columnDefinition = "TEXT")
    private String reviewNotes;

    /**
     * 领养原因
     */
    @Column(name = "adoption_reason", columnDefinition = "TEXT")
    private String adoptionReason;

    /**
     * 家庭情况描述
     */
    @Column(name = "family_situation", columnDefinition = "TEXT")
    private String familySituation;

    /**
     * 养宠经验
     */
    @Column(name = "pet_experience", columnDefinition = "TEXT")
    private String petExperience;

    /**
     * 合同文件路径
     */
    @Column(name = "contract_path", length = 500)
    private String contractPath;

    /**
     * 备注
     */
    @Column(columnDefinition = "TEXT")
    private String notes;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 创建者ID
     */
    @Column(name = "created_by")
    private Long createdBy;

    /**
     * 更新者ID
     */
    @Column(name = "updated_by")
    private Long updatedBy;

    /**
     * 领养状态枚举
     */
    public enum AdoptionStatus {
        PENDING("待审核"),
        APPROVED("已批准"),
        REJECTED("已拒绝"),
        COMPLETED("已完成"),
        CANCELLED("已取消");

        private final String description;

        AdoptionStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 状态枚举别名（为了兼容Repository）
     */
    public enum Status {
        PENDING("待审核"),
        APPROVED("已批准"),
        REJECTED("已拒绝"),
        COMPLETED("已完成"),
        CANCELLED("已取消");

        private final String description;

        Status(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
