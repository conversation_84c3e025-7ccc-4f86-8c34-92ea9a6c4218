export default {
  // 应用基本信息
  app: {
    title: '猫舍管理系统',
    description: '专业的猫舍管理系统',
    version: '版本 1.0.0'
  },

  // 通用文本
  common: {
    // 操作
    add: '添加',
    edit: '编辑',
    delete: '删除',
    save: '保存',
    cancel: '取消',
    confirm: '确认',
    submit: '提交',
    reset: '重置',
    search: '搜索',
    filter: '筛选',
    export: '导出',
    import: '导入',
    upload: '上传',
    download: '下载',
    view: '查看',
    detail: '详情',
    back: '返回',
    next: '下一步',
    previous: '上一步',
    finish: '完成',
    close: '关闭',
    refresh: '刷新',
    retry: '重试',
    loading: '加载中...',
    noData: '暂无数据',
    
    // 状态
    success: '成功',
    error: '错误',
    warning: '警告',
    info: '信息',
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    failed: '失败',
    active: '活跃',
    inactive: '非活跃',
    enabled: '启用',
    disabled: '禁用',
    
    // 时间
    today: '今天',
    yesterday: '昨天',
    tomorrow: '明天',
    thisWeek: '本周',
    thisMonth: '本月',
    thisYear: '今年',
    date: '日期',
    time: '时间',
    datetime: '日期时间',
    year: '年',
    month: '月',
    day: '日',
    hour: '时',
    minute: '分',
    second: '秒',
    years: '年',
    months: '个月',
    days: '天',
    hours: '小时',
    minutes: '分钟',
    seconds: '秒',
    
    // 数量单位
    total: '总计',
    count: '数量',
    amount: '金额',
    price: '价格',
    weight: '重量',
    height: '身高',
    length: '长度',
    age: '年龄',
    
    // 确认对话框
    confirmDelete: '确定要删除吗？',
    confirmSave: '确定要保存吗？',
    confirmCancel: '确定要取消吗？',
    deleteSuccess: '删除成功',
    saveSuccess: '保存成功',
    updateSuccess: '更新成功',
    createSuccess: '创建成功',
    operationSuccess: '操作成功',
    operationFailed: '操作失败',
    
    // 语言相关
    language: '语言',
    languageChanged: '语言切换成功',
    languageChangeFailed: '语言切换失败',
    
    // 其他
    all: '全部',
    none: '无',
    unknown: '未知',
    other: '其他',
    more: '更多',
    less: '收起',
    expand: '展开',
    collapse: '折叠',
    required: '必填',
    optional: '可选',
    placeholder: '请输入...',
    selectPlaceholder: '请选择...',
    clearFilters: '清除筛选'
  },

  // 导航菜单
  navigation: {
    dashboard: '仪表盘',
    cats: '猫咪管理',
    health: '健康管理',
    breeding: '繁育管理',
    customers: '客户管理',
    inventory: '库存管理',
    finance: '财务管理',
    reports: '报表中心',
    ai: 'AI功能',
    settings: '系统设置',
    profile: '个人资料',
    logout: '退出登录',
    home: '首页',
    more: '更多'
  },

  // 用户认证
  auth: {
    login: '登录',
    logout: '退出',
    register: '注册',
    forgotPassword: '忘记密码',
    resetPassword: '重置密码',
    changePassword: '修改密码',
    username: '用户名',
    password: '密码',
    confirmPassword: '确认密码',
    email: '邮箱',
    phone: '手机号',
    rememberMe: '记住我',
    loginSuccess: '登录成功',
    loginFailed: '登录失败',
    logoutSuccess: '退出成功',
    registerSuccess: '注册成功',
    passwordChanged: '密码修改成功',
    invalidCredentials: '用户名或密码错误',
    accountLocked: '账户已被锁定',
    sessionExpired: '会话已过期，请重新登录'
  },

  // 猫咪管理
  cat: {
    // 基本信息
    name: '姓名',
    breed: '品种',
    gender: '性别',
    dateOfBirth: '出生日期',
    color: '颜色',
    pattern: '花纹',
    weight: '体重',
    microchipId: '芯片号',
    registrationNumber: '注册号',
    status: '状态',
    description: '描述',
    notes: '备注',
    
    // 性别
    gender: {
      male: '公猫',
      female: '母猫',
      unknown: '未知'
    },
    
    // 状态
    status: {
      available: '可领养',
      adopted: '已领养',
      reserved: '已预定',
      breeding: '繁育中',
      medical: '医疗中',
      quarantine: '隔离中'
    },
    
    // 操作
    add: '添加猫咪',
    edit: '编辑猫咪',
    delete: '删除猫咪',
    view: '查看详情',
    addFirst: '添加第一只猫咪',
    
    // 列表
    list: '猫咪列表',
    noCats: '暂无猫咪',
    noCatsDescription: '还没有添加任何猫咪，点击下方按钮添加第一只猫咪',
    searchPlaceholder: '搜索猫咪姓名或品种...',
    
    // 详情页面
    basicInfo: '基本信息',
    healthInfo: '健康信息',
    breedingInfo: '繁育信息',
    photos: '照片',
    pedigree: '血统',
    
    // 照片管理
    uploadPhoto: '上传照片',
    deletePhoto: '删除照片',
    setPrimaryPhoto: '设为主照片',
    photoUploaded: '照片上传成功',
    photoDeleted: '照片删除成功',
    
    // 血统管理
    father: '父亲',
    mother: '母亲',
    offspring: '后代',
    siblings: '兄弟姐妹',
    pedigreeTree: '血统树',
    
    // 验证消息
    nameRequired: '请输入猫咪姓名',
    breedRequired: '请选择品种',
    genderRequired: '请选择性别',
    dateOfBirthRequired: '请选择出生日期',
    
    // 错误消息
    loadError: '加载猫咪信息失败',
    saveError: '保存猫咪信息失败',
    deleteError: '删除猫咪失败'
  },

  // 健康管理
  health: {
    // 记录类型
    recordType: {
      vaccination: '疫苗接种',
      checkup: '体检',
      treatment: '治疗',
      surgery: '手术',
      dental: '牙科',
      grooming: '美容',
      geneticTest: '基因检测',
      weightCheck: '体重检查'
    },
    
    // 基本信息
    record: '健康记录',
    recordDate: '记录日期',
    recordType: '记录类型',
    veterinarian: '兽医',
    clinic: '诊所',
    notes: '备注',
    nextAppointment: '下次预约',
    
    // 疫苗管理
    vaccine: '疫苗',
    vaccineName: '疫苗名称',
    vaccineDate: '接种日期',
    nextVaccineDate: '下次接种日期',
    vaccineStatus: '疫苗状态',
    upToDate: '已接种',
    overdue: '已过期',
    dueSoon: '即将到期',
    
    // 体检
    checkup: '体检',
    checkupDate: '体检日期',
    checkupResult: '体检结果',
    normal: '正常',
    abnormal: '异常',
    
    // 治疗
    treatment: '治疗',
    treatmentDate: '治疗日期',
    diagnosis: '诊断',
    medication: '用药',
    dosage: '剂量',
    
    // 统计
    healthScore: '健康评分',
    lastCheckup: '最近体检',
    vaccinesDue: '待接种疫苗',
    checkupsDue: '待体检',
    overallHealth: '整体健康状况'
  },

  // 繁育管理
  breeding: {
    // 基本信息
    mating: '配种',
    pregnancy: '怀孕',
    birth: '分娩',
    kitten: '幼猫',
    
    // 配种
    matingDate: '配种日期',
    maleParent: '公猫',
    femaleParent: '母猫',
    matingType: '配种方式',
    matingResult: '配种结果',
    
    // 怀孕
    pregnancyDate: '怀孕日期',
    dueDate: '预产期',
    pregnancyStatus: '怀孕状态',
    ultrasoundDate: 'B超日期',
    expectedKittens: '预期幼猫数',
    
    // 分娩
    birthDate: '分娩日期',
    litterSize: '产仔数',
    birthWeight: '出生体重',
    complications: '并发症',
    
    // 幼猫
    kittenName: '幼猫姓名',
    kittenGender: '幼猫性别',
    kittenWeight: '幼猫体重',
    kittenStatus: '幼猫状态',
    
    // 状态
    status: {
      planned: '计划中',
      mated: '已配种',
      pregnant: '怀孕中',
      born: '已出生',
      weaned: '已断奶'
    }
  },

  // 客户管理
  customer: {
    // 基本信息
    name: '客户姓名',
    email: '邮箱',
    phone: '电话',
    address: '地址',
    customerType: '客户类型',
    registrationDate: '注册日期',
    
    // 客户类型
    type: {
      potential: '潜在客户',
      active: '活跃客户',
      adopted: '已领养客户',
      breeder: '繁育者'
    },
    
    // 咨询管理
    inquiry: '咨询',
    inquiryDate: '咨询日期',
    inquiryType: '咨询类型',
    inquiryStatus: '咨询状态',
    response: '回复',
    
    // 跟进记录
    followUp: '跟进',
    followUpDate: '跟进日期',
    followUpType: '跟进方式',
    followUpResult: '跟进结果'
  },

  // 库存管理
  inventory: {
    // 基本信息
    item: '物品',
    itemName: '物品名称',
    category: '分类',
    quantity: '数量',
    unit: '单位',
    price: '价格',
    supplier: '供应商',
    
    // 库存操作
    stockIn: '入库',
    stockOut: '出库',
    stockAdjustment: '库存调整',
    stockCheck: '盘点',
    
    // 预警
    lowStock: '库存不足',
    outOfStock: '缺货',
    minStock: '最小库存',
    safeStock: '安全库存'
  },

  // 财务管理
  finance: {
    // 基本信息
    transaction: '交易',
    transactionDate: '交易日期',
    transactionType: '交易类型',
    amount: '金额',
    description: '描述',
    category: '分类',
    
    // 交易类型
    type: {
      income: '收入',
      expense: '支出',
      transfer: '转账'
    },
    
    // 收入分类
    incomeCategory: {
      adoption: '领养费',
      breeding: '繁育费',
      boarding: '寄养费',
      grooming: '美容费',
      other: '其他收入'
    },
    
    // 支出分类
    expenseCategory: {
      food: '食物',
      medical: '医疗',
      supplies: '用品',
      utilities: '水电费',
      rent: '租金',
      other: '其他支出'
    },
    
    // 统计
    totalIncome: '总收入',
    totalExpense: '总支出',
    netIncome: '净收入',
    monthlyIncome: '月收入',
    monthlyExpense: '月支出'
  },

  // AI功能
  ai: {
    // 猫咪识别
    catRecognition: '猫咪识别',
    breedRecognition: '品种识别',
    uploadImage: '上传图片',
    takePhoto: '拍照',
    recognizing: '识别中...',
    recognitionResult: '识别结果',
    confidence: '置信度',
    
    // 健康预测
    healthPrediction: '健康预测',
    healthScore: '健康评分',
    riskLevel: '风险等级',
    riskFactors: '风险因素',
    recommendations: '建议',
    
    // 行为分析
    behaviorAnalysis: '行为分析',
    behaviorScore: '行为评分',
    behaviorPattern: '行为模式',
    abnormalBehavior: '异常行为',
    
    // 服务状态
    serviceStatus: '服务状态',
    available: '可用',
    unavailable: '不可用',
    modelVersion: '模型版本',
    accuracy: '准确率'
  },

  // 报表
  reports: {
    // 基本信息
    report: '报表',
    reportType: '报表类型',
    reportPeriod: '报表周期',
    generateReport: '生成报表',
    exportReport: '导出报表',
    
    // 报表类型
    type: {
      catSummary: '猫咪汇总',
      healthSummary: '健康汇总',
      breedingSummary: '繁育汇总',
      financeSummary: '财务汇总',
      customerSummary: '客户汇总'
    },
    
    // 时间周期
    period: {
      daily: '日报',
      weekly: '周报',
      monthly: '月报',
      quarterly: '季报',
      yearly: '年报'
    }
  },

  // 设置
  settings: {
    // 基本设置
    general: '常规设置',
    appearance: '外观设置',
    language: '语言设置',
    notification: '通知设置',
    security: '安全设置',
    backup: '备份设置',
    
    // 主题
    theme: '主题',
    lightTheme: '浅色主题',
    darkTheme: '深色主题',
    autoTheme: '跟随系统',
    
    // 通知
    emailNotification: '邮件通知',
    smsNotification: '短信通知',
    pushNotification: '推送通知',
    
    // 安全
    changePassword: '修改密码',
    twoFactorAuth: '双因素认证',
    loginHistory: '登录历史',
    
    // 备份
    autoBackup: '自动备份',
    backupFrequency: '备份频率',
    backupLocation: '备份位置'
  },

  // 仪表盘
  dashboard: {
    welcome: '欢迎回来',
    todayOverview: '今日概览',
    quickActions: '快速操作',
    recentActivities: '最近活动',
    upcomingEvents: '即将到来的事件',
    healthOverview: '健康概览',
    breedingOverview: '繁育概览',
    
    // 统计卡片
    totalCats: '猫咪总数',
    healthAlerts: '健康警告',
    breeding: '繁育中',
    revenue: '月收入',
    
    // 快速操作
    addCat: '添加猫咪',
    healthCheck: '健康检查',
    
    // 健康概览
    vaccinesDue: '待接种疫苗',
    checkupsDue: '待体检',
    
    // 繁育概览
    pregnant: '怀孕中',
    dueSoon: '即将分娩',
    
    // 警告
    alerts: '警告',
    viewAllAlerts: '查看所有警告'
  },

  // 错误消息
  error: {
    // 网络错误
    networkError: '网络连接失败',
    serverError: '服务器错误',
    timeoutError: '请求超时',
    
    // 权限错误
    unauthorized: '未授权访问',
    forbidden: '权限不足',
    
    // 数据错误
    dataNotFound: '数据不存在',
    dataInvalid: '数据格式错误',
    
    // 文件错误
    fileUploadError: '文件上传失败',
    fileSizeError: '文件大小超出限制',
    fileTypeError: '文件类型不支持',
    
    // 通用错误
    unknownError: '未知错误',
    operationFailed: '操作失败'
  }
}
