<template>
  <div class="media-gallery">
    <div class="gallery-header">
      <div class="gallery-title">
        <h3>{{ title }}</h3>
        <span class="media-count">{{ mediaFiles.length }} 个文件</span>
      </div>
      
      <div class="gallery-actions">
        <el-button type="primary" @click="showUploadDialog" v-if="canUpload">
          <el-icon><Upload /></el-icon> 上传文件
        </el-button>
        
        <el-radio-group v-model="viewMode" size="small">
          <el-radio-button label="grid">
            <el-icon><Grid /></el-icon>
          </el-radio-button>
          <el-radio-button label="list">
            <el-icon><List /></el-icon>
          </el-radio-button>
        </el-radio-group>
      </div>
    </div>
    
    <el-empty v-if="mediaFiles.length === 0" description="暂无媒体文件" />
    
    <!-- 网格视图 -->
    <div v-else-if="viewMode === 'grid'" class="media-grid">
      <div 
        v-for="media in mediaFiles" 
        :key="media.id" 
        class="media-item"
        :class="{ 'is-primary': media.isPrimary }"
        @click="handleMediaClick(media)"
      >
        <div class="media-thumbnail">
          <img 
            v-if="media.mediaType === 'PHOTO' && media.thumbnailUrl" 
            :src="media.thumbnailUrl" 
            :alt="media.title || media.originalFileName" 
          />
          <div v-else-if="media.mediaType === 'VIDEO'" class="video-thumbnail">
            <img 
              v-if="media.previewImageUrl" 
              :src="media.previewImageUrl" 
              :alt="media.title || media.originalFileName" 
            />
            <el-icon class="video-icon"><VideoPlay /></el-icon>
          </div>
          <div v-else class="default-thumbnail">
            <el-icon><Document /></el-icon>
          </div>
          
          <div class="media-badges">
            <el-tag v-if="media.isPrimary" size="small" type="success">主要</el-tag>
          </div>
        </div>
        
        <div class="media-info">
          <div class="media-title">{{ media.title || formatFileName(media.originalFileName) }}</div>
          <div class="media-meta">
            <span>{{ formatFileSize(media.fileSize) }}</span>
            <span v-if="media.width && media.height">{{ media.width }}x{{ media.height }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 列表视图 -->
    <div v-else class="media-list">
      <el-table :data="mediaFiles" style="width: 100%">
        <el-table-column label="预览" width="100">
          <template #default="{ row }">
            <div class="list-thumbnail">
              <img 
                v-if="row.mediaType === 'PHOTO' && row.thumbnailUrl" 
                :src="row.thumbnailUrl" 
                :alt="row.title || row.originalFileName" 
              />
              <div v-else-if="row.mediaType === 'VIDEO'" class="video-thumbnail">
                <img 
                  v-if="row.previewImageUrl" 
                  :src="row.previewImageUrl" 
                  :alt="row.title || row.originalFileName" 
                />
                <el-icon class="video-icon"><VideoPlay /></el-icon>
              </div>
              <div v-else class="default-thumbnail">
                <el-icon><Document /></el-icon>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="title" label="标题">
          <template #default="{ row }">
            {{ row.title || formatFileName(row.originalFileName) }}
            <el-tag v-if="row.isPrimary" size="small" type="success" style="margin-left: 5px;">主要</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="mediaType" label="类型" width="100">
          <template #default="{ row }">
            <el-tag 
              :type="row.mediaType === 'PHOTO' ? 'success' : row.mediaType === 'VIDEO' ? 'warning' : 'info'"
              size="small"
            >
              {{ formatMediaType(row.mediaType) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="fileSize" label="大小" width="100">
          <template #default="{ row }">
            {{ formatFileSize(row.fileSize) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <el-button size="small" type="primary" @click="handleMediaClick(row)">
                <el-icon><View /></el-icon>
              </el-button>
              <el-button 
                v-if="canSetPrimary && row.mediaType === 'PHOTO' && !row.isPrimary" 
                size="small" 
                type="success" 
                @click="setPrimaryPhoto(row)"
              >
                <el-icon><Star /></el-icon>
              </el-button>
              <el-button 
                v-if="canDelete" 
                size="small" 
                type="danger" 
                @click="deleteMedia(row)"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 上传对话框 -->
    <el-dialog v-model="uploadDialogVisible" title="上传媒体文件" width="600px">
      <el-tabs v-model="uploadTab">
        <el-tab-pane label="照片" name="photo">
          <file-uploader
            accept="image/*"
            :upload-url="`/api/cats/${catId}/media/photos`"
            :upload-params="uploadParams"
            :file-type-hint="'支持的文件类型: JPG, PNG, GIF, WEBP'"
            @upload-success="handleUploadSuccess"
          />
        </el-tab-pane>
        <el-tab-pane label="视频" name="video">
          <file-uploader
            accept="video/*"
            :upload-url="`/api/cats/${catId}/media/videos`"
            :upload-params="uploadParams"
            :file-type-hint="'支持的文件类型: MP4, AVI, MOV'"
            @upload-success="handleUploadSuccess"
          />
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
    
    <!-- 媒体预览对话框 -->
    <el-dialog v-model="previewDialogVisible" :title="selectedMedia?.title || '媒体预览'" width="800px">
      <div v-if="selectedMedia" class="media-preview">
        <div v-if="selectedMedia.mediaType === 'PHOTO'" class="photo-preview">
          <img :src="selectedMedia.fileUrl" :alt="selectedMedia.title || selectedMedia.originalFileName" />
        </div>
        
        <div v-else-if="selectedMedia.mediaType === 'VIDEO'" class="video-preview">
          <video controls>
            <source :src="selectedMedia.fileUrl" :type="selectedMedia.mimeType">
            您的浏览器不支持视频播放
          </video>
        </div>
        
        <div class="media-details">
          <div class="detail-item">
            <span class="detail-label">文件名:</span>
            <span class="detail-value">{{ selectedMedia.originalFileName }}</span>
          </div>
          
          <div class="detail-item">
            <span class="detail-label">类型:</span>
            <span class="detail-value">{{ formatMediaType(selectedMedia.mediaType) }}</span>
          </div>
          
          <div class="detail-item">
            <span class="detail-label">大小:</span>
            <span class="detail-value">{{ formatFileSize(selectedMedia.fileSize) }}</span>
          </div>
          
          <div v-if="selectedMedia.width && selectedMedia.height" class="detail-item">
            <span class="detail-label">尺寸:</span>
            <span class="detail-value">{{ selectedMedia.width }}x{{ selectedMedia.height }}</span>
          </div>
          
          <div v-if="selectedMedia.description" class="detail-item description">
            <span class="detail-label">描述:</span>
            <span class="detail-value">{{ selectedMedia.description }}</span>
          </div>
        </div>
        
        <div class="media-actions">
          <el-button-group>
            <el-button 
              v-if="canSetPrimary && selectedMedia.mediaType === 'PHOTO' && !selectedMedia.isPrimary" 
              type="success" 
              @click="setPrimaryPhoto(selectedMedia)"
            >
              设为主要照片
            </el-button>
            <el-button 
              v-if="canEdit" 
              type="primary" 
              @click="editMedia(selectedMedia)"
            >
              编辑信息
            </el-button>
            <el-button 
              v-if="canDelete" 
              type="danger" 
              @click="deleteMedia(selectedMedia)"
            >
              删除
            </el-button>
          </el-button-group>
        </div>
      </div>
    </el-dialog>
    
    <!-- 编辑媒体信息对话框 -->
    <el-dialog v-model="editDialogVisible" title="编辑媒体信息" width="500px">
      <el-form v-if="selectedMedia" :model="editForm" label-width="80px">
        <el-form-item label="标题">
          <el-input v-model="editForm.title" placeholder="请输入标题"></el-input>
        </el-form-item>
        
        <el-form-item label="描述">
          <el-input 
            v-model="editForm.description" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入描述"
          ></el-input>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveMediaInfo">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Upload, Grid, List, VideoPlay, Document, View, Star, Delete 
} from '@element-plus/icons-vue'
import FileUploader from './FileUploader.vue'
import { useAuthStore } from '@/stores/auth'
import { mediaApi } from '@/api/media'

export default {
  name: 'MediaGallery',
  components: {
    Upload,
    Grid,
    List,
    VideoPlay,
    Document,
    View,
    Star,
    Delete,
    FileUploader
  },
  props: {
    // 猫咪ID
    catId: {
      type: [String, Number],
      required: true
    },
    // 画廊标题
    title: {
      type: String,
      default: '媒体文件'
    },
    // 是否可以上传
    canUpload: {
      type: Boolean,
      default: true
    },
    // 是否可以设置主要照片
    canSetPrimary: {
      type: Boolean,
      default: true
    },
    // 是否可以编辑
    canEdit: {
      type: Boolean,
      default: true
    },
    // 是否可以删除
    canDelete: {
      type: Boolean,
      default: true
    }
  },
  emits: ['media-updated', 'primary-photo-changed'],
  setup(props, { emit }) {
    const authStore = useAuthStore()
    
    // 响应式数据
    const mediaFiles = ref([])
    const viewMode = ref('grid')
    const uploadDialogVisible = ref(false)
    const previewDialogVisible = ref(false)
    const editDialogVisible = ref(false)
    const uploadTab = ref('photo')
    const selectedMedia = ref(null)
    const loading = ref(false)
    
    // 编辑表单
    const editForm = ref({
      title: '',
      description: ''
    })
    
    // 上传参数
    const uploadParams = computed(() => ({
      catId: props.catId
    }))
    
    // 加载媒体文件
    const loadMediaFiles = async () => {
      loading.value = true
      try {
        const response = await mediaApi.getCatMediaFiles(props.catId)
        mediaFiles.value = response.data || []
      } catch (error) {
        console.error('加载媒体文件失败:', error)
        ElMessage.error('加载媒体文件失败')
      } finally {
        loading.value = false
      }
    }
    
    // 显示上传对话框
    const showUploadDialog = () => {
      uploadDialogVisible.value = true
    }
    
    // 处理媒体点击
    const handleMediaClick = (media) => {
      selectedMedia.value = media
      previewDialogVisible.value = true
    }
    
    // 设置主要照片
    const setPrimaryPhoto = async (media) => {
      try {
        await mediaApi.setPrimaryPhoto(props.catId, media.id)
        ElMessage.success('设置主要照片成功')
        
        // 更新本地数据
        mediaFiles.value.forEach(item => {
          item.isPrimary = item.id === media.id
        })
        
        emit('primary-photo-changed', media)
        emit('media-updated')
        
        if (previewDialogVisible.value) {
          selectedMedia.value.isPrimary = true
        }
      } catch (error) {
        console.error('设置主要照片失败:', error)
        ElMessage.error('设置主要照片失败')
      }
    }
    
    // 删除媒体文件
    const deleteMedia = async (media) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除媒体文件 "${media.title || media.originalFileName}" 吗？`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        await mediaApi.deleteMedia(props.catId, media.id)
        ElMessage.success('删除成功')
        
        // 从列表中移除
        const index = mediaFiles.value.findIndex(item => item.id === media.id)
        if (index > -1) {
          mediaFiles.value.splice(index, 1)
        }
        
        emit('media-updated')
        
        // 如果正在预览被删除的媒体，关闭预览
        if (previewDialogVisible.value && selectedMedia.value?.id === media.id) {
          previewDialogVisible.value = false
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除媒体文件失败:', error)
          ElMessage.error('删除失败')
        }
      }
    }
    
    // 编辑媒体信息
    const editMedia = (media) => {
      editForm.value = {
        title: media.title || '',
        description: media.description || ''
      }
      editDialogVisible.value = true
    }
    
    // 保存媒体信息
    const saveMediaInfo = async () => {
      try {
        const response = await mediaApi.updateMediaInfo(
          props.catId,
          selectedMedia.value.id,
          editForm.value.title,
          editForm.value.description
        )
        
        ElMessage.success('保存成功')
        
        // 更新本地数据
        const index = mediaFiles.value.findIndex(item => item.id === selectedMedia.value.id)
        if (index > -1) {
          mediaFiles.value[index] = { ...mediaFiles.value[index], ...response.data }
        }
        
        // 更新选中的媒体
        selectedMedia.value = { ...selectedMedia.value, ...response.data }
        
        editDialogVisible.value = false
        emit('media-updated')
      } catch (error) {
        console.error('保存媒体信息失败:', error)
        ElMessage.error('保存失败')
      }
    }
    
    // 处理上传成功
    const handleUploadSuccess = (response) => {
      ElMessage.success('上传成功')
      uploadDialogVisible.value = false
      loadMediaFiles() // 重新加载媒体文件列表
      emit('media-updated')
    }
    
    // 格式化文件名
    const formatFileName = (fileName) => {
      if (!fileName) return ''
      const lastDotIndex = fileName.lastIndexOf('.')
      return lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName
    }
    
    // 格式化文件大小
    const formatFileSize = (size) => {
      if (!size) return '0 B'
      
      if (size < 1024) {
        return size + ' B'
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(1) + ' KB'
      } else if (size < 1024 * 1024 * 1024) {
        return (size / (1024 * 1024)).toFixed(1) + ' MB'
      } else {
        return (size / (1024 * 1024 * 1024)).toFixed(1) + ' GB'
      }
    }
    
    // 格式化媒体类型
    const formatMediaType = (type) => {
      const typeMap = {
        'PHOTO': '照片',
        'VIDEO': '视频',
        'DOCUMENT': '文档'
      }
      return typeMap[type] || type
    }
    
    // 组件挂载时加载数据
    onMounted(() => {
      loadMediaFiles()
    })
    
    return {
      mediaFiles,
      viewMode,
      uploadDialogVisible,
      previewDialogVisible,
      editDialogVisible,
      uploadTab,
      selectedMedia,
      loading,
      editForm,
      uploadParams,
      loadMediaFiles,
      showUploadDialog,
      handleMediaClick,
      setPrimaryPhoto,
      deleteMedia,
      editMedia,
      saveMediaInfo,
      handleUploadSuccess,
      formatFileName,
      formatFileSize,
      formatMediaType
    }
  }
}
</script>

<style scoped>
.media-gallery {
  width: 100%;
}

.gallery-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.gallery-title h3 {
  margin: 0;
  color: #303133;
}

.media-count {
  font-size: 14px;
  color: #909399;
  margin-left: 10px;
}

.gallery-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.media-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.media-item {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;
  background: white;
}

.media-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.media-item.is-primary {
  border-color: #67c23a;
  box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.2);
}

.media-thumbnail {
  position: relative;
  width: 100%;
  height: 150px;
  overflow: hidden;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.media-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-thumbnail {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-icon {
  position: absolute;
  font-size: 32px;
  color: white;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  padding: 8px;
}

.default-thumbnail {
  font-size: 48px;
  color: #c0c4cc;
}

.media-badges {
  position: absolute;
  top: 8px;
  right: 8px;
}

.media-info {
  padding: 12px;
}

.media-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.media-meta {
  font-size: 12px;
  color: #909399;
  display: flex;
  justify-content: space-between;
}

.list-thumbnail {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.list-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.list-thumbnail .video-thumbnail {
  width: 100%;
  height: 100%;
}

.list-thumbnail .video-icon {
  font-size: 16px;
  padding: 4px;
}

.list-thumbnail .default-thumbnail {
  font-size: 24px;
}

.media-preview {
  text-align: center;
}

.photo-preview img {
  max-width: 100%;
  max-height: 500px;
  border-radius: 8px;
}

.video-preview video {
  max-width: 100%;
  max-height: 500px;
  border-radius: 8px;
}

.media-details {
  margin-top: 20px;
  text-align: left;
}

.detail-item {
  display: flex;
  margin-bottom: 10px;
}

.detail-item.description {
  flex-direction: column;
}

.detail-label {
  font-weight: 500;
  color: #606266;
  width: 80px;
  flex-shrink: 0;
}

.detail-value {
  color: #303133;
  flex: 1;
}

.detail-item.description .detail-value {
  margin-top: 5px;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
}

.media-actions {
  margin-top: 20px;
  text-align: center;
}

.dialog-footer {
  text-align: right;
}
</style>