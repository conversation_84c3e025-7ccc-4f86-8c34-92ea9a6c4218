<template>
  <div class="error-page">
    <div class="error-container">
      <div class="error-icon">
        <el-icon v-if="errorCode === 404"><DocumentDelete /></el-icon>
        <el-icon v-else-if="errorCode === 403"><Lock /></el-icon>
        <el-icon v-else><Warning /></el-icon>
      </div>
      
      <div class="error-content">
        <h1 class="error-code">{{ errorCode }}</h1>
        <h2 class="error-title">{{ errorTitle }}</h2>
        <p class="error-message">{{ errorMessage }}</p>
        
        <div class="error-actions">
          <el-button type="primary" @click="goHome">
            返回首页
          </el-button>
          <el-button @click="goBack">
            返回上页
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { DocumentDelete, Lock, Warning } from '@element-plus/icons-vue'

interface Props {
  errorCode?: number
  errorMessage?: string
}

const props = withDefaults(defineProps<Props>(), {
  errorCode: 404,
  errorMessage: '页面不存在'
})

const router = useRouter()

const errorTitle = computed(() => {
  switch (props.errorCode) {
    case 403:
      return '访问被拒绝'
    case 404:
      return '页面未找到'
    case 500:
      return '服务器错误'
    default:
      return '出错了'
  }
})

const goHome = () => {
  router.push('/')
}

const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}
</script>

<style scoped>
.error-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

.error-container {
  text-align: center;
  max-width: 500px;
  width: 100%;
}

.error-icon {
  font-size: 120px;
  color: #e6a23c;
  margin-bottom: 30px;
}

.error-icon .el-icon {
  display: block;
}

.error-code {
  font-size: 72px;
  font-weight: 700;
  color: #333;
  margin: 0 0 16px 0;
  line-height: 1;
}

.error-title {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
}

.error-message {
  font-size: 18px;
  color: #666;
  margin: 0 0 40px 0;
  line-height: 1.5;
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.error-actions .el-button {
  min-width: 120px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .error-icon {
    font-size: 80px;
    margin-bottom: 20px;
  }
  
  .error-code {
    font-size: 48px;
  }
  
  .error-title {
    font-size: 24px;
  }
  
  .error-message {
    font-size: 16px;
    margin-bottom: 30px;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .error-actions .el-button {
    width: 100%;
    max-width: 200px;
  }
}
</style>
