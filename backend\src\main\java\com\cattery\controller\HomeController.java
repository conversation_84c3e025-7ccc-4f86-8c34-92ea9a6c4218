package com.cattery.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 首页控制器
 */
@Controller
public class HomeController {

    @GetMapping("/")
    @ResponseBody
    public String home() {
        return """
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>猫舍管理系统</title>
                <style>
                    body {
                        font-family: 'Microsoft YaHei', Arial, sans-serif;
                        margin: 0;
                        padding: 0;
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        min-height: 100vh;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                    .container {
                        background: white;
                        border-radius: 15px;
                        padding: 40px;
                        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                        text-align: center;
                        max-width: 600px;
                        width: 90%;
                    }
                    h1 {
                        color: #333;
                        margin-bottom: 20px;
                        font-size: 2.5em;
                    }
                    .emoji {
                        font-size: 4em;
                        margin-bottom: 20px;
                    }
                    .description {
                        color: #666;
                        font-size: 1.2em;
                        margin-bottom: 30px;
                        line-height: 1.6;
                    }
                    .links {
                        display: flex;
                        gap: 20px;
                        justify-content: center;
                        flex-wrap: wrap;
                    }
                    .link {
                        background: #667eea;
                        color: white;
                        text-decoration: none;
                        padding: 12px 24px;
                        border-radius: 8px;
                        transition: all 0.3s ease;
                        font-weight: bold;
                    }
                    .link:hover {
                        background: #5a6fd8;
                        transform: translateY(-2px);
                        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
                    }
                    .status {
                        background: #e8f5e8;
                        color: #2d5a2d;
                        padding: 10px 20px;
                        border-radius: 20px;
                        display: inline-block;
                        margin-bottom: 20px;
                        font-weight: bold;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="emoji">🐱</div>
                    <h1>猫舍管理系统</h1>
                    <div class="status">✅ 系统运行正常</div>
                    <div class="description">
                        欢迎使用猫舍管理系统！这是一个专业的猫咪繁育和领养管理平台，
                        提供完整的猫咪信息管理、健康记录、领养流程和财务管理功能。
                    </div>
                    <div class="links">
                        <a href="/swagger-ui/index.html" class="link">📚 API文档</a>
                        <a href="/h2-console" class="link">🗄️ 数据库控制台</a>
                        <a href="/actuator/health" class="link">💚 系统健康检查</a>
                    </div>
                </div>
            </body>
            </html>
            """;
    }

    @GetMapping("/health")
    @ResponseBody
    public String health() {
        return "OK";
    }
}
