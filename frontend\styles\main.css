/* 猫舍管理系统样式 */

/* 重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Aria<PERSON>, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f7fa;
}

/* 可访问性辅助类 */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.required {
    color: #e74c3c;
    font-weight: bold;
}

/* 焦点样式 */
*:focus {
    outline: 2px solid #3498db;
    outline-offset: 2px;
}

button:focus,
input:focus,
select:focus,
textarea:focus {
    outline: 2px solid #3498db;
    outline-offset: 2px;
}

/* 导航栏 */
.navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand h1 {
    font-size: 1.5rem;
    font-weight: 600;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: white;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.nav-menu a:hover,
.nav-menu a:focus {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.nav-icon {
    font-size: 1.2rem;
}

/* 主内容区域 */
.main-content {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 2rem;
}

.content-section {
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

.content-section.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.section-header {
    margin-bottom: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.section-header h2 {
    font-size: 2rem;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.section-header p {
    color: #7f8c8d;
    font-size: 1.1rem;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 44px; /* 确保触摸目标足够大 */
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-primary:hover,
.btn-primary:focus {
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover,
.btn-secondary:focus {
    background: #7f8c8d;
}

.btn-icon {
    font-size: 1.1rem;
}

/* 仪表盘样式 */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card h3 {
    color: #7f8c8d;
    font-size: 1rem;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-number {
    font-size: 3rem;
    font-weight: bold;
    color: #2c3e50;
}

.dashboard-charts {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.chart-container h3 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

/* 搜索和筛选 */
.filter-section {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.search-box {
    display: flex;
    margin-bottom: 1rem;
    max-width: 400px;
}

.search-box input {
    flex: 1;
    padding: 0.75rem;
    border: 2px solid #e0e6ed;
    border-right: none;
    border-radius: 8px 0 0 8px;
    font-size: 1rem;
}

.search-box input:focus {
    border-color: #3498db;
    outline: none;
}

.search-btn {
    padding: 0.75rem 1rem;
    background: #3498db;
    color: white;
    border: 2px solid #3498db;
    border-radius: 0 8px 8px 0;
    cursor: pointer;
    font-size: 1rem;
    min-width: 44px;
}

.search-btn:hover,
.search-btn:focus {
    background: #2980b9;
    border-color: #2980b9;
}

.filter-controls {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 200px;
}

.filter-group label {
    font-weight: 500;
    color: #2c3e50;
}

.filter-group select {
    padding: 0.75rem;
    border: 2px solid #e0e6ed;
    border-radius: 8px;
    font-size: 1rem;
    background: white;
    min-height: 44px;
}

.filter-group select:focus {
    border-color: #3498db;
    outline: none;
}

/* 猫咪网格 */
.cats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.cat-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.cat-card:hover {
    transform: translateY(-5px);
}

.cat-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    background: #f8f9fa;
}

.cat-info {
    padding: 1.5rem;
}

.cat-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.cat-details {
    color: #7f8c8d;
    margin-bottom: 1rem;
}

.cat-status {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-available {
    background: #d5f4e6;
    color: #27ae60;
}

.status-pregnant {
    background: #fef9e7;
    color: #f39c12;
}

.status-nursing {
    background: #ebf3fd;
    color: #3498db;
}

.status-retired {
    background: #fadbd8;
    color: #e74c3c;
}

/* 媒体上传 */
.upload-section {
    margin-bottom: 2rem;
}

.upload-area {
    border: 3px dashed #bdc3c7;
    border-radius: 12px;
    padding: 3rem;
    text-align: center;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-area:hover,
.upload-area:focus {
    border-color: #3498db;
    background: #f8f9fa;
}

.upload-area.dragover {
    border-color: #27ae60;
    background: #d5f4e6;
}

.upload-content {
    pointer-events: none;
}

.upload-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.upload-area p {
    font-size: 1.1rem;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.upload-hint {
    color: #7f8c8d;
    font-size: 0.9rem;
}

.upload-progress {
    margin-top: 1rem;
    text-align: center;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #ecf0f1;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #27ae60);
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    font-weight: 500;
    color: #2c3e50;
}

/* 媒体画廊 */
.media-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
}

.media-item {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.media-item:hover {
    transform: scale(1.05);
}

.media-thumbnail {
    width: 100%;
    height: 150px;
    object-fit: cover;
}

.media-info {
    padding: 1rem;
}

.media-name {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.media-size {
    color: #7f8c8d;
    font-size: 0.875rem;
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    animation: fadeIn 0.3s ease;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 12px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e0e6ed;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    color: #2c3e50;
    font-size: 1.25rem;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #7f8c8d;
    padding: 0.5rem;
    border-radius: 4px;
    min-width: 44px;
    min-height: 44px;
}

.modal-close:hover,
.modal-close:focus {
    background: #f8f9fa;
    color: #2c3e50;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid #e0e6ed;
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

/* 表单样式 */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #2c3e50;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e0e6ed;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    min-height: 44px;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: #3498db;
    outline: none;
}

.form-group input:invalid,
.form-group select:invalid {
    border-color: #e74c3c;
}

.radio-group {
    display: flex;
    gap: 1rem;
    margin-top: 0.5rem;
}

.radio-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.radio-label:hover {
    background: #f8f9fa;
}

.radio-label input[type="radio"] {
    width: auto;
    margin: 0;
    min-height: auto;
}

.radio-custom {
    width: 20px;
    height: 20px;
    border: 2px solid #bdc3c7;
    border-radius: 50%;
    position: relative;
}

.radio-label input[type="radio"]:checked + .radio-custom {
    border-color: #3498db;
}

.radio-label input[type="radio"]:checked + .radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 10px;
    height: 10px;
    background: #3498db;
    border-radius: 50%;
}

fieldset {
    border: none;
    padding: 0;
    margin: 0;
}

legend {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.help-text {
    font-size: 0.875rem;
    color: #7f8c8d;
    margin-top: 0.25rem;
}

.error-message {
    color: #e74c3c;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    display: none;
}

.error-message.show {
    display: block;
}

/* 加载和消息 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 3000;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e0e6ed;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.message-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 4000;
    max-width: 400px;
}

.message {
    background: white;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;
    border-left: 4px solid #3498db;
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.message.success {
    border-left-color: #27ae60;
}

.message.error {
    border-left-color: #e74c3c;
}

.message.warning {
    border-left-color: #f39c12;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav-menu {
        flex-wrap: wrap;
        justify-content: center;
        gap: 1rem;
    }
    
    .main-content {
        padding: 0 1rem;
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .filter-controls {
        flex-direction: column;
    }
    
    .filter-group {
        min-width: auto;
    }
    
    .cats-grid {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        width: 95%;
        margin: 1rem;
    }
    
    .modal-footer {
        flex-direction: column;
    }
    
    .radio-group {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .nav-menu {
        flex-direction: column;
        width: 100%;
    }
    
    .nav-menu a {
        justify-content: center;
        padding: 1rem;
    }
    
    .upload-area {
        padding: 2rem 1rem;
    }
    
    .media-gallery {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .btn-primary {
        background: #000;
        border: 2px solid #000;
    }
    
    .btn-secondary {
        background: #666;
        border: 2px solid #666;
    }
    
    .stat-card,
    .modal-content,
    .filter-section {
        border: 1px solid #000;
    }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 打印样式 */
@media print {
    .navbar,
    .section-actions,
    .filter-section,
    .modal,
    .loading-overlay,
    .message-container {
        display: none !important;
    }
    
    .main-content {
        max-width: none;
        margin: 0;
        padding: 0;
    }
    
    .content-section {
        display: block !important;
        page-break-after: always;
    }
    
    .stat-card,
    .cat-card {
        box-shadow: none;
        border: 1px solid #000;
    }
}