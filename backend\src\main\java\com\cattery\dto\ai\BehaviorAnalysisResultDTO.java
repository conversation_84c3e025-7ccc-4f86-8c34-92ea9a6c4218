package com.cattery.dto.ai;

import lombok.Data;
import java.util.List;

/**
 * 行为分析结果DTO
 */
@Data
public class BehaviorAnalysisResultDTO {
    
    /**
     * 行为评分
     */
    private Double behaviorScore;
    
    /**
     * 整体评估
     */
    private String overallAssessment;
    
    /**
     * 行为模式列表
     */
    private List<BehaviorPatternDTO> behaviorPatterns;
    
    /**
     * 异常行为列表
     */
    private List<BehaviorAnomalyDTO> anomalies;
    
    /**
     * 建议列表
     */
    private List<BehaviorRecommendationDTO> recommendations;
}
