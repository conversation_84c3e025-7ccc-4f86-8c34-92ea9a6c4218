# 环境配置示例文件
# 复制此文件为 .env 并根据实际情况修改配置

# 应用环境
SPRING_PROFILES_ACTIVE=prod

# 数据库配置
MYSQL_ROOT_PASSWORD=your_secure_root_password
MYSQL_DATABASE=cat_shelter_db
MYSQL_USER=catuser
MYSQL_PASSWORD=your_secure_password

# JWT 配置
JWT_SECRET=your_very_long_and_secure_jwt_secret_key_here_at_least_32_characters
JWT_EXPIRATION=86400000

# CORS 配置
CORS_ORIGINS=http://localhost,https://yourdomain.com

# 邮件配置（如果需要）
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_app_password

# 文件上传配置
UPLOAD_PATH=/app/uploads
MAX_FILE_SIZE=10MB

# 监控配置
MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE=health,info,metrics

# 日志级别
LOGGING_LEVEL_ROOT=INFO
LOGGING_LEVEL_COM_CATSHELTER=DEBUG
