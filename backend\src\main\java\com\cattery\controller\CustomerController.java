package com.cattery.controller;

import com.cattery.dto.ApiResponse;
import com.cattery.entity.Customer;
import com.cattery.entity.AdoptionRecord;
import com.cattery.service.CustomerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 客户管理控制器
 */
@RestController
@RequestMapping("/api/customers")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "客户管理", description = "客户信息管理和关系维护")
public class CustomerController {
    
    private final CustomerService customerService;
    
    /**
     * 获取客户统计
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取客户统计", description = "获取客户相关的统计数据")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getCustomerStatistics() {
        log.info("获取客户统计");
        
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalCustomers", customerService.getCustomerCount());
        statistics.put("activeCustomers", customerService.getActiveCustomerCount());
        
        // 获取本月新客户数量
        LocalDateTime startOfMonth = LocalDateTime.now().withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
        LocalDateTime now = LocalDateTime.now();
        statistics.put("newCustomersThisMonth", customerService.getNewCustomerCount(startOfMonth, now));
        
        return ResponseEntity.ok(ApiResponse.success("获取客户统计成功", statistics));
    }
    
    /**
     * 获取所有客户
     */
    @GetMapping
    @Operation(summary = "获取客户列表", description = "分页获取客户列表，支持筛选和搜索")
    public ResponseEntity<ApiResponse<Page<Customer>>> getAllCustomers(
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "客户状态") @RequestParam(required = false) Customer.Status status,
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword) {
        
        log.info("获取客户列表: page={}, size={}, status={}, keyword={}", page, size, status, keyword);
        
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        Page<Customer> customers;
        
        if (keyword != null && !keyword.trim().isEmpty()) {
            customers = customerService.searchCustomers(keyword, pageable);
        } else if (status != null) {
            customers = customerService.getCustomersByStatus(status, pageable);
        } else {
            customers = customerService.getAllCustomers(pageable);
        }
        
        return ResponseEntity.ok(ApiResponse.success("获取客户列表成功", customers));
    }
    
    /**
     * 根据ID获取客户
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取客户详情", description = "根据ID获取客户详细信息")
    public ResponseEntity<ApiResponse<Customer>> getCustomerById(
            @Parameter(description = "客户ID") @PathVariable Long id) {
        
        log.info("获取客户详情: id={}", id);
        
        return customerService.getCustomerById(id)
            .map(customer -> ResponseEntity.ok(ApiResponse.success("获取客户详情成功", customer)))
            .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * 创建客户
     */
    @PostMapping
    @Operation(summary = "创建客户", description = "创建新的客户记录")
    public ResponseEntity<ApiResponse<Customer>> createCustomer(
            @Parameter(description = "客户信息") @Valid @RequestBody Customer customer) {
        
        log.info("创建客户: name={}, email={}", customer.getName(), customer.getEmail());
        
        Customer createdCustomer = customerService.createCustomer(customer);
        return ResponseEntity.ok(ApiResponse.success("创建客户成功", createdCustomer));
    }
    
    /**
     * 更新客户
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新客户", description = "更新客户信息")
    public ResponseEntity<ApiResponse<Customer>> updateCustomer(
            @Parameter(description = "客户ID") @PathVariable Long id,
            @Parameter(description = "更新的客户信息") @Valid @RequestBody Customer customer) {
        
        log.info("更新客户: id={}", id);
        
        Customer updatedCustomer = customerService.updateCustomer(id, customer);
        return ResponseEntity.ok(ApiResponse.success("更新客户成功", updatedCustomer));
    }
    
    /**
     * 更新客户状态
     */
    @PatchMapping("/{id}/status")
    @Operation(summary = "更新客户状态", description = "更新客户的状态")
    public ResponseEntity<ApiResponse<Customer>> updateCustomerStatus(
            @Parameter(description = "客户ID") @PathVariable Long id,
            @Parameter(description = "新状态") @RequestParam Customer.Status status) {
        
        log.info("更新客户状态: id={}, status={}", id, status);
        
        Customer updatedCustomer = customerService.updateCustomerStatus(id, status);
        return ResponseEntity.ok(ApiResponse.success("更新客户状态成功", updatedCustomer));
    }
    
    /**
     * 删除客户
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除客户", description = "删除客户记录")
    public ResponseEntity<ApiResponse<Void>> deleteCustomer(
            @Parameter(description = "客户ID") @PathVariable Long id) {
        
        log.info("删除客户: id={}", id);
        
        customerService.deleteCustomer(id);
        return ResponseEntity.ok(ApiResponse.success("删除客户成功", null));
    }
    
    /**
     * 根据邮箱获取客户
     */
    @GetMapping("/by-email")
    @Operation(summary = "根据邮箱获取客户", description = "根据邮箱地址获取客户信息")
    public ResponseEntity<ApiResponse<Customer>> getCustomerByEmail(
            @Parameter(description = "邮箱地址") @RequestParam String email) {
        
        log.info("根据邮箱获取客户: email={}", email);
        
        return customerService.getCustomerByEmail(email)
            .map(customer -> ResponseEntity.ok(ApiResponse.success("获取客户信息成功", customer)))
            .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * 根据电话获取客户
     */
    @GetMapping("/by-phone")
    @Operation(summary = "根据电话获取客户", description = "根据电话号码获取客户信息")
    public ResponseEntity<ApiResponse<Customer>> getCustomerByPhone(
            @Parameter(description = "电话号码") @RequestParam String phone) {
        
        log.info("根据电话获取客户: phone={}", phone);
        
        return customerService.getCustomerByPhone(phone)
            .map(customer -> ResponseEntity.ok(ApiResponse.success("获取客户信息成功", customer)))
            .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * 获取客户的领养记录
     */
    @GetMapping("/{id}/adoptions")
    @Operation(summary = "获取客户的领养记录", description = "获取指定客户的所有领养记录")
    public ResponseEntity<ApiResponse<List<AdoptionRecord>>> getCustomerAdoptions(
            @Parameter(description = "客户ID") @PathVariable Long id) {
        
        log.info("获取客户的领养记录: customerId={}", id);
        
        List<AdoptionRecord> adoptions = customerService.getCustomerAdoptions(id);
        return ResponseEntity.ok(ApiResponse.success("获取客户领养记录成功", adoptions));
    }
}
