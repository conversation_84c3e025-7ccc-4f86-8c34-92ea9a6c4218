package com.cattery.service.ai;

import com.cattery.dto.ai.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;

/**
 * 行为分析服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BehaviorAnalysisService {

    private final RestTemplate restTemplate;

    @Value("${ai.behavior-analysis.api.url:http://localhost:5002}")
    private String apiUrl;

    @Value("${ai.behavior-analysis.api.key:}")
    private String apiKey;

    /**
     * 分析猫咪行为
     */
    public BehaviorAnalysisResultDTO analyzeBehavior(BehaviorAnalysisInputDTO input) {
        try {
            log.info("开始行为分析，猫咪ID: {}", input.getCatId());
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            if (!apiKey.isEmpty()) {
                headers.set("Authorization", "Bearer " + apiKey);
            }
            
            Map<String, Object> requestBody = buildAnalysisRequest(input);
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            
            @SuppressWarnings("rawtypes")
            ResponseEntity<Map> response = restTemplate.postForEntity(
                apiUrl + "/api/v1/analyze", request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> responseBody = (Map<String, Object>) response.getBody();
                return parseAnalysisResult(responseBody);
            } else {
                throw new RuntimeException("行为分析服务调用失败");
            }
            
        } catch (Exception e) {
            log.error("行为分析失败", e);
            return createDefaultAnalysisResult();
        }
    }

    /**
     * 检查服务可用性
     */
    public boolean isAvailable() {
        try {
            @SuppressWarnings("rawtypes")
            ResponseEntity<Map> response = restTemplate.getForEntity(
                apiUrl + "/api/v1/health", Map.class);
            return response.getStatusCode() == HttpStatus.OK;
        } catch (Exception e) {
            log.warn("行为分析服务不可用: {}", e.getMessage());
            return false;
        }
    }

    // 私有辅助方法

    private Map<String, Object> buildAnalysisRequest(BehaviorAnalysisInputDTO input) {
        Map<String, Object> request = new HashMap<>();
        request.put("cat_id", input.getCatId());
        request.put("age", input.getAge());
        request.put("breed", input.getBreed());
        request.put("gender", input.getGender());
        
        if (input.getBehaviorObservations() != null) {
            request.put("behavior_observations", input.getBehaviorObservations());
        }
        
        if (input.getEnvironmentFactors() != null) {
            request.put("environment_factors", input.getEnvironmentFactors());
        }
        
        if (input.getObservationPeriod() != null) {
            request.put("observation_period", input.getObservationPeriod());
        }
        
        return request;
    }

    private BehaviorAnalysisResultDTO parseAnalysisResult(Map<String, Object> responseBody) {
        BehaviorAnalysisResultDTO result = new BehaviorAnalysisResultDTO();
        
        result.setBehaviorScore(getDoubleValue(responseBody, "behavior_score"));
        result.setOverallAssessment(getStringValue(responseBody, "overall_assessment"));
        
        // 解析行为模式
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> patterns = (List<Map<String, Object>>) responseBody.get("behavior_patterns");
        if (patterns != null) {
            List<BehaviorPatternDTO> behaviorPatterns = new ArrayList<>();
            for (Map<String, Object> pattern : patterns) {
                BehaviorPatternDTO behaviorPattern = new BehaviorPatternDTO();
                behaviorPattern.setPatternType(getStringValue(pattern, "pattern_type"));
                behaviorPattern.setFrequency(getDoubleValue(pattern, "frequency"));
                behaviorPattern.setIntensity(getDoubleValue(pattern, "intensity"));
                behaviorPattern.setAbnormalityScore(getDoubleValue(pattern, "abnormality_score"));
                behaviorPattern.setDescription(getStringValue(pattern, "description"));
                behaviorPatterns.add(behaviorPattern);
            }
            result.setBehaviorPatterns(behaviorPatterns);
        }
        
        // 解析异常行为
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> anomalies = (List<Map<String, Object>>) responseBody.get("anomalies");
        if (anomalies != null) {
            List<BehaviorAnomalyDTO> behaviorAnomalies = new ArrayList<>();
            for (Map<String, Object> anomaly : anomalies) {
                BehaviorAnomalyDTO behaviorAnomaly = new BehaviorAnomalyDTO();
                behaviorAnomaly.setAnomalyType(getStringValue(anomaly, "anomaly_type"));
                behaviorAnomaly.setSeverity(getStringValue(anomaly, "severity"));
                behaviorAnomaly.setConfidence(getDoubleValue(anomaly, "confidence"));
                behaviorAnomaly.setDescription(getStringValue(anomaly, "description"));
                @SuppressWarnings("unchecked")
                List<String> possibleCauses = (List<String>) anomaly.get("possible_causes");
                behaviorAnomaly.setPossibleCauses(possibleCauses);
                behaviorAnomalies.add(behaviorAnomaly);
            }
            result.setAnomalies(behaviorAnomalies);
        }
        
        return result;
    }

    private BehaviorAnalysisResultDTO createDefaultAnalysisResult() {
        BehaviorAnalysisResultDTO result = new BehaviorAnalysisResultDTO();
        result.setBehaviorScore(75.0);
        result.setOverallAssessment("正常");
        result.setBehaviorPatterns(new ArrayList<>());
        result.setAnomalies(new ArrayList<>());
        // 创建简单的建议
        List<BehaviorRecommendationDTO> recommendations = new ArrayList<>();
        BehaviorRecommendationDTO rec1 = new BehaviorRecommendationDTO();
        rec1.setContent("继续观察猫咪行为");
        rec1.setPriority("LOW");
        recommendations.add(rec1);

        BehaviorRecommendationDTO rec2 = new BehaviorRecommendationDTO();
        rec2.setContent("保持良好的生活环境");
        rec2.setPriority("MEDIUM");
        recommendations.add(rec2);

        result.setRecommendations(recommendations);
        return result;
    }

    // 工具方法
    private String getStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : "";
    }

    private double getDoubleValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return 0.0;
    }
}
