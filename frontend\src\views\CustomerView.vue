<template>
  <div>
    <el-card>
      <template #header>
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <span>客户管理</span>
          <el-button type="primary" @click="handleOpenDialog()">添加新客户</el-button>
        </div>
      </template>
      
      <el-table :data="customers" v-loading="loading" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="姓名" width="120" />
        <el-table-column prop="phone" label="电话" width="150" />
        <el-table-column prop="address" label="地址" />
        <el-table-column prop="createdAt" label="创建时间" width="180" />
        <el-table-column label="操作" width="180">
          <template #default="scope">
            <el-button size="small" @click="handleOpenDialog(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加/编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="500px">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="电话" prop="phone">
          <el-input v-model="form.phone" />
        </el-form-item>
        <el-form-item label="地址" prop="address">
          <el-input v-model="form.address" type="textarea" :rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { customerApi } from '@/api/customers';
import type { Customer } from '@/types';

const customers = ref<Customer[]>([]);
const loading = ref(true);
const dialogVisible = ref(false);
const form = ref<Partial<Customer>>({});
const formRef = ref<any>(null);

const dialogTitle = computed(() => (form.value.id ? '编辑客户' : '添加新客户'));

const rules = {
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  phone: [{ required: true, message: '请输入电话', trigger: 'blur' }]
};

async function fetchData() {
  loading.value = true;
  try {
    customers.value = await customerApi.getAll();
  } catch (error) {
    ElMessage.error('获取客户列表失败');
  } finally {
    loading.value = false;
  }
}

function handleOpenDialog(customer: Customer | null = null) {
  dialogVisible.value = true;
  form.value = customer ? { ...customer } : { id: undefined, name: '', phone: '', address: '' };
}

async function handleSubmit() {
  if (!formRef.value) return;
  await formRef.value.validate();
  const isEdit = !!form.value.id;

  try {
    if (isEdit && form.value.id) {
      await customerApi.update(form.value.id, form.value);
      ElMessage.success('更新成功');
    } else {
      await customerApi.create(form.value as Omit<Customer, 'id'>);
      ElMessage.success('添加成功');
    }
    dialogVisible.value = false;
    fetchData();
  } catch (error) {
    ElMessage.error(error instanceof Error ? error.message : '操作失败');
  }
}

async function handleDelete(id: number) {
  try {
    await ElMessageBox.confirm('确定要删除这个客户吗？', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });

    await customerApi.delete(id);
    ElMessage.success('删除成功');
    fetchData();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
    }
  }
}

onMounted(() => {
  fetchData();
});
</script>
