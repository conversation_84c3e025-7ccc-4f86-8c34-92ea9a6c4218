import type { NavigationGuardNext, RouteLocationNormalized } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

// 需要认证的路由列表
const authRequiredRoutes = [
  '/cats',
  '/breeds',
  '/customers',
  '/products',
  '/health-records',
  '/orders',
  '/dashboard'
]

// 公开路由列表（不需要认证）
const publicRoutes = [
  '/login',
  '/about'
]

/**
 * 认证守卫
 * 检查用户是否已登录，未登录则重定向到登录页
 */
export const authGuard = (
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext
) => {
  const authStore = useAuthStore()
  
  // 初始化认证状态
  authStore.initializeAuth()
  
  // 检查是否是公开路由
  const isPublicRoute = publicRoutes.some(route => to.path.startsWith(route))
  
  // 检查是否需要认证
  const requiresAuth = authRequiredRoutes.some(route => to.path.startsWith(route)) || 
                      (!isPublicRoute && to.path !== '/')
  
  if (requiresAuth) {
    if (!authStore.isAuthenticated) {
      ElMessage.warning('请先登录')
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }
    
    // 检查token是否过期
    if (authStore.checkTokenExpiration()) {
      ElMessage.warning('登录已过期，请重新登录')
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }
  }
  
  // 如果已登录用户访问登录页，重定向到首页
  if (to.path === '/login' && authStore.isAuthenticated) {
    next('/')
    return
  }
  
  next()
}

/**
 * 权限守卫
 * 检查用户是否有访问特定路由的权限
 */
export const permissionGuard = (
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext
) => {
  const authStore = useAuthStore()
  
  // 如果未登录，跳过权限检查（由认证守卫处理）
  if (!authStore.isAuthenticated) {
    next()
    return
  }
  
  // 定义路由权限映射（使用后端实际的权限名称）
  const routePermissions: Record<string, string[]> = {
    '/cats': ['CAT_READ'],
    '/cats/create': ['CAT_CREATE'],
    '/cats/edit': ['CAT_UPDATE'],
    '/breeds': ['BREED_READ'],
    '/breeds/create': ['BREED_CREATE'],
    '/breeds/edit': ['BREED_UPDATE'],
    '/customers': ['CUSTOMER_READ'],
    '/customers/create': ['CUSTOMER_CREATE'],
    '/customers/edit': ['CUSTOMER_UPDATE'],
    '/products': ['PRODUCT_READ'],
    '/products/create': ['PRODUCT_CREATE'],
    '/products/edit': ['PRODUCT_UPDATE'],
    '/health-records': ['HEALTH_READ'],
    '/health-records/create': ['HEALTH_CREATE'],
    '/health-records/edit': ['HEALTH_UPDATE'],
    '/orders': ['ORDER_READ'],
    '/orders/create': ['ORDER_CREATE'],
    '/orders/edit': ['ORDER_UPDATE'],
    '/dashboard': ['DASHBOARD_READ']
  }
  
  // 检查当前路由是否需要特定权限
  const requiredPermissions = routePermissions[to.path]
  
  if (requiredPermissions) {
    const hasPermission = requiredPermissions.some(permission => 
      authStore.hasPermission(permission)
    )
    
    if (!hasPermission) {
      ElMessage.error('您没有访问此页面的权限')
      next('/403') // 重定向到403页面
      return
    }
  }
  
  next()
}

/**
 * 角色守卫
 * 检查用户是否有特定角色
 */
export const roleGuard = (requiredRoles: string[]) => {
  return (
    to: RouteLocationNormalized,
    from: RouteLocationNormalized,
    next: NavigationGuardNext
  ) => {
    const authStore = useAuthStore()
    
    if (!authStore.isAuthenticated) {
      next()
      return
    }
    
    const hasRole = requiredRoles.some(role => authStore.hasRole(role))
    
    if (!hasRole) {
      ElMessage.error('您没有访问此页面的权限')
      next('/403')
      return
    }
    
    next()
  }
}

/**
 * 管理员守卫
 * 只允许管理员访问
 */
export const adminGuard = roleGuard(['ADMIN', 'SUPER_ADMIN'])

/**
 * 页面标题守卫
 * 根据路由设置页面标题
 */
export const titleGuard = (
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext
) => {
  const routeTitles: Record<string, string> = {
    '/': '首页',
    '/login': '登录',
    '/cats': '猫咪管理',
    '/breeds': '品种管理',
    '/customers': '客户管理',
    '/products': '商品管理',
    '/health-records': '健康记录',
    '/orders': '订单管理',
    '/dashboard': '数据统计',
    '/about': '关于我们'
  }
  
  const title = routeTitles[to.path] || '猫舍管理系统'
  document.title = `${title} - 猫舍管理系统`
  
  next()
}
