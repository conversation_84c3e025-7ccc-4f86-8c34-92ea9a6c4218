@echo off
echo ========================================
echo 猫舍管理系统 - 简化启动脚本
echo ========================================
echo.

echo 检测到Node.js环境问题，使用简化方案启动系统...
echo.

echo 1. 启动后端服务 (Spring Boot)...
cd backend
echo 正在启动后端服务，请稍候...
start "后端服务" cmd /k "mvnw.cmd spring-boot:run"
echo 后端服务启动中... (端口: 8080)
echo.

echo 等待后端服务启动...
timeout /t 10 /nobreak >nul
echo.

echo 2. 启动前端页面 (静态文件)...
cd ..
echo.
echo 由于Node.js环境问题，将直接打开静态HTML文件
echo.

echo 打开登录页面...
start "" "login-test.html"
echo.

echo ========================================
echo 系统启动完成!
echo ========================================
echo.
echo 后端API服务: http://localhost:8080
echo 前端登录页面: 已在浏览器中打开
echo.
echo 测试账号:
echo   用户名: admin
echo   密码: 123456
echo.
echo 注意事项:
echo 1. 确保后端服务完全启动后再登录
echo 2. 如需修复Node.js环境，请运行 fix-nodejs.bat
echo 3. 修复后可使用完整的开发环境
echo.

echo ========================================
echo 可用页面列表:
echo ========================================
echo.
echo 主要功能页面:
echo   - login-test.html          (登录页面)
echo   - dashboard.html           (仪表盘)
echo   - cats-management.html     (猫咪管理)
echo   - customers-management.html (客户管理)
echo   - health-management.html   (健康记录)
echo   - breeding-management.html (繁育管理)
echo   - financial-reports.html  (财务报表)
echo   - data-management.html     (数据管理)
echo   - system-monitor.html      (系统监控)
echo.
echo 测试页面:
echo   - api-test.html           (API测试)
echo   - test-api.html           (接口测试)
echo.

echo ========================================
echo 故障排除:
echo ========================================
echo.
echo 如果遇到问题:
echo 1. 后端启动失败 - 检查Java环境和端口8080是否被占用
echo 2. 前端无法访问API - 确认后端服务已启动
echo 3. 登录失败 - 使用测试账号 admin/123456
echo 4. Node.js问题 - 运行 fix-nodejs.bat 修复
echo.

pause
