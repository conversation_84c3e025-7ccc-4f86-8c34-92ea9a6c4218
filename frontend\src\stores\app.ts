import { ref } from 'vue'
import { defineStore } from 'pinia'

export const useAppStore = defineStore('app', () => {
  // 状态
  const title = ref(import.meta.env.VITE_APP_TITLE || '猫舍管理系统')
  const loading = ref(false)
  const sidebarCollapsed = ref(false)

  // 操作
  function setLoading(value: boolean) {
    loading.value = value
  }

  function toggleSidebar() {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }

  function setSidebarCollapsed(value: boolean) {
    sidebarCollapsed.value = value
  }

  return {
    // 状态
    title,
    loading,
    sidebarCollapsed,
    // 操作
    setLoading,
    toggleSidebar,
    setSidebarCollapsed
  }
})
