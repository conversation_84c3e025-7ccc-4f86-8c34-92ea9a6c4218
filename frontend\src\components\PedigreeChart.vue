<template>
  <div class="pedigree-chart">
    <div v-if="loading" class="loading">
      <el-skeleton :rows="5" animated />
    </div>
    
    <div v-else-if="pedigreeData" class="pedigree-container">
      <!-- 工具栏 -->
      <div class="toolbar">
        <el-button-group>
          <el-button size="small" @click="zoomIn">放大</el-button>
          <el-button size="small" @click="zoomOut">缩小</el-button>
          <el-button size="small" @click="resetZoom">重置</el-button>
        </el-button-group>
        <el-button size="small" type="primary" @click="showFullPedigree = !showFullPedigree">
          {{ showFullPedigree ? '简化视图' : '完整谱系' }}
        </el-button>
      </div>
      
      <!-- SVG 血统图 -->
      <div class="chart-container" ref="chartContainer">
        <svg
          :width="svgWidth"
          :height="svgHeight"
          :viewBox="`0 0 ${svgWidth} ${svgHeight}`"
          class="pedigree-svg"
        >
          <!-- 连接线 -->
          <g class="connections">
            <line
              v-for="connection in connections"
              :key="`${connection.from}-${connection.to}`"
              :x1="connection.x1"
              :y1="connection.y1"
              :x2="connection.x2"
              :y2="connection.y2"
              stroke="#409EFF"
              stroke-width="2"
            />
          </g>
          
          <!-- 猫咪节点 -->
          <g class="nodes">
            <g
              v-for="node in nodes"
              :key="node.id"
              :transform="`translate(${node.x}, ${node.y})`"
              class="node"
              @click="handleNodeClick(node)"
            >
              <!-- 节点背景 -->
              <rect
                :width="nodeWidth"
                :height="nodeHeight"
                :rx="8"
                :class="getNodeClass(node)"
                stroke="#ddd"
                stroke-width="1"
              />
              
              <!-- 猫咪照片 -->
              <image
                v-if="node.photoUrl"
                :href="node.photoUrl"
                :x="5"
                :y="5"
                :width="nodeWidth - 10"
                :height="60"
                preserveAspectRatio="xMidYMid slice"
                clip-path="url(#photo-clip)"
              />
              
              <!-- 猫咪信息 -->
              <text :x="nodeWidth / 2" :y="75" text-anchor="middle" class="node-name">
                {{ node.name }}
              </text>
              <text :x="nodeWidth / 2" :y="90" text-anchor="middle" class="node-breed">
                {{ node.breedName }}
              </text>
              <text :x="nodeWidth / 2" :y="105" text-anchor="middle" class="node-gender">
                {{ node.gender === 'MALE' ? '♂' : '♀' }}
              </text>
            </g>
          </g>
          
          <!-- 定义裁剪路径 -->
          <defs>
            <clipPath id="photo-clip">
              <rect x="5" y="5" :width="nodeWidth - 10" height="60" rx="4" />
            </clipPath>
          </defs>
        </svg>
      </div>
      
      <!-- 图例 -->
      <div class="legend">
        <div class="legend-item">
          <div class="legend-color male"></div>
          <span>雄性</span>
        </div>
        <div class="legend-item">
          <div class="legend-color female"></div>
          <span>雌性</span>
        </div>
        <div class="legend-item">
          <div class="legend-color current"></div>
          <span>当前猫咪</span>
        </div>
      </div>
    </div>
    
    <el-empty v-else description="暂无血统信息" />
    
    <!-- 猫咪详情弹窗 -->
    <el-dialog v-model="showNodeDialog" :title="selectedNode?.name" width="500px">
      <div v-if="selectedNode">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="名字">{{ selectedNode.name }}</el-descriptions-item>
          <el-descriptions-item label="品种">{{ selectedNode.breedName }}</el-descriptions-item>
          <el-descriptions-item label="性别">
            {{ selectedNode.gender === 'MALE' ? '雄性' : '雌性' }}
          </el-descriptions-item>
          <el-descriptions-item label="出生日期">{{ selectedNode.dateOfBirth }}</el-descriptions-item>
          <el-descriptions-item label="毛色">{{ selectedNode.color }}</el-descriptions-item>
          <el-descriptions-item label="关系">{{ selectedNode.relationship }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="dialog-actions">
          <el-button type="primary" @click="viewCatDetail(selectedNode.id)">查看详情</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { catApi } from '@/api';
import type { PedigreeNode } from '@/types';

interface Connection {
  from: number;
  to: number;
  x1: number;
  y1: number;
  x2: number;
  y2: number;
}

const props = defineProps<{
  catId: number;
}>();

const router = useRouter();

const pedigreeData = ref<any>(null);
const loading = ref(true);
const showFullPedigree = ref(false);
const showNodeDialog = ref(false);
const selectedNode = ref<PedigreeNode | null>(null);
const chartContainer = ref<HTMLElement>();

const zoomLevel = ref(1);
const nodeWidth = 120;
const nodeHeight = 110;
const generationSpacing = 200;
const nodeSpacing = 140;

const svgWidth = computed(() => 800 * zoomLevel.value);
const svgHeight = computed(() => 600 * zoomLevel.value);

const nodes = computed(() => {
  if (!pedigreeData.value) return [];
  
  const result: PedigreeNode[] = [];
  const maxGenerations = showFullPedigree.value ? 4 : 3;
  
  // 递归构建节点
  function buildNodes(cat: any, generation: number, position: number, relationship: string) {
    if (generation > maxGenerations || !cat) return;
    
    const node: PedigreeNode = {
      id: cat.id,
      name: cat.name,
      breedName: cat.breedName || '未知品种',
      gender: cat.gender,
      dateOfBirth: cat.dateOfBirth,
      color: cat.color,
      photoUrl: cat.primaryPhoto,
      relationship,
      generation,
      x: generation * generationSpacing,
      y: position * nodeSpacing + 50
    };
    
    result.push(node);
    
    // 递归添加父母
    if (cat.father) {
      buildNodes(cat.father, generation + 1, position * 2, '父亲');
    }
    if (cat.mother) {
      buildNodes(cat.mother, generation + 1, position * 2 + 1, '母亲');
    }
  }
  
  buildNodes(pedigreeData.value, 0, 0, '本猫');
  return result;
});

const connections = computed(() => {
  const result: Connection[] = [];
  
  nodes.value.forEach(node => {
    if (node.generation > 0) {
      const parentPosition = Math.floor((node.y - 50) / nodeSpacing / 2);
      const parentNode = nodes.value.find(n => 
        n.generation === node.generation - 1 && 
        Math.floor((n.y - 50) / nodeSpacing) === parentPosition
      );
      
      if (parentNode) {
        result.push({
          from: parentNode.id,
          to: node.id,
          x1: parentNode.x + nodeWidth,
          y1: parentNode.y + nodeHeight / 2,
          x2: node.x,
          y2: node.y + nodeHeight / 2
        });
      }
    }
  });
  
  return result;
});

async function fetchPedigreeData() {
  try {
    loading.value = true;
    const data = await catApi.getPedigree(props.catId);
    pedigreeData.value = data;
  } catch (error) {
    console.error('获取血统信息失败:', error);
    ElMessage.error('获取血统信息失败');
  } finally {
    loading.value = false;
  }
}

function handleNodeClick(node: PedigreeNode) {
  selectedNode.value = node;
  showNodeDialog.value = true;
}

function viewCatDetail(catId: number) {
  router.push(`/cats/${catId}`);
  showNodeDialog.value = false;
}

function zoomIn() {
  zoomLevel.value = Math.min(zoomLevel.value * 1.2, 3);
}

function zoomOut() {
  zoomLevel.value = Math.max(zoomLevel.value / 1.2, 0.5);
}

function resetZoom() {
  zoomLevel.value = 1;
}

function getNodeClass(node: PedigreeNode) {
  const classes = ['node-rect'];
  
  if (node.generation === 0) {
    classes.push('current-cat');
  } else if (node.gender === 'MALE') {
    classes.push('male-cat');
  } else {
    classes.push('female-cat');
  }
  
  return classes.join(' ');
}

onMounted(() => {
  fetchPedigreeData();
});
</script>

<style scoped>
.pedigree-chart {
  width: 100%;
  height: 100%;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 6px;
}

.chart-container {
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: auto;
  background: #fff;
  max-height: 500px;
}

.pedigree-svg {
  display: block;
}

.node {
  cursor: pointer;
  transition: all 0.3s ease;
}

.node:hover {
  transform: scale(1.05);
}

.node-rect {
  fill: #f0f9ff;
  transition: all 0.3s ease;
}

.node-rect.current-cat {
  fill: #fef3c7;
  stroke: #f59e0b;
  stroke-width: 3;
}

.node-rect.male-cat {
  fill: #dbeafe;
  stroke: #3b82f6;
}

.node-rect.female-cat {
  fill: #fce7f3;
  stroke: #ec4899;
}

.node:hover .node-rect {
  stroke-width: 3;
}

.node-name {
  font-size: 12px;
  font-weight: bold;
  fill: #333;
}

.node-breed {
  font-size: 10px;
  fill: #666;
}

.node-gender {
  font-size: 14px;
  font-weight: bold;
  fill: #333;
}

.legend {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 15px;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 6px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.legend-color.male {
  background: #dbeafe;
  border-color: #3b82f6;
}

.legend-color.female {
  background: #fce7f3;
  border-color: #ec4899;
}

.legend-color.current {
  background: #fef3c7;
  border-color: #f59e0b;
}

.loading {
  padding: 20px;
}

.dialog-actions {
  margin-top: 20px;
  text-align: right;
}
</style>
