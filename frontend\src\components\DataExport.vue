<template>
  <div class="data-export">
    <el-button 
      :icon="Download" 
      @click="showExportDialog = true"
      :loading="exporting"
    >
      导出数据
    </el-button>

    <el-dialog
      v-model="showExportDialog"
      title="导出数据"
      width="600px"
      append-to-body
    >
      <el-form :model="exportForm" label-width="100px">
        <el-form-item label="导出类型">
          <el-select v-model="exportForm.type" placeholder="请选择导出类型" @change="handleTypeChange">
            <el-option
              v-for="option in exportOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="文件格式">
          <el-radio-group v-model="exportForm.format">
            <el-radio value="excel">Excel (.xlsx)</el-radio>
            <el-radio value="csv">CSV (.csv)</el-radio>
            <el-radio value="pdf" v-if="supportsPdf">PDF (.pdf)</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="日期范围" v-if="showDateRange">
          <el-date-picker
            v-model="exportForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>

        <el-form-item label="筛选条件" v-if="showFilters">
          <div class="filter-section">
            <!-- 猫咪筛选 -->
            <div v-if="exportForm.type === 'cats'" class="filter-group">
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-select v-model="exportForm.filters.breed" placeholder="品种" clearable>
                    <el-option
                      v-for="breed in catBreeds"
                      :key="breed"
                      :label="breed"
                      :value="breed"
                    />
                  </el-select>
                </el-col>
                <el-col :span="12">
                  <el-select v-model="exportForm.filters.status" placeholder="状态" clearable>
                    <el-option label="可售" value="AVAILABLE" />
                    <el-option label="已预订" value="RESERVED" />
                    <el-option label="已售出" value="SOLD" />
                    <el-option label="繁育中" value="BREEDING" />
                    <el-option label="治疗中" value="MEDICAL" />
                    <el-option label="已退役" value="RETIRED" />
                  </el-select>
                </el-col>
              </el-row>
            </div>

            <!-- 客户筛选 -->
            <div v-if="exportForm.type === 'customers'" class="filter-group">
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-select v-model="exportForm.filters.customerType" placeholder="客户类型" clearable>
                    <el-option label="潜在客户" value="POTENTIAL" />
                    <el-option label="活跃客户" value="ACTIVE" />
                    <el-option label="已购买" value="ADOPTED" />
                    <el-option label="繁育者" value="BREEDER" />
                    <el-option label="VIP客户" value="VIP" />
                  </el-select>
                </el-col>
                <el-col :span="12">
                  <el-select v-model="exportForm.filters.status" placeholder="状态" clearable>
                    <el-option label="活跃" value="ACTIVE" />
                    <el-option label="非活跃" value="INACTIVE" />
                    <el-option label="黑名单" value="BLACKLISTED" />
                  </el-select>
                </el-col>
              </el-row>
            </div>

            <!-- 财务筛选 -->
            <div v-if="exportForm.type === 'finance'" class="filter-group">
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-select v-model="exportForm.filters.recordType" placeholder="记录类型" clearable>
                    <el-option label="收入" value="INCOME" />
                    <el-option label="支出" value="EXPENSE" />
                  </el-select>
                </el-col>
                <el-col :span="12">
                  <el-input v-model="exportForm.filters.category" placeholder="分类" clearable />
                </el-col>
              </el-row>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="导出字段" v-if="showFieldSelection">
          <el-checkbox-group v-model="exportForm.fields">
            <el-checkbox
              v-for="field in availableFields"
              :key="field.value"
              :value="field.value"
            >
              {{ field.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="其他选项">
          <el-checkbox v-model="exportForm.includeImages">包含图片</el-checkbox>
          <el-checkbox v-model="exportForm.includeDeleted">包含已删除记录</el-checkbox>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showExportDialog = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleExport"
            :loading="exporting"
          >
            {{ exporting ? '导出中...' : '开始导出' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导出进度对话框 -->
    <el-dialog
      v-model="showProgressDialog"
      title="导出进度"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="progress-content">
        <el-progress 
          :percentage="exportProgress" 
          :status="exportStatus"
          :stroke-width="8"
        />
        <p class="progress-text">{{ progressText }}</p>
      </div>
      
      <template #footer>
        <el-button 
          v-if="exportStatus === 'success'"
          type="primary" 
          @click="downloadExportedFile"
        >
          下载文件
        </el-button>
        <el-button @click="closeProgressDialog">
          {{ exportStatus === 'success' ? '关闭' : '取消' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Download } from '@element-plus/icons-vue'
import { downloadFile } from '@/utils'

interface ExportOption {
  value: string
  label: string
  api?: string
}

interface ExportField {
  value: string
  label: string
}

interface Props {
  exportType?: string
  exportOptions?: ExportOption[]
  customFields?: ExportField[]
  onExport?: (params: any) => Promise<Blob>
}

const props = withDefaults(defineProps<Props>(), {
  exportType: '',
  exportOptions: () => [
    { value: 'cats', label: '猫咪数据', api: '/api/cats/export' },
    { value: 'customers', label: '客户数据', api: '/api/customers/export' },
    { value: 'health', label: '健康记录', api: '/api/health/export' },
    { value: 'breeding', label: '繁育记录', api: '/api/breeding/export' },
    { value: 'finance', label: '财务记录', api: '/api/finance/export' },
    { value: 'inventory', label: '库存数据', api: '/api/inventory/export' }
  ]
})

// 响应式数据
const showExportDialog = ref(false)
const showProgressDialog = ref(false)
const exporting = ref(false)
const exportProgress = ref(0)
const exportStatus = ref<'success' | 'exception' | undefined>(undefined)
const progressText = ref('')
const exportedFileUrl = ref('')

const exportForm = ref({
  type: props.exportType || '',
  format: 'excel',
  dateRange: [] as string[],
  filters: {} as Record<string, any>,
  fields: [] as string[],
  includeImages: false,
  includeDeleted: false
})

// 计算属性
const showDateRange = computed(() => {
  return ['health', 'breeding', 'finance'].includes(exportForm.value.type)
})

const showFilters = computed(() => {
  return exportForm.value.type !== ''
})

const showFieldSelection = computed(() => {
  return exportForm.value.type !== ''
})

const supportsPdf = computed(() => {
  return ['cats', 'customers'].includes(exportForm.value.type)
})

const availableFields = computed(() => {
  const fieldMap: Record<string, ExportField[]> = {
    cats: [
      { value: 'name', label: '名称' },
      { value: 'breed', label: '品种' },
      { value: 'gender', label: '性别' },
      { value: 'birthDate', label: '出生日期' },
      { value: 'color', label: '颜色' },
      { value: 'weight', label: '体重' },
      { value: 'price', label: '价格' },
      { value: 'status', label: '状态' },
      { value: 'isVaccinated', label: '疫苗状态' },
      { value: 'isSterilized', label: '绝育状态' },
      { value: 'microchipId', label: '芯片号' },
      { value: 'description', label: '描述' }
    ],
    customers: [
      { value: 'name', label: '姓名' },
      { value: 'email', label: '邮箱' },
      { value: 'phone', label: '电话' },
      { value: 'address', label: '地址' },
      { value: 'customerType', label: '客户类型' },
      { value: 'status', label: '状态' },
      { value: 'preferredBreed', label: '偏好品种' },
      { value: 'budgetRange', label: '预算范围' }
    ],
    finance: [
      { value: 'type', label: '类型' },
      { value: 'category', label: '分类' },
      { value: 'amount', label: '金额' },
      { value: 'description', label: '描述' },
      { value: 'transactionDate', label: '交易日期' },
      { value: 'paymentMethod', label: '支付方式' },
      { value: 'status', label: '状态' }
    ]
  }
  
  return props.customFields || fieldMap[exportForm.value.type] || []
})

const catBreeds = ref(['英国短毛猫', '美国短毛猫', '波斯猫', '暹罗猫', '布偶猫', '缅因猫'])

// 方法
const handleTypeChange = (type: string) => {
  exportForm.value.filters = {}
  exportForm.value.fields = availableFields.value.map(field => field.value)
}

const handleExport = async () => {
  if (!exportForm.value.type) {
    ElMessage.warning('请选择导出类型')
    return
  }

  try {
    exporting.value = true
    showExportDialog.value = false
    showProgressDialog.value = true
    exportProgress.value = 0
    exportStatus.value = undefined
    progressText.value = '准备导出...'

    // 模拟导出进度
    const progressInterval = setInterval(() => {
      if (exportProgress.value < 90) {
        exportProgress.value += Math.random() * 20
        progressText.value = `正在导出数据... ${Math.round(exportProgress.value)}%`
      }
    }, 500)

    let blob: Blob

    if (props.onExport) {
      // 使用自定义导出函数
      blob = await props.onExport(exportForm.value)
    } else {
      // 使用默认导出API
      const option = props.exportOptions.find(opt => opt.value === exportForm.value.type)
      if (!option?.api) {
        throw new Error('未找到对应的导出API')
      }

      const params = {
        format: exportForm.value.format,
        ...exportForm.value.filters,
        fields: exportForm.value.fields.join(','),
        includeImages: exportForm.value.includeImages,
        includeDeleted: exportForm.value.includeDeleted
      }

      if (exportForm.value.dateRange.length === 2) {
        params.startDate = exportForm.value.dateRange[0]
        params.endDate = exportForm.value.dateRange[1]
      }

      // 这里应该调用实际的API
      // blob = await api.get(option.api, { params, responseType: 'blob' })
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000))
      blob = new Blob(['模拟导出数据'], { type: 'application/octet-stream' })
    }

    clearInterval(progressInterval)
    exportProgress.value = 100
    exportStatus.value = 'success'
    progressText.value = '导出完成！'

    // 创建下载链接
    exportedFileUrl.value = URL.createObjectURL(blob)

    ElMessage.success('数据导出成功')
  } catch (error) {
    exportStatus.value = 'exception'
    progressText.value = '导出失败'
    ElMessage.error('导出失败: ' + (error as Error).message)
  } finally {
    exporting.value = false
  }
}

const downloadExportedFile = () => {
  if (exportedFileUrl.value) {
    const fileName = `${exportForm.value.type}_export_${new Date().toISOString().split('T')[0]}.${exportForm.value.format === 'excel' ? 'xlsx' : exportForm.value.format}`
    downloadFile(exportedFileUrl.value, fileName)
    closeProgressDialog()
  }
}

const closeProgressDialog = () => {
  showProgressDialog.value = false
  if (exportedFileUrl.value) {
    URL.revokeObjectURL(exportedFileUrl.value)
    exportedFileUrl.value = ''
  }
}

// 监听器
watch(() => props.exportType, (newType) => {
  if (newType) {
    exportForm.value.type = newType
    handleTypeChange(newType)
  }
})
</script>

<style scoped lang="scss">
.data-export {
  .filter-section {
    .filter-group {
      margin-bottom: 16px;
    }
  }

  .progress-content {
    text-align: center;
    padding: 20px 0;

    .progress-text {
      margin-top: 16px;
      color: #606266;
      font-size: 14px;
    }
  }

  .dialog-footer {
    text-align: right;
  }
}

:deep(.el-checkbox-group) {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

:deep(.el-checkbox) {
  margin-right: 0;
}
</style>
