import request from '@/utils/request'

export interface Cat {
  id?: number
  name: string
  breed: string
  gender: 'MA<PERSON>' | 'FEMALE'
  birthDate: string
  color: string
  status: 'AVAILABLE' | 'RESERVED' | 'SOLD' | 'BREEDING' | 'MEDICAL' | 'RETIRED'
  price?: number
  microchipId?: string
  description?: string
  primaryPhoto?: string
  photos?: string[]
  createdTime?: string
  updatedTime?: string
}

export interface CatQuery {
  page?: number
  size?: number
  keyword?: string
  status?: string
  breed?: string
  gender?: string
}

export interface ApiResponse<T> {
  success: boolean
  message: string
  data: T
  timestamp: number
}

export interface PageResult<T> {
  content: T[]
  totalElements: number
  totalPages: number
  size: number
  number: number
}

export const catApi = {
  // 获取猫咪列表
  getCats(params: CatQuery = {}): Promise<ApiResponse<PageResult<Cat>>> {
    return request.get('/api/cats', { params })
  },

  // 根据ID获取猫咪
  getCatById(id: number): Promise<ApiResponse<Cat>> {
    return request.get(`/api/cats/${id}`)
  },

  // 创建猫咪
  createCat(cat: Omit<Cat, 'id'>): Promise<ApiResponse<Cat>> {
    return request.post('/api/cats', cat)
  },

  // 更新猫咪
  updateCat(id: number, cat: Partial<Cat>): Promise<ApiResponse<Cat>> {
    return request.put(`/api/cats/${id}`, cat)
  },

  // 删除猫咪
  deleteCat(id: number): Promise<ApiResponse<void>> {
    return request.delete(`/api/cats/${id}`)
  },

  // 搜索猫咪
  searchCats(keyword: string): Promise<ApiResponse<Cat[]>> {
    return request.get('/api/cats/search', { params: { keyword } })
  },

  // 按状态获取猫咪
  getCatsByStatus(status: string): Promise<ApiResponse<Cat[]>> {
    return request.get(`/api/cats/status/${status}`)
  },

  // 获取统计信息
  getStatistics(): Promise<ApiResponse<any>> {
    return request.get('/api/cats/statistics')
  }
}