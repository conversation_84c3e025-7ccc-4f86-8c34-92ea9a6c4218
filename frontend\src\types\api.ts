// API响应类型定义

export interface ApiResponse<T = any> {
  success: boolean
  data: T
  message?: string
  code?: number
}

export interface PageResponse<T> {
  content: T[]
  totalElements: number
  totalPages: number
  size: number
  number: number
  first: boolean
  last: boolean
  empty: boolean
}

export interface PaginationParams {
  page: number
  size: number
  sortBy?: string
  sortDir?: 'asc' | 'desc'
}

// 猫咪相关类型
export interface Cat {
  id: number
  name: string
  breed: string
  gender: 'MALE' | 'FEMALE'
  birthDate: string
  color: string
  weight: number
  price: number
  status: 'AVAILABLE' | 'RESERVED' | 'SOLD' | 'BREEDING' | 'MEDICAL' | 'RETIRED'
  isVaccinated: boolean
  isSterilized: boolean
  microchipId?: string
  fatherInfo?: string
  motherInfo?: string
  description?: string
  notes?: string
  primaryPhoto?: string
  createdTime: string
  updatedTime?: string
}

export interface CatCreateRequest {
  name: string
  breed: string
  gender: 'MALE' | 'FEMALE'
  birthDate: string
  color: string
  weight?: number
  price?: number
  status?: 'AVAILABLE' | 'RESERVED' | 'SOLD' | 'BREEDING' | 'MEDICAL' | 'RETIRED'
  isVaccinated?: boolean
  isSterilized?: boolean
  microchipId?: string
  fatherInfo?: string
  motherInfo?: string
  description?: string
  notes?: string
  primaryPhoto?: string
}

// 客户相关类型
export interface Customer {
  id: number
  name: string
  email: string
  phone?: string
  address?: string
  customerType: 'POTENTIAL' | 'ACTIVE' | 'ADOPTED' | 'BREEDER' | 'VIP'
  status: 'ACTIVE' | 'INACTIVE' | 'BLACKLISTED' | 'DELETED'
  gender?: 'MALE' | 'FEMALE' | 'OTHER'
  age?: number
  occupation?: string
  maritalStatus?: 'SINGLE' | 'MARRIED' | 'DIVORCED' | 'WIDOWED'
  familySize?: number
  housingType?: 'APARTMENT' | 'HOUSE' | 'TOWNHOUSE' | 'CONDO' | 'OTHER'
  hasYard?: boolean
  hasPetExperience?: boolean
  preferredBreed?: string
  preferredGender?: 'MALE' | 'FEMALE'
  preferredAgeRange?: string
  budgetRange?: string
  customerScore?: number
  notes?: string
  createdAt: string
  updatedAt?: string
  lastContactDate?: string
}

// 健康记录相关类型
export interface HealthRecord {
  id: number
  catId: number
  recordType: 'VACCINATION' | 'CHECKUP' | 'TREATMENT' | 'SURGERY' | 'DENTAL' | 'GROOMING' | 'GENETIC_TEST' | 'WEIGHT_CHECK' | 'EMERGENCY' | 'FOLLOW_UP'
  recordDate: string
  veterinarian?: string
  clinic?: string
  diagnosis?: string
  treatment?: string
  medication?: string
  notes?: string
  cost?: number
  weight?: number
  temperature?: number
  vaccineName?: string
  vaccineBatch?: string
  nextVaccineDate?: string
  nextAppointment?: string
  attachmentPath?: string
  createdAt: string
  updatedAt?: string
}

// 繁育记录相关类型
export interface BreedingRecord {
  id: number
  motherId: number
  fatherId: number
  matingDate: string
  expectedBirthDate?: string
  actualBirthDate?: string
  litterSize?: number
  motherName?: string
  fatherName?: string
}

// 财务记录相关类型
export interface FinanceRecord {
  id: number
  type: 'INCOME' | 'EXPENSE'
  category: string
  amount: number
  description: string
  transactionDate: string
  paymentMethod: 'CASH' | 'BANK_TRANSFER' | 'ALIPAY' | 'WECHAT' | 'CREDIT_CARD' | 'OTHER'
  relatedCatId?: number
  relatedCustomerId?: number
  relatedBreedingRecordId?: number
  relatedHealthRecordId?: number
  invoiceNumber?: string
  receiptPath?: string
  notes?: string
  status: 'PENDING' | 'COMPLETED' | 'CANCELLED'
  createdAt: string
  updatedAt?: string
}

// 客户咨询相关类型
export interface CustomerInquiry {
  id: number
  customerId: number
  subject: string
  content: string
  inquiryType: 'GENERAL' | 'PURCHASE' | 'BREEDING' | 'HEALTH' | 'COMPLAINT' | 'SUGGESTION'
  status: 'PENDING' | 'REPLIED' | 'RESOLVED' | 'CLOSED'
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  assignedTo?: number
  reply?: string
  repliedAt?: string
  repliedBy?: number
  createdAt: string
  updatedAt?: string
  customer?: Customer
}

// 库存相关类型
export interface InventoryItem {
  id: number
  itemName: string
  category: string
  brand?: string
  model?: string
  description?: string
  unit: string
  currentStock: number
  minimumStock: number
  maximumStock?: number
  unitPrice?: number
  supplier?: string
  supplierContact?: string
  storageLocation?: string
  barcode?: string
  status: 'ACTIVE' | 'LOW_STOCK' | 'OUT_OF_STOCK' | 'EXPIRED' | 'DISCONTINUED'
  purchaseDate?: string
  expiryDate?: string
  notes?: string
  createdAt: string
  updatedAt?: string
}

// 统计数据类型
export interface DashboardStats {
  catStats: {
    totalCats: number
    availableCats: number
    soldCats: number
    breedingCats: number
    retiredCats: number
    catsByBreed: Record<string, number>
    catsByStatus: Record<string, number>
  }
  customerStats: {
    totalCustomers: number
    activeCustomers: number
    potentialCustomers: number
    vipCustomers: number
    newCustomersThisMonth: number
    customersByType: Record<string, number>
  }
  financeStats: {
    totalIncome: number
    totalExpense: number
    netProfit: number
    monthlyIncome: number
    monthlyExpense: number
    monthlyProfit: number
    monthlyTrends: Array<{
      month: string
      income: number
      expense: number
      profit: number
    }>
    incomeByCategory: Record<string, number>
    expenseByCategory: Record<string, number>
  }
  healthStats: {
    totalHealthRecords: number
    vaccinationRecords: number
    checkupRecords: number
    treatmentRecords: number
    upcomingVaccinations: number
    overdueVaccinations: number
    recordsByType: Record<string, number>
  }
  breedingStats: {
    totalBreedingRecords: number
    activeBreeding: number
    pregnantCats: number
    newbornThisMonth: number
    successRate: number
    upcomingBirths: number
    breedingByStatus: Record<string, number>
  }
  recentActivities: Array<{
    type: string
    title: string
    description: string
    timestamp: string
    icon: string
    color: string
    metadata?: Record<string, any>
  }>
}

// 搜索和过滤参数
export interface CatSearchParams extends PaginationParams {
  name?: string
  breed?: string
  status?: string
  gender?: string
  minPrice?: number
  maxPrice?: number
  isVaccinated?: boolean
  isSterilized?: boolean
}

export interface CustomerSearchParams extends PaginationParams {
  name?: string
  email?: string
  customerType?: string
  status?: string
  hasYard?: boolean
  hasPetExperience?: boolean
}

export interface HealthRecordSearchParams extends PaginationParams {
  catId?: number
  recordType?: string
  startDate?: string
  endDate?: string
  veterinarian?: string
}

export interface FinanceRecordSearchParams extends PaginationParams {
  type?: 'INCOME' | 'EXPENSE'
  category?: string
  status?: string
  startDate?: string
  endDate?: string
  minAmount?: number
  maxAmount?: number
}

// 用户和权限相关类型
export interface User {
  id: number
  username: string
  email: string
  fullName?: string
  phone?: string
  avatar?: string
  status: 'ACTIVE' | 'INACTIVE' | 'LOCKED'
  roles: Role[]
  createdAt: string
  updatedAt?: string
  lastLoginAt?: string
}

export interface Role {
  id: number
  name: string
  description?: string
  permissions: Permission[]
}

export interface Permission {
  id: number
  name: string
  description?: string
  resource: string
  action: string
}

// 登录相关类型
export interface LoginRequest {
  username: string
  password: string
}

export interface LoginResponse {
  token: string
  user: User
  expiresIn: number
}

// 文件上传相关类型
export interface FileUploadResponse {
  url: string
  filename: string
  size: number
  contentType: string
}
