@echo off
echo ========================================
echo Node.js Fresh Installation Guide
echo ========================================
echo.

echo Current Status:
echo   Node.js: NOT INSTALLED
echo   npm: NOT WORKING (residual files)
echo   Solution: Fresh installation needed
echo.

echo Step 1: Download Node.js
echo Opening Node.js official website...
start https://nodejs.org/zh-cn/

echo.
echo Download Requirements:
echo   - Version: LTS (Long Term Support)
echo   - Type: Windows Installer (.msi)
echo   - Architecture: 64-bit (recommended)
echo.

echo Step 2: Installation Settings
echo IMPORTANT: During installation, make sure to:
echo   [x] Install to: C:\Program Files\nodejs\
echo   [x] Check: "Add to PATH environment variable"
echo   [x] Check: "Install additional tools for Node.js" (optional)
echo.

echo Step 3: Verify Installation
echo After installation, open NEW command prompt and test:
echo   node --version
echo   npm --version
echo.

echo Step 4: Install Project Dependencies
echo Once Node.js is working:
echo   cd D:\噔噔\frontend
echo   npm install
echo   npm run dev
echo.

echo ========================================
echo Alternative: Use Yarn
echo ========================================
echo.

echo If npm still has issues after Node.js installation:
echo 1. Install Yarn: https://yarnpkg.com/getting-started/install
echo 2. Use Yarn commands:
echo    yarn install
echo    yarn dev
echo.

echo Opening Yarn website as backup...
start https://yarnpkg.com/getting-started/install

echo.
echo ========================================
echo Next Steps
echo ========================================
echo.

echo 1. Download and install Node.js from the opened website
echo 2. Restart command prompt after installation
echo 3. Run: node --version (should show version number)
echo 4. Run: npm --version (should show version number)
echo 5. Navigate to frontend folder and install dependencies
echo.

pause
