package com.cattery.dto.ai;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 健康记录摘要DTO
 */
@Data
public class HealthRecordSummaryDTO {
    
    /**
     * 记录类型
     */
    private String recordType;
    
    /**
     * 记录日期
     */
    private LocalDateTime recordDate;
    
    /**
     * 记录内容/备注
     */
    private String notes;
    
    /**
     * 诊断结果
     */
    private String diagnosis;
    
    /**
     * 治疗方案
     */
    private String treatment;
    
    /**
     * 兽医姓名
     */
    private String veterinarianName;
    
    /**
     * 严重程度
     */
    private String severity;
    
    /**
     * 是否已解决
     */
    private Boolean isResolved;
    
    /**
     * 相关症状
     */
    private String symptoms;
    
    /**
     * 用药信息
     */
    private String medications;
}
