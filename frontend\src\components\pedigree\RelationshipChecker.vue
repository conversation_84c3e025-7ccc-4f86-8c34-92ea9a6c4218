<template>
  <div class="relationship-checker">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>血缘关系检查</span>
        </div>
      </template>
      
      <el-form :model="form" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="第一只猫">
              <el-select v-model="form.catId1" placeholder="请选择猫咪" filterable remote :remote-method="searchCats1" :loading="loading1">
                <el-option v-for="cat in cats1" :key="cat.id" :label="`${cat.name} (${cat.chipNumber})`" :value="cat.id">
                  <span style="float: left">{{ cat.name }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ cat.breedName }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="第二只猫">
              <el-select v-model="form.catId2" placeholder="请选择猫咪" filterable remote :remote-method="searchCats2" :loading="loading2">
                <el-option v-for="cat in cats2" :key="cat.id" :label="`${cat.name} (${cat.chipNumber})`" :value="cat.id">
                  <span style="float: left">{{ cat.name }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ cat.breedName }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="追溯代数">
          <el-select v-model="form.maxGenerations" style="width: 120px;">
            <el-option label="1代" :value="1"></el-option>
            <el-option label="2代" :value="2"></el-option>
            <el-option label="3代" :value="3"></el-option>
            <el-option label="4代" :value="4"></el-option>
            <el-option label="5代" :value="5"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="checkRelationship" :loading="checking" :disabled="!form.catId1 || !form.catId2">
            检查血缘关系
          </el-button>
          <el-button type="success" @click="validateBreeding" :loading="validating" :disabled="!form.catId1 || !form.catId2">
            验证繁育配对
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 血缘关系结果 -->
    <el-card v-if="relationshipResult" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>血缘关系分析结果</span>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="cat-info">
            <h4>{{ relationshipResult.cat1.name }}</h4>
            <p>品种: {{ relationshipResult.cat1.breedName }}</p>
            <p>性别: {{ relationshipResult.cat1.gender === 'MALE' ? '雄性' : '雌性' }}</p>
            <p>芯片号: {{ relationshipResult.cat1.chipNumber }}</p>
          </div>
        </el-col>
        
        <el-col :span="12">
          <div class="cat-info">
            <h4>{{ relationshipResult.cat2.name }}</h4>
            <p>品种: {{ relationshipResult.cat2.breedName }}</p>
            <p>性别: {{ relationshipResult.cat2.gender === 'MALE' ? '雄性' : '雌性' }}</p>
            <p>芯片号: {{ relationshipResult.cat2.chipNumber }}</p>
          </div>
        </el-col>
      </el-row>
      
      <el-divider></el-divider>
      
      <div class="relationship-details">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-statistic title="是否直系亲属" :value="relationshipResult.isDirectRelative ? '是' : '否'">
              <template #suffix>
                <el-icon v-if="relationshipResult.isDirectRelative" style="color: #f56c6c;"><Warning /></el-icon>
                <el-icon v-else style="color: #67c23a;"><Check /></el-icon>
              </template>
            </el-statistic>
          </el-col>
          
          <el-col :span="8">
            <el-statistic title="共同祖先数量" :value="relationshipResult.commonAncestorsCount">
              <template #suffix>
                <span>个</span>
              </template>
            </el-statistic>
          </el-col>
          
          <el-col :span="8">
            <el-statistic title="近亲系数" :value="(relationshipResult.inbreedingCoefficient * 100).toFixed(2)">
              <template #suffix>
                <span>%</span>
              </template>
            </el-statistic>
          </el-col>
        </el-row>
        
        <div style="margin-top: 20px;">
          <el-tag v-if="relationshipResult.suitableForBreeding" type="success" size="large">
            <el-icon><Check /></el-icon>
            适合繁育
          </el-tag>
          <el-tag v-else type="danger" size="large">
            <el-icon><Close /></el-icon>
            不适合繁育
          </el-tag>
        </div>
        
        <div v-if="relationshipResult.commonAncestors && relationshipResult.commonAncestors.length > 0" style="margin-top: 20px;">
          <h4>共同祖先:</h4>
          <el-space wrap>
            <el-tag v-for="ancestor in relationshipResult.commonAncestors" :key="ancestor.id" type="info">
              {{ ancestor.name }}
            </el-tag>
          </el-space>
        </div>
      </div>
    </el-card>
    
    <!-- 繁育验证结果 -->
    <el-card v-if="breedingResult" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>繁育配对验证结果</span>
        </div>
      </template>
      
      <div class="breeding-validation">
        <el-alert 
          v-if="breedingResult.suitable" 
          title="配对验证通过" 
          type="success" 
          :closable="false"
          show-icon>
          <template #default>
            该配对符合繁育要求，可以进行繁育。
          </template>
        </el-alert>
        
        <el-alert 
          v-else 
          title="配对验证失败" 
          type="error" 
          :closable="false"
          show-icon>
          <template #default>
            该配对存在以下问题，不建议进行繁育：
            <ul style="margin: 10px 0 0 20px;">
              <li v-for="issue in breedingResult.issues" :key="issue">{{ issue }}</li>
            </ul>
          </template>
        </el-alert>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Warning, Check, Close } from '@element-plus/icons-vue'

export default {
  name: 'RelationshipChecker',
  components: {
    Warning,
    Check,
    Close
  },
  setup() {
    const form = reactive({
      catId1: null,
      catId2: null,
      maxGenerations: 3
    })
    
    const cats1 = ref([])
    const cats2 = ref([])
    const loading1 = ref(false)
    const loading2 = ref(false)
    const checking = ref(false)
    const validating = ref(false)
    const relationshipResult = ref(null)
    const breedingResult = ref(null)
    
    const searchCats1 = async (query) => {
      if (query) {
        loading1.value = true
        try {
          const response = await fetch(`/api/cats/search?name=${encodeURIComponent(query)}`)
          const data = await response.json()
          if (data.success) {
            cats1.value = data.data
          }
        } catch (error) {
          console.error('搜索猫咪失败:', error)
        } finally {
          loading1.value = false
        }
      } else {
        cats1.value = []
      }
    }
    
    const searchCats2 = async (query) => {
      if (query) {
        loading2.value = true
        try {
          const response = await fetch(`/api/cats/search?name=${encodeURIComponent(query)}`)
          const data = await response.json()
          if (data.success) {
            cats2.value = data.data
          }
        } catch (error) {
          console.error('搜索猫咪失败:', error)
        } finally {
          loading2.value = false
        }
      } else {
        cats2.value = []
      }
    }
    
    const checkRelationship = async () => {
      checking.value = true
      relationshipResult.value = null
      
      try {
        const response = await fetch(`/api/pedigree/check-relationship?catId1=${form.catId1}&catId2=${form.catId2}&maxGenerations=${form.maxGenerations}`)
        const data = await response.json()
        
        if (data.success) {
          relationshipResult.value = data.data
          ElMessage.success('血缘关系检查完成')
        } else {
          ElMessage.error(data.message || '检查血缘关系失败')
        }
      } catch (error) {
        console.error('检查血缘关系出错:', error)
        ElMessage.error('检查血缘关系出错')
      } finally {
        checking.value = false
      }
    }
    
    const validateBreeding = async () => {
      validating.value = true
      breedingResult.value = null
      
      try {
        const response = await fetch(`/api/pedigree/validate-breeding-pair?maleId=${form.catId1}&femaleId=${form.catId2}`)
        const data = await response.json()
        
        if (data.success) {
          breedingResult.value = data.data
          ElMessage.success('繁育配对验证完成')
        } else {
          ElMessage.error(data.message || '验证繁育配对失败')
        }
      } catch (error) {
        console.error('验证繁育配对出错:', error)
        ElMessage.error('验证繁育配对出错')
      } finally {
        validating.value = false
      }
    }
    
    return {
      form,
      cats1,
      cats2,
      loading1,
      loading2,
      checking,
      validating,
      relationshipResult,
      breedingResult,
      searchCats1,
      searchCats2,
      checkRelationship,
      validateBreeding
    }
  }
}
</script>

<style scoped>
.relationship-checker {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cat-info {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.cat-info h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.cat-info p {
  margin: 5px 0;
  color: #666;
}

.relationship-details {
  padding: 20px 0;
}

.breeding-validation {
  padding: 10px 0;
}

.breeding-validation ul {
  padding-left: 0;
}

.breeding-validation li {
  list-style-type: none;
  padding: 2px 0;
}

.breeding-validation li::before {
  content: "• ";
  color: #f56c6c;
  font-weight: bold;
}
</style>