package com.cattery.dto.ai;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * AI使用统计DTO
 */
@Data
public class AIUsageStatisticsDTO {
    
    /**
     * 总识别次数
     */
    private Long totalRecognitions;
    
    /**
     * 总健康预测次数
     */
    private Long totalHealthPredictions;
    
    /**
     * 总行为分析次数
     */
    private Long totalBehaviorAnalyses;
    
    /**
     * 总品种识别次数
     */
    private Long totalBreedRecognitions;
    
    /**
     * 平均响应时间（毫秒）
     */
    private Double averageResponseTime;
    
    /**
     * 成功率
     */
    private Double successRate;
    
    /**
     * 按日期的使用统计
     */
    private Map<String, Long> dailyUsage;
    
    /**
     * 按功能的使用分布
     */
    private Map<String, Long> featureUsageDistribution;
    
    /**
     * 错误统计
     */
    private Map<String, Long> errorStatistics;
    
    /**
     * 统计时间范围
     */
    private LocalDateTime statisticsStartDate;
    
    /**
     * 统计结束时间
     */
    private LocalDateTime statisticsEndDate;
    
    /**
     * 用户满意度评分
     */
    private Double userSatisfactionScore;

    /**
     * 平均准确率
     */
    private Double averageAccuracy;

    /**
     * 最常用功能
     */
    private String mostUsedFeature;
}
