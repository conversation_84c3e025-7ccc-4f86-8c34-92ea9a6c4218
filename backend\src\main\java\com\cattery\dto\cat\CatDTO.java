package com.cattery.dto.cat;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 猫咪DTO
 */
@Data
public class CatDTO {

    /**
     * 猫咪ID
     */
    private Long id;

    /**
     * 猫咪姓名
     */
    private String name;

    /**
     * 品种名称
     */
    private String breedName;

    /**
     * 性别
     */
    private String gender;

    /**
     * 出生日期
     */
    private LocalDateTime dateOfBirth;

    /**
     * 年龄（计算得出）
     */
    private String age;

    /**
     * 颜色
     */
    private String color;

    /**
     * 花纹
     */
    private String pattern;

    /**
     * 当前体重（克）
     */
    private BigDecimal currentWeight;

    /**
     * 芯片号
     */
    private String microchipId;

    /**
     * 注册号
     */
    private String registrationNumber;

    /**
     * 状态
     */
    private String status;

    /**
     * 状态描述
     */
    private String statusDescription;

    /**
     * 描述
     */
    private String description;

    /**
     * 备注
     */
    private String notes;

    /**
     * 主照片URL
     */
    private String primaryPhoto;

    /**
     * 是否绝育
     */
    private Boolean isNeutered;

    /**
     * 父亲信息
     */
    private CatSummaryDTO father;

    /**
     * 母亲信息
     */
    private CatSummaryDTO mother;

    /**
     * 后代列表
     */
    private List<CatSummaryDTO> offspring;

    /**
     * 照片列表
     */
    private List<CatPhotoDTO> photos;

    /**
     * 最近健康记录
     */
    private List<HealthRecordSummaryDTO> recentHealthRecords;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 创建者
     */
    private String createdBy;

    /**
     * 更新者
     */
    private String updatedBy;

    /**
     * 猫咪摘要DTO
     */
    @Data
    public static class CatSummaryDTO {
        private Long id;
        private String name;
        private String breedName;
        private String gender;
        private String color;
        private LocalDateTime dateOfBirth;
        private String primaryPhoto;
    }

    /**
     * 猫咪照片DTO
     */
    @Data
    public static class CatPhotoDTO {
        private Long id;
        private String fileName;
        private String filePath;
        private String thumbnailPath;
        private Boolean isPrimary;
        private Integer sortOrder;
        private String description;
        private LocalDateTime takenDate;
    }

    /**
     * 健康记录摘要DTO
     */
    @Data
    public static class HealthRecordSummaryDTO {
        private Long id;
        private String recordType;
        private LocalDateTime recordDate;
        private String veterinarian;
        private String diagnosis;
        private LocalDateTime nextAppointment;
    }
}
