<template>
  <div class="layout">
    <el-container>
      <!-- 顶部导航 -->
      <el-header class="header">
        <div class="header-left">
          <h1 class="logo">🐱 猫舍管理系统</h1>
        </div>
        <div class="header-right">
          <el-dropdown @command="handleCommand">
            <span class="user-info">
              <el-avatar :size="32" :src="userInfo?.avatar">
                {{ userInfo?.realName?.charAt(0) || userInfo?.username?.charAt(0) || 'U' }}
              </el-avatar>
              <span class="username">{{ userInfo?.realName || userInfo?.username }}</span>
              <el-icon><ArrowDown /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">个人资料</el-dropdown-item>
                <el-dropdown-item command="settings">设置</el-dropdown-item>
                <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>

      <el-container>
        <!-- 侧边导航 -->
        <el-aside class="sidebar" width="250px">
          <el-menu
            :default-active="$route.path"
            class="sidebar-menu"
            router
            unique-opened
          >
            <el-menu-item index="/">
              <el-icon><House /></el-icon>
              <span>仪表盘</span>
            </el-menu-item>
            
            <el-sub-menu index="cats">
              <template #title>
                <el-icon><Avatar /></el-icon>
                <span>猫咪管理</span>
              </template>
              <el-menu-item index="/cats">猫咪列表</el-menu-item>
              <el-menu-item index="/cats/create">新增猫咪</el-menu-item>
            </el-sub-menu>

            <el-sub-menu index="health">
              <template #title>
                <el-icon><FirstAidKit /></el-icon>
                <span>健康管理</span>
              </template>
              <el-menu-item index="/health">健康记录</el-menu-item>
              <el-menu-item index="/health/vaccines">疫苗管理</el-menu-item>
              <el-menu-item index="/health/reminders">健康提醒</el-menu-item>
            </el-sub-menu>

            <el-sub-menu index="breeding">
              <template #title>
                <el-icon><Management /></el-icon>
                <span>繁育管理</span>
              </template>
              <el-menu-item index="/breeding">繁育记录</el-menu-item>
              <el-menu-item index="/breeding/plans">繁育计划</el-menu-item>
            </el-sub-menu>

            <el-menu-item index="/customers">
              <el-icon><User /></el-icon>
              <span>客户管理</span>
            </el-menu-item>

            <el-sub-menu index="finance">
              <template #title>
                <el-icon><Money /></el-icon>
                <span>财务管理</span>
              </template>
              <el-menu-item index="/finance">财务记录</el-menu-item>
              <el-menu-item index="/finance/budget">预算管理</el-menu-item>
            </el-sub-menu>

            <el-menu-item index="/reports">
              <el-icon><DataAnalysis /></el-icon>
              <span>报表分析</span>
            </el-menu-item>

            <el-menu-item index="/api-test">
              <el-icon><Setting /></el-icon>
              <span>API测试</span>
            </el-menu-item>

            <el-menu-item index="/test">
              <el-icon><Tools /></el-icon>
              <span>页面测试</span>
            </el-menu-item>

            <el-menu-item index="/diagnostic">
              <el-icon><Monitor /></el-icon>
              <span>系统诊断</span>
            </el-menu-item>
          </el-menu>
        </el-aside>

        <!-- 主内容区 -->
        <el-main class="main-content">
          <router-view v-slot="{ Component, route }">
            <template v-if="Component">
              <Suspense>
                <component :is="Component" />
                <template #fallback>
                  <div class="loading-container">
                    <el-skeleton :rows="5" animated />
                    <p style="text-align: center; margin-top: 20px; color: #666;">
                      正在加载页面...
                    </p>
                  </div>
                </template>
              </Suspense>
            </template>
            <template v-else>
              <div class="error-container">
                <el-result
                  icon="warning"
                  title="页面加载失败"
                  sub-title="该页面可能不存在或正在开发中"
                >
                  <template #extra>
                    <el-button type="primary" @click="$router.push('/')">
                      返回首页
                    </el-button>
                  </template>
                </el-result>
              </div>
            </template>
          </router-view>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  House, Avatar, FirstAidKit, Management, User, Money,
  DataAnalysis, Setting, ArrowDown, Tools, Monitor
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const userInfo = computed(() => authStore.userInfo)

const handleCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人资料功能开发中...')
      break
    case 'settings':
      ElMessage.info('设置功能开发中...')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await authStore.logout()
        router.push('/login')
      } catch {
        // 用户取消
      }
      break
  }
}
</script>

<style scoped>
.layout {
  height: 100vh;
}

.header {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left .logo {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #409eff;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.username {
  font-size: 14px;
  color: #333;
}

.sidebar {
  background: #fff;
  border-right: 1px solid #e4e7ed;
}

.sidebar-menu {
  border-right: none;
  height: 100%;
}

.main-content {
  background: #f5f7fa;
  padding: 20px;
  overflow-y: auto;
}

.loading-container {
  padding: 40px;
  background: white;
  border-radius: 8px;
  margin: 20px;
}

.error-container {
  padding: 40px;
  background: white;
  border-radius: 8px;
  margin: 20px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    width: 200px !important;
  }
  
  .header-left .logo {
    font-size: 16px;
  }
  
  .main-content {
    padding: 10px;
  }
}
</style>
