package com.cattery.dto.ai;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 健康评估摘要DTO
 */
@Data
public class HealthAssessmentSummaryDTO {
    
    /**
     * 猫咪ID
     */
    private Long catId;
    
    /**
     * 猫咪名称
     */
    private String catName;
    
    /**
     * 评估时间
     */
    private LocalDateTime assessmentTime;
    
    /**
     * 整体健康评分 (0-100)
     */
    private Double overallHealthScore;
    
    /**
     * 健康状态 (EXCELLENT, GOOD, FAIR, POOR, CRITICAL)
     */
    private String healthStatus;
    
    /**
     * 风险等级 (LOW, MEDIUM, HIGH, CRITICAL)
     */
    private String riskLevel;
    
    /**
     * 主要健康问题
     */
    private List<String> primaryHealthConcerns;
    
    /**
     * 建议的下次检查时间
     */
    private LocalDateTime nextCheckupRecommendation;
    
    /**
     * 紧急关注点
     */
    private List<String> urgentConcerns;
    
    /**
     * 预防建议
     */
    private List<String> preventiveRecommendations;
    
    /**
     * 评估依据
     */
    private List<String> assessmentBasis;
    
    /**
     * 趋势 (IMPROVING, STABLE, DECLINING)
     */
    private String trend;

    /**
     * 评估日期
     */
    private LocalDateTime assessmentDate;

    /**
     * 疫苗接种状态
     */
    private String vaccinationStatus;

    /**
     * 最后检查日期
     */
    private LocalDateTime lastCheckupDate;

    /**
     * 健康趋势
     */
    private String healthTrend;

    /**
     * 风险因素
     */
    private List<String> riskFactors;

    /**
     * 建议
     */
    private List<String> recommendations;

    /**
     * 下次检查建议时间
     */
    private LocalDateTime nextCheckupRecommended;
}
