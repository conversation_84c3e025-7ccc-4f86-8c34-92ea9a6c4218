package com.cattery.dto.report;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 健康报表DTO
 */
@Data
public class HealthReportDTO {
    
    /**
     * 报表期间
     */
    private String reportPeriod;
    
    /**
     * 生成时间
     */
    private LocalDateTime generatedAt;
    
    /**
     * 总健康记录数
     */
    private Integer totalHealthRecords;
    
    /**
     * 新增健康记录数
     */
    private Long newHealthRecords;
    
    /**
     * 按类型的记录分布
     */
    private Map<String, Long> recordTypeDistribution;
    
    /**
     * 疫苗接种统计
     */
    private VaccinationStatisticsDTO vaccinationStatistics;
    
    /**
     * 体检统计
     */
    private CheckupStatisticsDTO checkupStatistics;
    
    /**
     * 健康问题统计
     */
    private List<HealthIssueStatisticsDTO> healthIssueStatistics;
    
    /**
     * 兽医统计
     */
    private Map<String, Long> veterinarianStats;
    
    /**
     * 健康趋势
     */
    private String healthTrend;
    
    /**
     * 预警信息
     */
    private List<String> healthAlerts;

    /**
     * 兽医统计
     */
    private Map<String, Long> veterinarianStatistics;
}
