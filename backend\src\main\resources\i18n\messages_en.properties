# Common messages
common.success=Operation successful
common.error=Operation failed
common.notFound=Resource not found
common.unauthorized=Unauthorized access
common.forbidden=Insufficient permissions
common.badRequest=Invalid request parameters
common.internalError=Internal server error

# Validation messages
validation.required=This field is required
validation.email=Please enter a valid email address
validation.phone=Please enter a valid phone number
validation.length.min=Length cannot be less than {0} characters
validation.length.max=Length cannot exceed {0} characters
validation.pattern=Invalid format

# Authentication messages
auth.login.success=Login successful
auth.login.failed=Invalid username or password
auth.logout.success=Logout successful
auth.register.success=Registration successful
auth.register.failed=Registration failed
auth.token.invalid=Invalid token
auth.token.expired=Token expired
auth.username.exists=Username already exists
auth.email.exists=Email already exists
auth.phone.exists=Phone number already exists

# Cat management
cat.created=Cat information created successfully
cat.updated=Cat information updated successfully
cat.deleted=Cat information deleted successfully
cat.notFound=Cat not found
cat.name.required=Cat name is required
cat.breed.required=Breed is required
cat.gender.required=Gender is required
cat.dateOfBirth.required=Date of birth is required

# Health management
health.record.created=Health record created successfully
health.record.updated=Health record updated successfully
health.record.deleted=Health record deleted successfully
health.record.notFound=Health record not found

# Customer management
customer.created=Customer information created successfully
customer.updated=Customer information updated successfully
customer.deleted=Customer information deleted successfully
customer.notFound=Customer not found

# Adoption management
adoption.created=Adoption application created successfully
adoption.approved=Adoption application approved
adoption.rejected=Adoption application rejected
adoption.completed=Adoption completed
adoption.cancelled=Adoption cancelled

# Financial management
finance.transaction.created=Financial record created successfully
finance.transaction.updated=Financial record updated successfully
finance.transaction.deleted=Financial record deleted successfully

# AI features
ai.recognition.success=Recognition successful
ai.recognition.failed=Recognition failed
ai.prediction.success=Prediction completed
ai.prediction.failed=Prediction failed
ai.analysis.success=Analysis completed
ai.analysis.failed=Analysis failed

# Reports
report.generated=Report generated successfully
report.export.success=Report exported successfully
report.export.failed=Report export failed

# File upload
file.upload.success=File uploaded successfully
file.upload.failed=File upload failed
file.size.exceeded=File size exceeds limit
file.type.invalid=File type not supported
