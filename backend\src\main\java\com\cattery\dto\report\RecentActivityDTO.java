package com.cattery.dto.report;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 最近活动DTO
 */
@Data
public class RecentActivityDTO {
    
    /**
     * 活动ID
     */
    private Long id;
    
    /**
     * 活动类型 (CAT_ADDED, HEALTH_RECORD, ADOPTION, VACCINATION, FEEDING)
     */
    private String activityType;
    
    /**
     * 活动描述
     */
    private String description;
    
    /**
     * 活动时间
     */
    private LocalDateTime activityTime;
    
    /**
     * 相关猫咪ID
     */
    private Long catId;
    
    /**
     * 相关猫咪名称
     */
    private String catName;
    
    /**
     * 操作用户
     */
    private String userName;
    
    /**
     * 重要程度 (HIGH, MEDIUM, LOW)
     */
    private String importance;
    
    /**
     * 详细信息
     */
    private String details;
    
    /**
     * 图标
     */
    private String icon;
    
    /**
     * 颜色标识
     */
    private String color;
}
