package com.cattery.dto.report;

import lombok.Data;

/**
 * 健康问题统计DTO
 */
@Data
public class HealthIssueStatisticsDTO {
    
    /**
     * 健康问题名称
     */
    private String issueName;
    
    /**
     * 发生次数
     */
    private Long occurrenceCount;
    
    /**
     * 影响的猫咪数量
     */
    private Long affectedCats;
    
    /**
     * 发生率
     */
    private Double incidenceRate;
    
    /**
     * 严重程度分布
     */
    private String severityDistribution;
    
    /**
     * 平均治疗时间（天）
     */
    private Double averageTreatmentDays;
    
    /**
     * 治愈率
     */
    private Double recoveryRate;
    
    /**
     * 复发率
     */
    private Double recurrenceRate;
    
    /**
     * 预防措施建议
     */
    private String preventionAdvice;
    
    /**
     * 趋势 (INCREASING, STABLE, DECREASING)
     */
    private String trend;
}
