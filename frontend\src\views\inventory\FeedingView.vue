<template>
  <div class="feeding-view">
    <div class="page-header">
      <h1>喂养管理</h1>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        新增喂养记录
      </el-button>
    </div>

    <div class="content-area">
      <el-card>
        <p>这是喂养管理页面，功能开发中...</p>
      </el-card>
    </div>

    <el-dialog v-model="showCreateDialog" title="新增喂养记录" width="600px">
      <el-form :model="form" label-width="100px">
        <el-form-item label="猫咪">
          <el-select v-model="form.catId" placeholder="选择猫咪">
            <el-option label="美美" value="1" />
            <el-option label="帅帅" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="食物">
          <el-input v-model="form.food" placeholder="输入食物名称" />
        </el-form-item>
        <el-form-item label="数量">
          <el-input-number v-model="form.amount" :min="0" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="save">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

const showCreateDialog = ref(false)
const form = reactive({
  catId: '',
  food: '',
  amount: 0
})

const save = () => {
  ElMessage.success('保存成功')
  showCreateDialog.value = false
}
</script>

<style scoped>
.feeding-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
</style>
