package com.cattery.dto.report;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 客户报表DTO
 */
@Data
public class CustomerReportDTO {
    
    /**
     * 报表期间
     */
    private String reportPeriod;
    
    /**
     * 生成时间
     */
    private LocalDateTime generatedAt;
    
    /**
     * 总客户数
     */
    private Long totalCustomers;
    
    /**
     * 新客户数
     */
    private Long newCustomers;
    
    /**
     * 按类型的客户分布
     */
    private Map<String, Long> customerTypeDistribution;
    
    /**
     * 总领养数
     */
    private Integer totalAdoptions;
    
    /**
     * 领养统计
     */
    private AdoptionStatisticsDTO adoptionStatistics;
    
    /**
     * 客户满意度统计
     */
    private CustomerSatisfactionDTO customerSatisfaction;
    
    /**
     * 地区分布
     */
    private Map<String, Long> regionDistribution;
    
    /**
     * 客户活跃度
     */
    private Map<String, Long> customerActivity;
    
    /**
     * 客户留存率
     */
    private Double customerRetentionRate;
    
    /**
     * 平均客户价值
     */
    private Double averageCustomerValue;
}
