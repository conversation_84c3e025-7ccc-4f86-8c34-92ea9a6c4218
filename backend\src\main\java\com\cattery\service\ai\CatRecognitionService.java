package com.cattery.service.ai;

import com.cattery.dto.ai.CatRecognitionResultDTO;
import com.cattery.dto.ai.BreedRecognitionResultDTO;
import com.cattery.dto.ai.FacialFeatureDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;

/**
 * 猫咪识别服务 - 提供猫咪个体识别和品种识别功能
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CatRecognitionService {

    private final RestTemplate restTemplate;

    @Value("${ai.cat-recognition.api.url:http://localhost:5000}")
    private String apiUrl;

    @Value("${ai.cat-recognition.api.key:}")
    private String apiKey;

    @Value("${ai.cat-recognition.timeout:30000}")
    private int timeout;

    /**
     * 猫咪个体识别
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    public CatRecognitionResultDTO recognizeCat(byte[] imageData) {
        try {
            log.info("开始猫咪个体识别");
            
            // 构建请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            if (!apiKey.isEmpty()) {
                headers.set("Authorization", "Bearer " + apiKey);
            }
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("image", Base64.getEncoder().encodeToString(imageData));
            requestBody.put("task", "individual_recognition");
            requestBody.put("extract_features", true);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            
            // 调用AI服务
            ResponseEntity<Map> response = restTemplate.postForEntity(
                apiUrl + "/api/v1/recognize", request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                return parseRecognitionResult(response.getBody());
            } else {
                throw new RuntimeException("AI服务调用失败");
            }
            
        } catch (Exception e) {
            log.error("猫咪识别失败", e);
            // 返回默认结果，避免系统崩溃
            return createDefaultRecognitionResult();
        }
    }

    /**
     * 品种识别
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    public BreedRecognitionResultDTO recognizeBreed(byte[] imageData) {
        try {
            log.info("开始品种识别");
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            if (!apiKey.isEmpty()) {
                headers.set("Authorization", "Bearer " + apiKey);
            }
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("image", Base64.getEncoder().encodeToString(imageData));
            requestBody.put("task", "breed_classification");
            requestBody.put("top_k", 5);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(
                apiUrl + "/api/v1/classify", request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                return parseBreedResult(response.getBody());
            } else {
                throw new RuntimeException("品种识别服务调用失败");
            }
            
        } catch (Exception e) {
            log.error("品种识别失败", e);
            return createDefaultBreedResult();
        }
    }

    /**
     * 提取面部特征
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    public List<FacialFeatureDTO> extractFacialFeatures(byte[] imageData) {
        try {
            log.info("开始提取面部特征");
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            if (!apiKey.isEmpty()) {
                headers.set("Authorization", "Bearer " + apiKey);
            }
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("image", Base64.getEncoder().encodeToString(imageData));
            requestBody.put("task", "feature_extraction");
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(
                apiUrl + "/api/v1/features", request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                return parseFacialFeatures(response.getBody());
            } else {
                throw new RuntimeException("特征提取服务调用失败");
            }
            
        } catch (Exception e) {
            log.error("面部特征提取失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 检查服务可用性
     */
    @SuppressWarnings("rawtypes")
    public boolean isAvailable() {
        try {
            ResponseEntity<Map> response = restTemplate.getForEntity(
                apiUrl + "/api/v1/health", Map.class);
            return response.getStatusCode() == HttpStatus.OK;
        } catch (Exception e) {
            log.warn("猫咪识别服务不可用: {}", e.getMessage());
            return false;
        }
    }

    // 私有辅助方法

    @SuppressWarnings("unchecked")
    private CatRecognitionResultDTO parseRecognitionResult(Map<String, Object> responseBody) {
        CatRecognitionResultDTO result = new CatRecognitionResultDTO();
        
        // 解析基本信息
        result.setConfidence(getDoubleValue(responseBody, "confidence"));
        result.setBreedName(getStringValue(responseBody, "breed"));
        result.setColor(getStringValue(responseBody, "color"));
        result.setGender(getStringValue(responseBody, "gender"));
        result.setEstimatedAge(getIntValue(responseBody, "estimated_age"));
        
        // 解析面部特征
        Map<String, Object> features = (Map<String, Object>) responseBody.get("features");
        if (features != null) {
            List<FacialFeatureDTO> facialFeatures = new ArrayList<>();
            
            // 眼睛特征
            if (features.containsKey("eyes")) {
                Map<String, Object> eyes = (Map<String, Object>) features.get("eyes");
                FacialFeatureDTO eyeFeature = new FacialFeatureDTO();
                eyeFeature.setFeatureType("eyes");
                eyeFeature.setColor(getStringValue(eyes, "color"));
                eyeFeature.setShape(getStringValue(eyes, "shape"));
                eyeFeature.setSize(getStringValue(eyes, "size"));
                facialFeatures.add(eyeFeature);
            }
            
            // 耳朵特征
            if (features.containsKey("ears")) {
                Map<String, Object> ears = (Map<String, Object>) features.get("ears");
                FacialFeatureDTO earFeature = new FacialFeatureDTO();
                earFeature.setFeatureType("ears");
                earFeature.setShape(getStringValue(ears, "shape"));
                earFeature.setSize(getStringValue(ears, "size"));
                facialFeatures.add(earFeature);
            }
            
            // 鼻子特征
            if (features.containsKey("nose")) {
                Map<String, Object> nose = (Map<String, Object>) features.get("nose");
                FacialFeatureDTO noseFeature = new FacialFeatureDTO();
                noseFeature.setFeatureType("nose");
                noseFeature.setColor(getStringValue(nose, "color"));
                noseFeature.setShape(getStringValue(nose, "shape"));
                facialFeatures.add(noseFeature);
            }
            
            result.setFacialFeatures(facialFeatures);
        }
        
        // 解析身体特征
        Map<String, Object> bodyFeatures = (Map<String, Object>) responseBody.get("body_features");
        if (bodyFeatures != null) {
            result.setBodySize(getStringValue(bodyFeatures, "size"));
            result.setCoatLength(getStringValue(bodyFeatures, "coat_length"));
            result.setCoatPattern(getStringValue(bodyFeatures, "coat_pattern"));
        }
        
        // 解析相似度匹配
        List<Map<String, Object>> matches = (List<Map<String, Object>>) responseBody.get("matches");
        if (matches != null) {
            List<CatRecognitionResultDTO.SimilarityMatch> similarityMatches = new ArrayList<>();
            for (Map<String, Object> match : matches) {
                CatRecognitionResultDTO.SimilarityMatch similarityMatch = 
                    new CatRecognitionResultDTO.SimilarityMatch();
                similarityMatch.setCatId(getLongValue(match, "cat_id"));
                similarityMatch.setSimilarity(getDoubleValue(match, "similarity"));
                similarityMatch.setMatchedFeatures((List<String>) match.get("matched_features"));
                similarityMatches.add(similarityMatch);
            }
            result.setSimilarityMatches(similarityMatches);
        }
        
        return result;
    }

    @SuppressWarnings("unchecked")
    private BreedRecognitionResultDTO parseBreedResult(Map<String, Object> responseBody) {
        BreedRecognitionResultDTO result = new BreedRecognitionResultDTO();
        
        result.setConfidence(getDoubleValue(responseBody, "confidence"));
        result.setBreedName(getStringValue(responseBody, "breed"));
        
        // 解析候选品种
        List<Map<String, Object>> candidates = (List<Map<String, Object>>) responseBody.get("candidates");
        if (candidates != null) {
            List<BreedRecognitionResultDTO.BreedCandidate> breedCandidates = new ArrayList<>();
            for (Map<String, Object> candidate : candidates) {
                BreedRecognitionResultDTO.BreedCandidate breedCandidate = 
                    new BreedRecognitionResultDTO.BreedCandidate();
                breedCandidate.setBreedName(getStringValue(candidate, "breed"));
                breedCandidate.setConfidence(getDoubleValue(candidate, "confidence"));
                breedCandidate.setCharacteristics((List<String>) candidate.get("characteristics"));
                breedCandidates.add(breedCandidate);
            }
            result.setCandidates(breedCandidates);
        }
        
        return result;
    }

    @SuppressWarnings("unchecked")
    private List<FacialFeatureDTO> parseFacialFeatures(Map<String, Object> responseBody) {
        List<FacialFeatureDTO> features = new ArrayList<>();
        
        Map<String, Object> extractedFeatures = (Map<String, Object>) responseBody.get("features");
        if (extractedFeatures != null) {
            for (Map.Entry<String, Object> entry : extractedFeatures.entrySet()) {
                FacialFeatureDTO feature = new FacialFeatureDTO();
                feature.setFeatureType(entry.getKey());
                
                if (entry.getValue() instanceof Map) {
                    Map<String, Object> featureData = (Map<String, Object>) entry.getValue();
                    feature.setColor(getStringValue(featureData, "color"));
                    feature.setShape(getStringValue(featureData, "shape"));
                    feature.setSize(getStringValue(featureData, "size"));
                    feature.setConfidence(getDoubleValue(featureData, "confidence"));
                }
                
                features.add(feature);
            }
        }
        
        return features;
    }

    private CatRecognitionResultDTO createDefaultRecognitionResult() {
        CatRecognitionResultDTO result = new CatRecognitionResultDTO();
        result.setConfidence(0.0);
        result.setBreedName("未知");
        result.setColor("未知");
        result.setGender("未知");
        result.setEstimatedAge(0);
        result.setFacialFeatures(new ArrayList<>());
        result.setSimilarityMatches(new ArrayList<>());
        return result;
    }

    private BreedRecognitionResultDTO createDefaultBreedResult() {
        BreedRecognitionResultDTO result = new BreedRecognitionResultDTO();
        result.setConfidence(0.0);
        result.setBreedName("未知");
        result.setCandidates(new ArrayList<>());
        return result;
    }

    // 工具方法
    private String getStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : "";
    }

    private double getDoubleValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return 0.0;
    }

    private int getIntValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return 0;
    }

    private long getLongValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        return 0L;
    }
}
