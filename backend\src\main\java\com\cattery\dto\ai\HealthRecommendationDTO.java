package com.cattery.dto.ai;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 健康建议DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HealthRecommendationDTO {

    /**
     * 三参数构造函数
     */
    public HealthRecommendationDTO(String title, String content, String type) {
        this.title = title;
        this.content = content;
        this.type = type;
    }
    
    /**
     * 建议标题
     */
    private String title;
    
    /**
     * 建议内容
     */
    private String content;
    
    /**
     * 建议类型 (URGENT, IMPORTANT, PREVENTIVE, GENERAL)
     */
    private String type;
    
    /**
     * 优先级 (1-5, 1最高)
     */
    private Integer priority;
    
    /**
     * 建议分类
     */
    private String category;
    
    /**
     * 是否需要兽医
     */
    private Boolean requiresVet;
    
    /**
     * 预期效果
     */
    private String expectedOutcome;
}
