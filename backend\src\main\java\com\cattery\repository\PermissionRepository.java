package com.cattery.repository;

import com.cattery.entity.Permission;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 权限仓库接口
 */
@Repository
public interface PermissionRepository extends JpaRepository<Permission, Long>, JpaSpecificationExecutor<Permission> {

    /**
     * 根据权限名称查找权限
     */
    Optional<Permission> findByName(String name);

    /**
     * 根据显示名称查找权限
     */
    Optional<Permission> findByDisplayName(String displayName);

    /**
     * 检查权限名称是否存在
     */
    boolean existsByName(String name);

    /**
     * 检查显示名称是否存在
     */
    boolean existsByDisplayName(String displayName);

    /**
     * 根据启用状态查找权限
     */
    List<Permission> findByEnabled(Boolean enabled);

    /**
     * 查找所有启用的权限
     */
    List<Permission> findByEnabledTrueOrderByName();

    /**
     * 根据权限分组查找权限
     */
    List<Permission> findByPermissionGroup(String permissionGroup);

    /**
     * 根据权限分组和启用状态查找权限
     */
    List<Permission> findByPermissionGroupAndEnabled(String permissionGroup, Boolean enabled);

    /**
     * 根据角色查找权限
     */
    @Query("SELECT DISTINCT p FROM Permission p JOIN p.roles r WHERE r.id = :roleId")
    List<Permission> findByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据角色名称查找权限
     */
    @Query("SELECT DISTINCT p FROM Permission p JOIN p.roles r WHERE r.name = :roleName")
    List<Permission> findByRoleName(@Param("roleName") String roleName);

    /**
     * 根据用户查找权限
     */
    @Query("SELECT DISTINCT p FROM Permission p JOIN p.roles r JOIN r.users u WHERE u.id = :userId")
    List<Permission> findByUserId(@Param("userId") Long userId);

    /**
     * 根据用户名查找权限
     */
    @Query("SELECT DISTINCT p FROM Permission p JOIN p.roles r JOIN r.users u WHERE u.username = :username")
    List<Permission> findByUsername(@Param("username") String username);

    /**
     * 统计启用的权限数量
     */
    long countByEnabled(Boolean enabled);

    /**
     * 根据关键词搜索权限
     */
    @Query("SELECT p FROM Permission p WHERE " +
           "LOWER(p.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(p.displayName) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(p.description) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(p.permissionGroup) LIKE LOWER(CONCAT('%', :keyword, '%'))")
    Page<Permission> searchPermissions(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 查找有角色的权限
     */
    @Query("SELECT DISTINCT p FROM Permission p WHERE SIZE(p.roles) > 0")
    List<Permission> findPermissionsWithRoles();

    /**
     * 查找没有角色的权限
     */
    @Query("SELECT p FROM Permission p WHERE SIZE(p.roles) = 0")
    List<Permission> findPermissionsWithoutRoles();

    /**
     * 统计各权限分组的权限数量
     */
    @Query("SELECT p.permissionGroup, COUNT(p) FROM Permission p WHERE p.permissionGroup IS NOT NULL " +
           "GROUP BY p.permissionGroup ORDER BY COUNT(p) DESC")
    List<Object[]> countPermissionsByGroup();

    /**
     * 统计各权限的角色数量
     */
    @Query("SELECT p.name, SIZE(p.roles) FROM Permission p GROUP BY p.name ORDER BY SIZE(p.roles) DESC")
    List<Object[]> countRolesByPermission();

    /**
     * 查找系统核心权限
     */
    @Query("SELECT p FROM Permission p WHERE p.name LIKE 'system:%' OR p.name LIKE 'user:%'")
    List<Permission> findSystemPermissions();

    /**
     * 查找业务权限
     */
    @Query("SELECT p FROM Permission p WHERE p.name NOT LIKE 'system:%' AND p.name NOT LIKE 'user:%'")
    List<Permission> findBusinessPermissions();

    /**
     * 根据权限分组查找所有分组
     */
    @Query("SELECT DISTINCT p.permissionGroup FROM Permission p WHERE p.permissionGroup IS NOT NULL ORDER BY p.permissionGroup")
    List<String> findAllPermissionGroups();
}
