<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="120px"
    class="cat-form"
  >
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="猫咪名字" prop="name">
          <el-input v-model="form.name" placeholder="请输入猫咪名字" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="品种" prop="breedId">
          <el-select v-model="form.breedId" placeholder="请选择品种" style="width: 100%">
            <el-option
              v-for="breed in breeds"
              :key="breed.id"
              :label="breed.name"
              :value="breed.id"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="性别" prop="gender">
          <el-radio-group v-model="form.gender">
            <el-radio value="MALE">雄性</el-radio>
            <el-radio value="FEMALE">雌性</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="出生日期" prop="dateOfBirth">
          <el-date-picker
            v-model="form.dateOfBirth"
            type="date"
            placeholder="选择出生日期"
            style="width: 100%"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="毛色" prop="color">
          <el-input v-model="form.color" placeholder="请输入毛色" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="体重(kg)" prop="weight">
          <el-input-number
            v-model="form.weight"
            :precision="2"
            :step="0.1"
            :min="0"
            style="width: 100%"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%">
            <el-option label="待领养" value="PENDING_ADOPTION" />
            <el-option label="已预定" value="RESERVED" />
            <el-option label="已领养" value="ADOPTED" />
            <el-option label="非卖品" value="NOT_FOR_SALE" />
            <el-option label="繁育中" value="BREEDING" />
            <el-option label="已退役" value="RETIRED" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="价格(元)" prop="price">
          <el-input-number
            v-model="form.price"
            :precision="2"
            :step="100"
            :min="0"
            style="width: 100%"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 血统信息 -->
    <el-divider content-position="left">血统信息</el-divider>
    
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="父亲" prop="fatherId">
          <el-select
            v-model="form.fatherId"
            placeholder="请选择父亲"
            style="width: 100%"
            filterable
            clearable
          >
            <el-option
              v-for="cat in maleCats"
              :key="cat.id"
              :label="`${cat.name} (${cat.breedName})`"
              :value="cat.id"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="母亲" prop="motherId">
          <el-select
            v-model="form.motherId"
            placeholder="请选择母亲"
            style="width: 100%"
            filterable
            clearable
          >
            <el-option
              v-for="cat in femaleCats"
              :key="cat.id"
              :label="`${cat.name} (${cat.breedName})`"
              :value="cat.id"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="注册号" prop="registrationNumber">
          <el-input v-model="form.registrationNumber" placeholder="请输入注册号" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="芯片号" prop="microchipNumber">
          <el-input v-model="form.microchipNumber" placeholder="请输入芯片号" />
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 健康信息 -->
    <el-divider content-position="left">健康信息</el-divider>
    
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="健康状态" prop="healthStatus">
          <el-select v-model="form.healthStatus" placeholder="请选择健康状态" style="width: 100%">
            <el-option label="优秀" value="EXCELLENT" />
            <el-option label="良好" value="GOOD" />
            <el-option label="一般" value="FAIR" />
            <el-option label="较差" value="POOR" />
            <el-option label="危急" value="CRITICAL" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="是否绝育" prop="isNeutered">
          <el-radio-group v-model="form.isNeutered">
            <el-radio :value="true">是</el-radio>
            <el-radio :value="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>

    <el-form-item label="特殊标记" prop="specialMarks">
      <el-input
        v-model="form.specialMarks"
        type="textarea"
        :rows="2"
        placeholder="请输入特殊标记或特征"
      />
    </el-form-item>

    <el-form-item label="描述" prop="description">
      <el-input
        v-model="form.description"
        type="textarea"
        :rows="3"
        placeholder="请输入猫咪描述"
      />
    </el-form-item>

    <!-- 照片上传 -->
    <el-form-item label="照片">
      <el-upload
        class="photo-uploader"
        :action="uploadUrl"
        :headers="uploadHeaders"
        :show-file-list="false"
        :on-success="handlePhotoSuccess"
        :before-upload="beforePhotoUpload"
        accept="image/*"
        multiple
      >
        <el-button type="primary" :icon="Plus">上传照片</el-button>
      </el-upload>
      
      <div class="photo-preview" v-if="photos.length > 0">
        <div v-for="(photo, index) in photos" :key="index" class="photo-item">
          <el-image
            :src="photo.url || photo"
            fit="cover"
            class="photo-image"
          />
          <div class="photo-actions">
            <el-button size="small" type="danger" @click="removePhoto(index)">删除</el-button>
          </div>
        </div>
      </div>
    </el-form-item>

    <el-form-item>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        {{ isEdit ? '更新' : '创建' }}
      </el-button>
      <el-button @click="handleReset">重置</el-button>
      <el-button @click="$emit('cancel')">取消</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import { catApi, breedApi } from '@/api';
import { useAuthStore } from '@/stores/auth';
import type { Cat, CatBreed } from '@/types';

interface Props {
  cat?: Cat;
  isEdit?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isEdit: false
});

const emit = defineEmits<{
  submit: [cat: Partial<Cat>];
  cancel: [];
}>();

const authStore = useAuthStore();
const formRef = ref<FormInstance>();

const form = ref<Partial<Cat>>({
  name: '',
  breedId: undefined,
  gender: 'MALE',
  dateOfBirth: '',
  color: '',
  weight: undefined,
  status: 'PENDING_ADOPTION',
  price: undefined,
  fatherId: undefined,
  motherId: undefined,
  registrationNumber: '',
  microchipNumber: '',
  healthStatus: 'GOOD',
  isNeutered: false,
  specialMarks: '',
  description: ''
});

const breeds = ref<CatBreed[]>([]);
const maleCats = ref<Cat[]>([]);
const femaleCats = ref<Cat[]>([]);
const photos = ref<any[]>([]);
const submitting = ref(false);

const uploadUrl = computed(() => `${import.meta.env.VITE_API_BASE_URL}/api/upload/photos`);
const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${authStore.token}`
}));

const rules: FormRules = {
  name: [
    { required: true, message: '请输入猫咪名字', trigger: 'blur' },
    { min: 1, max: 50, message: '名字长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  breedId: [
    { required: true, message: '请选择品种', trigger: 'change' }
  ],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ],
  dateOfBirth: [
    { required: true, message: '请选择出生日期', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
};

async function fetchData() {
  try {
    const [breedsData, catsData] = await Promise.all([
      breedApi.getAll(),
      catApi.getAll()
    ]);
    
    breeds.value = breedsData;
    maleCats.value = catsData.filter((cat: Cat) => cat.gender === 'MALE');
    femaleCats.value = catsData.filter((cat: Cat) => cat.gender === 'FEMALE');
  } catch (error) {
    ElMessage.error('获取数据失败');
  }
}

function beforePhotoUpload(file: File) {
  const isImage = file.type.startsWith('image/');
  const isLt5M = file.size / 1024 / 1024 < 5;

  if (!isImage) {
    ElMessage.error('只能上传图片文件!');
    return false;
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!');
    return false;
  }
  return true;
}

function handlePhotoSuccess(response: any) {
  photos.value.push(response.data);
  ElMessage.success('照片上传成功');
}

function removePhoto(index: number) {
  photos.value.splice(index, 1);
}

async function handleSubmit() {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    submitting.value = true;
    
    const catData = {
      ...form.value,
      photos: photos.value
    };
    
    emit('submit', catData);
  } catch (error) {
    ElMessage.error('请检查表单输入');
  } finally {
    submitting.value = false;
  }
}

function handleReset() {
  formRef.value?.resetFields();
  photos.value = [];
}

// 监听props变化，初始化表单
watch(() => props.cat, (newCat) => {
  if (newCat) {
    form.value = { ...newCat };
    photos.value = newCat.photos || [];
  }
}, { immediate: true });

onMounted(() => {
  fetchData();
});
</script>

<style scoped>
.cat-form {
  max-width: 800px;
}

.photo-uploader {
  margin-bottom: 15px;
}

.photo-preview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 10px;
}

.photo-item {
  position: relative;
  border-radius: 6px;
  overflow: hidden;
}

.photo-image {
  width: 100%;
  height: 120px;
}

.photo-actions {
  position: absolute;
  top: 5px;
  right: 5px;
  opacity: 0;
  transition: opacity 0.3s;
}

.photo-item:hover .photo-actions {
  opacity: 1;
}
</style>
