<template>
  <div class="health-assessment">
    <div class="page-header">
      <h1>健康评估</h1>
      <p>猫咪健康状况综合评估</p>
      <div class="header-actions">
        <el-button type="primary" @click="showAssessmentDialog = true">
          <el-icon><Plus /></el-icon>
          新建评估
        </el-button>
        <el-button @click="generateHealthReport">
          <el-icon><Document /></el-icon>
          生成报告
        </el-button>
      </div>
    </div>

    <!-- 健康概览 -->
    <el-row :gutter="20" class="overview-section">
      <el-col :span="8">
        <el-card class="health-overview-card">
          <template #header>
            <span>整体健康状况</span>
          </template>
          
          <div class="health-chart">
            <div class="chart-container">
              <div class="health-score">
                <div class="score-circle" :class="getHealthScoreClass(overallHealth.score)">
                  <span class="score-number">{{ overallHealth.score }}</span>
                  <span class="score-label">分</span>
                </div>
                <div class="score-description">{{ getHealthScoreText(overallHealth.score) }}</div>
              </div>
            </div>
            
            <div class="health-indicators">
              <div class="indicator-item">
                <span class="indicator-label">体重指数</span>
                <el-progress
                  :percentage="overallHealth.bmiScore"
                  :color="getIndicatorColor(overallHealth.bmiScore)"
                  :stroke-width="8"
                />
              </div>
              <div class="indicator-item">
                <span class="indicator-label">疫苗完整性</span>
                <el-progress
                  :percentage="overallHealth.vaccineScore"
                  :color="getIndicatorColor(overallHealth.vaccineScore)"
                  :stroke-width="8"
                />
              </div>
              <div class="indicator-item">
                <span class="indicator-label">体检频率</span>
                <el-progress
                  :percentage="overallHealth.checkupScore"
                  :color="getIndicatorColor(overallHealth.checkupScore)"
                  :stroke-width="8"
                />
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="16">
        <el-card class="health-trends-card">
          <template #header>
            <span>健康趋势</span>
          </template>
          
          <div class="trends-chart">
            <ChartCard
              type="line"
              :data="healthTrendsData"
              :options="healthTrendsOptions"
              height="300px"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 猫咪健康列表 -->
    <el-card class="cats-health-card">
      <template #header>
        <div class="card-header">
          <span>猫咪健康状况</span>
          <div class="filter-controls">
            <el-select v-model="healthFilter" placeholder="健康状态" style="width: 150px">
              <el-option label="全部" value="all" />
              <el-option label="优秀" value="excellent" />
              <el-option label="良好" value="good" />
              <el-option label="一般" value="fair" />
              <el-option label="需关注" value="poor" />
            </el-select>
            <el-input
              v-model="searchKeyword"
              placeholder="搜索猫咪"
              style="width: 200px"
              @input="filterCats"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
        </div>
      </template>
      
      <div class="cats-grid">
        <div
          v-for="cat in filteredCats"
          :key="cat.id"
          class="cat-health-card"
          @click="viewCatHealthDetail(cat)"
        >
          <div class="cat-header">
            <div class="cat-info">
              <el-avatar :src="cat.photo" :size="60">
                {{ cat.name.charAt(0) }}
              </el-avatar>
              <div class="cat-details">
                <h3>{{ cat.name }}</h3>
                <p>{{ cat.breedName }} · {{ cat.age }}岁</p>
                <el-tag :type="getHealthStatusType(cat.healthStatus)" size="small">
                  {{ getHealthStatusText(cat.healthStatus) }}
                </el-tag>
              </div>
            </div>
            <div class="health-score-mini">
              <div class="score-circle-mini" :class="getHealthScoreClass(cat.healthScore)">
                {{ cat.healthScore }}
              </div>
            </div>
          </div>
          
          <div class="health-metrics">
            <div class="metric-item">
              <span class="metric-label">体重</span>
              <span class="metric-value">{{ cat.weight }}kg</span>
              <span class="metric-trend" :class="cat.weightTrend">
                <el-icon v-if="cat.weightTrend === 'up'"><ArrowUp /></el-icon>
                <el-icon v-else-if="cat.weightTrend === 'down'"><ArrowDown /></el-icon>
                <el-icon v-else><Minus /></el-icon>
              </span>
            </div>
            <div class="metric-item">
              <span class="metric-label">最近体检</span>
              <span class="metric-value">{{ cat.lastCheckup || '无记录' }}</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">疫苗状态</span>
              <span class="metric-value" :class="getVaccineStatusClass(cat.vaccineStatus)">
                {{ getVaccineStatusText(cat.vaccineStatus) }}
              </span>
            </div>
          </div>
          
          <div class="health-alerts" v-if="cat.alerts && cat.alerts.length > 0">
            <el-tag
              v-for="alert in cat.alerts.slice(0, 2)"
              :key="alert.id"
              :type="alert.type"
              size="small"
              class="alert-tag"
            >
              {{ alert.message }}
            </el-tag>
            <span v-if="cat.alerts.length > 2" class="more-alerts">
              +{{ cat.alerts.length - 2 }}
            </span>
          </div>
        </div>
      </div>
      
      <el-empty v-if="filteredCats.length === 0" description="暂无符合条件的猫咪" />
    </el-card>

    <!-- 健康评估对话框 -->
    <el-dialog
      v-model="showAssessmentDialog"
      title="健康评估"
      width="800px"
    >
      <HealthAssessmentForm
        @submit="handleAssessmentSubmit"
        @cancel="showAssessmentDialog = false"
      />
    </el-dialog>

    <!-- 猫咪健康详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      :title="`${selectedCat?.name} - 健康详情`"
      width="900px"
    >
      <CatHealthDetail
        v-if="selectedCat"
        :cat="selectedCat"
        @update-assessment="updateAssessment"
        @schedule-checkup="scheduleCheckup"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Plus, Document, Search, ArrowUp, ArrowDown, Minus
} from '@element-plus/icons-vue'
import { healthApi } from '@/api'
import ChartCard from '@/components/ChartCard.vue'
import HealthAssessmentForm from '@/components/health/HealthAssessmentForm.vue'
import CatHealthDetail from '@/components/health/CatHealthDetail.vue'
import type { CatHealth, OverallHealth } from '@/types'

const loading = ref(false)
const showAssessmentDialog = ref(false)
const showDetailDialog = ref(false)
const selectedCat = ref<CatHealth | null>(null)
const healthFilter = ref('all')
const searchKeyword = ref('')

const overallHealth = ref<OverallHealth>({
  score: 85,
  bmiScore: 78,
  vaccineScore: 92,
  checkupScore: 85
})

const catsHealth = ref<CatHealth[]>([])

const healthTrendsData = ref({
  labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
  datasets: [{
    label: '平均健康分数',
    data: [82, 85, 83, 87, 89, 85],
    borderColor: '#409EFF',
    backgroundColor: 'rgba(64, 158, 255, 0.1)',
    tension: 0.4
  }]
})

const healthTrendsOptions = ref({
  responsive: true,
  plugins: {
    legend: {
      display: false
    }
  },
  scales: {
    y: {
      beginAtZero: false,
      min: 70,
      max: 100
    }
  }
})

const filteredCats = computed(() => {
  let filtered = catsHealth.value

  // 按健康状态筛选
  if (healthFilter.value !== 'all') {
    filtered = filtered.filter(cat => cat.healthStatus === healthFilter.value)
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(cat =>
      cat.name.toLowerCase().includes(keyword) ||
      cat.breedName.toLowerCase().includes(keyword)
    )
  }

  return filtered
})

async function fetchHealthData() {
  try {
    loading.value = true
    const [overallData, catsData] = await Promise.all([
      healthApi.getOverallHealth(),
      healthApi.getCatsHealth()
    ])
    
    overallHealth.value = overallData
    catsHealth.value = catsData
  } catch (error) {
    ElMessage.error('获取健康数据失败')
  } finally {
    loading.value = false
  }
}

function filterCats() {
  // 触发计算属性重新计算
}

function viewCatHealthDetail(cat: CatHealth) {
  selectedCat.value = cat
  showDetailDialog.value = true
}

function getHealthScoreClass(score: number) {
  if (score >= 90) return 'excellent'
  if (score >= 80) return 'good'
  if (score >= 70) return 'fair'
  return 'poor'
}

function getHealthScoreText(score: number) {
  if (score >= 90) return '优秀'
  if (score >= 80) return '良好'
  if (score >= 70) return '一般'
  return '需关注'
}

function getIndicatorColor(score: number) {
  if (score >= 90) return '#67C23A'
  if (score >= 80) return '#409EFF'
  if (score >= 70) return '#E6A23C'
  return '#F56C6C'
}

function getHealthStatusType(status: string) {
  const typeMap: Record<string, string> = {
    'excellent': 'success',
    'good': 'success',
    'fair': 'warning',
    'poor': 'danger'
  }
  return typeMap[status] || 'info'
}

function getHealthStatusText(status: string) {
  const textMap: Record<string, string> = {
    'excellent': '优秀',
    'good': '良好',
    'fair': '一般',
    'poor': '需关注'
  }
  return textMap[status] || status
}

function getVaccineStatusClass(status: string) {
  const classMap: Record<string, string> = {
    'up_to_date': 'success',
    'due_soon': 'warning',
    'overdue': 'danger'
  }
  return classMap[status] || ''
}

function getVaccineStatusText(status: string) {
  const textMap: Record<string, string> = {
    'up_to_date': '已接种',
    'due_soon': '即将到期',
    'overdue': '已过期'
  }
  return textMap[status] || status
}

async function handleAssessmentSubmit(assessmentData: any) {
  try {
    await healthApi.createHealthAssessment(assessmentData)
    ElMessage.success('健康评估创建成功')
    showAssessmentDialog.value = false
    fetchHealthData()
  } catch (error) {
    ElMessage.error('创建健康评估失败')
  }
}

function updateAssessment(cat: CatHealth) {
  // 更新评估
  ElMessage.info('更新健康评估功能')
}

function scheduleCheckup(cat: CatHealth) {
  // 安排体检
  ElMessage.info('安排体检功能')
}

function generateHealthReport() {
  // 生成健康报告
  ElMessage.info('生成健康报告功能')
}

onMounted(() => {
  fetchHealthData()
})
</script>

<style scoped>
.health-assessment {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #303133;
}

.page-header p {
  margin: 5px 0 0 0;
  color: #909399;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.overview-section,
.cats-health-card {
  margin-bottom: 20px;
}

.health-chart {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.health-score {
  text-align: center;
  margin-bottom: 20px;
}

.score-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto 10px;
  border: 8px solid;
}

.score-circle.excellent {
  border-color: #67C23A;
  background: rgba(103, 194, 58, 0.1);
}

.score-circle.good {
  border-color: #409EFF;
  background: rgba(64, 158, 255, 0.1);
}

.score-circle.fair {
  border-color: #E6A23C;
  background: rgba(230, 162, 60, 0.1);
}

.score-circle.poor {
  border-color: #F56C6C;
  background: rgba(245, 108, 108, 0.1);
}

.score-number {
  font-size: 36px;
  font-weight: bold;
  color: #303133;
}

.score-label {
  font-size: 14px;
  color: #606266;
}

.score-description {
  font-size: 16px;
  color: #606266;
}

.health-indicators {
  width: 100%;
}

.indicator-item {
  margin-bottom: 15px;
}

.indicator-label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  color: #606266;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-controls {
  display: flex;
  gap: 10px;
}

.cats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.cat-health-card {
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cat-health-card:hover {
  border-color: #409EFF;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.1);
  transform: translateY(-2px);
}

.cat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.cat-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.cat-details h3 {
  margin: 0 0 5px 0;
  color: #303133;
}

.cat-details p {
  margin: 0 0 8px 0;
  color: #909399;
  font-size: 14px;
}

.health-score-mini {
  text-align: center;
}

.score-circle-mini {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  color: white;
}

.score-circle-mini.excellent {
  background: #67C23A;
}

.score-circle-mini.good {
  background: #409EFF;
}

.score-circle-mini.fair {
  background: #E6A23C;
}

.score-circle-mini.poor {
  background: #F56C6C;
}

.health-metrics {
  margin-bottom: 15px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.metric-label {
  color: #606266;
}

.metric-value {
  color: #303133;
  font-weight: 500;
}

.metric-trend {
  margin-left: 5px;
}

.metric-trend.up {
  color: #67C23A;
}

.metric-trend.down {
  color: #F56C6C;
}

.metric-trend.stable {
  color: #909399;
}

.metric-value.success {
  color: #67C23A;
}

.metric-value.warning {
  color: #E6A23C;
}

.metric-value.danger {
  color: #F56C6C;
}

.health-alerts {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.alert-tag {
  font-size: 12px;
}

.more-alerts {
  font-size: 12px;
  color: #909399;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .cats-grid {
    grid-template-columns: 1fr;
  }
  
  .filter-controls {
    flex-direction: column;
    gap: 10px;
  }
}
</style>
