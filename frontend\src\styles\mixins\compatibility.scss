// CSS兼容性 Mixins - 解决跨浏览器兼容性问题

// SCSS Mixin 统一处理兼容性

// appearance 属性 Mixin
@mixin appearance($value: none) {
  -webkit-appearance: $value;
  -moz-appearance: $value;
  appearance: $value; // 添加标准属性支持现代浏览器
}

// image-rendering 属性 Mixin
@mixin image-rendering($value: crisp-edges) {
  @if $value == crisp-edges {
    image-rendering: -webkit-optimize-contrast; /* Edge 支持 */
    image-rendering: crisp-edges;
  } @else {
    image-rendering: $value;
  }
}

// mask 相关属性 Mixin - 正确顺序：前缀版本在前
@mixin mask($mask, $size: cover) {
  -webkit-mask: $mask;
  mask: $mask;
  -webkit-mask-size: $size;
  mask-size: $size;
}

// text-size-adjust 属性 Mixin
@mixin text-size-adjust($value: 100%) {
  -webkit-text-size-adjust: $value; // 仅 WebKit 内核支持
  text-size-adjust: $value; // 标准属性
  // Firefox 和 Safari 不支持此属性，但不会报错
}

// scrollbar 样式 Mixin - Safari/Chrome 兼容
@mixin scrollbar-style($width: 8px, $track: #f1f1f1, $thumb: #888) {
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: $thumb $track;

  /* Safari/Chrome */
  &::-webkit-scrollbar {
    width: $width;
    height: $width;
  }

  &::-webkit-scrollbar-track {
    background: $track;
    border-radius: calc($width / 2);
  }

  &::-webkit-scrollbar-thumb {
    background: $thumb;
    border-radius: calc($width / 2);
  }

  &::-webkit-scrollbar-thumb:hover {
    background: darken($thumb, 10%);
  }
}

// 高性能动画 Mixin - 只使用合成层属性
@mixin performance-animation($duration: 0.3s, $easing: ease) {
  // 提升到合成层
  will-change: transform, opacity;
  transform: translateZ(0); // 强制GPU加速
  backface-visibility: hidden;
  
  // 只对性能友好的属性进行过渡
  transition: transform $duration $easing, opacity $duration $easing;
}

// 高性能滑入动画 Mixin
@mixin slide-animation($direction: left, $distance: 100%) {
  @if $direction == left {
    transform: translateX(-$distance);
  } @else if $direction == right {
    transform: translateX($distance);
  } @else if $direction == up {
    transform: translateY(-$distance);
  } @else if $direction == down {
    transform: translateY($distance);
  }
  
  opacity: 0;
  
  &.active {
    transform: translate(0, 0);
    opacity: 1;
  }
}

// 用户偏好设置 Mixin
@mixin respect-user-preferences {
  // 减少动画偏好
  @media (prefers-reduced-motion: reduce) {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
    
    will-change: auto;
    transform: none !important;
  }
  
  // 高对比度偏好
  @media (prefers-contrast: high) {
    border: 2px solid currentColor;
    outline: 2px solid currentColor;
  }
}

// 安全区域 Mixin (iPhone X等)
@mixin safe-area($property: padding, $fallback: 1rem) {
  #{$property}-top: max($fallback, env(safe-area-inset-top));
  #{$property}-right: max($fallback, env(safe-area-inset-right));
  #{$property}-bottom: max($fallback, env(safe-area-inset-bottom));
  #{$property}-left: max($fallback, env(safe-area-inset-left));
}

// 触摸设备优化 Mixin
@mixin touch-optimization {
  @media (hover: none) and (pointer: coarse) {
    // 增大可点击区域
    min-height: 44px;
    min-width: 44px;
    
    // 移除触摸高亮
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    
    // 优化滚动
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
  }
}

// 深色模式 Mixin
@mixin dark-mode($light-color, $dark-color) {
  color: $light-color;
  
  @media (prefers-color-scheme: dark) {
    color: $dark-color;
  }
}

// 打印优化 Mixin
@mixin print-optimization {
  @media print {
    // 移除所有动画和变换
    animation: none !important;
    transition: none !important;
    transform: none !important;
    will-change: auto !important;
    
    // 重置浏览器特定样式
    -webkit-appearance: revert;
    -moz-appearance: revert;
    appearance: revert;
    
    // 优化颜色
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
}

// 现代CSS特性检测 Mixin
@mixin feature-support($property, $value, $fallback-styles) {
  // 不支持时的回退样式
  @supports not (#{$property}: #{$value}) {
    #{$fallback-styles}
  }
  
  // 支持时的现代样式
  @supports (#{$property}: #{$value}) {
    #{$property}: #{$value};
  }
}

// 浏览器特定样式 Mixin
@mixin browser-specific($browser) {
  @if $browser == safari {
    @supports (-webkit-appearance: none) and (not (appearance: none)) {
      @content;
    }
  } @else if $browser == firefox {
    @supports (-moz-appearance: none) {
      @content;
    }
  } @else if $browser == edge {
    @supports (-ms-ime-align: auto) {
      @content;
    }
  } @else if $browser == chrome {
    @supports (-webkit-background-clip: text) {
      @content;
    }
  }
}
