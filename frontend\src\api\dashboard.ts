import { http } from '@/utils/http'
import type { DashboardData } from '@/types'

export const dashboardApi = {
  // 获取仪表板统计数据
  getStats(): Promise<DashboardData> {
    return http.get<DashboardData>('/dashboard/stats')
  },

  // 获取概览统计
  getOverviewStats() {
    return http.get('/api/reports/overview-statistics')
  },

  // 获取健康统计
  getHealthStats(params?: any) {
    return http.get('/api/reports/health-statistics', { params })
  },

  // 获取繁育统计
  getBreedingStats(params?: any) {
    return http.get('/api/reports/breeding-statistics', { params })
  },

  // 获取客户统计
  getCustomerStats(params?: any) {
    return http.get('/api/reports/customer-statistics', { params })
  },

  // 获取库存统计
  getInventoryStats() {
    return http.get('/api/reports/inventory-statistics')
  },

  // 获取财务统计
  getFinanceStats(params?: any) {
    return http.get('/api/reports/financial-statistics', { params })
  },

  // 获取现金流报表
  getCashFlowReport(params?: any) {
    return http.get('/api/financial-management/cash-flow-report', { params })
  },

  // 获取预算执行报表
  getBudgetExecutionReport() {
    return http.get('/api/financial-management/budget-execution-report')
  }
}
