<template>
  <div class="ancestor-tree">
    <div class="tree-container">
      <div class="generation-level" v-for="level in generationLevels" :key="level">
        <div class="generation-label">第{{ level }}代</div>
      </div>
      
      <div class="tree-content">
        <div class="node-container">
          <cat-node :node="node" :is-root="true" @node-click="handleNodeClick"></cat-node>
        </div>
        
        <div v-if="node.father || node.mother" class="parents-container">
          <div class="parent-branch">
            <div v-if="node.father" class="node-container">
              <cat-node :node="node.father" @node-click="handleNodeClick"></cat-node>
            </div>
            <div v-else class="empty-node">无记录</div>
            
            <div v-if="node.father && (node.father.father || node.father.mother)" class="grandparents-container">
              <div class="grandparent-branch">
                <div v-if="node.father && node.father.father" class="node-container">
                  <cat-node :node="node.father.father" @node-click="handleNodeClick"></cat-node>
                </div>
                <div v-else class="empty-node">无记录</div>
              </div>
              
              <div class="grandparent-branch">
                <div v-if="node.father && node.father.mother" class="node-container">
                  <cat-node :node="node.father.mother" @node-click="handleNodeClick"></cat-node>
                </div>
                <div v-else class="empty-node">无记录</div>
              </div>
            </div>
          </div>
          
          <div class="parent-branch">
            <div v-if="node.mother" class="node-container">
              <cat-node :node="node.mother" @node-click="handleNodeClick"></cat-node>
            </div>
            <div v-else class="empty-node">无记录</div>
            
            <div v-if="node.mother && (node.mother.father || node.mother.mother)" class="grandparents-container">
              <div class="grandparent-branch">
                <div v-if="node.mother && node.mother.father" class="node-container">
                  <cat-node :node="node.mother.father" @node-click="handleNodeClick"></cat-node>
                </div>
                <div v-else class="empty-node">无记录</div>
              </div>
              
              <div class="grandparent-branch">
                <div v-if="node.mother && node.mother.mother" class="node-container">
                  <cat-node :node="node.mother.mother" @node-click="handleNodeClick"></cat-node>
                </div>
                <div v-else class="empty-node">无记录</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import CatNode from './CatNode.vue'

export default {
  name: 'AncestorTree',
  components: {
    CatNode
  },
  props: {
    node: {
      type: Object,
      required: true
    },
    generations: {
      type: Number,
      default: 3
    }
  },
  setup(props, { emit }) {
    const generationLevels = computed(() => {
      return Array.from({ length: props.generations }, (_, i) => i + 1)
    })
    
    const handleNodeClick = (node) => {
      emit('node-click', node)
    }
    
    return {
      generationLevels,
      handleNodeClick
    }
  }
}
</script>

<style scoped>
.ancestor-tree {
  width: 100%;
  overflow: auto;
}

.tree-container {
  display: flex;
  flex-direction: row;
  min-width: 800px;
}

.generation-level {
  width: 50px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-right: 1px dashed #ccc;
  padding: 0 10px;
}

.generation-label {
  writing-mode: vertical-lr;
  text-orientation: mixed;
  font-size: 12px;
  color: #666;
  font-weight: bold;
}

.tree-content {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 20px;
}

.node-container {
  margin: 10px;
}

.parents-container {
  display: flex;
  flex-direction: column;
  margin-left: 50px;
}

.parent-branch {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 20px 0;
  position: relative;
}

.parent-branch::before {
  content: '';
  position: absolute;
  left: -25px;
  top: 50%;
  width: 20px;
  height: 1px;
  background-color: #ccc;
}

.grandparents-container {
  display: flex;
  flex-direction: column;
  margin-left: 50px;
}

.grandparent-branch {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 10px 0;
  position: relative;
}

.grandparent-branch::before {
  content: '';
  position: absolute;
  left: -25px;
  top: 50%;
  width: 20px;
  height: 1px;
  background-color: #ccc;
}

.empty-node {
  width: 120px;
  height: 100px;
  border: 2px dashed #ddd;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 12px;
  margin: 10px;
  background-color: #fafafa;
}

/* 连接线样式 */
.parents-container::before {
  content: '';
  position: absolute;
  left: -25px;
  top: 0;
  bottom: 0;
  width: 1px;
  background-color: #ccc;
}

.grandparents-container::before {
  content: '';
  position: absolute;
  left: -25px;
  top: 0;
  bottom: 0;
  width: 1px;
  background-color: #ccc;
}
</style>