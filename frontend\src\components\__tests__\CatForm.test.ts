import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { ElForm, ElFormItem, ElInput, ElSelect, ElButton } from 'element-plus'
import CatForm from '../CatForm.vue'

// Mock API modules
vi.mock('@/api', () => ({
  catApi: {
    getAll: vi.fn(() => Promise.resolve([])),
    create: vi.fn(() => Promise.resolve({ id: 1 })),
    update: vi.fn(() => Promise.resolve({ id: 1 }))
  },
  breedApi: {
    getAll: vi.fn(() => Promise.resolve([
      { id: 1, name: '英国短毛猫' },
      { id: 2, name: '美国短毛猫' }
    ]))
  }
}))

// Mock auth store
vi.mock('@/stores/auth', () => ({
  useAuthStore: vi.fn(() => ({
    token: 'mock-token'
  }))
}))

// Mock environment variables
vi.mock('import.meta', () => ({
  env: {
    VITE_API_BASE_URL: 'http://localhost:8080'
  }
}))

describe('CatForm', () => {
  let wrapper: any

  beforeEach(() => {
    wrapper = mount(CatForm, {
      global: {
        components: {
          ElForm,
          ElFormItem,
          ElInput,
          ElSelect,
          ElButton
        },
        stubs: {
          'el-date-picker': true,
          'el-input-number': true,
          'el-radio-group': true,
          'el-radio': true,
          'el-upload': true,
          'el-image': true,
          'el-divider': true
        }
      }
    })
  })

  it('renders properly', () => {
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.cat-form').exists()).toBe(true)
  })

  it('displays form fields correctly', () => {
    // 检查必要的表单字段是否存在
    const nameInput = wrapper.find('input[placeholder="请输入猫咪名字"]')
    expect(nameInput.exists()).toBe(true)

    const breedSelect = wrapper.find('.el-select')
    expect(breedSelect.exists()).toBe(true)
  })

  it('emits submit event when form is submitted', async () => {
    // 填写表单
    const nameInput = wrapper.find('input[placeholder="请输入猫咪名字"]')
    await nameInput.setValue('测试猫咪')

    // 模拟表单验证通过
    const form = wrapper.findComponent({ name: 'ElForm' })
    form.vm.validate = vi.fn(() => Promise.resolve(true))

    // 点击提交按钮
    const submitButton = wrapper.find('button[type="primary"]')
    await submitButton.trigger('click')

    // 检查是否发出了submit事件
    expect(wrapper.emitted('submit')).toBeTruthy()
  })

  it('emits cancel event when cancel button is clicked', async () => {
    const cancelButton = wrapper.find('button:last-child')
    await cancelButton.trigger('click')

    expect(wrapper.emitted('cancel')).toBeTruthy()
  })

  it('validates required fields', async () => {
    const form = wrapper.findComponent({ name: 'ElForm' })
    
    // 模拟表单验证失败
    form.vm.validate = vi.fn(() => Promise.reject(new Error('Validation failed')))

    const submitButton = wrapper.find('button[type="primary"]')
    await submitButton.trigger('click')

    // 验证失败时不应该发出submit事件
    expect(wrapper.emitted('submit')).toBeFalsy()
  })

  it('initializes form with cat data when editing', async () => {
    const catData = {
      id: 1,
      name: '测试猫咪',
      breedId: 1,
      gender: 'MALE',
      dateOfBirth: '2023-01-01',
      color: '白色',
      status: 'PENDING_ADOPTION'
    }

    wrapper = mount(CatForm, {
      props: {
        cat: catData,
        isEdit: true
      },
      global: {
        components: {
          ElForm,
          ElFormItem,
          ElInput,
          ElSelect,
          ElButton
        },
        stubs: {
          'el-date-picker': true,
          'el-input-number': true,
          'el-radio-group': true,
          'el-radio': true,
          'el-upload': true,
          'el-image': true,
          'el-divider': true
        }
      }
    })

    await wrapper.vm.$nextTick()

    // 检查表单是否用猫咪数据初始化
    expect(wrapper.vm.form.name).toBe('测试猫咪')
    expect(wrapper.vm.form.breedId).toBe(1)
    expect(wrapper.vm.form.gender).toBe('MALE')
  })

  it('handles photo upload correctly', async () => {
    const mockFile = new File([''], 'test.jpg', { type: 'image/jpeg' })
    
    // 模拟照片上传成功
    const mockResponse = {
      data: {
        id: 1,
        url: 'http://example.com/photo.jpg'
      }
    }

    wrapper.vm.handlePhotoSuccess(mockResponse)

    expect(wrapper.vm.photos).toHaveLength(1)
    expect(wrapper.vm.photos[0]).toEqual(mockResponse.data)
  })

  it('validates photo upload file type', () => {
    const invalidFile = new File([''], 'test.txt', { type: 'text/plain' })
    const validFile = new File([''], 'test.jpg', { type: 'image/jpeg' })

    expect(wrapper.vm.beforePhotoUpload(invalidFile)).toBe(false)
    expect(wrapper.vm.beforePhotoUpload(validFile)).toBe(true)
  })

  it('validates photo upload file size', () => {
    // 创建一个超过5MB的文件
    const largeFile = new File(['x'.repeat(6 * 1024 * 1024)], 'large.jpg', { type: 'image/jpeg' })
    const smallFile = new File([''], 'small.jpg', { type: 'image/jpeg' })

    expect(wrapper.vm.beforePhotoUpload(largeFile)).toBe(false)
    expect(wrapper.vm.beforePhotoUpload(smallFile)).toBe(true)
  })

  it('removes photos correctly', () => {
    wrapper.vm.photos = [
      { id: 1, url: 'photo1.jpg' },
      { id: 2, url: 'photo2.jpg' }
    ]

    wrapper.vm.removePhoto(0)

    expect(wrapper.vm.photos).toHaveLength(1)
    expect(wrapper.vm.photos[0].id).toBe(2)
  })

  it('resets form correctly', async () => {
    // 设置一些表单数据
    wrapper.vm.form.name = '测试猫咪'
    wrapper.vm.photos = [{ id: 1, url: 'photo.jpg' }]

    // 模拟表单重置
    const form = wrapper.findComponent({ name: 'ElForm' })
    form.vm.resetFields = vi.fn()

    wrapper.vm.handleReset()

    expect(form.vm.resetFields).toHaveBeenCalled()
    expect(wrapper.vm.photos).toHaveLength(0)
  })
})
