<template>
  <div class="checkup-record">
    <div class="page-header">
      <h1>体检记录</h1>
      <p>猫咪健康体检记录管理</p>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          添加体检记录
        </el-button>
        <el-button @click="showScheduleDialog = true">
          <el-icon><Calendar /></el-icon>
          安排体检
        </el-button>
      </div>
    </div>

    <!-- 体检统计 -->
    <el-row :gutter="20" class="stats-section">
      <el-col :span="6">
        <StatCard
          title="本月体检"
          :value="checkupStats.thisMonth"
          icon="FirstAidKit"
          color="#409EFF"
          :trend="checkupStats.monthlyTrend"
        />
      </el-col>
      <el-col :span="6">
        <StatCard
          title="待体检"
          :value="checkupStats.pending"
          icon="Clock"
          color="#E6A23C"
          :trend="checkupStats.pendingTrend"
        />
      </el-col>
      <el-col :span="6">
        <StatCard
          title="健康异常"
          :value="checkupStats.abnormal"
          icon="Warning"
          color="#F56C6C"
          :trend="checkupStats.abnormalTrend"
        />
      </el-col>
      <el-col :span="6">
        <StatCard
          title="体检覆盖率"
          :value="checkupStats.coverageRate"
          icon="TrendCharts"
          color="#67C23A"
          format="percentage"
          :trend="checkupStats.coverageTrend"
        />
      </el-col>
    </el-row>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <el-form :model="filters" inline>
        <el-form-item label="猫咪">
          <el-select v-model="filters.catId" placeholder="选择猫咪" clearable style="width: 200px">
            <el-option
              v-for="cat in cats"
              :key="cat.id"
              :label="cat.name"
              :value="cat.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="体检类型">
          <el-select v-model="filters.checkupType" placeholder="选择类型" clearable style="width: 150px">
            <el-option label="常规体检" value="routine" />
            <el-option label="专项检查" value="specialized" />
            <el-option label="术前检查" value="pre_surgery" />
            <el-option label="术后复查" value="post_surgery" />
            <el-option label="疾病诊断" value="diagnosis" />
          </el-select>
        </el-form-item>
        <el-form-item label="体检日期">
          <el-date-picker
            v-model="filters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item label="健康状态">
          <el-select v-model="filters.healthStatus" placeholder="选择状态" clearable style="width: 120px">
            <el-option label="正常" value="normal" />
            <el-option label="异常" value="abnormal" />
            <el-option label="需关注" value="attention" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="fetchCheckupRecords">查询</el-button>
          <el-button @click="resetFilters">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 体检记录列表 -->
    <el-card class="records-card">
      <el-table :data="checkupRecords" v-loading="loading" stripe>
        <el-table-column label="猫咪" width="150">
          <template #default="scope">
            <div class="cat-info">
              <el-avatar :src="scope.row.catPhoto" :size="30">
                {{ scope.row.catName.charAt(0) }}
              </el-avatar>
              <span>{{ scope.row.catName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="checkupDate" label="体检日期" width="120">
          <template #default="scope">
            {{ formatDate(scope.row.checkupDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="checkupType" label="体检类型" width="120">
          <template #default="scope">
            {{ getCheckupTypeText(scope.row.checkupType) }}
          </template>
        </el-table-column>
        <el-table-column prop="veterinarian" label="兽医" width="100" />
        <el-table-column prop="weight" label="体重(kg)" width="100" />
        <el-table-column prop="temperature" label="体温(°C)" width="100" />
        <el-table-column prop="heartRate" label="心率" width="80" />
        <el-table-column prop="healthStatus" label="健康状态" width="100">
          <template #default="scope">
            <el-tag :type="getHealthStatusType(scope.row.healthStatus)">
              {{ getHealthStatusText(scope.row.healthStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="viewDetail(scope.row)">详情</el-button>
            <el-button size="small" @click="editRecord(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="deleteRecord(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="fetchCheckupRecords"
        @current-change="fetchCheckupRecords"
      />
    </el-card>

    <!-- 新增/编辑体检记录对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingRecord ? '编辑体检记录' : '添加体检记录'"
      width="800px"
    >
      <CheckupRecordForm
        :record="editingRecord"
        @submit="handleSubmit"
        @cancel="handleCancel"
      />
    </el-dialog>

    <!-- 安排体检对话框 -->
    <el-dialog
      v-model="showScheduleDialog"
      title="安排体检"
      width="600px"
    >
      <CheckupScheduleForm
        @submit="handleScheduleSubmit"
        @cancel="showScheduleDialog = false"
      />
    </el-dialog>

    <!-- 体检详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="体检记录详情"
      width="900px"
    >
      <CheckupRecordDetail
        v-if="selectedRecord"
        :record="selectedRecord"
        @edit="editRecord"
        @generate-report="generateReport"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Calendar, FirstAidKit, Clock, Warning, TrendCharts
} from '@element-plus/icons-vue'
import { healthApi, catApi } from '@/api'
import StatCard from '@/components/StatCard.vue'
import CheckupRecordForm from '@/components/health/CheckupRecordForm.vue'
import CheckupScheduleForm from '@/components/health/CheckupScheduleForm.vue'
import CheckupRecordDetail from '@/components/health/CheckupRecordDetail.vue'
import type { CheckupRecord, CheckupStats, Cat } from '@/types'

const loading = ref(false)
const showCreateDialog = ref(false)
const showScheduleDialog = ref(false)
const showDetailDialog = ref(false)
const editingRecord = ref<CheckupRecord | null>(null)
const selectedRecord = ref<CheckupRecord | null>(null)

const filters = ref({
  catId: null,
  checkupType: null,
  dateRange: null,
  healthStatus: null
})

const pagination = ref({
  page: 1,
  size: 20,
  total: 0
})

const checkupStats = ref<CheckupStats>({
  thisMonth: 0,
  pending: 0,
  abnormal: 0,
  coverageRate: 0,
  monthlyTrend: { value: 0, type: 'flat' },
  pendingTrend: { value: 0, type: 'flat' },
  abnormalTrend: { value: 0, type: 'flat' },
  coverageTrend: { value: 0, type: 'flat' }
})

const checkupRecords = ref<CheckupRecord[]>([])
const cats = ref<Cat[]>([])

async function fetchCheckupRecords() {
  try {
    loading.value = true
    const params = {
      page: pagination.value.page,
      size: pagination.value.size,
      ...filters.value
    }
    
    const response = await healthApi.getCheckupRecords(params)
    checkupRecords.value = response.data
    pagination.value.total = response.total
  } catch (error) {
    ElMessage.error('获取体检记录失败')
  } finally {
    loading.value = false
  }
}

async function fetchDashboardData() {
  try {
    const [statsData, catsData] = await Promise.all([
      healthApi.getCheckupStats(),
      catApi.getAll()
    ])
    
    checkupStats.value = statsData
    cats.value = catsData
  } catch (error) {
    ElMessage.error('获取体检数据失败')
  }
}

function resetFilters() {
  filters.value = {
    catId: null,
    checkupType: null,
    dateRange: null,
    healthStatus: null
  }
  pagination.value.page = 1
  fetchCheckupRecords()
}

function viewDetail(record: CheckupRecord) {
  selectedRecord.value = record
  showDetailDialog.value = true
}

function editRecord(record: CheckupRecord) {
  editingRecord.value = { ...record }
  showCreateDialog.value = true
}

async function deleteRecord(id: number) {
  try {
    await ElMessageBox.confirm('确定要删除这条体检记录吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await healthApi.deleteCheckupRecord(id)
    ElMessage.success('删除成功')
    fetchCheckupRecords()
    fetchDashboardData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

async function handleSubmit(recordData: Partial<CheckupRecord>) {
  try {
    if (editingRecord.value) {
      await healthApi.updateCheckupRecord(editingRecord.value.id, recordData)
      ElMessage.success('更新成功')
    } else {
      await healthApi.createCheckupRecord(recordData)
      ElMessage.success('创建成功')
    }
    
    showCreateDialog.value = false
    editingRecord.value = null
    fetchCheckupRecords()
    fetchDashboardData()
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

function handleCancel() {
  showCreateDialog.value = false
  editingRecord.value = null
}

async function handleScheduleSubmit(scheduleData: any) {
  try {
    await healthApi.scheduleCheckup(scheduleData)
    ElMessage.success('体检安排成功')
    showScheduleDialog.value = false
    fetchCheckupRecords()
    fetchDashboardData()
  } catch (error) {
    ElMessage.error('安排体检失败')
  }
}

function generateReport(record: CheckupRecord) {
  // 生成体检报告
  ElMessage.info('生成体检报告功能')
}

function formatDate(date: string | Date) {
  return new Date(date).toLocaleDateString('zh-CN')
}

function getCheckupTypeText(type: string) {
  const typeMap: Record<string, string> = {
    'routine': '常规体检',
    'specialized': '专项检查',
    'pre_surgery': '术前检查',
    'post_surgery': '术后复查',
    'diagnosis': '疾病诊断'
  }
  return typeMap[type] || type
}

function getHealthStatusType(status: string) {
  const typeMap: Record<string, string> = {
    'normal': 'success',
    'abnormal': 'danger',
    'attention': 'warning'
  }
  return typeMap[status] || 'info'
}

function getHealthStatusText(status: string) {
  const textMap: Record<string, string> = {
    'normal': '正常',
    'abnormal': '异常',
    'attention': '需关注'
  }
  return textMap[status] || status
}

onMounted(() => {
  fetchCheckupRecords()
  fetchDashboardData()
})
</script>

<style scoped>
.checkup-record {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #303133;
}

.page-header p {
  margin: 5px 0 0 0;
  color: #909399;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.stats-section,
.filter-card,
.records-card {
  margin-bottom: 20px;
}

.cat-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.cat-info span {
  font-size: 14px;
  color: #303133;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
}
</style>
