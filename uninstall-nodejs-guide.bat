@echo off
chcp 65001 >nul
echo ========================================
echo Node.js 完全卸载指导
echo ========================================
echo.

echo 🗑️ 步骤1: 卸载Node.js程序
echo ----------------------------------------
echo.
echo 请按以下步骤操作:
echo.
echo 1. 按 Win + R 键
echo 2. 输入: appwiz.cpl
echo 3. 按回车键打开"程序和功能"
echo.
echo 在程序列表中找到并卸载:
echo   ✓ Node.js
echo   ✓ npm (如果单独列出)
echo   ✓ 任何其他Node.js相关程序
echo.

echo 正在为您打开"程序和功能"...
start appwiz.cpl
echo.
echo 请完成卸载后按任意键继续...
pause >nul

echo.
echo 🗂️ 步骤2: 删除残留目录
echo ----------------------------------------
echo.

echo 检查并删除以下目录:
echo.

echo 检查: C:\Program Files\nodejs\
if exist "C:\Program Files\nodejs\" (
    echo ✓ 找到目录，正在删除...
    rmdir /s /q "C:\Program Files\nodejs\" 2>nul
    if exist "C:\Program Files\nodejs\" (
        echo ❌ 删除失败，请手动删除此目录
    ) else (
        echo ✅ 删除成功
    )
) else (
    echo ℹ️ 目录不存在
)
echo.

echo 检查: C:\Program Files (x86)\nodejs\
if exist "C:\Program Files (x86)\nodejs\" (
    echo ✓ 找到目录，正在删除...
    rmdir /s /q "C:\Program Files (x86)\nodejs\" 2>nul
    if exist "C:\Program Files (x86)\nodejs\" (
        echo ❌ 删除失败，请手动删除此目录
    ) else (
        echo ✅ 删除成功
    )
) else (
    echo ℹ️ 目录不存在
)
echo.

echo 检查: D:\软件\nodejs\
if exist "D:\软件\nodejs\" (
    echo ✓ 找到目录，正在删除...
    rmdir /s /q "D:\软件\nodejs\" 2>nul
    if exist "D:\软件\nodejs\" (
        echo ❌ 删除失败，请手动删除此目录
    ) else (
        echo ✅ 删除成功
    )
) else (
    echo ℹ️ 目录不存在
)
echo.

echo 检查: D:\软件\node_modules\ (问题根源)
if exist "D:\软件\node_modules\" (
    echo ⚠️ 找到问题目录，正在删除...
    rmdir /s /q "D:\软件\node_modules\" 2>nul
    if exist "D:\软件\node_modules\" (
        echo ❌ 删除失败，请手动删除此目录
        echo 这是导致问题的关键目录！
    ) else (
        echo ✅ 问题目录删除成功！
    )
) else (
    echo ℹ️ 问题目录不存在
)
echo.

echo 🗂️ 步骤3: 清理用户目录
echo ----------------------------------------
echo.

echo 检查: %APPDATA%\npm
if exist "%APPDATA%\npm" (
    echo ✓ 找到npm用户目录，正在删除...
    rmdir /s /q "%APPDATA%\npm" 2>nul
    if exist "%APPDATA%\npm" (
        echo ❌ 删除失败，请手动删除
    ) else (
        echo ✅ 删除成功
    )
) else (
    echo ℹ️ npm用户目录不存在
)
echo.

echo 检查: %APPDATA%\npm-cache
if exist "%APPDATA%\npm-cache" (
    echo ✓ 找到npm缓存目录，正在删除...
    rmdir /s /q "%APPDATA%\npm-cache" 2>nul
    if exist "%APPDATA%\npm-cache" (
        echo ❌ 删除失败，请手动删除
    ) else (
        echo ✅ 删除成功
    )
) else (
    echo ℹ️ npm缓存目录不存在
)
echo.

echo 🔧 步骤4: 清理环境变量
echo ----------------------------------------
echo.
echo 需要手动清理环境变量:
echo.
echo 1. 右键"此电脑" → "属性"
echo 2. 点击"高级系统设置"
echo 3. 点击"环境变量"
echo 4. 在"系统变量"中找到"Path"，点击"编辑"
echo 5. 删除包含以下内容的所有路径:
echo    - nodejs
echo    - npm  
echo    - D:\软件\node_modules
echo    - D:\软件\nodejs
echo 6. 删除"NODE_PATH"变量(如果存在)
echo 7. 点击"确定"保存所有更改
echo.

echo 正在为您打开系统属性...
start sysdm.cpl
echo.
echo 请完成环境变量清理后按任意键继续...
pause >nul

echo.
echo ✅ 步骤5: 卸载完成验证
echo ----------------------------------------
echo.

echo 验证卸载是否完成:
echo.

echo 测试Node.js:
node --version 2>nul
if %errorlevel% equ 0 (
    echo ❌ Node.js仍然可以执行，卸载不完整
    echo 请检查环境变量是否清理干净
) else (
    echo ✅ Node.js已完全卸载
)
echo.

echo 测试npm:
npm --version 2>nul
if %errorlevel% equ 0 (
    echo ❌ npm仍然可以执行，卸载不完整
) else (
    echo ✅ npm已完全卸载
)
echo.

echo ========================================
echo 卸载完成！
echo ========================================
echo.
echo 接下来请:
echo 1. 重启命令行窗口
echo 2. 运行 install-new-nodejs.bat 安装新版本
echo 3. 或手动访问 https://nodejs.org 下载安装
echo.

pause
