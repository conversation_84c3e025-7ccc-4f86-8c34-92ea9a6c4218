-- 数据库性能监控脚本
-- 用于监控和分析数据库性能

-- ================================
-- 1. 索引使用情况分析
-- ================================

-- 查看表的索引使用统计
SELECT 
    TABLE_SCHEMA,
    TABLE_NAME,
    INDEX_NAME,
    CARDINALITY,
    NULLABLE,
    INDEX_TYPE,
    COMMENT
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = 'cat_shelter_db'
ORDER BY TABLE_NAME, INDEX_NAME;

-- 查看未使用的索引（需要开启性能监控）
SELECT 
    object_schema,
    object_name,
    index_name
FROM performance_schema.table_io_waits_summary_by_index_usage 
WHERE object_schema = 'cat_shelter_db'
  AND index_name IS NOT NULL
  AND count_star = 0
ORDER BY object_name, index_name;

-- ================================
-- 2. 慢查询分析
-- ================================

-- 查看慢查询统计
SELECT 
    schema_name,
    digest_text,
    count_star,
    sum_timer_wait/1000000000000 as sum_timer_wait_sec,
    avg_timer_wait/1000000000000 as avg_timer_wait_sec,
    sum_rows_examined,
    sum_rows_sent,
    first_seen,
    last_seen
FROM performance_schema.events_statements_summary_by_digest 
WHERE schema_name = 'cat_shelter_db'
ORDER BY sum_timer_wait DESC
LIMIT 20;

-- ================================
-- 3. 表空间使用情况
-- ================================

-- 查看表大小和行数统计
SELECT 
    TABLE_NAME,
    TABLE_ROWS,
    ROUND(DATA_LENGTH / 1024 / 1024, 2) AS 'Data Size (MB)',
    ROUND(INDEX_LENGTH / 1024 / 1024, 2) AS 'Index Size (MB)',
    ROUND((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024, 2) AS 'Total Size (MB)',
    ROUND(INDEX_LENGTH / DATA_LENGTH, 2) AS 'Index/Data Ratio'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'cat_shelter_db'
  AND TABLE_TYPE = 'BASE TABLE'
ORDER BY (DATA_LENGTH + INDEX_LENGTH) DESC;

-- ================================
-- 4. 查询性能测试
-- ================================

-- 测试常用查询的执行计划

-- 1. 按品种查询猫咪
EXPLAIN FORMAT=JSON
SELECT c.*, cb.name as breed_name 
FROM cats c 
LEFT JOIN cat_breeds cb ON c.breed_id = cb.id 
WHERE cb.name = '英国短毛猫';

-- 2. 按状态和价格范围查询
EXPLAIN FORMAT=JSON
SELECT * FROM cats 
WHERE status = 'AVAILABLE' 
  AND for_sale = true 
  AND price BETWEEN 1000 AND 5000;

-- 3. 查询猫咪的健康记录
EXPLAIN FORMAT=JSON
SELECT c.name, chr.record_type, chr.record_date, chr.description
FROM cats c
JOIN cat_health_records chr ON c.id = chr.cat_id
WHERE c.id = 1
ORDER BY chr.record_date DESC;

-- 4. 查询猫咪的媒体文件
EXPLAIN FORMAT=JSON
SELECT c.name, cm.media_type, cm.file_path, cm.is_primary
FROM cats c
JOIN cat_media cm ON c.id = cm.cat_id
WHERE c.id = 1
ORDER BY cm.sort_order;

-- ================================
-- 5. 数据完整性检查
-- ================================

-- 检查孤儿记录
-- 健康记录中引用不存在的猫咪
SELECT 'Orphan health records' as issue_type, COUNT(*) as count
FROM cat_health_records chr
LEFT JOIN cats c ON chr.cat_id = c.id
WHERE c.id IS NULL;

-- 媒体文件中引用不存在的猫咪
SELECT 'Orphan media files' as issue_type, COUNT(*) as count
FROM cat_media cm
LEFT JOIN cats c ON cm.cat_id = c.id
WHERE c.id IS NULL;

-- 猫咪引用不存在的品种
SELECT 'Cats with invalid breed' as issue_type, COUNT(*) as count
FROM cats c
LEFT JOIN cat_breeds cb ON c.breed_id = cb.id
WHERE c.breed_id IS NOT NULL AND cb.id IS NULL;

-- 猫咪引用不存在的父母
SELECT 'Cats with invalid father' as issue_type, COUNT(*) as count
FROM cats c1
LEFT JOIN cats c2 ON c1.father_id = c2.id
WHERE c1.father_id IS NOT NULL AND c2.id IS NULL;

SELECT 'Cats with invalid mother' as issue_type, COUNT(*) as count
FROM cats c1
LEFT JOIN cats c2 ON c1.mother_id = c2.id
WHERE c1.mother_id IS NOT NULL AND c2.id IS NULL;

-- ================================
-- 6. 业务数据统计
-- ================================

-- 猫咪数量统计
SELECT 
    '总猫咪数' as metric,
    COUNT(*) as value
FROM cats
UNION ALL
SELECT 
    '可售猫咪数',
    COUNT(*)
FROM cats 
WHERE for_sale = true
UNION ALL
SELECT 
    '已售出猫咪数',
    COUNT(*)
FROM cats 
WHERE status IN ('SOLD', 'ADOPTED')
UNION ALL
SELECT 
    '健康猫咪数',
    COUNT(*)
FROM cats 
WHERE health_status = 'HEALTHY';

-- 品种分布统计
SELECT 
    COALESCE(cb.name, '未分类') as breed_name,
    COUNT(c.id) as cat_count,
    ROUND(COUNT(c.id) * 100.0 / (SELECT COUNT(*) FROM cats), 2) as percentage
FROM cats c
LEFT JOIN cat_breeds cb ON c.breed_id = cb.id
GROUP BY cb.name
ORDER BY cat_count DESC;

-- 年龄分布统计
SELECT 
    CASE 
        WHEN TIMESTAMPDIFF(MONTH, date_of_birth, CURDATE()) < 6 THEN '0-6个月'
        WHEN TIMESTAMPDIFF(MONTH, date_of_birth, CURDATE()) < 12 THEN '6-12个月'
        WHEN TIMESTAMPDIFF(MONTH, date_of_birth, CURDATE()) < 24 THEN '1-2岁'
        WHEN TIMESTAMPDIFF(MONTH, date_of_birth, CURDATE()) < 60 THEN '2-5岁'
        ELSE '5岁以上'
    END as age_group,
    COUNT(*) as cat_count
FROM cats 
WHERE date_of_birth IS NOT NULL
GROUP BY age_group
ORDER BY 
    CASE 
        WHEN TIMESTAMPDIFF(MONTH, date_of_birth, CURDATE()) < 6 THEN 1
        WHEN TIMESTAMPDIFF(MONTH, date_of_birth, CURDATE()) < 12 THEN 2
        WHEN TIMESTAMPDIFF(MONTH, date_of_birth, CURDATE()) < 24 THEN 3
        WHEN TIMESTAMPDIFF(MONTH, date_of_birth, CURDATE()) < 60 THEN 4
        ELSE 5
    END;

-- 价格分布统计
SELECT 
    CASE 
        WHEN price < 1000 THEN '1000元以下'
        WHEN price < 2000 THEN '1000-2000元'
        WHEN price < 3000 THEN '2000-3000元'
        WHEN price < 5000 THEN '3000-5000元'
        ELSE '5000元以上'
    END as price_range,
    COUNT(*) as cat_count,
    AVG(price) as avg_price
FROM cats 
WHERE price IS NOT NULL AND for_sale = true
GROUP BY price_range
ORDER BY 
    CASE 
        WHEN price < 1000 THEN 1
        WHEN price < 2000 THEN 2
        WHEN price < 3000 THEN 3
        WHEN price < 5000 THEN 4
        ELSE 5
    END;

-- ================================
-- 7. 性能优化建议
-- ================================

-- 查找缺少索引的外键
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE
WHERE TABLE_SCHEMA = 'cat_shelter_db'
  AND REFERENCED_TABLE_NAME IS NOT NULL
  AND COLUMN_NAME NOT IN (
    SELECT COLUMN_NAME 
    FROM information_schema.STATISTICS 
    WHERE TABLE_SCHEMA = 'cat_shelter_db'
      AND TABLE_NAME = KEY_COLUMN_USAGE.TABLE_NAME
      AND COLUMN_NAME = KEY_COLUMN_USAGE.COLUMN_NAME
  );

-- 查找重复的索引
SELECT 
    s1.TABLE_NAME,
    s1.INDEX_NAME as index1,
    s2.INDEX_NAME as index2,
    GROUP_CONCAT(s1.COLUMN_NAME ORDER BY s1.SEQ_IN_INDEX) as columns
FROM information_schema.STATISTICS s1
JOIN information_schema.STATISTICS s2 ON 
    s1.TABLE_SCHEMA = s2.TABLE_SCHEMA 
    AND s1.TABLE_NAME = s2.TABLE_NAME
    AND s1.COLUMN_NAME = s2.COLUMN_NAME
    AND s1.INDEX_NAME < s2.INDEX_NAME
WHERE s1.TABLE_SCHEMA = 'cat_shelter_db'
GROUP BY s1.TABLE_NAME, s1.INDEX_NAME, s2.INDEX_NAME
HAVING COUNT(*) > 1;

-- ================================
-- 8. 定期维护建议
-- ================================

-- 查看表的碎片化情况
SELECT 
    TABLE_NAME,
    ROUND(DATA_FREE / 1024 / 1024, 2) AS 'Fragmentation (MB)',
    ROUND(DATA_FREE / (DATA_LENGTH + INDEX_LENGTH + DATA_FREE) * 100, 2) AS 'Fragmentation %'
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = 'cat_shelter_db'
  AND DATA_FREE > 0
ORDER BY DATA_FREE DESC;

-- 生成优化表的SQL（当碎片化超过10%时）
SELECT 
    CONCAT('OPTIMIZE TABLE ', TABLE_NAME, ';') as optimize_sql
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = 'cat_shelter_db'
  AND DATA_FREE > 0
  AND ROUND(DATA_FREE / (DATA_LENGTH + INDEX_LENGTH + DATA_FREE) * 100, 2) > 10;

SELECT 'Performance monitoring completed!' as message;
