<template>
  <el-card class="chart-card">
    <template #header>
      <div class="chart-header">
        <div class="chart-title">
          <h3>{{ title }}</h3>
          <p v-if="subtitle">{{ subtitle }}</p>
        </div>
        <div class="chart-actions">
          <slot name="actions">
            <el-button 
              v-if="refreshable" 
              type="text" 
              :loading="loading"
              @click="handleRefresh"
            >
              <el-icon><Refresh /></el-icon>
            </el-button>
            <el-dropdown v-if="exportable" @command="handleExport">
              <el-button type="text">
                <el-icon><Download /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="png">导出为PNG</el-dropdown-item>
                  <el-dropdown-item command="jpg">导出为JPG</el-dropdown-item>
                  <el-dropdown-item command="svg">导出为SVG</el-dropdown-item>
                  <el-dropdown-item command="pdf">导出为PDF</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </slot>
        </div>
      </div>
    </template>
    
    <div class="chart-container" :style="containerStyle">
      <div v-if="loading" class="chart-loading">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>{{ loadingText }}</span>
      </div>
      
      <div v-else-if="error" class="chart-error">
        <el-icon><Warning /></el-icon>
        <span>{{ error }}</span>
        <el-button v-if="refreshable" type="text" @click="handleRefresh">
          重试
        </el-button>
      </div>
      
      <div v-else-if="!hasData" class="chart-empty">
        <el-icon><DocumentRemove /></el-icon>
        <span>{{ emptyText }}</span>
      </div>
      
      <div v-else class="chart-content">
        <slot></slot>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  Refresh, 
  Download, 
  Loading, 
  Warning, 
  DocumentRemove 
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

interface Props {
  title: string
  subtitle?: string
  height?: string | number
  loading?: boolean
  error?: string
  hasData?: boolean
  refreshable?: boolean
  exportable?: boolean
  loadingText?: string
  emptyText?: string
}

const props = withDefaults(defineProps<Props>(), {
  height: '400px',
  loading: false,
  hasData: true,
  refreshable: true,
  exportable: true,
  loadingText: '加载中...',
  emptyText: '暂无数据'
})

const emit = defineEmits<{
  refresh: []
  export: [format: string]
}>()

// 计算属性
const containerStyle = computed(() => {
  const height = typeof props.height === 'number' ? `${props.height}px` : props.height
  return {
    height
  }
})

// 方法
const handleRefresh = () => {
  emit('refresh')
}

const handleExport = (format: string) => {
  emit('export', format)
  ElMessage.success(`正在导出${format.toUpperCase()}格式...`)
}
</script>

<style scoped lang="scss">
.chart-card {
  margin-bottom: 20px;
  
  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    
    .chart-title {
      flex: 1;
      
      h3 {
        margin: 0 0 4px 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
      
      p {
        margin: 0;
        font-size: 12px;
        color: #909399;
      }
    }
    
    .chart-actions {
      display: flex;
      align-items: center;
      gap: 8px;
      flex-shrink: 0;
    }
  }
  
  .chart-container {
    position: relative;
    width: 100%;
    
    .chart-loading,
    .chart-error,
    .chart-empty {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: #909399;
      
      .el-icon {
        font-size: 48px;
        margin-bottom: 16px;
      }
      
      span {
        font-size: 14px;
        margin-bottom: 12px;
      }
    }
    
    .chart-loading {
      .el-icon {
        color: #409eff;
      }
    }
    
    .chart-error {
      .el-icon {
        color: #f56c6c;
      }
    }
    
    .chart-empty {
      .el-icon {
        color: #c0c4cc;
      }
    }
    
    .chart-content {
      width: 100%;
      height: 100%;
    }
  }
}

@media (max-width: 768px) {
  .chart-card {
    .chart-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
      
      .chart-actions {
        align-self: flex-end;
      }
    }
  }
}
</style>
