package com.cattery.service;

import com.cattery.entity.Cat;
import com.cattery.entity.Mating;
import com.cattery.entity.Pregnancy;
import com.cattery.entity.BreedingRecord;
import com.cattery.repository.CatRepository;
import com.cattery.repository.MatingRepository;
import com.cattery.repository.PregnancyRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 繁育管理服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class BreedingService {
    
    private final MatingRepository matingRepository;
    private final PregnancyRepository pregnancyRepository;
    private final CatRepository catRepository;
    
    /**
     * 记录配种信息
     */
    public Mating recordMating(Mating mating) {
        log.info("记录配种信息: 母猫ID={}, 公猫ID={}",
            mating.getFemaleParent().getId(), mating.getMaleParent().getId());

        // 验证猫咪是否存在
        Cat femaleCat = catRepository.findById(mating.getFemaleParent().getId())
            .orElseThrow(() -> new IllegalArgumentException("母猫不存在"));
        Cat maleCat = catRepository.findById(mating.getMaleParent().getId())
            .orElseThrow(() -> new IllegalArgumentException("公猫不存在"));

        // 验证性别
        if (femaleCat.getGender() != Cat.Gender.FEMALE) {
            throw new IllegalArgumentException("选择的母猫性别不正确");
        }
        if (maleCat.getGender() != Cat.Gender.MALE) {
            throw new IllegalArgumentException("选择的公猫性别不正确");
        }

        mating.setFemaleParent(femaleCat);
        mating.setMaleParent(maleCat);
        mating.setMatingDate(LocalDateTime.now());

        return matingRepository.save(mating);
    }
    
    /**
     * 记录怀孕信息
     */
    public Pregnancy recordPregnancy(Pregnancy pregnancy) {
        log.info("记录怀孕信息: 母猫ID={}", pregnancy.getMother().getId());

        // 验证母猫是否存在
        Cat motherCat = catRepository.findById(pregnancy.getMother().getId())
            .orElseThrow(() -> new IllegalArgumentException("母猫不存在"));

        pregnancy.setMother(motherCat);
        pregnancy.setStatus(Pregnancy.Status.PREGNANT);

        return pregnancyRepository.save(pregnancy);
    }
    
    /**
     * 更新怀孕状态
     */
    public Pregnancy updatePregnancyStatus(Long pregnancyId, Pregnancy.Status status) {
        log.info("更新怀孕状态: pregnancyId={}, status={}", pregnancyId, status);

        Pregnancy pregnancy = pregnancyRepository.findById(pregnancyId)
            .orElseThrow(() -> new IllegalArgumentException("怀孕记录不存在"));

        pregnancy.setStatus(status);

        // 如果是分娩完成，设置实际分娩日期
        if (status == Pregnancy.Status.COMPLETED) {
            pregnancy.setBirthDate(LocalDateTime.now());
        }

        return pregnancyRepository.save(pregnancy);
    }
    
    /**
     * 获取所有配种记录
     */
    @Transactional(readOnly = true)
    public Page<Mating> getAllMatings(Pageable pageable) {
        log.debug("获取所有配种记录: pageable={}", pageable);
        return matingRepository.findAll(pageable);
    }
    
    /**
     * 根据母猫获取配种记录
     */
    @Transactional(readOnly = true)
    public List<Mating> getMatingsByFemaleCat(Long femaleCatId) {
        log.debug("根据母猫获取配种记录: femaleCatId={}", femaleCatId);
        return matingRepository.findByFemaleParentIdOrderByMatingDateDesc(femaleCatId);
    }

    /**
     * 根据公猫获取配种记录
     */
    @Transactional(readOnly = true)
    public List<Mating> getMatingsByMaleCat(Long maleCatId) {
        log.debug("根据公猫获取配种记录: maleCatId={}", maleCatId);
        return matingRepository.findByMaleParentIdOrderByMatingDateDesc(maleCatId);
    }
    
    /**
     * 获取所有怀孕记录
     */
    @Transactional(readOnly = true)
    public Page<Pregnancy> getAllPregnancies(Pageable pageable) {
        log.debug("获取所有怀孕记录: pageable={}", pageable);
        return pregnancyRepository.findAll(pageable);
    }
    
    /**
     * 根据状态获取怀孕记录
     */
    @Transactional(readOnly = true)
    public List<Pregnancy> getPregnanciesByStatus(Pregnancy.Status status) {
        log.debug("根据状态获取怀孕记录: status={}", status);
        return pregnancyRepository.findByStatusOrderByExpectedBirthDateAsc(status);
    }

    /**
     * 获取当前怀孕的猫咪
     */
    @Transactional(readOnly = true)
    public List<Pregnancy> getCurrentPregnancies() {
        log.debug("获取当前怀孕的猫咪");
        return pregnancyRepository.findByStatusOrderByExpectedBirthDateAsc(
            Pregnancy.Status.PREGNANT);
    }
    
    /**
     * 获取即将分娩的猫咪
     */
    @Transactional(readOnly = true)
    public List<Pregnancy> getUpcomingDeliveries(int daysAhead) {
        log.debug("获取即将分娩的猫咪: daysAhead={}", daysAhead);
        LocalDateTime cutoffDate = LocalDateTime.now().plusDays(daysAhead);
        return pregnancyRepository.findUpcomingDeliveries(cutoffDate);
    }
    
    /**
     * 获取繁育统计
     */
    @Transactional(readOnly = true)
    public long getMatingCount(LocalDateTime startDate, LocalDateTime endDate) {
        log.debug("获取配种统计: {} - {}", startDate, endDate);
        return matingRepository.countByMatingDateBetween(startDate, endDate);
    }
    
    /**
     * 获取分娩统计
     */
    @Transactional(readOnly = true)
    public long getDeliveryCount(LocalDateTime startDate, LocalDateTime endDate) {
        log.debug("获取分娩统计: {} - {}", startDate, endDate);
        return pregnancyRepository.countByBirthDateBetween(startDate, endDate);
    }
    
    /**
     * 获取可繁育的母猫
     */
    @Transactional(readOnly = true)
    public List<Cat> getBreedableFemaleCats() {
        log.debug("获取可繁育的母猫");
        // 计算年龄范围：12个月到8年
        LocalDate now = LocalDate.now();
        LocalDate minBirthDate = now.minusYears(8); // 8年前
        LocalDate maxBirthDate = now.minusMonths(12); // 12个月前
        return catRepository.findBreedableFemaleCats(minBirthDate, maxBirthDate);
    }

    /**
     * 获取可繁育的公猫
     */
    @Transactional(readOnly = true)
    public List<Cat> getBreedableMaleCats() {
        log.debug("获取可繁育的公猫");
        // 计算年龄范围：12个月到10年
        LocalDate now = LocalDate.now();
        LocalDate minBirthDate = now.minusYears(10); // 10年前
        LocalDate maxBirthDate = now.minusMonths(12); // 12个月前
        return catRepository.findBreedableMaleCats(minBirthDate, maxBirthDate);
    }
}
