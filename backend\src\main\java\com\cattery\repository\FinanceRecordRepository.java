package com.cattery.repository;

import com.cattery.entity.FinanceRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 财务记录Repository
 */
@Repository
public interface FinanceRecordRepository extends JpaRepository<FinanceRecord, Long> {

    /**
     * 根据类型查询财务记录
     */
    Page<FinanceRecord> findByType(FinanceRecord.RecordType type, Pageable pageable);

    /**
     * 根据分类查询财务记录
     */
    Page<FinanceRecord> findByCategory(String category, Pageable pageable);

    /**
     * 根据状态查询财务记录
     */
    Page<FinanceRecord> findByStatus(FinanceRecord.RecordStatus status, Pageable pageable);

    /**
     * 根据日期范围查询财务记录
     */
    Page<FinanceRecord> findByTransactionDateBetween(LocalDate startDate, LocalDate endDate, Pageable pageable);

    /**
     * 根据关联猫咪ID查询财务记录
     */
    List<FinanceRecord> findByRelatedCatId(Long catId);

    /**
     * 根据关联客户ID查询财务记录
     */
    List<FinanceRecord> findByRelatedCustomerId(Long customerId);

    /**
     * 计算总收入
     */
    @Query("SELECT COALESCE(SUM(f.amount), 0) FROM FinanceRecord f WHERE f.type = 'INCOME' AND f.status = 'COMPLETED'")
    BigDecimal calculateTotalIncome();

    /**
     * 计算总支出
     */
    @Query("SELECT COALESCE(SUM(f.amount), 0) FROM FinanceRecord f WHERE f.type = 'EXPENSE' AND f.status = 'COMPLETED'")
    BigDecimal calculateTotalExpense();

    /**
     * 计算指定日期范围内的收入
     */
    @Query("SELECT COALESCE(SUM(f.amount), 0) FROM FinanceRecord f WHERE f.type = 'INCOME' AND f.status = 'COMPLETED' AND f.transactionDate BETWEEN :startDate AND :endDate")
    BigDecimal calculateIncomeByDateRange(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 计算指定日期范围内的支出
     */
    @Query("SELECT COALESCE(SUM(f.amount), 0) FROM FinanceRecord f WHERE f.type = 'EXPENSE' AND f.status = 'COMPLETED' AND f.transactionDate BETWEEN :startDate AND :endDate")
    BigDecimal calculateExpenseByDateRange(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 按分类统计收入
     */
    @Query("SELECT f.category, SUM(f.amount) FROM FinanceRecord f WHERE f.type = 'INCOME' AND f.status = 'COMPLETED' GROUP BY f.category")
    List<Object[]> getIncomeByCategory();

    /**
     * 按分类统计支出
     */
    @Query("SELECT f.category, SUM(f.amount) FROM FinanceRecord f WHERE f.type = 'EXPENSE' AND f.status = 'COMPLETED' GROUP BY f.category")
    List<Object[]> getExpenseByCategory();

    /**
     * 按月份统计收支
     */
    @Query("SELECT YEAR(f.transactionDate), MONTH(f.transactionDate), f.type, SUM(f.amount) " +
           "FROM FinanceRecord f WHERE f.status = 'COMPLETED' " +
           "GROUP BY YEAR(f.transactionDate), MONTH(f.transactionDate), f.type " +
           "ORDER BY YEAR(f.transactionDate), MONTH(f.transactionDate)")
    List<Object[]> getMonthlyTrend();

    /**
     * 复合条件查询
     */
    @Query("SELECT f FROM FinanceRecord f WHERE " +
           "(:type IS NULL OR f.type = :type) AND " +
           "(:category IS NULL OR f.category LIKE %:category%) AND " +
           "(:status IS NULL OR f.status = :status) AND " +
           "(:startDate IS NULL OR f.transactionDate >= :startDate) AND " +
           "(:endDate IS NULL OR f.transactionDate <= :endDate) AND " +
           "(:minAmount IS NULL OR f.amount >= :minAmount) AND " +
           "(:maxAmount IS NULL OR f.amount <= :maxAmount)")
    Page<FinanceRecord> findByConditions(
            @Param("type") FinanceRecord.RecordType type,
            @Param("category") String category,
            @Param("status") FinanceRecord.RecordStatus status,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("minAmount") BigDecimal minAmount,
            @Param("maxAmount") BigDecimal maxAmount,
            Pageable pageable
    );
}
