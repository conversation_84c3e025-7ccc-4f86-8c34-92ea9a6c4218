package com.cattery.service;

import com.cattery.dto.auth.LoginResponse;
import com.cattery.dto.auth.RegisterRequest;
import com.cattery.entity.Role;
import com.cattery.entity.User;
import com.cattery.repository.RoleRepository;
import com.cattery.repository.UserRepository;
import com.cattery.security.JwtTokenProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 认证服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AuthService {

    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtTokenProvider tokenProvider;

    /**
     * 用户注册
     */
    @Transactional
    public void register(RegisterRequest request) {
        // 验证密码匹配
        if (!request.isPasswordMatching()) {
            throw new RuntimeException("密码和确认密码不匹配");
        }

        // 检查用户名是否已存在
        if (userRepository.existsByUsername(request.getUsername())) {
            throw new RuntimeException("用户名已存在");
        }

        // 检查邮箱是否已存在
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new RuntimeException("邮箱已存在");
        }

        // 检查手机号是否已存在（如果提供）
        if (request.getPhone() != null && userRepository.existsByPhone(request.getPhone())) {
            throw new RuntimeException("手机号已存在");
        }

        // 创建用户
        User user = new User();
        user.setUsername(request.getUsername());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setEmail(request.getEmail());
        user.setPhone(request.getPhone());
        user.setRealName(request.getRealName());
        user.setEnabled(true);
        user.setAccountNonExpired(true);
        user.setAccountNonLocked(true);
        user.setCredentialsNonExpired(true);

        // 分配默认角色
        Role defaultRole = roleRepository.findByName("ROLE_USER")
            .orElseThrow(() -> new RuntimeException("默认角色不存在"));
        
        Set<Role> roles = new HashSet<>();
        roles.add(defaultRole);
        user.setRoles(roles);

        userRepository.save(user);
        
        log.info("用户注册成功: {}", request.getUsername());
    }

    /**
     * 构建登录响应
     */
    public LoginResponse buildLoginResponse(Authentication authentication, String jwt) {
        User user = (User) authentication.getPrincipal();
        
        LoginResponse response = new LoginResponse();
        response.setAccessToken(jwt);
        response.setTokenType("Bearer");
        response.setExpiresAt(tokenProvider.getExpirationDateFromToken(jwt).toInstant()
            .atZone(java.time.ZoneId.systemDefault()).toLocalDateTime());
        response.setUserId(user.getId());
        response.setUsername(user.getUsername());
        response.setEmail(user.getEmail());
        response.setRealName(user.getRealName());
        response.setAvatar(user.getAvatar());
        response.setLastLoginTime(user.getLastLoginTime());
        response.setLastLoginIp(user.getLastLoginIp());

        // 设置角色
        List<String> roles = user.getRoles().stream()
            .map(Role::getName)
            .collect(Collectors.toList());
        response.setRoles(roles);

        // 设置权限
        List<String> permissions = authentication.getAuthorities().stream()
            .map(GrantedAuthority::getAuthority)
            .collect(Collectors.toList());
        response.setPermissions(permissions);

        // 更新最后登录时间
        updateLastLoginTime(user);

        return response;
    }

    /**
     * 构建刷新响应
     */
    public LoginResponse buildRefreshResponse(String username, String newToken) {
        User user = userRepository.findByUsername(username)
            .orElseThrow(() -> new RuntimeException("用户不存在"));

        LoginResponse response = new LoginResponse();
        response.setAccessToken(newToken);
        response.setTokenType("Bearer");
        response.setExpiresAt(tokenProvider.getExpirationDateFromToken(newToken).toInstant()
            .atZone(java.time.ZoneId.systemDefault()).toLocalDateTime());
        response.setUserId(user.getId());
        response.setUsername(user.getUsername());
        response.setEmail(user.getEmail());
        response.setRealName(user.getRealName());
        response.setAvatar(user.getAvatar());

        return response;
    }

    /**
     * 构建当前用户响应
     */
    public LoginResponse buildCurrentUserResponse(Authentication authentication) {
        User user = (User) authentication.getPrincipal();

        LoginResponse response = new LoginResponse();
        response.setUserId(user.getId());
        response.setUsername(user.getUsername());
        response.setEmail(user.getEmail());
        response.setRealName(user.getRealName());
        response.setAvatar(user.getAvatar());
        response.setLastLoginTime(user.getLastLoginTime());
        response.setLastLoginIp(user.getLastLoginIp());

        // 设置角色
        List<String> roles = user.getRoles().stream()
            .map(Role::getName)
            .collect(Collectors.toList());
        response.setRoles(roles);

        // 设置权限
        List<String> permissions = authentication.getAuthorities().stream()
            .map(GrantedAuthority::getAuthority)
            .collect(Collectors.toList());
        response.setPermissions(permissions);

        return response;
    }

    /**
     * 检查用户名是否可用
     */
    public boolean isUsernameAvailable(String username) {
        return !userRepository.existsByUsername(username);
    }

    /**
     * 检查邮箱是否可用
     */
    public boolean isEmailAvailable(String email) {
        return !userRepository.existsByEmail(email);
    }

    /**
     * 更新最后登录时间
     */
    @Transactional
    private void updateLastLoginTime(User user) {
        user.setLastLoginTime(LocalDateTime.now());
        // 这里可以设置IP地址，需要从请求中获取
        userRepository.save(user);
    }
}
