package com.cattery.dto.report;

import lombok.Data;
import java.util.Map;

/**
 * 体检统计DTO
 */
@Data
public class CheckupStatisticsDTO {
    
    /**
     * 总体检次数
     */
    private Long totalCheckups;
    
    /**
     * 定期体检次数
     */
    private Long routineCheckups;
    
    /**
     * 紧急体检次数
     */
    private Long emergencyCheckups;
    
    /**
     * 按体检结果统计
     */
    private Map<String, Long> checkupResultDistribution;
    
    /**
     * 按年龄段的体检频率
     */
    private Map<String, Double> checkupFrequencyByAge;
    
    /**
     * 平均体检间隔（天）
     */
    private Double averageCheckupInterval;
    
    /**
     * 需要复查的猫咪数量
     */
    private Long catsNeedingFollowUp;
    
    /**
     * 体检合规率
     */
    private Double checkupComplianceRate;
    
    /**
     * 发现问题的体检比例
     */
    private Double issueDetectionRate;
}
