<template>
  <div class="report-dashboard-view">
    <div class="page-header">
      <h1>报表中心</h1>
    </div>

    <div class="report-cards">
      <el-row :gutter="20">
        <el-col :span="6" v-for="report in reportTypes" :key="report.id">
          <el-card class="report-card" @click="generateReport(report)">
            <div class="card-content">
              <div class="card-icon" :class="report.iconClass">
                <el-icon><component :is="report.icon" /></el-icon>
              </div>
              <div class="card-info">
                <h3>{{ report.name }}</h3>
                <p>{{ report.description }}</p>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <div class="recent-reports">
      <el-card>
        <template #header>
          <span>最近生成的报表</span>
        </template>
        <el-table :data="recentReports">
          <el-table-column prop="name" label="报表名称" />
          <el-table-column prop="type" label="报表类型" />
          <el-table-column prop="generateTime" label="生成时间" />
          <el-table-column label="操作" width="150">
            <template #default="{ row }">
              <el-button size="small" @click="viewReport(row)">查看</el-button>
              <el-button size="small" @click="downloadReport(row)">下载</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { TrendCharts, Money, User, PieChart } from '@element-plus/icons-vue'

const reportTypes = ref([
  {
    id: 1,
    name: '财务报表',
    description: '收支统计、利润分析',
    icon: Money,
    iconClass: 'finance'
  },
  {
    id: 2,
    name: '销售报表',
    description: '猫咪销售统计',
    icon: TrendCharts,
    iconClass: 'sales'
  },
  {
    id: 3,
    name: '客户报表',
    description: '客户分析报告',
    icon: User,
    iconClass: 'customer'
  },
  {
    id: 4,
    name: '库存报表',
    description: '库存状况分析',
    icon: PieChart,
    iconClass: 'inventory'
  }
])

const recentReports = ref([
  {
    id: 1,
    name: '2024年1月财务报表',
    type: '财务报表',
    generateTime: '2024-01-31 18:30:00'
  }
])

const generateReport = (report: any) => {
  ElMessage.success(`正在生成${report.name}...`)
}

const viewReport = (report: any) => {
  ElMessage.info('查看报表功能开发中')
}

const downloadReport = (report: any) => {
  ElMessage.success('报表下载中...')
}

onMounted(() => {
  // 初始化
})
</script>

<style scoped>
.report-dashboard-view {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.report-cards {
  margin-bottom: 30px;
}

.report-card {
  cursor: pointer;
  transition: all 0.3s;
}

.report-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.card-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.card-icon.finance {
  background: #67c23a;
}

.card-icon.sales {
  background: #409eff;
}

.card-icon.customer {
  background: #e6a23c;
}

.card-icon.inventory {
  background: #f56c6c;
}

.card-info h3 {
  margin: 0 0 5px 0;
  font-size: 16px;
  color: #333;
}

.card-info p {
  margin: 0;
  font-size: 12px;
  color: #666;
}
</style>
