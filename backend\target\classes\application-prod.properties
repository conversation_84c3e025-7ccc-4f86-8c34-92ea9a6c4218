# ===================================================================
# 生产环境配置
# ===================================================================

# 数据库配置
spring.datasource.url=${DB_URL}
spring.datasource.username=${DB_USERNAME}
spring.datasource.password=${DB_PASSWORD}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# 连接池配置
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000

# JPA/Hibernate配置
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true

# 日志配置
logging.level.com.catshelter.managementsystem=INFO
logging.level.org.springframework.security=WARN
logging.level.org.hibernate.SQL=WARN

# 服务器配置
server.port=${SERVER_PORT:8080}
server.compression.enabled=true
server.compression.mime-types=text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
server.compression.min-response-size=1024

# JWT配置
jwt.secret=${JWT_SECRET}
jwt.expiration=${JWT_EXPIRATION:86400000}

# CORS配置
cors.allowed-origins=${CORS_ORIGINS}

# Swagger配置 - 生产环境禁用
springdoc.swagger-ui.enabled=false
springdoc.api-docs.enabled=false

# 安全配置
server.error.include-stacktrace=never
server.error.include-message=never
