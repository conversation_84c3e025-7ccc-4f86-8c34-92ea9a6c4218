package com.cattery.repository;

import com.cattery.entity.Customer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * 客户仓库接口
 */
@Repository
public interface CustomerRepository extends JpaRepository<Customer, Long>, JpaSpecificationExecutor<Customer> {

    /**
     * 根据邮箱查找客户
     */
    Optional<Customer> findByEmail(String email);

    /**
     * 根据电话查找客户
     */
    Optional<Customer> findByPhone(String phone);

    /**
     * 根据客户类型查找客户
     */
    List<Customer> findByCustomerType(Customer.CustomerType customerType);

    /**
     * 根据状态查找客户
     */
    List<Customer> findByStatus(Customer.Status status);

    /**
     * 根据创建时间范围查找客户
     */
    List<Customer> findByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 统计指定时间范围内的新客户数量
     */
    long countByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 根据地址关键词查找客户
     */
    @Query("SELECT c FROM Customer c WHERE LOWER(c.address) LIKE LOWER(CONCAT('%', :keyword, '%'))")
    List<Customer> findByAddressContaining(@Param("keyword") String keyword);

    /**
     * 根据姓名关键词查找客户
     */
    @Query("SELECT c FROM Customer c WHERE LOWER(c.name) LIKE LOWER(CONCAT('%', :keyword, '%'))")
    List<Customer> findByNameContaining(@Param("keyword") String keyword);

    /**
     * 查找活跃客户（最近有活动的客户）
     */
    @Query("SELECT c FROM Customer c WHERE c.lastContactDate >= :cutoffDate")
    List<Customer> findActiveCustomers(@Param("cutoffDate") LocalDateTime cutoffDate);

    /**
     * 按客户类型统计数量
     */
    @Query("SELECT c.customerType, COUNT(c) FROM Customer c GROUP BY c.customerType")
    List<Object[]> countByCustomerTypeGrouped();

    /**
     * 按状态统计数量
     */
    @Query("SELECT c.status, COUNT(c) FROM Customer c GROUP BY c.status")
    List<Object[]> countByStatusGrouped();

    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 检查电话是否存在
     */
    boolean existsByPhone(String phone);

    /**
     * 根据状态分页查询
     */
    Page<Customer> findByStatus(Customer.Status status, Pageable pageable);

    /**
     * 根据状态统计数量
     */
    long countByStatus(Customer.Status status);

    /**
     * 搜索客户（姓名或邮箱）
     */
    @Query("SELECT c FROM Customer c WHERE " +
           "LOWER(c.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(c.email) LIKE LOWER(CONCAT('%', :keyword, '%'))")
    Page<Customer> findByNameContainingIgnoreCaseOrEmailContainingIgnoreCase(
        @Param("keyword") String name, @Param("keyword") String email, Pageable pageable);
}
