import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia } from 'pinia'
import LoginView from '@/views/LoginView.vue'
import { useAuthStore } from '@/stores/auth'
import { authApi } from '@/api'

// Mock API responses
const mockUser = {
  id: 1,
  username: '<EMAIL>',
  name: '测试用户',
  roles: ['ADMIN'],
  permissions: ['cat:read', 'cat:write', 'health:read', 'health:write']
}

const mockLoginResponse = {
  token: 'mock-jwt-token',
  user: mockUser,
  expiresIn: 3600
}

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// Mock API
vi.mock('@/api', () => ({
  authApi: {
    login: vi.fn(),
    logout: vi.fn(),
    refreshToken: vi.fn(),
    getCurrentUser: vi.fn(),
    checkPermission: vi.fn()
  }
}))

// Mock router
const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/login', component: LoginView },
    { path: '/dashboard', component: { template: '<div>Dashboard</div>' } },
    { path: '/cats', component: { template: '<div>Cats</div>' }, meta: { requiresAuth: true } },
    { path: '/admin', component: { template: '<div>Admin</div>' }, meta: { requiresAuth: true, roles: ['ADMIN'] } }
  ]
})

describe('Authentication and Authorization Tests', () => {
  let pinia: any
  let authStore: any

  beforeEach(() => {
    pinia = createPinia()
    authStore = useAuthStore(pinia)
    
    // 重置 mocks
    vi.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
  })

  afterEach(() => {
    authStore.$reset()
  })

  describe('Login Process', () => {
    it('should login successfully with valid credentials', async () => {
      vi.mocked(authApi.login).mockResolvedValue(mockLoginResponse)

      const wrapper = mount(LoginView, {
        global: {
          plugins: [router, pinia],
          stubs: {
            'el-form': {
              template: '<form @submit.prevent="$emit(\'submit\')"><slot /></form>',
              emits: ['submit']
            },
            'el-form-item': {
              template: '<div class="form-item"><slot /></div>'
            },
            'el-input': {
              template: '<input v-model="modelValue" @input="$emit(\'update:modelValue\', $event.target.value)" />',
              props: ['modelValue'],
              emits: ['update:modelValue']
            },
            'el-button': {
              template: '<button type="submit" @click="$emit(\'click\')"><slot /></button>',
              emits: ['click']
            },
            'el-card': {
              template: '<div class="card"><slot /></div>'
            }
          }
        }
      })

      // 填写登录表单
      wrapper.vm.loginForm.username = '<EMAIL>'
      wrapper.vm.loginForm.password = 'password123'

      // 提交登录
      await wrapper.vm.handleLogin()

      // 验证API调用
      expect(authApi.login).toHaveBeenCalledWith({
        username: '<EMAIL>',
        password: 'password123'
      })

      // 验证store状态
      expect(authStore.token).toBe(mockLoginResponse.token)
      expect(authStore.user).toEqual(mockLoginResponse.user)
      expect(authStore.isAuthenticated).toBe(true)
    })

    it('should handle login failure', async () => {
      const errorMessage = 'Invalid credentials'
      vi.mocked(authApi.login).mockRejectedValue(new Error(errorMessage))

      const wrapper = mount(LoginView, {
        global: {
          plugins: [router, pinia],
          stubs: {
            'el-form': true,
            'el-form-item': true,
            'el-input': true,
            'el-button': true,
            'el-card': true
          }
        }
      })

      wrapper.vm.loginForm.username = '<EMAIL>'
      wrapper.vm.loginForm.password = 'wrongpassword'

      await wrapper.vm.handleLogin()

      // 验证错误处理
      expect(authStore.isAuthenticated).toBe(false)
      expect(authStore.token).toBeNull()
    })

    it('should validate form fields', async () => {
      const wrapper = mount(LoginView, {
        global: {
          plugins: [router, pinia],
          stubs: {
            'el-form': {
              template: '<form><slot /></form>',
              methods: {
                validate: vi.fn().mockResolvedValue(false)
              }
            },
            'el-form-item': true,
            'el-input': true,
            'el-button': true,
            'el-card': true
          }
        }
      })

      // 尝试提交空表单
      await wrapper.vm.handleLogin()

      // 验证API未被调用
      expect(authApi.login).not.toHaveBeenCalled()
    })
  })

  describe('Token Management', () => {
    it('should store token in localStorage on login', async () => {
      vi.mocked(authApi.login).mockResolvedValue(mockLoginResponse)

      await authStore.login('<EMAIL>', 'password123')

      expect(localStorageMock.setItem).toHaveBeenCalledWith('auth_token', mockLoginResponse.token)
      expect(localStorageMock.setItem).toHaveBeenCalledWith('auth_user', JSON.stringify(mockLoginResponse.user))
    })

    it('should restore token from localStorage on app start', () => {
      localStorageMock.getItem.mockImplementation((key: string) => {
        if (key === 'auth_token') return mockLoginResponse.token
        if (key === 'auth_user') return JSON.stringify(mockLoginResponse.user)
        return null
      })

      authStore.initializeAuth()

      expect(authStore.token).toBe(mockLoginResponse.token)
      expect(authStore.user).toEqual(mockLoginResponse.user)
      expect(authStore.isAuthenticated).toBe(true)
    })

    it('should refresh token when expired', async () => {
      const newToken = 'new-jwt-token'
      vi.mocked(authApi.refreshToken).mockResolvedValue({ token: newToken })

      authStore.token = 'old-token'
      authStore.user = mockUser

      await authStore.refreshToken()

      expect(authApi.refreshToken).toHaveBeenCalled()
      expect(authStore.token).toBe(newToken)
      expect(localStorageMock.setItem).toHaveBeenCalledWith('auth_token', newToken)
    })

    it('should clear token on logout', async () => {
      vi.mocked(authApi.logout).mockResolvedValue(undefined)

      authStore.token = mockLoginResponse.token
      authStore.user = mockUser

      await authStore.logout()

      expect(authApi.logout).toHaveBeenCalled()
      expect(authStore.token).toBeNull()
      expect(authStore.user).toBeNull()
      expect(authStore.isAuthenticated).toBe(false)
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_token')
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_user')
    })
  })

  describe('Route Guards', () => {
    it('should redirect to login for protected routes when not authenticated', async () => {
      // 确保用户未登录
      authStore.token = null
      authStore.user = null

      const routerPushSpy = vi.spyOn(router, 'push')

      // 尝试访问受保护的路由
      await router.push('/cats')

      // 应该被重定向到登录页
      expect(routerPushSpy).toHaveBeenCalledWith('/login')
    })

    it('should allow access to protected routes when authenticated', async () => {
      // 设置用户已登录
      authStore.token = mockLoginResponse.token
      authStore.user = mockUser

      // 访问受保护的路由
      await router.push('/cats')

      // 应该成功访问
      expect(router.currentRoute.value.path).toBe('/cats')
    })

    it('should check role-based access', async () => {
      // 设置用户已登录但没有管理员权限
      authStore.token = mockLoginResponse.token
      authStore.user = { ...mockUser, roles: ['USER'] }

      const routerPushSpy = vi.spyOn(router, 'push')

      // 尝试访问需要管理员权限的路由
      await router.push('/admin')

      // 应该被拒绝访问
      expect(routerPushSpy).toHaveBeenCalledWith('/unauthorized')
    })
  })

  describe('Permission System', () => {
    it('should check user permissions correctly', () => {
      authStore.user = mockUser

      expect(authStore.hasPermission('cat:read')).toBe(true)
      expect(authStore.hasPermission('cat:write')).toBe(true)
      expect(authStore.hasPermission('finance:read')).toBe(false)
    })

    it('should check user roles correctly', () => {
      authStore.user = mockUser

      expect(authStore.hasRole('ADMIN')).toBe(true)
      expect(authStore.hasRole('USER')).toBe(false)
    })

    it('should handle multiple role checks', () => {
      authStore.user = mockUser

      expect(authStore.hasAnyRole(['ADMIN', 'MANAGER'])).toBe(true)
      expect(authStore.hasAnyRole(['USER', 'GUEST'])).toBe(false)
    })

    it('should handle permission-based UI rendering', () => {
      authStore.user = mockUser

      // 模拟组件中的权限检查
      const canEditCats = authStore.hasPermission('cat:write')
      const canViewFinance = authStore.hasPermission('finance:read')

      expect(canEditCats).toBe(true)
      expect(canViewFinance).toBe(false)
    })
  })

  describe('Session Management', () => {
    it('should handle session timeout', async () => {
      authStore.token = mockLoginResponse.token
      authStore.user = mockUser

      // 模拟token过期
      vi.mocked(authApi.refreshToken).mockRejectedValue(new Error('Token expired'))

      await authStore.refreshToken()

      // 应该自动登出
      expect(authStore.isAuthenticated).toBe(false)
    })

    it('should handle concurrent requests with expired token', async () => {
      authStore.token = 'expired-token'
      authStore.user = mockUser

      // 模拟多个并发请求
      const requests = [
        authStore.refreshToken(),
        authStore.refreshToken(),
        authStore.refreshToken()
      ]

      // 只有第一个请求应该真正调用API
      await Promise.allSettled(requests)

      expect(authApi.refreshToken).toHaveBeenCalledTimes(1)
    })

    it('should maintain user session across page refreshes', () => {
      // 模拟页面刷新前的状态
      localStorageMock.getItem.mockImplementation((key: string) => {
        if (key === 'auth_token') return mockLoginResponse.token
        if (key === 'auth_user') return JSON.stringify(mockLoginResponse.user)
        return null
      })

      // 重新初始化store（模拟页面刷新）
      const newStore = useAuthStore(createPinia())
      newStore.initializeAuth()

      expect(newStore.isAuthenticated).toBe(true)
      expect(newStore.token).toBe(mockLoginResponse.token)
      expect(newStore.user).toEqual(mockLoginResponse.user)
    })
  })

  describe('Security Features', () => {
    it('should handle XSS protection', () => {
      const maliciousInput = '<script>alert("xss")</script>'
      
      // 确保用户输入被正确转义
      authStore.user = { ...mockUser, name: maliciousInput }
      
      // 在实际应用中，这应该被转义
      expect(authStore.user.name).toBe(maliciousInput)
    })

    it('should handle CSRF protection', async () => {
      // 模拟CSRF token
      const csrfToken = 'csrf-token-123'
      
      vi.mocked(authApi.login).mockImplementation(async (credentials) => {
        // 验证CSRF token是否包含在请求中
        return mockLoginResponse
      })

      await authStore.login('<EMAIL>', 'password123')

      expect(authApi.login).toHaveBeenCalled()
    })

    it('should handle rate limiting', async () => {
      // 模拟多次失败登录
      vi.mocked(authApi.login).mockRejectedValue(new Error('Too many attempts'))

      const wrapper = mount(LoginView, {
        global: {
          plugins: [router, pinia],
          stubs: {
            'el-form': true,
            'el-form-item': true,
            'el-input': true,
            'el-button': true,
            'el-card': true
          }
        }
      })

      // 尝试多次登录
      for (let i = 0; i < 5; i++) {
        await wrapper.vm.handleLogin()
      }

      // 应该显示限制消息
      expect(wrapper.vm.loginAttempts).toBeGreaterThan(3)
    })
  })

  describe('Error Handling', () => {
    it('should handle network errors gracefully', async () => {
      vi.mocked(authApi.login).mockRejectedValue(new Error('Network error'))

      const wrapper = mount(LoginView, {
        global: {
          plugins: [router, pinia],
          stubs: {
            'el-form': true,
            'el-form-item': true,
            'el-input': true,
            'el-button': true,
            'el-card': true
          }
        }
      })

      wrapper.vm.loginForm.username = '<EMAIL>'
      wrapper.vm.loginForm.password = 'password123'

      await wrapper.vm.handleLogin()

      // 应该显示网络错误消息
      expect(wrapper.vm.loading).toBe(false)
    })

    it('should handle server errors gracefully', async () => {
      vi.mocked(authApi.login).mockRejectedValue({
        response: {
          status: 500,
          data: { message: 'Internal server error' }
        }
      })

      await expect(authStore.login('<EMAIL>', 'password123')).rejects.toThrow()
    })

    it('should handle malformed responses', async () => {
      vi.mocked(authApi.login).mockResolvedValue({
        // 缺少必要字段的响应
        token: null,
        user: null
      })

      await expect(authStore.login('<EMAIL>', 'password123')).rejects.toThrow()
    })
  })
})
