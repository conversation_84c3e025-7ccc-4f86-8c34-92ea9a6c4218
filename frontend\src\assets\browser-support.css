/* 浏览器兼容性检测和回退方案 */

/* Edge浏览器特定支持 */
@supports (-ms-ime-align: auto) {
  /* Edge特定样式 */
  .high-res-image {
    image-rendering: -webkit-optimize-contrast;
  }
}

/* Safari浏览器特定支持 */
@supports (-webkit-appearance: none) and (not (appearance: none)) {
  /* Safari 15.4以下版本 */
  input, button, select, textarea {
    -webkit-appearance: none;
  }
  
  html {
    -webkit-text-size-adjust: 100%;
  }
}

/* Firefox浏览器特定支持 */
@supports (-moz-appearance: none) {
  /* Firefox特定样式 */
  input, button, select, textarea {
    -moz-appearance: none;
  }
  
  /* Firefox滚动条样式 */
  * {
    scrollbar-width: thin;
    scrollbar-color: #dcdfe6 #ffffff;
  }
}

/* Chrome/Chromium浏览器支持 */
@supports (-webkit-background-clip: text) {
  /* Chrome特定优化 */
  .text-gradient {
    background: linear-gradient(45deg, #409eff, #67c23a);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

/* 现代浏览器特性检测 */
@supports (appearance: none) {
  /* 支持现代appearance属性的浏览器 */
  input, button, select, textarea {
    appearance: none;
  }
}

@supports (text-size-adjust: 100%) {
  /* 支持现代text-size-adjust的浏览器 */
  html {
    text-size-adjust: 100%;
  }
}

@supports (mask: url()) {
  /* 支持现代mask属性的浏览器 */
  .mask-element {
    mask: url('mask.svg') no-repeat center;
    mask-size: cover;
  }
}

@supports (image-rendering: crisp-edges) {
  /* 支持crisp-edges的浏览器 */
  .high-res-image {
    image-rendering: crisp-edges;
  }
}

/* 不支持某些特性的浏览器回退 */
@supports not (scrollbar-width: thin) {
  /* 不支持scrollbar-width的浏览器(主要是Safari) */
  .custom-scrollbar {
    /* 使用WebKit滚动条样式 */
    -webkit-overflow-scrolling: touch;
  }
  
  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
}

@supports not (appearance: none) {
  /* 不支持现代appearance的浏览器 */
  input, button, select, textarea {
    -webkit-appearance: none;
    -moz-appearance: none;
  }
}

@supports not (text-size-adjust: 100%) {
  /* 不支持现代text-size-adjust的浏览器 */
  html {
    -webkit-text-size-adjust: 100%;
    -moz-text-size-adjust: 100%;
    font-size: 14px; /* 固定字体大小回退 */
  }
}

/* 移动设备特定支持 */
@supports (env(safe-area-inset-top)) {
  /* 支持安全区域的设备(iPhone X等) */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .safe-area-left {
    padding-left: env(safe-area-inset-left);
  }
  
  .safe-area-right {
    padding-right: env(safe-area-inset-right);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --border-color: #000000;
    --text-color: #000000;
    --bg-color: #ffffff;
  }
  
  .high-contrast {
    border: 2px solid var(--border-color);
    color: var(--text-color);
    background: var(--bg-color);
  }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  /* 为偏好减少动画的用户禁用动画 */
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  /* 移除transform动画 */
  .performance-optimized {
    will-change: auto;
    transform: none;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-color: #1a1a1a;
    --text-color: #ffffff;
    --border-color: #333333;
    --scrollbar-thumb: #555555;
    --scrollbar-track: #2a2a2a;
  }
  
  /* 深色模式下的滚动条 */
  ::-webkit-scrollbar-track {
    background: var(--scrollbar-track);
  }
  
  ::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb);
  }
  
  /* Firefox深色滚动条 */
  @supports (scrollbar-color: auto) {
    * {
      scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
    }
  }
}

/* 打印模式优化 */
@media print {
  /* 打印时移除所有浏览器特定样式 */
  * {
    -webkit-appearance: revert;
    -moz-appearance: revert;
    appearance: revert;
    
    -webkit-text-size-adjust: auto;
    -moz-text-size-adjust: auto;
    text-size-adjust: auto;
    
    image-rendering: auto;
    
    /* 移除所有动画和变换 */
    animation: none !important;
    transition: none !important;
    transform: none !important;
    will-change: auto !important;
  }
  
  /* 打印时隐藏滚动条 */
  ::-webkit-scrollbar {
    display: none;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  /* 触摸设备上的特殊处理 */
  .touch-optimized {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }
  
  /* 触摸设备上的滚动优化 */
  .scroll-container {
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
  }
}
