@echo off
chcp 65001 >nul
echo ========================================
echo Node.js 快速修复方案
echo ========================================
echo.

echo 当前问题: npm核心文件缺失
echo 错误信息: Cannot find module 'D:\软件\node_modules\npm\bin\npm-prefix.js'
echo.

echo ========================================
echo 方案1: 尝试修复当前npm
echo ========================================
echo.

echo 步骤1: 设置npm配置...
npm config set prefix "%APPDATA%\npm" 2>nul
npm config set cache "%APPDATA%\npm-cache" 2>nul
echo 配置完成

echo.
echo 步骤2: 清理npm缓存...
npm cache clean --force 2>nul
if %errorlevel% equ 0 (
    echo ✅ 缓存清理成功
) else (
    echo ❌ 缓存清理失败
)

echo.
echo 步骤3: 尝试重新安装npm...
npm install -g npm@latest 2>nul
if %errorlevel% equ 0 (
    echo ✅ npm重新安装成功
    goto :test_npm
) else (
    echo ❌ npm重新安装失败
    goto :alternative_solution
)

:test_npm
echo.
echo 步骤4: 测试npm是否正常...
npm --version 2>nul
if %errorlevel% equ 0 (
    echo ✅ npm修复成功!
    goto :install_deps
) else (
    echo ❌ npm仍然无法工作
    goto :alternative_solution
)

:install_deps
echo.
echo ========================================
echo 安装项目依赖
echo ========================================
echo.

cd /d "%~dp0frontend"
if not exist package.json (
    echo ❌ 未找到package.json文件
    goto :end
)

echo 清理旧依赖...
if exist node_modules rmdir /s /q node_modules 2>nul
if exist package-lock.json del package-lock.json 2>nul

echo.
echo 安装依赖包...
npm install
if %errorlevel% equ 0 (
    echo ✅ 依赖安装成功!
    echo.
    echo 启动开发服务器...
    echo 访问地址: http://localhost:3000
    echo.
    npm run dev
) else (
    echo ❌ 依赖安装失败
    goto :alternative_solution
)
goto :end

:alternative_solution
echo.
echo ========================================
echo 方案2: 使用替代解决方案
echo ========================================
echo.

echo npm修复失败，提供以下替代方案:
echo.

echo 选项1: 重新安装Node.js
echo   1. 访问: https://nodejs.org
echo   2. 下载LTS版本 (v20.x)
echo   3. 卸载当前版本后重新安装
echo.

echo 选项2: 使用yarn替代npm
echo   1. 下载yarn: https://yarnpkg.com/getting-started/install
echo   2. 安装后使用: yarn install, yarn dev
echo.

echo 选项3: 使用静态文件版本
echo   1. 运行: start-simple.bat
echo   2. 直接使用HTML文件，无需Node.js
echo.

echo 推荐: 选择选项3，立即开始使用系统
echo.

pause
echo.
echo 正在启动静态文件版本...
cd /d "%~dp0"
call start-simple.bat

:end
echo.
echo 脚本执行完成
pause
