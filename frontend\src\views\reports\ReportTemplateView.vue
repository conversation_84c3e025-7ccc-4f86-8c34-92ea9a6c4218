<template>
  <div class="report-template-view">
    <div class="page-header">
      <h1>报表模板</h1>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        新增模板
      </el-button>
    </div>

    <el-table :data="templates" v-loading="loading">
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="name" label="模板名称" />
      <el-table-column prop="type" label="模板类型" />
      <el-table-column prop="description" label="描述" />
      <el-table-column label="状态">
        <template #default="{ row }">
          <el-tag :type="row.enabled ? 'success' : 'danger'">
            {{ row.enabled ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button size="small" @click="previewTemplate(row)">预览</el-button>
          <el-button size="small" type="primary" @click="editTemplate(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="deleteTemplate(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog v-model="showCreateDialog" title="报表模板" width="600px">
      <el-form :model="templateForm" label-width="100px">
        <el-form-item label="模板名称">
          <el-input v-model="templateForm.name" placeholder="输入模板名称" />
        </el-form-item>
        <el-form-item label="模板类型">
          <el-select v-model="templateForm.type" placeholder="选择模板类型">
            <el-option label="财务报表" value="FINANCIAL" />
            <el-option label="销售报表" value="SALES" />
            <el-option label="库存报表" value="INVENTORY" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="templateForm.description" type="textarea" :rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveTemplate">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

const loading = ref(false)
const showCreateDialog = ref(false)
const templates = ref([
  { id: 1, name: '月度财务报表', type: '财务报表', description: '月度收支统计', enabled: true }
])

const templateForm = reactive({
  name: '',
  type: '',
  description: ''
})

const previewTemplate = (template: any) => ElMessage.info('预览模板功能开发中')
const editTemplate = (template: any) => {
  Object.assign(templateForm, template)
  showCreateDialog.value = true
}
const deleteTemplate = (template: any) => ElMessage.success('删除成功')
const saveTemplate = () => {
  ElMessage.success('保存成功')
  showCreateDialog.value = false
}

onMounted(() => {
  loading.value = false
})
</script>

<style scoped>
.report-template-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
</style>
