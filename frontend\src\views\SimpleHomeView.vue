<template>
  <div class="home-container">
    <header class="header">
      <div class="header-content">
        <h1>🐱 猫舍管理系统</h1>
        <div class="user-info">
          <span>欢迎，{{ userInfo?.username || '用户' }}</span>
          <button @click="handleLogout" class="logout-btn">退出登录</button>
        </div>
      </div>
    </header>

    <main class="main-content">
      <div class="dashboard">
        <h2>系统概览</h2>
        
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">🐱</div>
            <div class="stat-info">
              <h3>猫咪总数</h3>
              <p class="stat-number">{{ stats.totalCats }}</p>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">👥</div>
            <div class="stat-info">
              <h3>客户总数</h3>
              <p class="stat-number">{{ stats.totalCustomers }}</p>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">💰</div>
            <div class="stat-info">
              <h3>本月收入</h3>
              <p class="stat-number">¥{{ stats.monthlyIncome }}</p>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">📋</div>
            <div class="stat-info">
              <h3>待处理事项</h3>
              <p class="stat-number">{{ stats.pendingTasks }}</p>
            </div>
          </div>
        </div>

        <div class="features-grid">
          <div class="feature-card" @click="navigateTo('/cats')">
            <div class="feature-icon">🐱</div>
            <h3>猫咪管理</h3>
            <p>管理猫咪信息、健康记录、照片等</p>
          </div>
          
          <div class="feature-card" @click="navigateTo('/customers')">
            <div class="feature-icon">👥</div>
            <h3>客户管理</h3>
            <p>管理客户信息、咨询记录、领养申请</p>
          </div>
          
          <div class="feature-card" @click="navigateTo('/health')">
            <div class="feature-icon">🏥</div>
            <h3>健康管理</h3>
            <p>疫苗记录、体检报告、健康提醒</p>
          </div>
          
          <div class="feature-card" @click="navigateTo('/finance')">
            <div class="feature-icon">💰</div>
            <h3>财务管理</h3>
            <p>收支记录、财务报表、预算管理</p>
          </div>
          
          <div class="feature-card" @click="navigateTo('/inventory')">
            <div class="feature-icon">📦</div>
            <h3>库存管理</h3>
            <p>物品管理、库存预警、采购计划</p>
          </div>
          
          <div class="feature-card" @click="navigateTo('/breeding')">
            <div class="feature-icon">🧬</div>
            <h3>繁育管理</h3>
            <p>配种记录、血统管理、繁育计划</p>
          </div>
        </div>

        <div class="quick-actions">
          <h3>快速操作</h3>
          <div class="action-buttons">
            <button class="action-btn primary" @click="showMessage('新增猫咪功能')">
              ➕ 新增猫咪
            </button>
            <button class="action-btn" @click="showMessage('健康检查功能')">
              🏥 健康检查
            </button>
            <button class="action-btn" @click="showMessage('财务记录功能')">
              💰 财务记录
            </button>
            <button class="action-btn" @click="openH2Console">
              🗄️ 数据库控制台
            </button>
            <button class="action-btn" @click="openSwagger">
              📚 API文档
            </button>
          </div>
        </div>
      </div>
    </main>

    <div v-if="message" class="message-popup" @click="message = ''">
      {{ message }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const userInfo = ref<any>(null)
const message = ref('')

const stats = reactive({
  totalCats: 0,
  totalCustomers: 0,
  monthlyIncome: 0,
  pendingTasks: 0
})

onMounted(() => {
  // 获取用户信息
  const userInfoStr = localStorage.getItem('userInfo')
  if (userInfoStr) {
    userInfo.value = JSON.parse(userInfoStr)
  }
  
  // 模拟加载统计数据
  loadStats()
})

const loadStats = () => {
  // 模拟数据
  stats.totalCats = 156
  stats.totalCustomers = 89
  stats.monthlyIncome = 45600
  stats.pendingTasks = 12
}

const handleLogout = () => {
  localStorage.removeItem('token')
  localStorage.removeItem('userInfo')
  router.push('/login')
}

const navigateTo = (path: string) => {
  showMessage(`导航到 ${path} 功能（前端依赖修复后可用）`)
}

const showMessage = (msg: string) => {
  message.value = msg
  setTimeout(() => {
    message.value = ''
  }, 3000)
}

const openH2Console = () => {
  window.open('http://localhost:8080/h2-console', '_blank')
}

const openSwagger = () => {
  window.open('http://localhost:8080/swagger-ui/index.html', '_blank')
}
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
  background: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  padding: 1rem 0;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header h1 {
  margin: 0;
  color: #333;
  font-size: 1.5rem;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logout-btn {
  padding: 0.5rem 1rem;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
}

.logout-btn:hover {
  background: #c82333;
}

.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.dashboard h2 {
  margin-bottom: 2rem;
  color: #333;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 3rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-icon {
  font-size: 2rem;
}

.stat-info h3 {
  margin: 0 0 0.5rem 0;
  color: #666;
  font-size: 0.9rem;
}

.stat-number {
  margin: 0;
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 3rem;
}

.feature-card {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  text-align: center;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.feature-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.feature-card h3 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.feature-card p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.quick-actions {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.quick-actions h3 {
  margin: 0 0 1rem 0;
  color: #333;
}

.action-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.action-btn {
  padding: 0.75rem 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.9rem;
}

.action-btn:hover {
  background: #f8f9fa;
  border-color: #007bff;
}

.action-btn.primary {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.action-btn.primary:hover {
  background: #0056b3;
}

.message-popup {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #28a745;
  color: white;
  padding: 1rem;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  cursor: pointer;
  z-index: 1000;
}
</style>
