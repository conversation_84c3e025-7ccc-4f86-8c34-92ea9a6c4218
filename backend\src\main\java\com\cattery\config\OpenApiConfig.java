package com.cattery.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.Components;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * OpenAPI (Swagger) 配置类
 * 
 * <AUTHOR> Team
 * @version 1.0
 * @since 2024-01-01
 */
@Configuration
public class OpenApiConfig {

    @Value("${spring.application.version:1.0.0}")
    private String appVersion;

    @Value("${server.port:8080}")
    private String serverPort;

    @Value("${server.servlet.context-path:}")
    private String contextPath;

    /**
     * 配置 OpenAPI 文档
     */
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(apiInfo())
                .servers(List.of(
                    new Server()
                        .url("http://localhost:" + serverPort + contextPath)
                        .description("开发环境"),
                    new Server()
                        .url("https://api.cattery.com" + contextPath)
                        .description("生产环境")
                ))
                .addSecurityItem(new SecurityRequirement().addList("Bearer Authentication"))
                .components(new Components()
                    .addSecuritySchemes("Bearer Authentication", createAPIKeyScheme()));
    }

    /**
     * API 信息配置
     */
    private Info apiInfo() {
        return new Info()
                .title("猫舍管理系统 API")
                .description("""
                    ## 猫舍管理系统后端API文档
                    
                    这是一个专业的猫舍管理系统，提供以下功能：
                    
                    ### 核心功能
                    - 🐱 **猫咪管理**: 猫咪信息、血统管理、健康记录
                    - 🏥 **健康管理**: 疫苗接种、体检记录、医疗档案
                    - 👥 **客户管理**: 客户信息、领养申请、售后服务
                    - 💰 **财务管理**: 收支记录、财务报表、成本分析
                    - 📊 **报表统计**: 数据分析、图表展示、导出功能
                    - 🤖 **AI功能**: 猫咪识别、品种分析、健康预测
                    
                    ### 技术特性
                    - RESTful API 设计
                    - JWT 身份认证
                    - 基于角色的权限控制
                    - 数据验证和异常处理
                    - 国际化支持
                    - 文件上传和处理
                    
                    ### 认证说明
                    大部分API需要JWT令牌认证，请先调用登录接口获取token，然后在请求头中添加：
                    ```
                    Authorization: Bearer <your-jwt-token>
                    ```
                    """)
                .version(appVersion)
                .contact(new Contact()
                    .name("猫舍管理系统开发团队")
                    .email("<EMAIL>")
                    .url("https://cattery.com"))
                .license(new License()
                    .name("MIT License")
                    .url("https://opensource.org/licenses/MIT"));
    }

    /**
     * JWT 认证方案配置
     */
    private SecurityScheme createAPIKeyScheme() {
        return new SecurityScheme()
                .type(SecurityScheme.Type.HTTP)
                .scheme("bearer")
                .bearerFormat("JWT")
                .in(SecurityScheme.In.HEADER)
                .name("Authorization")
                .description("请输入JWT令牌，格式：Bearer <token>");
    }
}
