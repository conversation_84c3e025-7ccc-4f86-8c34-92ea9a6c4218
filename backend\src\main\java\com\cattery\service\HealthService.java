package com.cattery.service;

import com.cattery.entity.Cat;
import com.cattery.entity.CatHealthRecord;
import com.cattery.repository.CatHealthRecordRepository;
import com.cattery.repository.CatRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 健康管理服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class HealthService {
    
    private final CatHealthRecordRepository catHealthRecordRepository;
    private final CatRepository catRepository;
    
    /**
     * 添加猫咪健康记录
     */
    public CatHealthRecord addCatHealthRecord(CatHealthRecord healthRecord) {
        log.info("添加猫咪健康记录: 猫咪ID={}, 记录类型={}", 
            healthRecord.getCat().getId(), healthRecord.getRecordType());
        
        // 验证猫咪是否存在
        Cat cat = catRepository.findById(healthRecord.getCat().getId())
            .orElseThrow(() -> new IllegalArgumentException("猫咪不存在"));
        
        healthRecord.setCat(cat);
        healthRecord.setRecordDate(LocalDateTime.now());
        
        return catHealthRecordRepository.save(healthRecord);
    }
    
    /**
     * 获取猫咪的所有健康记录
     */
    @Transactional(readOnly = true)
    public List<CatHealthRecord> getCatHealthRecords(Long catId) {
        log.debug("获取猫咪健康记录: catId={}", catId);
        return catHealthRecordRepository.findByCatIdOrderByRecordDateDesc(catId);
    }
    
    /**
     * 分页获取猫咪健康记录
     */
    @Transactional(readOnly = true)
    public Page<CatHealthRecord> getCatHealthRecords(Long catId, Pageable pageable) {
        log.debug("分页获取猫咪健康记录: catId={}, pageable={}", catId, pageable);
        return catHealthRecordRepository.findByCatIdOrderByRecordDateDesc(catId, pageable);
    }
    
    /**
     * 根据记录类型获取健康记录
     */
    @Transactional(readOnly = true)
    public List<CatHealthRecord> getCatHealthRecordsByType(Long catId, CatHealthRecord.RecordType recordType) {
        log.debug("根据类型获取猫咪健康记录: catId={}, recordType={}", catId, recordType);
        return catHealthRecordRepository.findByCatIdAndRecordTypeOrderByRecordDateDesc(catId, recordType);
    }
    
    /**
     * 获取最近的健康记录
     */
    @Transactional(readOnly = true)
    public Optional<CatHealthRecord> getLatestHealthRecord(Long catId) {
        log.debug("获取最新健康记录: catId={}", catId);
        List<CatHealthRecord> records = catHealthRecordRepository.findByCatIdOrderByRecordDateDesc(catId);
        return records.isEmpty() ? Optional.empty() : Optional.of(records.get(0));
    }
    
    /**
     * 更新健康记录
     */
    public CatHealthRecord updateHealthRecord(Long recordId, CatHealthRecord updatedRecord) {
        log.info("更新健康记录: recordId={}", recordId);
        
        CatHealthRecord existingRecord = catHealthRecordRepository.findById(recordId)
            .orElseThrow(() -> new IllegalArgumentException("健康记录不存在"));
        
        // 更新字段
        existingRecord.setRecordType(updatedRecord.getRecordType());
        existingRecord.setDiagnosis(updatedRecord.getDiagnosis());
        existingRecord.setVeterinarian(updatedRecord.getVeterinarian());
        existingRecord.setTreatment(updatedRecord.getTreatment());
        existingRecord.setMedication(updatedRecord.getMedication());
        existingRecord.setNextAppointment(updatedRecord.getNextAppointment());
        existingRecord.setNotes(updatedRecord.getNotes());
        
        return catHealthRecordRepository.save(existingRecord);
    }
    
    /**
     * 删除健康记录
     */
    public void deleteHealthRecord(Long recordId) {
        log.info("删除健康记录: recordId={}", recordId);
        
        if (!catHealthRecordRepository.existsById(recordId)) {
            throw new IllegalArgumentException("健康记录不存在");
        }
        
        catHealthRecordRepository.deleteById(recordId);
    }
    
    /**
     * 获取需要体检的猫咪
     */
    @Transactional(readOnly = true)
    public List<Cat> getCatsNeedingCheckup() {
        log.debug("获取需要体检的猫咪");
        LocalDateTime cutoffDate = LocalDateTime.now().minusMonths(6); // 6个月未体检
        return catRepository.findCatsNeedingCheckup(cutoffDate);
    }
    
    /**
     * 获取疫苗接种统计
     */
    @Transactional(readOnly = true)
    public long getVaccinationCount(LocalDateTime startDate, LocalDateTime endDate) {
        log.debug("获取疫苗接种统计: {} - {}", startDate, endDate);
        return catHealthRecordRepository.countByRecordTypeAndRecordDateBetween(
            CatHealthRecord.RecordType.VACCINATION, startDate, endDate);
    }
    
    /**
     * 获取体检统计
     */
    @Transactional(readOnly = true)
    public long getCheckupCount(LocalDateTime startDate, LocalDateTime endDate) {
        log.debug("获取体检统计: {} - {}", startDate, endDate);
        return catHealthRecordRepository.countByRecordTypeAndRecordDateBetween(
            CatHealthRecord.RecordType.CHECKUP, startDate, endDate);
    }
}
