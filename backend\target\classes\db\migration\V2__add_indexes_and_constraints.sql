-- 数据库优化脚本：添加索引和约束
-- 版本: V2
-- 描述: 为猫舍管理系统添加必要的索引和约束以提升性能

-- ================================
-- 1. 猫咪表 (cats) 索引优化
-- ================================

-- 芯片号索引（已有unique约束）
-- ALTER TABLE cats ADD UNIQUE INDEX idx_cats_microchip_id (microchip_id);

-- 品种名称索引 - 用于按品种查询
CREATE INDEX idx_cats_breed_name ON cats (breed_name);

-- 状态索引 - 用于按状态筛选
CREATE INDEX idx_cats_status ON cats (status);

-- 性别索引 - 用于按性别筛选
CREATE INDEX idx_cats_gender ON cats (gender);

-- 出生日期索引 - 用于年龄计算和筛选
CREATE INDEX idx_cats_date_of_birth ON cats (date_of_birth);

-- 创建时间索引 - 用于时间范围查询
CREATE INDEX idx_cats_created_at ON cats (created_at);

-- 体重索引 - 用于体重范围查询
CREATE INDEX idx_cats_current_weight ON cats (current_weight);

-- 绝育状态索引 - 用于筛选
CREATE INDEX idx_cats_for_sale ON cats (for_sale);

-- 健康状态索引 - 用于健康状况筛选
CREATE INDEX idx_cats_health_status ON cats (health_status);

-- 繁育状态索引 - 用于繁育管理
CREATE INDEX idx_cats_breeding_status ON cats (breeding_status);

-- 笼舍编号索引 - 用于位置管理
CREATE INDEX idx_cats_cage_number ON cats (cage_number);

-- 父亲ID索引 - 用于血统查询
CREATE INDEX idx_cats_father_id ON cats (father_id);

-- 母亲ID索引 - 用于血统查询  
CREATE INDEX idx_cats_mother_id ON cats (mother_id);

-- 复合索引：状态+品种 - 用于常见的组合查询
CREATE INDEX idx_cats_status_breed ON cats (status, breed_id);

-- 复合索引：出售状态+价格 - 用于商品筛选
CREATE INDEX idx_cats_for_sale_price ON cats (for_sale, price);

-- 复合索引：性别+品种 - 用于繁育配对
CREATE INDEX idx_cats_gender_breed ON cats (gender, breed_id);

-- 创建时间索引 - 用于数据统计
CREATE INDEX idx_cats_created_at ON cats (created_at);

-- 更新时间索引 - 用于数据同步
CREATE INDEX idx_cats_updated_at ON cats (updated_at);

-- ================================
-- 2. 猫咪品种表 (cat_breeds) 索引优化
-- ================================

-- 品种名称索引（已有unique约束）
-- ALTER TABLE cat_breeds ADD UNIQUE INDEX idx_breeds_name (name);

-- 创建时间索引
CREATE INDEX idx_breeds_created_at ON cat_breeds (created_at);

-- ================================
-- 3. 用户表 (users) 索引优化
-- ================================

-- 用户名索引（已有unique约束）
-- ALTER TABLE users ADD UNIQUE INDEX idx_users_username (username);

-- 邮箱索引（已有unique约束）
-- ALTER TABLE users ADD UNIQUE INDEX idx_users_email (email);

-- 启用状态索引 - 用于筛选活跃用户
CREATE INDEX idx_users_enabled ON users (enabled);

-- 最后登录时间索引 - 用于活跃度统计
CREATE INDEX idx_users_last_login_at ON users (last_login_at);

-- 创建时间索引 - 用于用户注册统计
CREATE INDEX idx_users_created_at ON users (created_at);

-- 手机号索引 - 用于联系方式查询
CREATE INDEX idx_users_phone ON users (phone);

-- ================================
-- 4. 猫咪媒体表 (cat_media) 索引优化
-- ================================

-- 猫咪ID索引 - 用于查询特定猫咪的媒体
CREATE INDEX idx_cat_media_cat_id ON cat_media (cat_id);

-- 媒体类型索引 - 用于按类型筛选
CREATE INDEX idx_cat_media_type ON cat_media (media_type);

-- 是否为主要媒体索引 - 用于查找主图
CREATE INDEX idx_cat_media_is_primary ON cat_media (is_primary);

-- 排序顺序索引 - 用于媒体排序
CREATE INDEX idx_cat_media_sort_order ON cat_media (sort_order);

-- 创建时间索引 - 用于时间排序
CREATE INDEX idx_cat_media_created_at ON cat_media (created_at);

-- 复合索引：猫咪ID+媒体类型+排序 - 用于媒体列表查询
CREATE INDEX idx_cat_media_cat_type_sort ON cat_media (cat_id, media_type, sort_order);

-- 复合索引：猫咪ID+是否主要 - 用于查找主图
CREATE INDEX idx_cat_media_cat_primary ON cat_media (cat_id, is_primary);

-- 文件大小索引 - 用于存储统计
CREATE INDEX idx_cat_media_file_size ON cat_media (file_size);

-- ================================
-- 5. 健康记录表 (cat_health_records) 索引优化
-- ================================

-- 猫咪ID索引 - 用于查询特定猫咪的健康记录
CREATE INDEX idx_health_records_cat_id ON cat_health_records (cat_id);

-- 记录类型索引 - 用于按类型筛选
CREATE INDEX idx_health_records_type ON cat_health_records (record_type);

-- 记录日期索引 - 用于时间范围查询
CREATE INDEX idx_health_records_date ON cat_health_records (record_date);

-- 兽医索引 - 用于按兽医查询
CREATE INDEX idx_health_records_veterinarian ON cat_health_records (veterinarian);

-- 诊所索引 - 用于按诊所查询
CREATE INDEX idx_health_records_clinic ON cat_health_records (clinic);

-- 复合索引：猫咪ID+记录日期 - 用于时间线查询
CREATE INDEX idx_health_records_cat_date ON cat_health_records (cat_id, record_date DESC);

-- 复合索引：猫咪ID+记录类型 - 用于特定类型记录查询
CREATE INDEX idx_health_records_cat_type ON cat_health_records (cat_id, record_type);

-- 创建时间索引
CREATE INDEX idx_health_records_created_at ON cat_health_records (created_at);

-- 下次预约日期索引 - 用于提醒功能
CREATE INDEX idx_health_records_next_appointment ON cat_health_records (next_appointment_date);

-- ================================
-- 6. 角色权限相关表索引优化
-- ================================

-- 用户角色关联表 (user_roles)
CREATE INDEX idx_user_roles_user_id ON user_roles (user_id);
CREATE INDEX idx_user_roles_role_id ON user_roles (role_id);

-- 角色权限关联表 (role_permissions) 
CREATE INDEX idx_role_permissions_role_id ON role_permissions (role_id);
CREATE INDEX idx_role_permissions_permission_id ON role_permissions (permission_id);

-- 角色表 (roles)
CREATE INDEX idx_roles_name ON roles (name);

-- 权限表 (permissions)  
CREATE INDEX idx_permissions_name ON permissions (name);

-- ================================
-- 7. 其他业务表索引优化
-- ================================

-- 客户表 (customers) - 如果存在
-- CREATE INDEX idx_customers_phone ON customers (phone);
-- CREATE INDEX idx_customers_email ON customers (email);
-- CREATE INDEX idx_customers_created_at ON customers (created_at);

-- 订单表 (orders) - 如果存在
-- CREATE INDEX idx_orders_customer_id ON orders (customer_id);
-- CREATE INDEX idx_orders_status ON orders (status);
-- CREATE INDEX idx_orders_order_date ON orders (order_date);
-- CREATE INDEX idx_orders_total_amount ON orders (total_amount);

-- 商品表 (products) - 如果存在
-- CREATE INDEX idx_products_category ON products (category);
-- CREATE INDEX idx_products_price ON products (price);
-- CREATE INDEX idx_products_stock ON products (stock);

-- ================================
-- 8. 添加数据约束
-- ================================

-- 猫咪表约束
ALTER TABLE cats ADD CONSTRAINT chk_cats_price_positive 
    CHECK (price IS NULL OR price >= 0);

ALTER TABLE cats ADD CONSTRAINT chk_cats_weight_positive 
    CHECK (weight IS NULL OR weight > 0);

ALTER TABLE cats ADD CONSTRAINT chk_cats_body_condition_score 
    CHECK (body_condition_score IS NULL OR (body_condition_score >= 1 AND body_condition_score <= 9));

-- 防止自引用（猫咪不能是自己的父母）
ALTER TABLE cats ADD CONSTRAINT chk_cats_not_self_parent 
    CHECK (id != father_id AND id != mother_id);

-- 媒体文件约束
ALTER TABLE cat_media ADD CONSTRAINT chk_cat_media_file_size_positive 
    CHECK (file_size IS NULL OR file_size > 0);

-- 健康记录约束
ALTER TABLE cat_health_records ADD CONSTRAINT chk_health_records_date_not_future 
    CHECK (record_date <= CURRENT_DATE);

ALTER TABLE cat_health_records ADD CONSTRAINT chk_health_records_weight_positive 
    CHECK (weight IS NULL OR weight > 0);

ALTER TABLE cat_health_records ADD CONSTRAINT chk_health_records_temperature_range 
    CHECK (temperature IS NULL OR (temperature >= 35.0 AND temperature <= 45.0));

-- ================================
-- 9. 创建视图用于常用查询
-- ================================

-- 可售猫咪视图
CREATE OR REPLACE VIEW v_available_cats AS
SELECT 
    c.id,
    c.name,
    c.chip_number,
    cb.name as breed_name,
    c.gender,
    c.date_of_birth,
    c.color,
    c.price,
    c.status,
    c.health_status,
    c.entry_date,
    TIMESTAMPDIFF(MONTH, c.date_of_birth, CURDATE()) as age_months
FROM cats c
LEFT JOIN cat_breeds cb ON c.breed_id = cb.id
WHERE c.for_sale = true 
  AND c.status IN ('AVAILABLE', 'PENDING_ADOPTION')
  AND c.health_status = 'HEALTHY';

-- 猫咪统计视图
CREATE OR REPLACE VIEW v_cat_statistics AS
SELECT 
    COUNT(*) as total_cats,
    COUNT(CASE WHEN for_sale = true THEN 1 END) as for_sale_count,
    COUNT(CASE WHEN status = 'AVAILABLE' THEN 1 END) as available_count,
    COUNT(CASE WHEN status = 'ADOPTED' THEN 1 END) as adopted_count,
    COUNT(CASE WHEN gender = 'MALE' THEN 1 END) as male_count,
    COUNT(CASE WHEN gender = 'FEMALE' THEN 1 END) as female_count,
    AVG(price) as average_price,
    MIN(price) as min_price,
    MAX(price) as max_price
FROM cats;

-- 品种统计视图
CREATE OR REPLACE VIEW v_breed_statistics AS
SELECT 
    cb.id,
    cb.name,
    COUNT(c.id) as cat_count,
    COUNT(CASE WHEN c.for_sale = true THEN 1 END) as for_sale_count,
    AVG(c.price) as average_price
FROM cat_breeds cb
LEFT JOIN cats c ON cb.id = c.breed_id
GROUP BY cb.id, cb.name;

-- ================================
-- 10. 添加注释说明
-- ================================

-- 为重要表添加注释
ALTER TABLE cats COMMENT = '猫咪基础信息表';
ALTER TABLE cat_breeds COMMENT = '猫咪品种表';
ALTER TABLE cat_media COMMENT = '猫咪媒体文件表';
ALTER TABLE cat_health_records COMMENT = '猫咪健康记录表';
ALTER TABLE users COMMENT = '系统用户表';

-- 为重要字段添加注释
ALTER TABLE cats MODIFY COLUMN chip_number VARCHAR(15) COMMENT '芯片号，全局唯一';
ALTER TABLE cats MODIFY COLUMN price DECIMAL(10,2) COMMENT '售价，单位：元';
ALTER TABLE cats MODIFY COLUMN weight DECIMAL(5,2) COMMENT '体重，单位：千克';
ALTER TABLE cats MODIFY COLUMN body_condition_score INT COMMENT '体况评分，1-9分';
ALTER TABLE cats MODIFY COLUMN cage_number VARCHAR(20) COMMENT '笼舍编号';

-- 优化完成提示
SELECT 'Database optimization completed successfully!' as message;
