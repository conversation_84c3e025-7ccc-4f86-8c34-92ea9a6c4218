import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { login as apiLogin, logout as apiLogout, getUserInfo, refreshToken as apiRefreshToken } from '@/api/auth'

// 用户信息接口
export interface UserInfo {
  id: number
  username: string
  realName: string
  email: string
  phone?: string
  avatar?: string
  roles: string[]
  permissions: string[]
  enabled: boolean
  accountNonExpired: boolean
  accountNonLocked: boolean
  credentialsNonExpired: boolean
}

// 登录请求接口
export interface LoginRequest {
  username: string
  password: string
  rememberMe?: boolean
}

// 登录响应接口
export interface LoginResponse {
  token: string
  userInfo: UserInfo
}

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string | null>(localStorage.getItem('token'))
  const userInfo = ref<UserInfo | null>(null)
  const isLoading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!userInfo.value)
  const hasRole = (role: string) => userInfo.value?.roles.includes(role) ?? false
  const hasPermission = (permission: string) => userInfo.value?.permissions.includes(permission) ?? false

  // 登录
  const login = async (loginData: LoginRequest): Promise<boolean> => {
    isLoading.value = true
    try {
      const response = await apiLogin({
        username: loginData.username,
        password: loginData.password
      })

      if (!response.success) {
        throw new Error(response.message || '登录失败')
      }

      const result = response.data

      // 保存token和用户信息
      token.value = result.token
      userInfo.value = result.userInfo
      localStorage.setItem('token', result.token)
      localStorage.setItem('userInfo', JSON.stringify(result.userInfo))

      ElMessage.success('登录成功')
      return true
    } catch (error) {
      console.error('登录错误:', error)
      ElMessage.error(error instanceof Error ? error.message : '登录失败')
      return false
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      // 调用后端登出接口
      if (token.value) {
        await apiLogout()
      }
    } catch (error) {
      console.warn('登出请求失败:', error)
    } finally {
      // 清除本地存储
      token.value = null
      userInfo.value = null
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')

      ElMessage.success('已退出登录')
    }
  }

  // 刷新token
  const refreshToken = async (): Promise<boolean> => {
    if (!token.value) return false

    try {
      const response = await apiRefreshToken()

      if (!response.success) {
        throw new Error('Token刷新失败')
      }

      const result = response.data

      token.value = result.token
      userInfo.value = result.userInfo
      localStorage.setItem('token', result.token)
      localStorage.setItem('userInfo', JSON.stringify(result.userInfo))

      return true
    } catch (error) {
      console.error('Token刷新失败:', error)
      await logout()
      return false
    }
  }

  // 检查token是否过期
  const checkTokenExpiration = (): boolean => {
    const expirationTime = localStorage.getItem('tokenExpiration')
    if (!expirationTime) return false

    const now = Date.now()
    const expiration = parseInt(expirationTime)
    
    // 如果token在5分钟内过期，尝试刷新
    if (expiration - now < 5 * 60 * 1000) {
      refreshToken()
      return false
    }

    // 如果已经过期，登出
    if (now >= expiration) {
      logout()
      return true
    }

    return false
  }

  // 初始化用户信息
  const initializeAuth = () => {
    const storedUserInfo = localStorage.getItem('userInfo')
    if (storedUserInfo && token.value) {
      try {
        userInfo.value = JSON.parse(storedUserInfo)
        // 检查token是否过期
        checkTokenExpiration()
      } catch (error) {
        console.error('解析用户信息失败:', error)
        logout()
      }
    }
  }

  // 获取当前用户信息
  const getCurrentUser = async (): Promise<UserInfo | null> => {
    if (!token.value) return null

    try {
      const response = await getUserInfo()

      if (!response.success) {
        throw new Error(response.message || '获取用户信息失败')
      }

      const user: UserInfo = response.data
      userInfo.value = user
      localStorage.setItem('userInfo', JSON.stringify(user))

      return user
    } catch (error) {
      console.error('获取用户信息失败:', error)
      await logout()
      return null
    }
  }

  return {
    // 状态
    token,
    userInfo,
    isLoading,
    
    // 计算属性
    isAuthenticated,
    hasRole,
    hasPermission,
    
    // 方法
    login,
    logout,
    refreshToken,
    checkTokenExpiration,
    initializeAuth,
    getCurrentUser
  }
})
