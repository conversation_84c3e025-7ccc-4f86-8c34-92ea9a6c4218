<template>
  <div class="invoice-view">
    <div class="page-header">
      <h1>发票管理</h1>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        新增发票
      </el-button>
    </div>

    <el-table :data="invoices" v-loading="loading">
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="invoiceNumber" label="发票号码" />
      <el-table-column prop="customerName" label="客户" />
      <el-table-column prop="amount" label="金额" />
      <el-table-column prop="issueDate" label="开票日期" />
      <el-table-column label="状态">
        <template #default="{ row }">
          <el-tag :type="getStatusColor(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button size="small" @click="viewInvoice(row)">查看</el-button>
          <el-button size="small" type="primary" @click="editInvoice(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="deleteInvoice(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog v-model="showCreateDialog" title="发票管理" width="600px">
      <el-form :model="invoiceForm" label-width="100px">
        <el-form-item label="发票号码">
          <el-input v-model="invoiceForm.invoiceNumber" placeholder="输入发票号码" />
        </el-form-item>
        <el-form-item label="客户">
          <el-input v-model="invoiceForm.customerName" placeholder="输入客户名称" />
        </el-form-item>
        <el-form-item label="金额">
          <el-input-number v-model="invoiceForm.amount" :min="0" :precision="2" />
        </el-form-item>
        <el-form-item label="开票日期">
          <el-date-picker v-model="invoiceForm.issueDate" type="date" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveInvoice">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

const loading = ref(false)
const showCreateDialog = ref(false)
const invoices = ref([
  { id: 1, invoiceNumber: 'INV-2024-001', customerName: '张先生', amount: 8000, issueDate: '2024-01-15', status: 'PAID' }
])

const invoiceForm = reactive({
  invoiceNumber: '',
  customerName: '',
  amount: 0,
  issueDate: ''
})

const viewInvoice = (invoice: any) => ElMessage.info('查看发票功能开发中')
const editInvoice = (invoice: any) => {
  Object.assign(invoiceForm, invoice)
  showCreateDialog.value = true
}
const deleteInvoice = (invoice: any) => ElMessage.success('删除成功')
const saveInvoice = () => {
  ElMessage.success('保存成功')
  showCreateDialog.value = false
}

const getStatusColor = (status: string) => status === 'PAID' ? 'success' : 'warning'
const getStatusText = (status: string) => status === 'PAID' ? '已付款' : '未付款'

onMounted(() => {
  loading.value = false
})
</script>

<style scoped>
.invoice-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
</style>
