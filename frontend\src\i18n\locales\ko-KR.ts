export default {
  // 통용
  common: {
    confirm: '확인',
    cancel: '취소',
    save: '저장',
    delete: '삭제',
    edit: '편집',
    add: '추가',
    search: '검색',
    reset: '재설정',
    submit: '제출',
    back: '뒤로',
    next: '다음',
    previous: '이전',
    loading: '로딩 중...',
    noData: '데이터 없음',
    success: '성공',
    error: '오류',
    warning: '경고',
    info: '정보',
    yes: '예',
    no: '아니오',
    close: '닫기',
    refresh: '새로고침',
    export: '내보내기',
    import: '가져오기',
    download: '다운로드',
    upload: '업로드',
    view: '보기',
    detail: '상세',
    list: '목록',
    total: '총계',
    page: '페이지',
    size: '크기',
    all: '전체',
    select: '선택',
    clear: '지우기',
    copy: '복사',
    paste: '붙여넣기',
    cut: '잘라내기',
    undo: '실행 취소',
    redo: '다시 실행'
  },

  // 내비게이션 메뉴
  menu: {
    dashboard: '대시보드',
    cats: '고양이 관리',
    catList: '고양이 목록',
    catAdd: '고양이 추가',
    health: '건강 관리',
    healthRecords: '건강 기록',
    vaccinations: '예방접종',
    checkups: '건강검진',
    breeding: '번식 관리',
    matingRecords: '교배 기록',
    pregnancies: '임신 기록',
    customers: '고객 관리',
    customerList: '고객 목록',
    inquiries: '문의사항',
    adoptions: '입양 관리',
    inventory: '재고 관리',
    supplies: '용품 관리',
    food: '사료 관리',
    finance: '재무 관리',
    transactions: '거래 기록',
    reports: '보고서',
    ai: 'AI 기능',
    catRecognition: '고양이 인식',
    breedIdentification: '품종 식별',
    healthPrediction: '건강 예측',
    behaviorAnalysis: '행동 분석',
    settings: '설정',
    userManagement: '사용자 관리',
    systemSettings: '시스템 설정',
    profile: '프로필'
  },

  // 인증
  auth: {
    login: '로그인',
    logout: '로그아웃',
    register: '회원가입',
    username: '사용자명',
    password: '비밀번호',
    confirmPassword: '비밀번호 확인',
    email: '이메일',
    phone: '전화번호',
    realName: '실명',
    rememberMe: '로그인 상태 유지',
    forgotPassword: '비밀번호 찾기',
    loginSuccess: '로그인 성공',
    loginFailed: '로그인 실패',
    logoutSuccess: '로그아웃 성공',
    registerSuccess: '회원가입 성공',
    registerFailed: '회원가입 실패',
    invalidCredentials: '사용자명 또는 비밀번호가 올바르지 않습니다',
    accountLocked: '계정이 잠겨있습니다',
    accountExpired: '계정이 만료되었습니다',
    passwordExpired: '비밀번호가 만료되었습니다'
  },

  // 고양이 관리
  cat: {
    name: '이름',
    breed: '품종',
    gender: '성별',
    dateOfBirth: '생년월일',
    age: '나이',
    color: '색상',
    pattern: '무늬',
    weight: '체중',
    microchipId: '마이크로칩 ID',
    registrationNumber: '등록번호',
    status: '상태',
    description: '설명',
    notes: '비고',
    photos: '사진',
    healthRecords: '건강 기록',
    father: '아버지',
    mother: '어머니',
    offspring: '자손',
    isNeutered: '중성화 완료',
    male: '수컷',
    female: '암컷',
    unknown: '알 수 없음',
    available: '입양 가능',
    adopted: '입양 완료',
    reserved: '예약됨',
    breeding: '번식 중',
    medical: '치료 중',
    quarantine: '격리 중'
  }
}
