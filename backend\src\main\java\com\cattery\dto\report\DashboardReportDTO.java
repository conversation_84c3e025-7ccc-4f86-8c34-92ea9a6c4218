package com.cattery.dto.report;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 仪表盘报表DTO
 */
@Data
public class DashboardReportDTO {
    
    /**
     * 生成时间
     */
    private LocalDateTime generatedAt;
    
    /**
     * 总猫咪数
     */
    private Long totalCats;
    
    /**
     * 总客户数
     */
    private Long totalCustomers;
    
    /**
     * 总健康记录数
     */
    private Long totalHealthRecords;
    
    /**
     * 本月收入
     */
    private BigDecimal monthlyIncome;
    
    /**
     * 本月支出
     */
    private BigDecimal monthlyExpense;
    
    /**
     * 待办事项
     */
    private List<TodoItemDTO> todoItems;
    
    /**
     * 最近活动
     */
    private List<RecentActivityDTO> recentActivities;
    
    /**
     * 健康预警
     */
    private List<HealthAlertDTO> healthAlerts;
    
    /**
     * 系统状态
     */
    private String systemStatus;
    
    /**
     * 关键指标
     */
    private List<KeyMetricDTO> keyMetrics;
}
