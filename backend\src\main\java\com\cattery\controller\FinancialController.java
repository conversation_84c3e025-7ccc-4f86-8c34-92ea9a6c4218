package com.cattery.controller;

import com.cattery.dto.ApiResponse;
import com.cattery.entity.FinancialTransaction;
import com.cattery.service.FinancialService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 财务管理控制器
 */
@RestController
@RequestMapping("/api/financial")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "财务管理", description = "财务交易、统计和报表管理")
public class FinancialController {
    
    private final FinancialService financialService;
    
    /**
     * 获取财务统计
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取财务统计", description = "获取指定时间范围内的财务统计数据")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getFinancialStatistics(
            @Parameter(description = "开始日期") 
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @Parameter(description = "结束日期") 
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        
        log.info("获取财务统计: startDate={}, endDate={}", startDate, endDate);
        
        // 默认时间范围：当月
        if (startDate == null) {
            startDate = LocalDateTime.now().withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
        }
        if (endDate == null) {
            endDate = LocalDateTime.now();
        }
        
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalIncome", financialService.getTotalIncome(startDate, endDate));
        statistics.put("totalExpense", financialService.getTotalExpense(startDate, endDate));
        statistics.put("netProfit", financialService.getNetProfit(startDate, endDate));
        statistics.put("incomeByCategory", financialService.getIncomeByCategory(startDate, endDate));
        statistics.put("expenseByCategory", financialService.getExpenseByCategory(startDate, endDate));
        statistics.put("paymentMethodStats", financialService.getPaymentMethodStats(startDate, endDate));
        
        return ResponseEntity.ok(ApiResponse.success("获取财务统计成功", statistics));
    }
    
    /**
     * 获取所有财务交易
     */
    @GetMapping("/transactions")
    @Operation(summary = "获取财务交易列表", description = "分页获取财务交易列表，支持筛选和搜索")
    public ResponseEntity<ApiResponse<Page<FinancialTransaction>>> getTransactions(
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "交易类型") @RequestParam(required = false) FinancialTransaction.TransactionType type,
            @Parameter(description = "分类") @RequestParam(required = false) String category,
            @Parameter(description = "开始日期") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword) {
        
        log.info("获取财务交易列表: page={}, size={}, type={}, category={}", page, size, type, category);
        
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "transactionDate"));
        Page<FinancialTransaction> transactions;
        
        if (keyword != null && !keyword.trim().isEmpty()) {
            transactions = financialService.searchTransactions(keyword, pageable);
        } else if (type != null) {
            transactions = financialService.getTransactionsByType(type, pageable);
        } else if (category != null) {
            transactions = financialService.getTransactionsByCategory(category, pageable);
        } else if (startDate != null && endDate != null) {
            transactions = financialService.getTransactionsByDateRange(startDate, endDate, pageable);
        } else {
            transactions = financialService.getAllTransactions(pageable);
        }
        
        return ResponseEntity.ok(ApiResponse.success("获取财务交易列表成功", transactions));
    }
    
    /**
     * 根据ID获取财务交易
     */
    @GetMapping("/transactions/{id}")
    @Operation(summary = "获取财务交易详情", description = "根据ID获取财务交易详情")
    public ResponseEntity<ApiResponse<FinancialTransaction>> getTransactionById(
            @Parameter(description = "交易ID") @PathVariable Long id) {
        
        log.info("获取财务交易详情: id={}", id);
        
        return financialService.getTransactionById(id)
            .map(transaction -> ResponseEntity.ok(ApiResponse.success("获取财务交易详情成功", transaction)))
            .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * 创建财务交易
     */
    @PostMapping("/transactions")
    @Operation(summary = "创建财务交易", description = "创建新的财务交易记录")
    public ResponseEntity<ApiResponse<FinancialTransaction>> createTransaction(
            @Parameter(description = "财务交易信息") @Valid @RequestBody FinancialTransaction transaction) {
        
        log.info("创建财务交易: type={}, amount={}", transaction.getTransactionType(), transaction.getAmount());
        
        FinancialTransaction createdTransaction = financialService.recordTransaction(transaction);
        return ResponseEntity.ok(ApiResponse.success("创建财务交易成功", createdTransaction));
    }
    
    /**
     * 更新财务交易
     */
    @PutMapping("/transactions/{id}")
    @Operation(summary = "更新财务交易", description = "更新财务交易信息")
    public ResponseEntity<ApiResponse<FinancialTransaction>> updateTransaction(
            @Parameter(description = "交易ID") @PathVariable Long id,
            @Parameter(description = "更新的财务交易信息") @Valid @RequestBody FinancialTransaction transaction) {
        
        log.info("更新财务交易: id={}", id);
        
        FinancialTransaction updatedTransaction = financialService.updateTransaction(id, transaction);
        return ResponseEntity.ok(ApiResponse.success("更新财务交易成功", updatedTransaction));
    }
    
    /**
     * 删除财务交易
     */
    @DeleteMapping("/transactions/{id}")
    @Operation(summary = "删除财务交易", description = "删除财务交易记录")
    public ResponseEntity<ApiResponse<Void>> deleteTransaction(
            @Parameter(description = "交易ID") @PathVariable Long id) {
        
        log.info("删除财务交易: id={}", id);
        
        financialService.deleteTransaction(id);
        return ResponseEntity.ok(ApiResponse.success("删除财务交易成功", null));
    }
    
    /**
     * 获取月度财务统计
     */
    @GetMapping("/monthly-stats/{year}")
    @Operation(summary = "获取月度财务统计", description = "获取指定年份的月度财务统计")
    public ResponseEntity<ApiResponse<List<Object[]>>> getMonthlyStats(
            @Parameter(description = "年份") @PathVariable int year) {
        
        log.info("获取月度财务统计: year={}", year);
        
        List<Object[]> monthlyStats = financialService.getMonthlyFinancialStats(year);
        return ResponseEntity.ok(ApiResponse.success("获取月度财务统计成功", monthlyStats));
    }
    
    /**
     * 获取最近的交易
     */
    @GetMapping("/recent-transactions")
    @Operation(summary = "获取最近的交易", description = "获取最近的财务交易记录")
    public ResponseEntity<ApiResponse<List<FinancialTransaction>>> getRecentTransactions(
            @Parameter(description = "数量限制") @RequestParam(defaultValue = "10") int limit) {
        
        log.info("获取最近的交易: limit={}", limit);
        
        List<FinancialTransaction> recentTransactions = financialService.getRecentTransactions(limit);
        return ResponseEntity.ok(ApiResponse.success("获取最近的交易成功", recentTransactions));
    }
}
