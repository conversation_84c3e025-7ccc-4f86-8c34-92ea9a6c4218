import request from '@/utils/request'

export interface Customer {
  id?: number
  name: string
  phone: string
  email?: string
  address?: string
  wechat?: string
  qq?: string
  preferredBreeds?: string
  budget?: number
  notes?: string
  status: 'POTENTIAL' | 'ACTIVE' | 'INACTIVE'
  source: 'WEBSITE' | 'REFERRAL' | 'SOCIAL_MEDIA' | 'EXHIBITION' | 'OTHER'
  createdAt?: string
  updatedAt?: string
  createdBy?: number
  updatedBy?: number
}

export interface CustomerQuery {
  page?: number
  size?: number
  name?: string
  phone?: string
  email?: string
  status?: string
  source?: string
  preferredBreeds?: string
  startDate?: string
  endDate?: string
}

export interface CustomerInquiry {
  id?: number
  customerId: number
  subject: string
  content: string
  inquiryType: 'GENERAL' | 'PURCHASE' | 'BREEDING' | 'HEALTH' | 'COMPLAINT' | 'SUGGESTION'
  status: 'PENDING' | 'REPLIED' | 'RESOLVED' | 'CLOSED'
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  assignedTo?: number
  reply?: string
  repliedAt?: string
  repliedBy?: number
  createdAt?: string
  updatedAt?: string
  customer?: {
    id: number
    name: string
    phone: string
    email?: string
  }
}

export interface InquiryQuery {
  page?: number
  size?: number
  customerId?: number
  inquiryType?: string
  status?: string
  priority?: string
  assignedTo?: number
  startDate?: string
  endDate?: string
}

// 获取客户列表
export const getCustomers = (params?: CustomerQuery) => {
  return request.get('/customers', { params })
}

// 根据ID获取客户
export const getCustomerById = (id: number) => {
  return request.get(`/customers/${id}`)
}

// 创建客户
export const createCustomer = (data: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>) => {
  return request.post('/customers', data)
}

// 更新客户
export const updateCustomer = (id: number, data: Partial<Customer>) => {
  return request.put(`/customers/${id}`, data)
}

// 删除客户
export const deleteCustomer = (id: number) => {
  return request.delete(`/customers/${id}`)
}

// 获取客户统计
export const getCustomerStats = () => {
  return request.get('/customers/stats')
}

// 搜索客户
export const searchCustomers = (keyword: string) => {
  return request.get('/customers/search', { params: { keyword } })
}

// 获取客户咨询列表
export const getCustomerInquiries = (params?: InquiryQuery) => {
  return request.get('/customer-inquiries', { params })
}

// 根据ID获取客户咨询
export const getCustomerInquiryById = (id: number) => {
  return request.get(`/customer-inquiries/${id}`)
}

// 创建客户咨询
export const createCustomerInquiry = (data: Omit<CustomerInquiry, 'id' | 'createdAt' | 'updatedAt'>) => {
  return request.post('/customer-inquiries', data)
}

// 更新客户咨询
export const updateCustomerInquiry = (id: number, data: Partial<CustomerInquiry>) => {
  return request.put(`/customer-inquiries/${id}`, data)
}

// 删除客户咨询
export const deleteCustomerInquiry = (id: number) => {
  return request.delete(`/customer-inquiries/${id}`)
}

// 回复客户咨询
export const replyCustomerInquiry = (id: number, data: { reply: string }) => {
  return request.post(`/customer-inquiries/${id}/reply`, data)
}

// 分配客户咨询
export const assignCustomerInquiry = (id: number, data: { assignedTo: number }) => {
  return request.post(`/customer-inquiries/${id}/assign`, data)
}

// 关闭客户咨询
export const closeCustomerInquiry = (id: number) => {
  return request.post(`/customer-inquiries/${id}/close`)
}

// 获取客户的咨询历史
export const getCustomerInquiryHistory = (customerId: number) => {
  return request.get(`/customers/${customerId}/inquiries`)
}

// 获取客户的购买历史
export const getCustomerPurchaseHistory = (customerId: number) => {
  return request.get(`/customers/${customerId}/purchases`)
}

// 导出客户数据
export const exportCustomers = (params?: CustomerQuery) => {
  return request.get('/customers/export', {
    params,
    responseType: 'blob'
  })
}

// 导出客户咨询数据
export const exportCustomerInquiries = (params?: InquiryQuery) => {
  return request.get('/customer-inquiries/export', {
    params,
    responseType: 'blob'
  })
}

// 批量删除客户
export const batchDeleteCustomers = (ids: number[]) => {
  return request.delete('/customers/batch', { data: { ids } })
}

// 批量更新客户状态
export const batchUpdateCustomerStatus = (ids: number[], status: string) => {
  return request.put('/customers/batch/status', { ids, status })
}
