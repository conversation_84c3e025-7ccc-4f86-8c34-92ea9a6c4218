// 猫舍管理系统主要JavaScript功能

// 全局变量
let currentSection = 'dashboard';
let cats = [];
let mediaFiles = [];

// API基础URL - 自动检测
const API_BASE_URL = window.location.protocol === 'file:' 
    ? 'http://localhost:8080/api' 
    : window.location.protocol + '//' + window.location.hostname + ':8080/api';

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
    console.log('猫舍管理系统初始化中...');
    console.log('API Base URL:', API_BASE_URL);
    initializeApp();
});

// 应用初始化
function initializeApp() {
    setupNavigation();
    setupFileUpload();
    setupFormValidation();
    setupAccessibility();
    
    // 测试API连接
    testApiConnection();
    
    // 显示默认页面
    showSection('dashboard');
}

// 测试API连接
async function testApiConnection() {
    try {
        console.log('测试API连接...');
        // 使用正确的健康检查端点
        const response = await fetch(`${API_BASE_URL}/test/health`);

        if (response.ok) {
            const result = await response.json();
            console.log('API连接成功:', result);
            showMessage('系统连接正常', 'success');

            // 加载初始数据
            loadCatsData();
        } else {
            console.warn('API响应异常:', response.status);
            showMessage('API连接异常，使用模拟数据', 'warning');
            loadMockData();
        }
    } catch (error) {
        console.error('API连接失败:', error);
        showMessage('无法连接到后端服务，使用模拟数据', 'warning');
        loadMockData();
    }
}

// 设置导航
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-menu a');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const sectionId = this.getAttribute('href').substring(1);
            showSection(sectionId);
            
            // 更新导航状态
            navLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');
        });
        
        // 键盘导航支持
        link.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });
    });
}

// 显示指定页面
function showSection(sectionId) {
    console.log('切换到页面:', sectionId);
    
    // 隐藏所有页面
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => {
        section.classList.remove('active');
        section.setAttribute('aria-hidden', 'true');
    });
    
    // 显示目标页面
    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
        targetSection.classList.add('active');
        targetSection.setAttribute('aria-hidden', 'false');
        currentSection = sectionId;
        
        // 根据页面加载相应数据
        switch(sectionId) {
            case 'dashboard':
                loadDashboardData();
                break;
            case 'cats':
                loadCatsData();
                break;
            case 'media':
                loadMediaData();
                break;
        }
        
        // 设置焦点到页面标题
        const pageTitle = targetSection.querySelector('h2');
        if (pageTitle) {
            pageTitle.focus();
        }
    } else {
        console.error('页面不存在:', sectionId);
    }
}

// 加载仪表盘数据
async function loadDashboardData() {
    try {
        console.log('加载仪表盘数据...');
        
        // 首先测试API连接
        const response = await fetch(`${API_BASE_URL}/test/stats`);
        
        if (response.ok) {
            const result = await response.json();
            const stats = result.data;
            
            console.log('获取到统计数据:', stats);
            
            // 更新统计数据
            updateElement('total-cats', stats.totalCats);
            updateElement('breeding-cats', stats.breedingCats);
            updateElement('health-alerts', stats.healthAlerts);
            updateElement('customer-inquiries', stats.customerInquiries);
        } else {
            throw new Error(`API响应错误: ${response.status}`);
        }
        
    } catch (error) {
        console.error('加载仪表盘数据失败:', error);
        
        // 使用模拟数据作为后备
        const stats = {
            totalCats: 25,
            breedingCats: 8,
            healthAlerts: 3,
            customerInquiries: 12
        };
        
        console.log('使用模拟数据:', stats);
        
        updateElement('total-cats', stats.totalCats);
        updateElement('breeding-cats', stats.breedingCats);
        updateElement('health-alerts', stats.healthAlerts);
        updateElement('customer-inquiries', stats.customerInquiries);
    }
}

// 加载猫咪数据
async function loadCatsData() {
    try {
        console.log('加载猫咪数据...');
        showLoading(true);
        
        const response = await fetch(`${API_BASE_URL}/test/cats`);
        
        if (response.ok) {
            const result = await response.json();
            cats = result.data.cats || [];
            console.log('获取到猫咪数据:', cats);
        } else {
            throw new Error(`API响应错误: ${response.status}`);
        }
        
    } catch (error) {
        console.error('加载猫咪数据失败:', error);
        
        // 使用模拟数据
        cats = [
            {
                id: 1,
                name: '小白',
                breed: '英国短毛猫',
                gender: '母',
                status: 'available',
                image: 'https://via.placeholder.com/300x200/f8f9fa/6c757d?text=小白'
            },
            {
                id: 2,
                name: '小黑',
                breed: '波斯猫',
                gender: '公',
                status: 'breeding',
                image: 'https://via.placeholder.com/300x200/f8f9fa/6c757d?text=小黑'
            },
            {
                id: 3,
                name: '小花',
                breed: '布偶猫',
                gender: '母',
                status: 'pregnant',
                image: 'https://via.placeholder.com/300x200/f8f9fa/6c757d?text=小花'
            }
        ];
        console.log('使用模拟猫咪数据:', cats);
    } finally {
        renderCatsGrid();
        showLoading(false);
    }
}

// 渲染猫咪网格
function renderCatsGrid() {
    const grid = document.getElementById('cats-grid');
    if (!grid) {
        console.error('找不到cats-grid元素');
        return;
    }
    
    if (cats.length === 0) {
        grid.innerHTML = '<p>暂无猫咪数据</p>';
        return;
    }
    
    const html = cats.map(cat => `
        <div class="cat-card" role="gridcell">
            <img src="${cat.image}" alt="${cat.name}的照片" class="cat-image" loading="lazy" onerror="this.src='https://via.placeholder.com/300x200/f8f9fa/6c757d?text=${encodeURIComponent(cat.name)}'">
            <div class="cat-info">
                <h3 class="cat-name">${cat.name}</h3>
                <div class="cat-details">
                    <p>品种: ${cat.breed}</p>
                    <p>性别: ${cat.gender}</p>
                </div>
                <span class="cat-status status-${cat.status}">${getStatusText(cat.status)}</span>
            </div>
        </div>
    `).join('');
    
    grid.innerHTML = html;
    console.log('猫咪网格渲染完成');
}

// 获取状态文本
function getStatusText(status) {
    const statusMap = {
        'available': '可繁育',
        'breeding': '繁育中',
        'pregnant': '怀孕中',
        'nursing': '哺乳中',
        'retired': '已退役'
    };
    return statusMap[status] || status;
}

// 设置文件上传
function setupFileUpload() {
    const uploadArea = document.getElementById('upload-area');
    const fileInput = document.getElementById('file-input');
    
    if (!uploadArea || !fileInput) {
        console.warn('上传组件未找到');
        return;
    }
    
    console.log('设置文件上传功能');
    
    // 点击上传区域
    uploadArea.addEventListener('click', function() {
        fileInput.click();
    });
    
    // 键盘支持
    uploadArea.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            fileInput.click();
        }
    });
    
    // 文件选择
    fileInput.addEventListener('change', function(e) {
        const files = Array.from(e.target.files);
        if (files.length > 0) {
            console.log('选择了文件:', files);
            uploadFiles(files);
        }
    });
    
    // 拖拽上传
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
        
        const files = Array.from(e.dataTransfer.files);
        if (files.length > 0) {
            console.log('拖拽了文件:', files);
            uploadFiles(files);
        }
    });
}

// 上传文件
async function uploadFiles(files) {
    console.log('开始上传文件:', files);
    
    const progressContainer = document.getElementById('upload-progress');
    const progressFill = document.getElementById('progress-fill');
    const progressText = document.getElementById('progress-text');
    
    if (!progressContainer || !progressFill || !progressText) {
        console.error('进度条元素未找到');
        return;
    }
    
    progressContainer.style.display = 'block';
    progressContainer.setAttribute('aria-hidden', 'false');
    
    try {
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            const progress = Math.round(((i + 1) / files.length) * 100);
            
            console.log(`处理文件 ${i + 1}/${files.length}: ${file.name}`);
            
            // 更新进度
            progressFill.style.width = progress + '%';
            progressText.textContent = progress + '%';
            progressContainer.setAttribute('aria-valuenow', progress);
            
            // 验证文件
            if (!validateFile(file)) {
                continue;
            }
            
            // 模拟上传（实际项目中这里会调用真实的上传API）
            await simulateUpload(file);
        }
        
        showMessage('文件上传成功！', 'success');
        loadMediaData(); // 重新加载媒体数据
        
    } catch (error) {
        console.error('上传失败:', error);
        showMessage('文件上传失败：' + error.message, 'error');
    } finally {
        // 隐藏进度条
        setTimeout(() => {
            progressContainer.style.display = 'none';
            progressContainer.setAttribute('aria-hidden', 'true');
            progressFill.style.width = '0%';
            progressText.textContent = '0%';
        }, 1000);
    }
}

// 模拟上传
function simulateUpload(file) {
    return new Promise((resolve) => {
        setTimeout(() => {
            console.log('模拟上传完成:', file.name);
            resolve();
        }, 500);
    });
}

// 验证文件
function validateFile(file) {
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'video/mp4'];
    
    if (file.size > maxSize) {
        showMessage(`文件 ${file.name} 超过大小限制 (10MB)`, 'error');
        return false;
    }
    
    if (!allowedTypes.includes(file.type)) {
        showMessage(`文件 ${file.name} 类型不支持`, 'error');
        return false;
    }
    
    return true;
}

// 加载媒体数据
async function loadMediaData() {
    try {
        console.log('加载媒体数据...');
        
        // 模拟媒体数据
        mediaFiles = [
            {
                id: 1,
                name: 'cat1.jpg',
                size: 1024000,
                type: 'image',
                url: 'https://via.placeholder.com/200x150/f8f9fa/6c757d?text=Cat1'
            },
            {
                id: 2,
                name: 'cat2.mp4',
                size: 5120000,
                type: 'video',
                url: 'https://via.placeholder.com/200x150/f8f9fa/6c757d?text=Video'
            }
        ];
        
        renderMediaGallery();
        
    } catch (error) {
        console.error('加载媒体数据失败:', error);
        showMessage('加载媒体数据失败', 'error');
    }
}

// 渲染媒体画廊
function renderMediaGallery() {
    const gallery = document.getElementById('media-gallery');
    if (!gallery) {
        console.error('找不到media-gallery元素');
        return;
    }
    
    if (mediaFiles.length === 0) {
        gallery.innerHTML = '<p>暂无媒体文件</p>';
        return;
    }
    
    const html = mediaFiles.map(media => `
        <div class="media-item">
            <img src="${media.url}" alt="${media.name}" class="media-thumbnail" loading="lazy">
            <div class="media-info">
                <div class="media-name">${media.name}</div>
                <div class="media-size">${formatFileSize(media.size)}</div>
            </div>
        </div>
    `).join('');
    
    gallery.innerHTML = html;
    console.log('媒体画廊渲染完成');
}

// 设置表单验证
function setupFormValidation() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (validateForm(this)) {
                handleFormSubmit(this);
            }
        });
        
        // 实时验证
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
            
            input.addEventListener('input', function() {
                clearFieldError(this);
            });
        });
    });
}

// 验证表单
function validateForm(form) {
    let isValid = true;
    const requiredFields = form.querySelectorAll('[required]');
    
    requiredFields.forEach(field => {
        if (!validateField(field)) {
            isValid = false;
        }
    });
    
    return isValid;
}

// 验证字段
function validateField(field) {
    const value = field.value.trim();
    const fieldName = field.name;
    let isValid = true;
    let errorMessage = '';
    
    // 必填验证
    if (field.hasAttribute('required') && !value) {
        isValid = false;
        errorMessage = '此字段为必填项';
    }
    
    // 特定字段验证
    if (value && fieldName === 'cat-chip-id') {
        if (!/^\d{15}$/.test(value)) {
            isValid = false;
            errorMessage = '芯片号必须是15位数字';
        }
    }
    
    if (value && field.type === 'email') {
        if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
            isValid = false;
            errorMessage = '请输入有效的邮箱地址';
        }
    }
    
    // 显示或隐藏错误信息
    const errorElement = document.getElementById(field.getAttribute('aria-describedby'));
    if (errorElement && errorElement.classList.contains('error-message')) {
        if (isValid) {
            errorElement.textContent = '';
            errorElement.classList.remove('show');
            field.setAttribute('aria-invalid', 'false');
        } else {
            errorElement.textContent = errorMessage;
            errorElement.classList.add('show');
            field.setAttribute('aria-invalid', 'true');
        }
    }
    
    return isValid;
}

// 清除字段错误
function clearFieldError(field) {
    const errorElement = document.getElementById(field.getAttribute('aria-describedby'));
    if (errorElement && errorElement.classList.contains('error-message')) {
        errorElement.textContent = '';
        errorElement.classList.remove('show');
        field.setAttribute('aria-invalid', 'false');
    }
}

// 处理表单提交
async function handleFormSubmit(form) {
    const formId = form.id;
    console.log('提交表单:', formId);
    
    switch(formId) {
        case 'add-cat-form':
            await handleAddCat(form);
            break;
        default:
            console.log('未知表单:', formId);
    }
}

// 处理添加猫咪
async function handleAddCat(form) {
    const formData = new FormData(form);
    const catData = {
        name: formData.get('cat-name'),
        chipId: formData.get('cat-chip-id'),
        breed: formData.get('cat-breed'),
        gender: formData.get('cat-gender'),
        birthDate: formData.get('cat-birth-date')
    };
    
    console.log('添加猫咪数据:', catData);
    
    try {
        showLoading(true);
        
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        showMessage('猫咪添加成功！', 'success');
        closeModal('add-cat-modal');
        form.reset();
        loadCatsData(); // 重新加载猫咪数据
        
    } catch (error) {
        console.error('添加猫咪失败:', error);
        showMessage('添加猫咪失败：' + error.message, 'error');
    } finally {
        showLoading(false);
    }
}

// 设置可访问性
function setupAccessibility() {
    console.log('设置可访问性功能');
    
    // 跳转到主内容的链接
    const skipLink = document.createElement('a');
    skipLink.href = '#main-content';
    skipLink.textContent = '跳转到主内容';
    skipLink.className = 'sr-only';
    skipLink.addEventListener('focus', function() {
        this.classList.remove('sr-only');
    });
    skipLink.addEventListener('blur', function() {
        this.classList.add('sr-only');
    });
    document.body.insertBefore(skipLink, document.body.firstChild);
    
    // ESC键关闭模态框
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal.show');
            if (openModal) {
                closeModal(openModal.id);
            }
        }
    });
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

// 显示/隐藏加载状态
function showLoading(show) {
    const loading = document.getElementById('loading');
    if (loading) {
        loading.style.display = show ? 'flex' : 'none';
        loading.setAttribute('aria-hidden', show ? 'false' : 'true');
    }
}

// 显示消息
function showMessage(message, type = 'info') {
    console.log(`消息 [${type}]:`, message);
    
    const container = document.getElementById('message-container');
    if (!container) {
        console.error('找不到message-container元素');
        return;
    }
    
    const messageEl = document.createElement('div');
    messageEl.className = `message ${type}`;
    messageEl.textContent = message;
    messageEl.setAttribute('role', 'alert');
    
    container.appendChild(messageEl);
    
    // 自动移除消息
    setTimeout(() => {
        if (messageEl.parentNode) {
            messageEl.parentNode.removeChild(messageEl);
        }
    }, 5000);
}

// 更新元素内容
function updateElement(id, content) {
    const element = document.getElementById(id);
    if (element) {
        element.textContent = content;
        console.log(`更新元素 ${id}:`, content);
    } else {
        console.error('找不到元素:', id);
    }
}

// 显示模态框
function showModal(modalId) {
    console.log('显示模态框:', modalId);
    
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('show');
        modal.setAttribute('aria-hidden', 'false');
        
        // 禁用背景滚动
        document.body.style.overflow = 'hidden';
        
        // 设置焦点到第一个可聚焦元素
        const firstFocusable = modal.querySelector('input, button, select, textarea, [tabindex]:not([tabindex="-1"])');
        if (firstFocusable) {
            setTimeout(() => firstFocusable.focus(), 100);
        }
    } else {
        console.error('找不到模态框:', modalId);
    }
}

// 关闭模态框
function closeModal(modalId) {
    console.log('关闭模态框:', modalId);
    
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('show');
        modal.setAttribute('aria-hidden', 'true');
        
        // 恢复背景滚动
        document.body.style.overflow = '';
        
        // 清除表单数据
        const form = modal.querySelector('form');
        if (form) {
            form.reset();
            // 清除错误信息
            const errorMessages = form.querySelectorAll('.error-message');
            errorMessages.forEach(error => {
                error.textContent = '';
                error.classList.remove('show');
            });
        }
    } else {
        console.error('找不到模态框:', modalId);
    }
}

// 显示添加猫咪模态框
function showAddCatModal() {
    showModal('add-cat-modal');
}

// 加载真实猫咪数据
async function loadCatsData() {
    try {
        console.log('加载猫咪数据...');
        const response = await fetch(`${API_BASE_URL}/cats`);

        if (response.ok) {
            const result = await response.json();
            console.log('猫咪数据加载成功:', result);

            // 根据后端返回的数据结构处理
            if (result.data && Array.isArray(result.data)) {
                cats = result.data;
            } else if (Array.isArray(result)) {
                cats = result;
            } else {
                console.warn('猫咪数据格式异常:', result);
                loadMockData();
                return;
            }

            renderCatsGrid();
            updateDashboardStats();
        } else {
            console.warn('加载猫咪数据失败:', response.status);
            loadMockData();
        }
    } catch (error) {
        console.error('加载猫咪数据异常:', error);
        loadMockData();
    }
}

// 加载模拟数据
function loadMockData() {
    console.log('加载模拟数据...');

    cats = [
        {
            id: 1,
            name: '小花',
            breed: '英国短毛猫',
            gender: 'FEMALE',
            birthDate: '2023-03-15',
            color: '银渐层',
            status: 'AVAILABLE',
            isVaccinated: true,
            isSpayed: false,
            weight: 3.2,
            microchipId: '123456789012345',
            description: '性格温顺，喜欢和人互动',
            primaryPhoto: 'https://via.placeholder.com/300x200/FFB6C1/000000?text=小花'
        },
        {
            id: 2,
            name: '大橘',
            breed: '橘猫',
            gender: 'MALE',
            birthDate: '2022-08-20',
            color: '橘色',
            status: 'BREEDING',
            isVaccinated: true,
            isSpayed: false,
            weight: 5.8,
            microchipId: '123456789012346',
            description: '活泼好动，食量很大',
            primaryPhoto: 'https://via.placeholder.com/300x200/FFA500/000000?text=大橘'
        },
        {
            id: 3,
            name: '雪球',
            breed: '波斯猫',
            gender: 'FEMALE',
            birthDate: '2023-01-10',
            color: '纯白',
            status: 'AVAILABLE',
            isVaccinated: true,
            isSpayed: true,
            weight: 4.1,
            microchipId: '123456789012347',
            description: '优雅安静，毛发柔软',
            primaryPhoto: 'https://via.placeholder.com/300x200/FFFFFF/000000?text=雪球'
        }
    ];

    renderCatsGrid();
    updateDashboardStats();
    showMessage('已加载模拟数据', 'info');
}

// 更新仪表盘统计数据
function updateDashboardStats() {
    const totalCats = cats.length;
    const breedingCats = cats.filter(cat => cat.status === 'BREEDING').length;
    const healthAlerts = Math.floor(Math.random() * 5); // 模拟健康提醒
    const customerInquiries = Math.floor(Math.random() * 10); // 模拟客户咨询

    // 更新DOM
    const totalCatsElement = document.getElementById('total-cats');
    const breedingCatsElement = document.getElementById('breeding-cats');
    const healthAlertsElement = document.getElementById('health-alerts');
    const customerInquiriesElement = document.getElementById('customer-inquiries');

    if (totalCatsElement) totalCatsElement.textContent = totalCats;
    if (breedingCatsElement) breedingCatsElement.textContent = breedingCats;
    if (healthAlertsElement) healthAlertsElement.textContent = healthAlerts;
    if (customerInquiriesElement) customerInquiriesElement.textContent = customerInquiries;
}

// 全局函数，供HTML调用
window.showAddCatModal = showAddCatModal;
window.closeModal = closeModal;

console.log('猫舍管理系统JavaScript加载完成');