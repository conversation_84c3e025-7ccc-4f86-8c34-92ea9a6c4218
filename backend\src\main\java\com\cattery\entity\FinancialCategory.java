package com.cattery.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 财务分类实体类
 */
@Entity
@Table(name = "financial_categories")
@Data
@EqualsAndHashCode(callSuper = false)
public class FinancialCategory {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 分类名称
     */
    @Column(nullable = false, length = 100)
    private String name;

    /**
     * 分类描述
     */
    @Column(columnDefinition = "TEXT")
    private String description;

    /**
     * 分类类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "category_type", nullable = false)
    private CategoryType categoryType;

    /**
     * 父分类
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id")
    private FinancialCategory parent;

    /**
     * 子分类
     */
    @OneToMany(mappedBy = "parent", fetch = FetchType.LAZY)
    private List<FinancialCategory> children;

    /**
     * 排序顺序
     */
    @Column(name = "sort_order")
    private Integer sortOrder = 0;

    /**
     * 是否启用
     */
    @Column(nullable = false)
    private Boolean enabled = true;

    /**
     * 财务交易
     */
    @OneToMany(mappedBy = "category", fetch = FetchType.LAZY)
    private List<FinancialTransaction> transactions;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 创建者ID
     */
    @Column(name = "created_by")
    private Long createdBy;

    /**
     * 更新者ID
     */
    @Column(name = "updated_by")
    private Long updatedBy;

    /**
     * 分类类型枚举
     */
    public enum CategoryType {
        INCOME("收入"),
        EXPENSE("支出"),
        BOTH("收支");

        private final String description;

        CategoryType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
