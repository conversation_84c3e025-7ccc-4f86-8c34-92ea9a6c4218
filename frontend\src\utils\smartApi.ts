// 智能API服务 - 自动切换真实API和虚拟数据
import { api } from './request'
import { mockApi, shouldUseMockData, setMockDataMode } from './mockData'
import type { ApiResponse } from './request'

// API路由映射
const API_ROUTES = {
  // 认证相关
  login: '/auth/login',
  logout: '/auth/logout',
  getCurrentUser: '/auth/me',
  
  // 猫咪相关
  getCats: '/cats',
  getCatById: (id: number) => `/cats/${id}`,
  createCat: '/cats',
  updateCat: (id: number) => `/cats/${id}`,
  deleteCat: (id: number) => `/cats/${id}`,
  
  // 客户相关
  getCustomers: '/customers',
  getCustomerById: (id: number) => `/customers/${id}`,
  createCustomer: '/customers',
  updateCustomer: (id: number) => `/customers/${id}`,
  deleteCustomer: (id: number) => `/customers/${id}`,
  
  // 统计相关
  getStats: '/stats/dashboard',
  
  // API测试相关
  testHealth: '/test/health',
  testConnection: '/test/connection',
  testAuthStatus: '/test/auth-status'
}

// 智能API服务类
class SmartApiService {
  private useMockData: boolean = false
  private connectionTested: boolean = false

  constructor() {
    this.useMockData = shouldUseMockData()
    this.logCurrentMode()
  }

  private logCurrentMode() {
    if (this.useMockData) {
      console.log('🎭 当前使用虚拟数据模式')
    } else {
      console.log('🔗 当前使用真实API模式')
    }
  }

  // 切换数据模式
  switchMode(useMock: boolean) {
    this.useMockData = useMock
    setMockDataMode(useMock)
    this.logCurrentMode()
  }

  // 检查连接状态
  async checkConnection(): Promise<boolean> {
    if (this.useMockData) {
      return true
    }

    try {
      await api.get('/test/health', { timeout: 5000 })
      this.connectionTested = true
      return true
    } catch (error) {
      console.warn('后端连接失败，切换到虚拟数据模式:', error)
      this.switchMode(true)
      return false
    }
  }

  // 智能请求方法 - 自动选择真实API或虚拟数据
  private async smartRequest<T>(
    realApiCall: () => Promise<ApiResponse<T>>,
    mockApiCall: () => Promise<ApiResponse<T>>
  ): Promise<ApiResponse<T>> {
    // 如果明确设置使用虚拟数据，直接返回
    if (this.useMockData) {
      return mockApiCall()
    }

    try {
      // 尝试真实API调用
      return await realApiCall()
    } catch (error: any) {
      // 如果是网络错误，自动切换到虚拟数据
      if (error.message === 'Network Error' || error.code === 'ECONNABORTED') {
        console.warn('API调用失败，切换到虚拟数据模式:', error.message)
        this.switchMode(true)
        return mockApiCall()
      }
      // 其他错误直接抛出
      throw error
    }
  }

  // 认证相关API
  async login(username: string, password: string) {
    return this.smartRequest(
      () => api.post(API_ROUTES.login, { username, password }),
      () => mockApi.login(username, password)
    )
  }

  async logout() {
    return this.smartRequest(
      () => api.post(API_ROUTES.logout),
      () => mockApi.logout()
    )
  }

  async getCurrentUser() {
    return this.smartRequest(
      () => api.get(API_ROUTES.getCurrentUser),
      () => mockApi.getCurrentUser()
    )
  }

  // 猫咪相关API
  async getCats(params?: any) {
    return this.smartRequest(
      () => api.get(API_ROUTES.getCats, { params }),
      () => mockApi.getCats(params)
    )
  }

  async getCatById(id: number) {
    return this.smartRequest(
      () => api.get(API_ROUTES.getCatById(id)),
      () => mockApi.getCatById(id)
    )
  }

  async createCat(data: any) {
    return this.smartRequest(
      () => api.post(API_ROUTES.createCat, data),
      async () => {
        // 虚拟创建逻辑
        await new Promise(resolve => setTimeout(resolve, 500))
        return {
          code: 200,
          success: true,
          message: '创建成功(虚拟数据)',
          data: { id: Date.now(), ...data },
          timestamp: new Date().toISOString()
        }
      }
    )
  }

  // 客户相关API
  async getCustomers(params?: any) {
    return this.smartRequest(
      () => api.get(API_ROUTES.getCustomers, { params }),
      () => mockApi.getCustomers(params)
    )
  }

  async getCustomerById(id: number) {
    return this.smartRequest(
      () => api.get(API_ROUTES.getCustomerById(id)),
      () => mockApi.getCustomerById(id)
    )
  }

  async createCustomer(data: any) {
    return this.smartRequest(
      () => api.post(API_ROUTES.createCustomer, data),
      async () => {
        await new Promise(resolve => setTimeout(resolve, 500))
        return {
          code: 200,
          success: true,
          message: '创建成功(虚拟数据)',
          data: { id: Date.now(), ...data },
          timestamp: new Date().toISOString()
        }
      }
    )
  }

  // 统计相关API
  async getStats() {
    return this.smartRequest(
      () => api.get(API_ROUTES.getStats),
      () => mockApi.getStats()
    )
  }

  // API测试相关
  async testHealth() {
    return this.smartRequest(
      () => api.get(API_ROUTES.testHealth),
      () => mockApi.testHealth()
    )
  }

  async testConnection() {
    return this.smartRequest(
      () => api.get(API_ROUTES.testConnection),
      () => mockApi.testConnection()
    )
  }

  async testAuthStatus() {
    return this.smartRequest(
      () => api.get(API_ROUTES.testAuthStatus),
      () => mockApi.testAuthStatus()
    )
  }

  // 获取当前模式状态
  getStatus() {
    return {
      useMockData: this.useMockData,
      connectionTested: this.connectionTested,
      mode: this.useMockData ? '虚拟数据模式' : '真实API模式'
    }
  }

  // 强制重新检查连接
  async forceCheckConnection() {
    this.connectionTested = false
    return this.checkConnection()
  }
}

// 创建单例实例
export const smartApi = new SmartApiService()

// 导出常用方法
export const {
  login,
  logout,
  getCurrentUser,
  getCats,
  getCatById,
  createCat,
  getCustomers,
  getCustomerById,
  createCustomer,
  getStats,
  testHealth,
  testConnection,
  testAuthStatus,
  switchMode,
  checkConnection,
  getStatus,
  forceCheckConnection
} = smartApi

// 默认导出
export default smartApi
