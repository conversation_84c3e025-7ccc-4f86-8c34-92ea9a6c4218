# Force Node.js Cleanup Script
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Force Node.js Cleanup Script" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Issue: Installer says 'A later version of Node.js is already installed'" -ForegroundColor Red
Write-Host "But Node.js commands don't work in command line" -ForegroundColor Red
Write-Host ""

# Step 1: Check Windows Programs
Write-Host "Step 1: Checking Windows installed programs" -ForegroundColor Yellow
Write-Host ""

$nodePrograms = Get-WmiObject -Class Win32_Product | Where-Object { $_.Name -like "*Node.js*" }
if ($nodePrograms) {
    Write-Host "Found Node.js in Windows programs:" -ForegroundColor Green
    foreach ($program in $nodePrograms) {
        Write-Host "  Name: $($program.Name)" -ForegroundColor Cyan
        Write-Host "  Version: $($program.Version)" -ForegroundColor Cyan
        Write-Host "  Install Date: $($program.InstallDate)" -ForegroundColor Cyan
        Write-Host ""
        
        $uninstall = Read-Host "Uninstall this Node.js installation? (Y/N)"
        if ($uninstall -eq 'Y' -or $uninstall -eq 'y') {
            try {
                Write-Host "Uninstalling..." -ForegroundColor Yellow
                $program.Uninstall()
                Write-Host "Successfully uninstalled!" -ForegroundColor Green
            } catch {
                Write-Host "Failed to uninstall: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    }
} else {
    Write-Host "No Node.js found in Windows programs list" -ForegroundColor Yellow
}

# Step 2: Check registry
Write-Host ""
Write-Host "Step 2: Cleaning registry entries" -ForegroundColor Yellow
Write-Host ""

$registryPaths = @(
    "HKLM:\SOFTWARE\Node.js",
    "HKLM:\SOFTWARE\WOW6432Node\Node.js",
    "HKCU:\SOFTWARE\Node.js",
    "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\*",
    "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\*"
)

foreach ($path in $registryPaths) {
    try {
        if ($path -like "*Uninstall*") {
            # Check uninstall entries for Node.js
            $uninstallEntries = Get-ItemProperty $path -ErrorAction SilentlyContinue | Where-Object { $_.DisplayName -like "*Node.js*" }
            if ($uninstallEntries) {
                foreach ($entry in $uninstallEntries) {
                    Write-Host "Found Node.js uninstall entry: $($entry.DisplayName)" -ForegroundColor Cyan
                    $registryKey = $entry.PSPath
                    try {
                        Remove-Item $registryKey -Recurse -Force
                        Write-Host "Removed registry entry: $registryKey" -ForegroundColor Green
                    } catch {
                        Write-Host "Failed to remove: $registryKey" -ForegroundColor Red
                    }
                }
            }
        } else {
            if (Test-Path $path) {
                Remove-Item $path -Recurse -Force
                Write-Host "Removed registry path: $path" -ForegroundColor Green
            }
        }
    } catch {
        Write-Host "Could not access: $path" -ForegroundColor Gray
    }
}

# Step 3: Clean directories
Write-Host ""
Write-Host "Step 3: Cleaning directories" -ForegroundColor Yellow
Write-Host ""

$directories = @(
    "C:\Program Files\nodejs",
    "C:\Program Files (x86)\nodejs",
    "$env:APPDATA\npm",
    "$env:APPDATA\npm-cache",
    "$env:LOCALAPPDATA\npm",
    "$env:LOCALAPPDATA\npm-cache",
    "$env:USERPROFILE\nodejs"
)

foreach ($dir in $directories) {
    if (Test-Path $dir) {
        try {
            Remove-Item $dir -Recurse -Force
            Write-Host "Removed directory: $dir" -ForegroundColor Green
        } catch {
            Write-Host "Failed to remove: $dir (may need admin rights)" -ForegroundColor Red
        }
    } else {
        Write-Host "Directory not found: $dir" -ForegroundColor Gray
    }
}

# Step 4: Clean environment variables
Write-Host ""
Write-Host "Step 4: Cleaning environment variables" -ForegroundColor Yellow
Write-Host ""

# Get current PATH
$systemPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")
$userPath = [Environment]::GetEnvironmentVariable("PATH", "User")

# Clean system PATH
if ($systemPath) {
    $cleanSystemPath = ($systemPath -split ';' | Where-Object { $_ -notlike '*node*' -and $_ -notlike '*npm*' }) -join ';'
    if ($systemPath -ne $cleanSystemPath) {
        try {
            [Environment]::SetEnvironmentVariable("PATH", $cleanSystemPath, "Machine")
            Write-Host "Cleaned system PATH variable" -ForegroundColor Green
        } catch {
            Write-Host "Failed to clean system PATH (need admin rights)" -ForegroundColor Red
        }
    }
}

# Clean user PATH
if ($userPath) {
    $cleanUserPath = ($userPath -split ';' | Where-Object { $_ -notlike '*node*' -and $_ -notlike '*npm*' }) -join ';'
    if ($userPath -ne $cleanUserPath) {
        try {
            [Environment]::SetEnvironmentVariable("PATH", $cleanUserPath, "User")
            Write-Host "Cleaned user PATH variable" -ForegroundColor Green
        } catch {
            Write-Host "Failed to clean user PATH" -ForegroundColor Red
        }
    }
}

# Remove NODE_PATH if exists
try {
    [Environment]::SetEnvironmentVariable("NODE_PATH", $null, "Machine")
    [Environment]::SetEnvironmentVariable("NODE_PATH", $null, "User")
    Write-Host "Removed NODE_PATH variables" -ForegroundColor Green
} catch {
    Write-Host "Could not remove NODE_PATH variables" -ForegroundColor Yellow
}

# Step 5: Verification
Write-Host ""
Write-Host "Step 5: Verification" -ForegroundColor Yellow
Write-Host ""

Write-Host "Testing if Node.js is completely removed..." -ForegroundColor Cyan
try {
    $nodeTest = node --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "WARNING: Node.js still accessible!" -ForegroundColor Red
        Write-Host "Version: $nodeTest" -ForegroundColor Red
    } else {
        Write-Host "SUCCESS: Node.js completely removed!" -ForegroundColor Green
    }
} catch {
    Write-Host "SUCCESS: Node.js completely removed!" -ForegroundColor Green
}

# Step 6: Download fresh installer
Write-Host ""
Write-Host "Step 6: Download fresh Node.js installer" -ForegroundColor Yellow
Write-Host ""

Write-Host "Opening Node.js download page..." -ForegroundColor Cyan
Start-Process "https://nodejs.org/zh-cn/"

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Cleanup Complete!" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Next Steps:" -ForegroundColor Yellow
Write-Host "1. Restart your computer (recommended)" -ForegroundColor Cyan
Write-Host "2. Download Node.js from the opened website" -ForegroundColor Cyan
Write-Host "3. Install with these settings:" -ForegroundColor Cyan
Write-Host "   ✓ Install to: C:\Program Files\nodejs\" -ForegroundColor Green
Write-Host "   ✓ Add to PATH: YES" -ForegroundColor Green
Write-Host "   ✓ Install additional tools: YES" -ForegroundColor Green
Write-Host "4. Open NEW command prompt and test:" -ForegroundColor Cyan
Write-Host "   node --version" -ForegroundColor White
Write-Host "   npm --version" -ForegroundColor White

Write-Host ""
Write-Host "If you still get the 'later version' error:" -ForegroundColor Yellow
Write-Host "- Restart computer first" -ForegroundColor Cyan
Write-Host "- Try running installer as Administrator" -ForegroundColor Cyan
Write-Host "- Use the MSI installer, not the ZIP version" -ForegroundColor Cyan

Write-Host ""
Read-Host "Press Enter to exit"
