package com.cattery.dto.ai;

import lombok.Data;

import java.util.List;

/**
 * 健康预测输入DTO
 */
@Data
public class HealthPredictionInputDTO {
    
    /**
     * 猫咪ID
     */
    private Long catId;
    
    /**
     * 年龄（月）
     */
    private Integer age;
    
    /**
     * 品种
     */
    private String breed;
    
    /**
     * 性别
     */
    private String gender;
    
    /**
     * 体重
     */
    private Double weight;
    
    /**
     * 历史健康记录
     */
    private List<HealthRecordSummaryDTO> healthRecords;
    
    /**
     * 当前症状
     */
    private List<String> currentSymptoms;
    
    /**
     * 行为变化
     */
    private List<String> behaviorChanges;
    
    /**
     * 环境因素
     */
    private List<String> environmentalFactors;
    
    /**
     * 遗传风险因素
     */
    private List<String> geneticRiskFactors;
    
    /**
     * 疫苗接种状态
     */
    private String vaccinationStatus;
    
    /**
     * 绝育状态
     */
    private Boolean isNeutered;
    
    /**
     * 生活方式 (INDOOR, OUTDOOR, MIXED)
     */
    private String lifestyle;

    /**
     * 活动水平
     */
    private String activityLevel;

    /**
     * 健康状况
     */
    private List<String> healthConditions;
}
