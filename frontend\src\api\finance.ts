import request from '@/utils/request'

export interface FinanceRecord {
  id?: number
  type: 'INCOME' | 'EXPENSE'
  category: string
  amount: number
  description: string
  transactionDate: string
  paymentMethod: 'CASH' | 'BANK_TRANSFER' | 'ALIPAY' | 'WECHAT' | 'CREDIT_CARD' | 'OTHER'
  relatedCatId?: number
  relatedCustomerId?: number
  relatedBreedingRecordId?: number
  relatedHealthRecordId?: number
  invoiceNumber?: string
  receiptPath?: string
  notes?: string
  status: 'PENDING' | 'COMPLETED' | 'CANCELLED'
  createdAt?: string
  updatedAt?: string
  createdBy?: number
  updatedBy?: number
  relatedCat?: {
    id: number
    name: string
    breed: string
  }
  relatedCustomer?: {
    id: number
    name: string
    phone: string
  }
}

export interface FinanceQuery {
  page?: number
  size?: number
  type?: 'INCOME' | 'EXPENSE'
  category?: string
  paymentMethod?: string
  status?: string
  relatedCatId?: number
  relatedCustomerId?: number
  startDate?: string
  endDate?: string
  minAmount?: number
  maxAmount?: number
  startMonth?: string
  endMonth?: string
}

export interface FinanceStats {
  totalIncome: number
  totalExpense: number
  netProfit: number
  monthlyIncome: number
  monthlyExpense: number
  monthlyProfit: number
  incomeByCategory: Array<{
    category: string
    amount: number
    percentage: number
  }>
  expenseByCategory: Array<{
    category: string
    amount: number
    percentage: number
  }>
  monthlyTrend: Array<{
    month: string
    income: number
    expense: number
    profit: number
  }>
}

// 获取财务记录列表
export const getFinanceRecords = (params?: FinanceQuery) => {
  return request.get('/finance-records', { params })
}

// 根据ID获取财务记录
export const getFinanceRecordById = (id: number) => {
  return request.get(`/finance-records/${id}`)
}

// 创建财务记录
export const createFinanceRecord = (data: Omit<FinanceRecord, 'id' | 'createdAt' | 'updatedAt'>) => {
  return request.post('/finance-records', data)
}

// 更新财务记录
export const updateFinanceRecord = (id: number, data: Partial<FinanceRecord>) => {
  return request.put(`/finance-records/${id}`, data)
}

// 删除财务记录
export const deleteFinanceRecord = (id: number) => {
  return request.delete(`/finance-records/${id}`)
}

// 获取财务统计
export const getFinanceStats = (params?: { startDate?: string; endDate?: string }) => {
  return request.get<FinanceStats>('/finance-records/stats', { params })
}

  // 根据ID获取财务交易
  getTransactionById(id: number): Promise<FinancialTransaction> {
    return http.get<FinancialTransaction>(`/api/financial/transactions/${id}`)
  },

  // 创建财务交易
  createTransaction(transaction: Omit<FinancialTransaction, 'id' | 'createdAt'>): Promise<FinancialTransaction> {
    return http.post<FinancialTransaction>('/api/financial/transactions', transaction)
  },

  // 更新财务交易
  updateTransaction(id: number, transaction: Partial<FinancialTransaction>): Promise<FinancialTransaction> {
    return http.put<FinancialTransaction>(`/api/financial/transactions/${id}`, transaction)
  },

  // 删除财务交易
  deleteTransaction(id: number): Promise<void> {
    return http.delete<void>(`/api/financial/transactions/${id}`)
  },

  // 获取收入分类统计
  getIncomeByCategory(startDate?: string, endDate?: string): Promise<Array<{
    category: string
    amount: number
    percentage: number
  }>> {
    const params = { startDate, endDate }
    return http.get('/api/financial/income-by-category', { params })
  },

  // 获取支出分类统计
  getExpenseByCategory(startDate?: string, endDate?: string): Promise<Array<{
    category: string
    amount: number
    percentage: number
  }>> {
    const params = { startDate, endDate }
    return http.get('/api/financial/expense-by-category', { params })
  },

  // 获取月度财务统计
  getMonthlyStats(year: number): Promise<Array<{
    month: number
    income: number
    expense: number
    profit: number
  }>> {
    return http.get(`/api/financial/monthly-stats/${year}`)
  },

  // 获取支付方式统计
  getPaymentMethodStats(startDate?: string, endDate?: string): Promise<Array<{
    paymentMethod: string
    amount: number
    count: number
    percentage: number
  }>> {
    const params = { startDate, endDate }
    return http.get('/api/financial/payment-method-stats', { params })
  },

  // 获取最近的交易
  getRecentTransactions(limit: number = 10): Promise<FinancialTransaction[]> {
    return http.get<FinancialTransaction[]>(`/api/financial/recent-transactions?limit=${limit}`)
  },

  // 获取财务趋势数据
  getFinancialTrend(params?: {
    startDate?: string
    endDate?: string
    period?: 'daily' | 'weekly' | 'monthly'
  }): Promise<Array<{
    date: string
    income: number
    expense: number
    profit: number
  }>> {
    return http.get('/api/financial/trend', { params })
  },

  // 导出财务报表
  exportReport(params: {
    startDate: string
    endDate: string
    format: 'excel' | 'pdf'
    type?: 'summary' | 'detailed'
  }): Promise<Blob> {
    return http.get('/api/financial/export', { 
      params,
      responseType: 'blob'
    })
  },

  // 获取财务分析
  getFinancialAnalysis(startDate: string, endDate: string): Promise<{
    profitMargin: number
    growthRate: number
    averageTransactionAmount: number
    topIncomeCategories: Array<{ category: string; amount: number }>
    topExpenseCategories: Array<{ category: string; amount: number }>
    recommendations: string[]
  }> {
    const params = { startDate, endDate }
    return http.get('/api/financial/analysis', { params })
  }
}

export default financeApi
