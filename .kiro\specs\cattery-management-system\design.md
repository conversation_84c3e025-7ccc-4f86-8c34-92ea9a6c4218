# 设计文档

## 概述

猫舍管理系统采用前后端分离的架构设计，后端使用Spring Boot框架提供RESTful API服务，前端使用Vue 3 + Element Plus构建现代化的用户界面。系统设计遵循领域驱动设计(DDD)原则，将业务逻辑清晰地组织在不同的领域模块中。

## 架构

### 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   后端API服务   │    │   数据库服务    │
│   Vue 3 +       │◄──►│   Spring Boot   │◄──►│   MySQL 8.0     │
│   Element Plus  │    │   + Spring      │    │                 │
│                 │    │   Security      │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   文件存储      │
                       │   本地/云存储   │
                       └─────────────────┘
```

### 技术栈

**后端技术栈：**
- Spring Boot 3.2.0 - 主框架
- Spring Security - 认证授权
- Spring Data JPA - 数据访问
- MySQL 8.0 - 主数据库
- JWT - 令牌认证
- Maven - 项目管理
- Swagger/OpenAPI - API文档

**前端技术栈：**
- Vue 3 - 前端框架
- Element Plus - UI组件库
- Vue Router - 路由管理
- Pinia - 状态管理
- Axios - HTTP客户端
- ECharts - 数据可视化

## 组件和接口

### 后端模块设计

#### 1. 认证授权模块 (Auth Module)
```java
// 核心组件
- JwtAuthenticationFilter: JWT令牌验证过滤器
- UserDetailsServiceImpl: 用户详情服务实现
- SecurityConfig: 安全配置类
- AuthController: 认证控制器

// 主要接口
POST /api/auth/login - 用户登录
POST /api/auth/register - 用户注册
POST /api/auth/refresh - 刷新令牌
POST /api/auth/logout - 用户登出
```

#### 2. 猫咪管理模块 (Cat Module)
```java
// 核心组件
- Cat: 猫咪实体类
- CatBreed: 品种实体类
- CatMedia: 媒体文件实体类
- CatService: 猫咪业务服务
- CatController: 猫咪控制器

// 主要接口
GET /api/cats - 获取猫咪列表
POST /api/cats - 创建猫咪档案
GET /api/cats/{id} - 获取猫咪详情
PUT /api/cats/{id} - 更新猫咪信息
DELETE /api/cats/{id} - 删除猫咪档案
GET /api/cats/{id}/pedigree - 获取血统树
POST /api/cats/{id}/media - 上传媒体文件
```

#### 3. 健康管理模块 (Health Module)
```java
// 核心组件
- HealthRecord: 健康记录实体
- VaccineRecord: 疫苗记录实体
- HealthService: 健康服务
- HealthController: 健康控制器

// 主要接口
GET /api/health/records/{catId} - 获取健康记录
POST /api/health/records - 创建健康记录
GET /api/health/vaccines/{catId} - 获取疫苗记录
POST /api/health/vaccines - 记录疫苗接种
GET /api/health/reminders - 获取健康提醒
```

#### 4. 繁育管理模块 (Breeding Module)
```java
// 核心组件
- HeatCycle: 发情周期实体
- Mating: 配种记录实体
- Pregnancy: 怀孕记录实体
- Litter: 产仔记录实体
- BreedingService: 繁育服务

// 主要接口
GET /api/breeding/heat-cycles - 获取发情记录
POST /api/breeding/matings - 记录配种
GET /api/breeding/pregnancies - 获取怀孕记录
POST /api/breeding/litters - 记录分娩
GET /api/breeding/inbreeding-check - 近亲繁殖检查
```

#### 5. 客户管理模块 (Customer Module)
```java
// 核心组件
- Customer: 客户实体
- Inquiry: 咨询记录实体
- Contract: 合同实体
- FollowUp: 跟进记录实体
- CustomerService: 客户服务

// 主要接口
GET /api/customers - 获取客户列表
POST /api/customers - 创建客户档案
GET /api/customers/{id}/inquiries - 获取咨询记录
POST /api/contracts - 创建合同
GET /api/follow-ups - 获取跟进记录
```

#### 6. 库存管理模块 (Inventory Module)
```java
// 核心组件
- Inventory: 库存实体
- InventoryTransaction: 库存交易实体
- FeedingPlan: 喂养计划实体
- InventoryService: 库存服务

// 主要接口
GET /api/inventory/items - 获取库存列表
POST /api/inventory/transactions - 记录库存交易
GET /api/inventory/alerts - 获取库存预警
POST /api/inventory/feeding-plans - 创建喂养计划
```

#### 7. 财务管理模块 (Finance Module)
```java
// 核心组件
- FinancialRecord: 财务记录实体
- Budget: 预算实体
- FinanceService: 财务服务
- FinanceController: 财务控制器

// 主要接口
GET /api/finance/records - 获取财务记录
POST /api/finance/records - 创建财务记录
GET /api/finance/reports - 获取财务报表
GET /api/finance/analysis - 获取盈利分析
```

### 前端组件设计

#### 1. 布局组件
```vue
// 主要组件
- AppLayout: 应用主布局
- Sidebar: 侧边导航栏
- Header: 顶部导航栏
- Breadcrumb: 面包屑导航
```

#### 2. 业务组件
```vue
// 猫咪管理组件
- CatList: 猫咪列表
- CatCard: 猫咪卡片
- CatForm: 猫咪表单
- PedigreeTree: 血统树
- MediaGallery: 媒体画廊

// 健康管理组件
- HealthTimeline: 健康时间线
- VaccineSchedule: 疫苗计划
- HealthChart: 健康图表

// 繁育管理组件
- BreedingBoard: 繁育看板
- MatingRecord: 配种记录
- PregnancyTracker: 孕期跟踪

// 客户管理组件
- CustomerList: 客户列表
- InquiryForm: 咨询表单
- ContractGenerator: 合同生成器

// 财务管理组件
- FinanceChart: 财务图表
- ExpenseTracker: 支出跟踪
- ProfitAnalysis: 盈利分析
```

## 数据模型

### 核心实体关系图

```
Cat (猫咪)
├── CatBreed (品种) - Many to One
├── CatMedia (媒体) - One to Many
├── HealthRecord (健康记录) - One to Many
├── HeatCycle (发情周期) - One to Many
├── Mating (配种记录) - One to Many
├── Pregnancy (怀孕记录) - One to Many
└── Contract (合同) - One to Many

Customer (客户)
├── Inquiry (咨询) - One to Many
├── Contract (合同) - One to Many
└── FollowUp (跟进) - One to Many

FinancialRecord (财务记录)
├── Cat - Many to One (可选)
├── Customer - Many to One (可选)
└── Inventory - Many to One (可选)
```

### 主要实体设计

#### Cat 实体
```java
@Entity
public class Cat {
    private Long id;
    private String chipNumber;        // 芯片号
    private String registeredName;    // 注册名
    private String nickname;          // 昵称
    private Gender gender;            // 性别
    private LocalDate birthDate;      // 生日
    private String color;             // 毛色
    private String eyeColor;          // 眼色
    private CatStatus status;         // 状态
    private CatBreed breed;           // 品种
    private Cat father;               // 父亲
    private Cat mother;               // 母亲
    private List<CatMedia> media;     // 媒体文件
    private List<HealthRecord> healthRecords; // 健康记录
}
```

#### HealthRecord 实体
```java
@Entity
public class HealthRecord {
    private Long id;
    private Cat cat;                  // 关联猫咪
    private HealthRecordType type;    // 记录类型
    private LocalDateTime recordDate; // 记录日期
    private String description;       // 描述
    private String veterinarian;      // 兽医
    private BigDecimal weight;        // 体重
    private String notes;             // 备注
}
```

#### Customer 实体
```java
@Entity
public class Customer {
    private Long id;
    private String name;              // 姓名
    private String phone;             // 电话
    private String email;             // 邮箱
    private String address;           // 地址
    private CustomerStatus status;    // 状态
    private List<Inquiry> inquiries;  // 咨询记录
    private List<Contract> contracts; // 合同记录
}
```

## 错误处理

### 异常处理策略

#### 1. 全局异常处理器
```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<ApiResponse> handleBusinessException(BusinessException e) {
        return ResponseEntity.badRequest()
            .body(ApiResponse.error(e.getCode(), e.getMessage()));
    }
    
    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<ApiResponse> handleValidationException(ValidationException e) {
        return ResponseEntity.badRequest()
            .body(ApiResponse.error("VALIDATION_ERROR", e.getMessage()));
    }
}
```

#### 2. 业务异常定义
```java
public class BusinessException extends RuntimeException {
    private String code;
    private String message;
    
    // 常见业务异常
    public static final String CAT_NOT_FOUND = "CAT_NOT_FOUND";
    public static final String INBREEDING_RISK = "INBREEDING_RISK";
    public static final String INVALID_BREEDING_AGE = "INVALID_BREEDING_AGE";
    public static final String DUPLICATE_CHIP_NUMBER = "DUPLICATE_CHIP_NUMBER";
}
```

#### 3. 前端错误处理
```javascript
// HTTP拦截器
axios.interceptors.response.use(
  response => response,
  error => {
    if (error.response?.status === 401) {
      // 处理认证失败
      router.push('/login');
    } else if (error.response?.status === 403) {
      // 处理权限不足
      ElMessage.error('权限不足');
    } else {
      // 处理其他错误
      ElMessage.error(error.response?.data?.message || '系统错误');
    }
    return Promise.reject(error);
  }
);
```

## 测试策略

### 后端测试

#### 1. 单元测试
```java
// Service层测试
@ExtendWith(MockitoExtension.class)
class CatServiceTest {
    @Mock
    private CatRepository catRepository;
    
    @InjectMocks
    private CatService catService;
    
    @Test
    void shouldCreateCatSuccessfully() {
        // 测试猫咪创建逻辑
    }
}
```

#### 2. 集成测试
```java
// Controller层测试
@SpringBootTest
@AutoConfigureTestDatabase
@Transactional
class CatControllerIntegrationTest {
    
    @Test
    void shouldReturnCatList() {
        // 测试API接口
    }
}
```

#### 3. 数据库测试
```java
// Repository层测试
@DataJpaTest
class CatRepositoryTest {
    
    @Test
    void shouldFindCatsByBreed() {
        // 测试数据库查询
    }
}
```

### 前端测试

#### 1. 组件测试
```javascript
// Vue组件测试
import { mount } from '@vue/test-utils'
import CatCard from '@/components/CatCard.vue'

describe('CatCard', () => {
  test('renders cat information correctly', () => {
    // 测试组件渲染
  })
})
```

#### 2. E2E测试
```javascript
// Cypress E2E测试
describe('Cat Management', () => {
  it('should create new cat', () => {
    cy.visit('/cats')
    cy.get('[data-cy=add-cat-btn]').click()
    // 测试完整用户流程
  })
})
```

### 测试覆盖率目标
- 单元测试覆盖率：≥ 80%
- 集成测试覆盖率：≥ 70%
- E2E测试覆盖率：≥ 60%

## 性能优化

### 数据库优化
```sql
-- 关键索引设计
CREATE INDEX idx_cat_breed_status ON cats(breed_id, status);
CREATE INDEX idx_health_record_cat_date ON health_records(cat_id, record_date);
CREATE INDEX idx_financial_record_date ON financial_records(record_date);
```

### 缓存策略
```java
// Redis缓存配置
@Cacheable(value = "cats", key = "#id")
public Cat findById(Long id) {
    return catRepository.findById(id);
}

@CacheEvict(value = "cats", key = "#cat.id")
public Cat updateCat(Cat cat) {
    return catRepository.save(cat);
}
```

### 前端性能优化
```javascript
// 路由懒加载
const routes = [
  {
    path: '/cats',
    component: () => import('@/views/CatManagement.vue')
  }
]

// 组件懒加载
const CatForm = defineAsyncComponent(() => import('@/components/CatForm.vue'))
```

## 安全设计

### 认证授权
```java
// JWT配置
@Configuration
public class JwtConfig {
    @Value("${jwt.secret}")
    private String secret;
    
    @Value("${jwt.expiration}")
    private long expiration;
}

// 权限控制
@PreAuthorize("hasRole('ADMIN') or hasRole('BREEDER')")
public ResponseEntity<Cat> createCat(@RequestBody Cat cat) {
    // 创建猫咪逻辑
}
```

### 数据验证
```java
// 输入验证
public class CatDTO {
    @NotBlank(message = "芯片号不能为空")
    @Pattern(regexp = "^[0-9]{15}$", message = "芯片号格式不正确")
    private String chipNumber;
    
    @NotNull(message = "品种不能为空")
    private Long breedId;
    
    @Past(message = "生日必须是过去的日期")
    private LocalDate birthDate;
}
```

### 文件上传安全
```java
// 文件类型验证
@Component
public class FileValidator {
    private static final List<String> ALLOWED_TYPES = 
        Arrays.asList("image/jpeg", "image/png", "video/mp4");
    
    public boolean isValidFile(MultipartFile file) {
        return ALLOWED_TYPES.contains(file.getContentType()) 
            && file.getSize() <= MAX_FILE_SIZE;
    }
}
```