﻿# 猫舍管理系统

一个专业的猫咪繁育和健康管理平台，提供全面的猫舍运营管理功能。

## 🐱 功能特性

### 核心功能
- **猫咪档案管理** - 完整的猫咪信息记录和管理
- **繁育管理** - 发情周期、配种记录、孕期跟踪
- **健康管理** - 疫苗接种、体检记录、遗传病筛查
- **媒体管理** - 照片、视频上传和时间线展示
- **客户关系管理** - 客户档案、咨询跟进、合同管理
- **库存管理** - 猫粮、药品、营养品库存跟踪
- **财务管理** - 收支记录、盈利分析、报表生成

### 技术特性
- **响应式设计** - 适配桌面和移动设备
- **可访问性支持** - 符合WCAG 2.1标准
- **多语言支持** - 中文界面
- **文件管理** - 安全的文件上传和存储
- **数据可视化** - 图表和统计分析
- **权限控制** - 基于角色的访问控制

## 🏗️ 技术架构

### 后端技术栈
- **Java 17** - 编程语言
- **Spring Boot 3.x** - 应用框架
- **Spring Security** - 安全框架
- **Spring Data JPA** - 数据访问层
- **MySQL** - 数据库
- **Maven** - 构建工具
- **Swagger/OpenAPI** - API文档

### 前端技术栈
- **Vue 3** - 渐进式JavaScript框架
- **TypeScript** - 类型安全的JavaScript
- **Element Plus** - Vue 3组件库
- **Vite** - 现代化构建工具
- **Pinia** - 状态管理
- **Vue Router** - 路由管理
- **Axios** - HTTP客户端
- **ECharts** - 数据可视化

## 🚀 快速开始

### 环境要求
- Java 17+
- MySQL 8.0+
- Maven 3.6+
- Node.js 16+ (可选，用于前端开发)

### 安装步骤

1. **克隆项目**
   ```bash
   git clone https://github.com/your-repo/cattery-management-system.git
   cd cattery-management-system
   ```

2. **配置数据库**
   ```sql
   CREATE DATABASE cat_shelter CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

3. **配置应用**
   ```bash
   # 复制配置文件
   cp backend/src/main/resources/application.yml.example backend/src/main/resources/application.yml
   
   # 修改数据库连接信息
   vim backend/src/main/resources/application.yml
   ```

4. **一键启动（推荐）**
   ```bash
   # Windows用户
   start.bat

   # 或手动启动
   ```

5. **手动启动后端服务**
   ```bash
   cd backend
   .\mvnw.cmd spring-boot:run
   # 或使用 mvn spring-boot:run（如果已安装Maven）
   ```

6. **手动启动前端服务**
   ```bash
   cd frontend

   # 方式1: Vue开发服务器（推荐）
   npm install
   npm run dev
   # 访问 http://localhost:3000

   # 方式2: 静态HTML测试页面
   # 直接打开 test-connection.html 文件
   ```

### 使用Docker启动

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 停止服务
docker-compose down
```

## 📖 使用指南

### 基本操作

1. **添加猫咪**
   - 点击"猫咪管理"页面的"添加猫咪"按钮
   - 填写猫咪基本信息（名称、品种、性别等）
   - 上传猫咪照片
   - 保存信息

2. **媒体管理**
   - 进入"媒体管理"页面
   - 拖拽文件到上传区域或点击选择文件
   - 支持批量上传照片和视频
   - 自动生成缩略图

3. **健康记录**
   - 在猫咪详情页面添加健康记录
   - 记录疫苗接种、体检结果
   - 设置提醒事项

### API使用

系统提供RESTful API接口，详细文档请访问：
- Swagger UI: http://localhost:8080/swagger-ui/index.html
- API文档: http://localhost:8080/v3/api-docs

### 常用API端点

```bash
# 获取所有猫咪
GET /api/cats

# 添加新猫咪
POST /api/cats

# 上传媒体文件
POST /api/cats/{catId}/media/photos

# 获取媒体时间线
GET /api/cats/{catId}/media/timeline
```

## 🔧 配置说明

### 应用配置

主要配置文件：`backend/src/main/resources/application.yml`

```yaml
# 数据库配置
spring:
  datasource:
    url: ***************************************
    username: your_username
    password: your_password

# 文件存储配置
file:
  storage:
    upload-dir: uploads
    max-file-size: 10485760  # 10MB
    allowed-image-types: image/jpeg,image/png,image/gif
    allowed-video-types: video/mp4,video/avi
```

### 安全配置

- CORS已配置支持本地开发
- 文件上传有大小和类型限制
- API访问暂时开放（开发阶段）

## 🎨 界面预览

### 仪表盘
- 显示猫舍运营概览
- 统计数据卡片
- 月度趋势图表

### 猫咪管理
- 网格视图展示猫咪卡片
- 搜索和筛选功能
- 详细信息编辑

### 媒体管理
- 拖拽上传界面
- 媒体文件画廊
- 时间线展示

## 🔍 可访问性特性

本系统严格遵循Web可访问性标准：

### WCAG 2.1 合规性
- ✅ **键盘导航** - 所有功能都可通过键盘操作
- ✅ **屏幕阅读器支持** - 完整的ARIA标签和语义化HTML
- ✅ **焦点管理** - 清晰的焦点指示器
- ✅ **颜色对比度** - 符合AA级标准
- ✅ **响应式设计** - 支持缩放到200%
- ✅ **表单标签** - 所有表单元素都有适当的标签
- ✅ **错误提示** - 清晰的错误信息和验证反馈

### 浏览器兼容性
- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+

### 移动设备支持
- ✅ iOS Safari
- ✅ Android Chrome
- ✅ 触摸友好的界面设计

## 🧪 测试

### 运行测试

```bash
# 后端单元测试
cd backend
mvn test

# 后端集成测试
mvn test -Dtest=**/*IntegrationTest

# 前端测试（如果配置了测试框架）
cd frontend
npm test
```

### 测试覆盖率

- 单元测试覆盖率目标：80%+
- 集成测试覆盖核心业务流程
- API测试覆盖所有端点

## 📝 开发指南

### 代码规范

- Java代码遵循Google Java Style Guide
- JavaScript代码使用ES6+标准
- CSS使用BEM命名规范
- 提交信息遵循Conventional Commits

### 项目结构

```
cattery-management-system/
├── backend/                 # 后端Spring Boot应用
│   ├── src/main/java/      # Java源代码
│   ├── src/main/resources/ # 配置文件
│   └── src/test/           # 测试代码
├── frontend/               # 前端静态文件
│   ├── index.html         # 主页面
│   ├── styles/            # CSS样式
│   └── scripts/           # JavaScript脚本
├── docker-compose.yml     # Docker编排文件
└── README.md             # 项目文档
```

### 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 🐛 问题排查

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 验证数据库连接配置
   - 确认数据库用户权限

2. **文件上传失败**
   - 检查文件大小是否超限
   - 验证文件类型是否支持
   - 确认上传目录权限

3. **前端无法访问API**
   - 检查CORS配置
   - 验证API服务是否启动
   - 查看浏览器控制台错误

### 日志查看

```bash
# 查看应用日志
tail -f backend/logs/application.log

# 查看错误日志
tail -f backend/logs/error.log
```

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 支持

如果您遇到问题或有建议，请：

1. 查看[常见问题](#问题排查)
2. 搜索[已有Issues](https://github.com/your-repo/cattery-management-system/issues)
3. 创建新的Issue描述问题
4. 联系开发团队

## 🎯 路线图

### v1.0 (当前版本)
- ✅ 基础猫咪管理功能
- ✅ 媒体文件管理
- ✅ 响应式界面设计
- ✅ 可访问性支持

### v1.1 (计划中)
- 🔄 用户认证和权限管理
- 🔄 繁育管理功能
- 🔄 健康记录管理
- 🔄 数据导入导出

### v1.2 (未来版本)
- 📋 客户关系管理
- 📋 财务管理模块
- 📋 报表和分析功能
- 📋 移动应用支持

---

**猫舍管理系统** - 让猫咪繁育管理更简单、更专业！ 🐾