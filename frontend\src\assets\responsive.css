/* 响应式缩放样式 */

/* 基础响应式设置 - 管理系统专用 */
* {
  box-sizing: border-box;
}

html {
  font-size: 14px;
  text-size-adjust: 100%;
  scroll-behavior: smooth;
  width: 100%;
  height: 100%;
}

body {
  margin: 0;
  padding: 0;
  overflow: hidden;
  min-width: 320px;
  width: 100%;
  height: 100%;
}

/* 管理系统容器响应式 */
.layout-container-demo {
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.main-content {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  padding: 20px;
}

/* 响应式断点 */

/* 超小屏幕 (手机) */
@media (max-width: 480px) {
  html {
    font-size: 12px;
  }
  
  .container {
    padding: 0 0.5rem;
  }
  
  /* Element Plus 组件响应式调整 */
  .el-table {
    font-size: 0.8rem;
  }
  
  .el-button {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }
  
  .el-input {
    font-size: 0.8rem;
  }
  
  .el-dialog {
    width: 95% !important;
    margin: 1rem auto !important;
  }
  
  .el-form-item__label {
    font-size: 0.8rem;
  }
}

/* 小屏幕 (平板竖屏) */
@media (min-width: 481px) and (max-width: 768px) {
  html {
    font-size: 14px;
  }
  
  .container {
    padding: 0 0.75rem;
  }
  
  .el-dialog {
    width: 90% !important;
  }
}

/* 中等屏幕 (平板横屏/小桌面) */
@media (min-width: 769px) and (max-width: 1024px) {
  html {
    font-size: 15px;
  }
  
  .container {
    padding: 0 1rem;
  }
  
  .el-dialog {
    width: 80% !important;
  }
}

/* 大屏幕 (桌面) */
@media (min-width: 1025px) and (max-width: 1200px) {
  html {
    font-size: 16px;
  }
}

/* 超大屏幕 */
@media (min-width: 1201px) and (max-width: 1600px) {
  html {
    font-size: 18px;
  }
}

/* 4K屏幕 */
@media (min-width: 1601px) {
  html {
    font-size: 20px;
  }
  
  .container {
    max-width: 1400px;
  }
}

/* 布局响应式 */
.responsive-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

@media (max-width: 768px) {
  .responsive-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
}

/* 侧边栏响应式 */
.layout-sidebar {
  transition: all 0.3s ease;
}

@media (max-width: 768px) {
  .layout-sidebar {
    position: fixed;
    z-index: 1000;
    transform: translateX(-100%);
  }
  
  .layout-sidebar.mobile-open {
    transform: translateX(0);
  }
  
  .layout-main {
    margin-left: 0 !important;
  }
}

/* 表格响应式 */
@media (max-width: 768px) {
  .el-table {
    font-size: 0.75rem;
  }
  
  .el-table .el-table__cell {
    padding: 0.5rem 0.25rem;
  }
  
  .el-table__header th {
    padding: 0.5rem 0.25rem;
  }
}

/* 表单响应式 */
@media (max-width: 768px) {
  .el-form-item {
    margin-bottom: 1rem;
  }
  
  .el-form-item__label {
    line-height: 1.5;
    margin-bottom: 0.25rem;
  }
  
  .el-form--label-top .el-form-item__label {
    text-align: left;
    float: none;
    display: block;
    width: 100%;
  }
}

/* 按钮组响应式 */
.button-group {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

@media (max-width: 480px) {
  .button-group {
    flex-direction: column;
  }
  
  .button-group .el-button {
    width: 100%;
    margin: 0;
  }
}

/* 卡片响应式 */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

@media (max-width: 768px) {
  .card-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
}

/* 统计卡片响应式 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }
}

/* 图表响应式 */
.chart-container {
  width: 100%;
  height: 400px;
  min-height: 300px;
}

@media (max-width: 768px) {
  .chart-container {
    height: 300px;
    min-height: 250px;
  }
}

@media (max-width: 480px) {
  .chart-container {
    height: 250px;
    min-height: 200px;
  }
}

/* 文字响应式 */
.responsive-text {
  font-size: 1rem;
  line-height: 1.5;
}

.responsive-title {
  font-size: 1.5rem;
  line-height: 1.3;
  margin-bottom: 1rem;
}

@media (max-width: 768px) {
  .responsive-title {
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .responsive-title {
    font-size: 1.1rem;
  }
}

/* 间距响应式 */
.responsive-padding {
  padding: 1rem;
}

@media (max-width: 768px) {
  .responsive-padding {
    padding: 0.75rem;
  }
}

@media (max-width: 480px) {
  .responsive-padding {
    padding: 0.5rem;
  }
}

/* 隐藏/显示响应式 */
.hide-mobile {
  display: block;
}

.show-mobile {
  display: none;
}

@media (max-width: 768px) {
  .hide-mobile {
    display: none;
  }
  
  .show-mobile {
    display: block;
  }
}
