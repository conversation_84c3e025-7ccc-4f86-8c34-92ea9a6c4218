<template>
  <div class="cat-create">
    <el-page-header @back="$router.go(-1)" content="新增猫咪" />
    
    <div class="create-container">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>猫咪信息</span>
            <el-steps :active="currentStep" simple>
              <el-step title="基本信息" />
              <el-step title="血统信息" />
              <el-step title="健康信息" />
              <el-step title="完成" />
            </el-steps>
          </div>
        </template>
        
        <CatForm
          :is-edit="false"
          @submit="handleSubmit"
          @cancel="handleCancel"
        />
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { catApi } from '@/api';
import CatForm from '@/components/CatForm.vue';
import type { Cat } from '@/types';

const router = useRouter();
const currentStep = ref(0);

async function handleSubmit(catData: Partial<Cat>) {
  try {
    const result = await catApi.create(catData);
    ElMessage.success('猫咪创建成功');
    
    // 询问是否查看详情
    const action = await ElMessageBox.confirm(
      '猫咪创建成功！是否查看详情？',
      '创建成功',
      {
        confirmButtonText: '查看详情',
        cancelButtonText: '返回列表',
        type: 'success'
      }
    ).catch(() => 'cancel');
    
    if (action === 'confirm') {
      router.push(`/cats/${result.id}`);
    } else {
      router.push('/cats');
    }
  } catch (error: any) {
    ElMessage.error(error.message || '创建失败');
  }
}

function handleCancel() {
  router.push('/cats');
}
</script>

<style scoped>
.cat-create {
  padding: 20px;
}

.create-container {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
