<template>
  <div class="api-test-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>🔧 API连接测试</span>
          <el-button type="primary" @click="runAllTests">运行所有测试</el-button>
        </div>
      </template>
      
      <el-space direction="vertical" style="width: 100%" size="large">
        <!-- 基础连接测试 -->
        <el-card>
          <template #header>
            <span>基础连接测试</span>
          </template>
          
          <el-space wrap>
            <el-button @click="testHealth" :loading="loading.health">
              <el-icon><Connection /></el-icon>
              健康检查
            </el-button>
            
            <el-button @click="testHello" :loading="loading.hello">
              <el-icon><ChatDotRound /></el-icon>
              Hello接口
            </el-button>
          </el-space>
          
          <div v-if="results.health" class="test-result">
            <h4>健康检查结果:</h4>
            <pre>{{ JSON.stringify(results.health, null, 2) }}</pre>
          </div>
          
          <div v-if="results.hello" class="test-result">
            <h4>Hello接口结果:</h4>
            <pre>{{ JSON.stringify(results.hello, null, 2) }}</pre>
          </div>
        </el-card>
        
        <!-- 猫咪API测试 -->
        <el-card>
          <template #header>
            <span>猫咪API测试</span>
          </template>
          
          <el-space wrap>
            <el-button @click="testCatList" :loading="loading.catList">
              <el-icon><List /></el-icon>
              获取猫咪列表
            </el-button>
            
            <el-button @click="testCatStats" :loading="loading.catStats">
              <el-icon><DataAnalysis /></el-icon>
              获取统计信息
            </el-button>
          </el-space>
          
          <div v-if="results.catList" class="test-result">
            <h4>猫咪列表结果:</h4>
            <pre>{{ JSON.stringify(results.catList, null, 2) }}</pre>
          </div>
          
          <div v-if="results.catStats" class="test-result">
            <h4>统计信息结果:</h4>
            <pre>{{ JSON.stringify(results.catStats, null, 2) }}</pre>
          </div>
        </el-card>
        
        <!-- 测试状态 -->
        <el-card>
          <template #header>
            <span>测试状态</span>
          </template>
          
          <el-descriptions :column="2" border>
            <el-descriptions-item label="后端服务">
              <el-tag :type="backendStatus === 'online' ? 'success' : 'danger'">
                {{ backendStatus === 'online' ? '在线' : '离线' }}
              </el-tag>
            </el-descriptions-item>
            
            <el-descriptions-item label="API版本">
              <el-tag type="info">v1.0.0</el-tag>
            </el-descriptions-item>
            
            <el-descriptions-item label="最后测试时间">
              {{ lastTestTime || '未测试' }}
            </el-descriptions-item>
            
            <el-descriptions-item label="测试次数">
              {{ testCount }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-space>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { testApi, catApi } from '@/api'

const loading = reactive({
  health: false,
  hello: false,
  catList: false,
  catStats: false
})

const results = reactive({
  health: null as any,
  hello: null as any,
  catList: null as any,
  catStats: null as any
})

const backendStatus = ref('unknown')
const lastTestTime = ref('')
const testCount = ref(0)

const updateTestInfo = () => {
  lastTestTime.value = new Date().toLocaleString()
  testCount.value++
}

const testHealth = async () => {
  loading.health = true
  try {
    const response = await testApi.health()
    results.health = response.data
    backendStatus.value = 'online'
    ElMessage.success('健康检查通过')
    updateTestInfo()
  } catch (error: any) {
    results.health = { error: error.message }
    backendStatus.value = 'offline'
    ElMessage.error('健康检查失败: ' + error.message)
  } finally {
    loading.health = false
  }
}

const testHello = async () => {
  loading.hello = true
  try {
    const response = await testApi.hello()
    results.hello = response.data
    ElMessage.success('Hello接口调用成功')
    updateTestInfo()
  } catch (error: any) {
    results.hello = { error: error.message }
    ElMessage.error('Hello接口调用失败: ' + error.message)
  } finally {
    loading.hello = false
  }
}

const testCatList = async () => {
  loading.catList = true
  try {
    const response = await catApi.getAll()
    results.catList = response.data
    ElMessage.success('获取猫咪列表成功')
    updateTestInfo()
  } catch (error: any) {
    results.catList = { error: error.message }
    ElMessage.error('获取猫咪列表失败: ' + error.message)
  } finally {
    loading.catList = false
  }
}

const testCatStats = async () => {
  loading.catStats = true
  try {
    const response = await catApi.getStatistics()
    results.catStats = response.data
    ElMessage.success('获取统计信息成功')
    updateTestInfo()
  } catch (error: any) {
    results.catStats = { error: error.message }
    ElMessage.error('获取统计信息失败: ' + error.message)
  } finally {
    loading.catStats = false
  }
}

const runAllTests = async () => {
  ElMessage.info('开始运行所有测试...')
  await testHealth()
  await testHello()
  await testCatList()
  await testCatStats()
  ElMessage.success('所有测试完成')
}
</script>

<style scoped>
.api-test-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-result {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.test-result h4 {
  margin: 0 0 10px 0;
  color: #409eff;
}

.test-result pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 300px;
  overflow-y: auto;
  font-size: 12px;
  line-height: 1.4;
}
</style>

