package com.cattery.service.ai;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

/**
 * 图像处理服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ImageProcessingService {

    /**
     * 预处理图像
     */
    public byte[] preprocessImage(byte[] imageData) throws IOException {
        try {
            // 读取图像
            BufferedImage originalImage = ImageIO.read(new ByteArrayInputStream(imageData));
            if (originalImage == null) {
                throw new IOException("无法读取图像数据");
            }

            // 调整图像大小和质量
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            
            Thumbnails.of(originalImage)
                .size(800, 600) // 标准化尺寸
                .outputQuality(0.8) // 压缩质量
                .outputFormat("jpg")
                .toOutputStream(outputStream);

            return outputStream.toByteArray();
            
        } catch (IOException e) {
            log.error("图像预处理失败", e);
            throw e;
        }
    }

    /**
     * 生成缩略图
     */
    public byte[] generateThumbnail(byte[] imageData, int width, int height) throws IOException {
        try {
            BufferedImage originalImage = ImageIO.read(new ByteArrayInputStream(imageData));
            if (originalImage == null) {
                throw new IOException("无法读取图像数据");
            }

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            
            Thumbnails.of(originalImage)
                .size(width, height)
                .outputQuality(0.9)
                .outputFormat("jpg")
                .toOutputStream(outputStream);

            return outputStream.toByteArray();
            
        } catch (IOException e) {
            log.error("缩略图生成失败", e);
            throw e;
        }
    }

    /**
     * 验证图像格式
     */
    public boolean isValidImageFormat(byte[] imageData) {
        try {
            BufferedImage image = ImageIO.read(new ByteArrayInputStream(imageData));
            return image != null;
        } catch (IOException e) {
            return false;
        }
    }

    /**
     * 获取图像信息
     */
    public ImageInfo getImageInfo(byte[] imageData) throws IOException {
        BufferedImage image = ImageIO.read(new ByteArrayInputStream(imageData));
        if (image == null) {
            throw new IOException("无法读取图像数据");
        }

        return new ImageInfo(
            image.getWidth(),
            image.getHeight(),
            imageData.length
        );
    }

    /**
     * 检查服务可用性
     */
    public boolean isAvailable() {
        return true; // 本地图像处理服务总是可用
    }

    /**
     * 图像信息类
     */
    public static class ImageInfo {
        private final int width;
        private final int height;
        private final long size;

        public ImageInfo(int width, int height, long size) {
            this.width = width;
            this.height = height;
            this.size = size;
        }

        public int getWidth() { return width; }
        public int getHeight() { return height; }
        public long getSize() { return size; }
    }
}
