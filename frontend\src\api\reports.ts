import request from '@/utils/request'
import type { 
  ReportData,
  ReportHistoryItem,
  PaginationResponse
} from '@/types/reports'

/**
 * 报表相关API
 */
export const reportApi = {
  /**
   * 生成猫咪统计报表
   */
  generateCatStatistics(startDate: string, endDate: string): Promise<ReportData> {
    return request({
      url: '/api/reports/cat-statistics',
      method: 'post',
      data: { startDate, endDate }
    })
  },

  /**
   * 生成财务报表
   */
  generateFinancialReport(startDate: string, endDate: string): Promise<ReportData> {
    return request({
      url: '/api/reports/financial',
      method: 'post',
      data: { startDate, endDate }
    })
  },

  /**
   * 生成健康报表
   */
  generateHealthReport(startDate: string, endDate: string): Promise<ReportData> {
    return request({
      url: '/api/reports/health',
      method: 'post',
      data: { startDate, endDate }
    })
  },

  /**
   * 生成繁育报表
   */
  generateBreedingReport(startDate: string, endDate: string): Promise<ReportData> {
    return request({
      url: '/api/reports/breeding',
      method: 'post',
      data: { startDate, endDate }
    })
  },

  /**
   * 生成客户报表
   */
  generateCustomerReport(startDate: string, endDate: string): Promise<ReportData> {
    return request({
      url: '/api/reports/customer',
      method: 'post',
      data: { startDate, endDate }
    })
  },

  /**
   * 生成综合仪表盘报表
   */
  generateDashboardReport(): Promise<ReportData> {
    return request({
      url: '/api/reports/dashboard',
      method: 'post'
    })
  },

  /**
   * 导出报表
   */
  exportReport(
    reportType: string, 
    startDate: string, 
    endDate: string, 
    format: 'pdf' | 'excel' | 'csv'
  ): Promise<Blob> {
    return request({
      url: '/api/reports/export',
      method: 'post',
      data: { reportType, startDate, endDate, format },
      responseType: 'blob'
    })
  },

  /**
   * 获取报表历史
   */
  getReportHistory(params: {
    page: number
    size: number
    reportType?: string
  }): Promise<PaginationResponse<ReportHistoryItem>> {
    return request({
      url: '/api/reports/history',
      method: 'get',
      params
    })
  },

  /**
   * 下载历史报表
   */
  downloadHistoryReport(reportId: number): Promise<Blob> {
    return request({
      url: `/api/reports/history/${reportId}/download`,
      method: 'get',
      responseType: 'blob'
    })
  },

  /**
   * 删除报表
   */
  deleteReport(reportId: number): Promise<void> {
    return request({
      url: `/api/reports/${reportId}`,
      method: 'delete'
    })
  },

  /**
   * 获取报表模板
   */
  getReportTemplates(): Promise<any[]> {
    return request({
      url: '/api/reports/templates',
      method: 'get'
    })
  },

  /**
   * 保存报表模板
   */
  saveReportTemplate(template: any): Promise<void> {
    return request({
      url: '/api/reports/templates',
      method: 'post',
      data: template
    })
  }
}
