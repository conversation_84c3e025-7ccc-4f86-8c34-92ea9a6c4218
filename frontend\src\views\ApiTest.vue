<template>
  <div class="api-test-container">
    <div class="header">
      <h1>API 测试中心</h1>
      <div class="status-info">
        <el-tag :type="apiStatus.useMockData ? 'warning' : 'success'" size="large">
          {{ apiStatus.mode }}
        </el-tag>
        <el-button 
          @click="switchApiMode" 
          :type="apiStatus.useMockData ? 'primary' : 'warning'"
          size="small"
        >
          切换到{{ apiStatus.useMockData ? '真实API' : '虚拟数据' }}模式
        </el-button>
        <el-button @click="checkConnection" :loading="checking" size="small">
          检查连接
        </el-button>
      </div>
    </div>

    <el-row :gutter="20">
      <!-- 测试控制面板 -->
      <el-col :span="8">
        <el-card title="测试控制面板">
          <template #header>
            <span>测试控制面板</span>
          </template>
          
          <div class="test-controls">
            <el-button 
              @click="runAllTests" 
              type="primary" 
              :loading="runningAll"
              style="width: 100%; margin-bottom: 10px;"
            >
              运行所有测试
            </el-button>
            
            <el-button 
              @click="clearLogs" 
              type="info" 
              style="width: 100%; margin-bottom: 20px;"
            >
              清空日志
            </el-button>

            <div class="test-list">
              <h4>可用测试</h4>
              <div 
                v-for="test in availableTests" 
                :key="test.key"
                class="test-item"
              >
                <el-button 
                  @click="runSingleTest(test)" 
                  :loading="test.loading"
                  :type="getTestButtonType(test)"
                  size="small"
                  style="width: 100%; margin-bottom: 5px;"
                >
                  {{ test.name }}
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 测试结果 -->
      <el-col :span="16">
        <el-card title="测试结果">
          <template #header>
            <span>测试结果</span>
            <el-tag 
              v-if="testResults.length > 0" 
              :type="allTestsPassed ? 'success' : 'danger'"
              style="margin-left: 10px;"
            >
              {{ allTestsPassed ? '全部通过' : '存在失败' }}
            </el-tag>
          </template>

          <div class="test-results">
            <div v-if="testResults.length === 0" class="no-results">
              <el-empty description="暂无测试结果，请运行测试" />
            </div>
            
            <div v-else class="results-list">
              <div 
                v-for="(result, index) in testResults" 
                :key="index"
                class="result-item"
                :class="{ 'success': result.success, 'error': !result.success }"
              >
                <div class="result-header">
                  <el-icon :color="result.success ? '#67C23A' : '#F56C6C'">
                    <Check v-if="result.success" />
                    <Close v-else />
                  </el-icon>
                  <span class="test-name">{{ result.testName }}</span>
                  <span class="test-time">{{ result.duration }}ms</span>
                </div>
                
                <div class="result-content">
                  <div class="response-data">
                    <pre>{{ JSON.stringify(result.data, null, 2) }}</pre>
                  </div>
                  
                  <div v-if="result.error" class="error-info">
                    <el-alert 
                      :title="result.error" 
                      type="error" 
                      :closable="false"
                      show-icon
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 实时日志 -->
    <el-card title="实时日志" style="margin-top: 20px;">
      <div class="logs-container">
        <div v-if="logs.length === 0" class="no-logs">
          <el-empty description="暂无日志" />
        </div>
        
        <div v-else class="logs-list">
          <div 
            v-for="(log, index) in logs" 
            :key="index"
            class="log-item"
            :class="log.level"
          >
            <span class="log-time">{{ log.timestamp }}</span>
            <span class="log-level">{{ log.level.toUpperCase() }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Check, Close } from '@element-plus/icons-vue'
import { smartApi } from '@/utils/smartApi'

// 响应式数据
const checking = ref(false)
const runningAll = ref(false)
const testResults = ref<any[]>([])
const logs = ref<any[]>([])

// API状态
const apiStatus = reactive({
  useMockData: false,
  mode: '真实API模式'
})

// 可用测试列表
const availableTests = ref([
  { key: 'health', name: '健康检查', loading: false, lastResult: null },
  { key: 'connection', name: '连接测试', loading: false, lastResult: null },
  { key: 'authStatus', name: '认证状态', loading: false, lastResult: null },
  { key: 'getCats', name: '获取猫咪列表', loading: false, lastResult: null },
  { key: 'getCustomers', name: '获取客户列表', loading: false, lastResult: null },
  { key: 'getStats', name: '获取统计数据', loading: false, lastResult: null }
])

// 计算属性
const allTestsPassed = computed(() => {
  return testResults.value.length > 0 && testResults.value.every(r => r.success)
})

// 获取测试按钮类型
const getTestButtonType = (test: any) => {
  if (test.loading) return 'primary'
  if (test.lastResult === null) return 'default'
  return test.lastResult ? 'success' : 'danger'
}

// 添加日志
const addLog = (level: string, message: string) => {
  logs.value.unshift({
    timestamp: new Date().toLocaleTimeString(),
    level,
    message
  })
  
  // 限制日志数量
  if (logs.value.length > 100) {
    logs.value = logs.value.slice(0, 100)
  }
}

// 更新API状态
const updateApiStatus = () => {
  const status = smartApi.getStatus()
  apiStatus.useMockData = status.useMockData
  apiStatus.mode = status.mode
}

// 切换API模式
const switchApiMode = () => {
  const newMode = !apiStatus.useMockData
  smartApi.switchMode(newMode)
  updateApiStatus()
  addLog('info', `已切换到${newMode ? '虚拟数据' : '真实API'}模式`)
  ElMessage.success(`已切换到${newMode ? '虚拟数据' : '真实API'}模式`)
}

// 检查连接
const checkConnection = async () => {
  checking.value = true
  addLog('info', '开始检查后端连接...')
  
  try {
    const connected = await smartApi.forceCheckConnection()
    updateApiStatus()
    
    if (connected) {
      addLog('success', '后端连接正常')
      ElMessage.success('后端连接正常')
    } else {
      addLog('warning', '后端连接失败，已切换到虚拟数据模式')
      ElMessage.warning('后端连接失败，已切换到虚拟数据模式')
    }
  } catch (error) {
    addLog('error', `连接检查失败: ${error}`)
    ElMessage.error('连接检查失败')
  } finally {
    checking.value = false
  }
}

// 运行单个测试
const runSingleTest = async (test: any) => {
  test.loading = true
  const startTime = Date.now()
  
  addLog('info', `开始运行测试: ${test.name}`)
  
  try {
    let result
    
    switch (test.key) {
      case 'health':
        result = await smartApi.testHealth()
        break
      case 'connection':
        result = await smartApi.testConnection()
        break
      case 'authStatus':
        result = await smartApi.testAuthStatus()
        break
      case 'getCats':
        result = await smartApi.getCats({ page: 1, size: 5 })
        break
      case 'getCustomers':
        result = await smartApi.getCustomers({ page: 1, size: 5 })
        break
      case 'getStats':
        result = await smartApi.getStats()
        break
      default:
        throw new Error('未知的测试类型')
    }
    
    const duration = Date.now() - startTime
    const success = result.success
    
    test.lastResult = success
    
    testResults.value.unshift({
      testName: test.name,
      success,
      data: result.data,
      duration,
      timestamp: new Date().toLocaleTimeString(),
      error: success ? null : result.message
    })
    
    addLog(success ? 'success' : 'error', 
      `测试 ${test.name} ${success ? '通过' : '失败'} (${duration}ms)`)
    
  } catch (error: any) {
    const duration = Date.now() - startTime
    test.lastResult = false
    
    testResults.value.unshift({
      testName: test.name,
      success: false,
      data: null,
      duration,
      timestamp: new Date().toLocaleTimeString(),
      error: error.message || '测试执行失败'
    })
    
    addLog('error', `测试 ${test.name} 失败: ${error.message}`)
  } finally {
    test.loading = false
  }
}

// 运行所有测试
const runAllTests = async () => {
  runningAll.value = true
  addLog('info', '开始运行所有测试...')
  
  for (const test of availableTests.value) {
    await runSingleTest(test)
    // 添加小延迟避免请求过于频繁
    await new Promise(resolve => setTimeout(resolve, 200))
  }
  
  runningAll.value = false
  addLog('info', '所有测试运行完成')
  ElMessage.success('所有测试运行完成')
}

// 清空日志
const clearLogs = () => {
  logs.value = []
  testResults.value = []
  availableTests.value.forEach(test => {
    test.lastResult = null
  })
  addLog('info', '日志已清空')
}

// 组件挂载时初始化
onMounted(() => {
  updateApiStatus()
  addLog('info', 'API测试中心已启动')
  addLog('info', `当前模式: ${apiStatus.mode}`)
})
</script>

<style scoped>
.api-test-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.test-controls {
  padding: 10px 0;
}

.test-list h4 {
  margin: 0 0 10px 0;
  color: #606266;
}

.test-item {
  margin-bottom: 5px;
}

.test-results {
  max-height: 600px;
  overflow-y: auto;
}

.no-results, .no-logs {
  text-align: center;
  padding: 40px 0;
}

.result-item {
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  margin-bottom: 10px;
  overflow: hidden;
}

.result-item.success {
  border-color: #67C23A;
}

.result-item.error {
  border-color: #F56C6C;
}

.result-header {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  background-color: #F5F7FA;
  border-bottom: 1px solid #EBEEF5;
}

.result-header .test-name {
  margin-left: 8px;
  font-weight: 500;
  flex: 1;
}

.result-header .test-time {
  color: #909399;
  font-size: 12px;
}

.result-content {
  padding: 15px;
}

.response-data pre {
  background-color: #F5F7FA;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
  margin: 0;
}

.error-info {
  margin-top: 10px;
}

.logs-container {
  max-height: 300px;
  overflow-y: auto;
}

.logs-list {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-item {
  padding: 5px 10px;
  border-bottom: 1px solid #EBEEF5;
  display: flex;
  align-items: center;
}

.log-item.info {
  color: #409EFF;
}

.log-item.success {
  color: #67C23A;
}

.log-item.warning {
  color: #E6A23C;
}

.log-item.error {
  color: #F56C6C;
}

.log-time {
  margin-right: 10px;
  color: #909399;
  min-width: 80px;
}

.log-level {
  margin-right: 10px;
  font-weight: bold;
  min-width: 60px;
}

.log-message {
  flex: 1;
}
</style>
