# ===================================================================
# MySQL数据库配置 - dengdeng库
# ===================================================================

# 数据库配置 - MySQL
spring.datasource.url=****************************************************************************************************************************************
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.username=root
spring.datasource.password=123456
spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect

# JPA/Hibernate配置
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.defer-datasource-initialization=true

# 初始化数据
spring.sql.init.mode=always
spring.sql.init.data-locations=classpath:data-mysql.sql

# 日志配置
logging.level.com.catshelter.managementsystem=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.hibernate.SQL=DEBUG

# 服务器配置
server.port=8080

# JWT配置
jwt.secret=mySecretKey123456789012345678901234567890
jwt.expiration=86400000

# CORS配置
cors.allowed-origins=http://localhost:5173,http://localhost:5174

# Swagger配置
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.enabled=true

# Actuator配置
management.endpoints.web.exposure.include=health,info
management.endpoint.health.show-details=always
