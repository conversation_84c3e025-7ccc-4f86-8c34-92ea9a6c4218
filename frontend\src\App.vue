<template>
  <div id="app">
    <!-- 登录页面不使用布局 -->
    <template v-if="$route.path === '/login'">
      <router-view />
    </template>

    <!-- 其他页面使用布局 -->
    <template v-else>
      <Layout>
        <router-view />
      </Layout>
    </template>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import Layout from '@/components/Layout.vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

onMounted(() => {
  // 初始化认证状态
  authStore.initializeAuth()
})
</script>

<style>
/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  background-color: #f5f7fa;
}

#app {
  height: 100vh;
}

/* Element Plus 样式覆盖 */
.el-menu-item.is-active {
  background-color: #ecf5ff !important;
  color: #409eff !important;
}

.el-sub-menu__title:hover {
  background-color: #f5f7fa !important;
}

.el-menu-item:hover {
  background-color: #f5f7fa !important;
}
</style>
