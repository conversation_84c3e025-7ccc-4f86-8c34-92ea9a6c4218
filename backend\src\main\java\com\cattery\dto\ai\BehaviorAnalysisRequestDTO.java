package com.cattery.dto.ai;

import lombok.Data;
import java.util.List;

/**
 * 行为分析请求DTO
 */
@Data
public class BehaviorAnalysisRequestDTO {
    
    /**
     * 观察到的行为变化
     */
    private List<String> behaviorChanges;
    
    /**
     * 观察时间段（天）
     */
    private Integer observationPeriodDays;
    
    /**
     * 行为频率变化
     */
    private String frequencyChange;
    
    /**
     * 社交行为变化
     */
    private String socialBehaviorChange;
    
    /**
     * 食物相关行为
     */
    private String eatingBehavior;
    
    /**
     * 睡眠行为
     */
    private String sleepingBehavior;
    
    /**
     * 玩耍行为
     */
    private String playBehavior;
    
    /**
     * 攻击性行为
     */
    private String aggressiveBehavior;
    
    /**
     * 焦虑相关行为
     */
    private String anxietyBehavior;
    
    /**
     * 环境互动行为
     */
    private String environmentalInteraction;
    
    /**
     * 其他异常行为
     */
    private String otherAbnormalBehaviors;
    
    /**
     * 触发因素
     */
    private List<String> triggers;
    
    /**
     * 分析类型 (GENERAL, STRESS, HEALTH_RELATED, BEHAVIORAL_ISSUE)
     */
    private String analysisType;

    /**
     * 行为观察数据
     */
    private List<String> behaviorObservations;

    /**
     * 环境因素
     */
    private List<String> environmentFactors;

    /**
     * 观察周期
     */
    private Integer observationPeriod;
}
