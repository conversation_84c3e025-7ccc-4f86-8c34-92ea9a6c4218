@echo off
echo ========================================
echo Fix NVM Symlink and PATH Issues
echo ========================================
echo.

echo Problem: NVM activated Node.js but commands not recognized
echo Solution: Fix symlink and PATH configuration
echo.

echo Step 1: Check current NVM status
echo.
nvm list
echo.

echo Step 2: Check if Node.js symlink exists
echo.
if exist "C:\Program Files\nodejs\node.exe" (
    echo ✅ Node.js symlink exists at C:\Program Files\nodejs\
    echo Version:
    "C:\Program Files\nodejs\node.exe" --version 2>nul
) else (
    echo ❌ Node.js symlink missing at C:\Program Files\nodejs\
    echo This is the problem!
)
echo.

echo Step 3: Recreate symlink (requires admin rights)
echo.
echo Attempting to recreate Node.js symlink...
echo This may require administrator privileges
echo.

REM Try to recreate the symlink
nvm use 20.11.0
echo.

echo Step 4: Manual symlink creation (if needed)
echo.
echo If automatic symlink creation failed, try these manual steps:
echo.
echo 1. Run Command Prompt as Administrator
echo 2. Run: nvm use 20.11.0
echo 3. Or manually create symlink:
echo    mklink /D "C:\Program Files\nodejs" "%USERPROFILE%\AppData\Roaming\nvm\v20.11.0"
echo.

echo Step 5: Check PATH environment variable
echo.
echo Current PATH contains:
echo %PATH% | findstr /i node
if %errorlevel% equ 0 (
    echo ✅ Node.js path found in PATH
) else (
    echo ❌ Node.js path NOT found in PATH
    echo.
    echo Adding Node.js to PATH for current session...
    set "PATH=%PATH%;C:\Program Files\nodejs"
    echo ✅ Node.js added to PATH for this session
)
echo.

echo Step 6: Test Node.js after fixes
echo.
echo Testing Node.js...
node --version 2>nul
if %errorlevel% equ 0 (
    echo ✅ Node.js is now working!
    node --version
) else (
    echo ❌ Node.js still not working
    echo.
    echo Try these solutions:
    echo 1. Restart Command Prompt as Administrator
    echo 2. Run: nvm use 20.11.0
    echo 3. Restart computer
    echo 4. Manually add C:\Program Files\nodejs to PATH
)
echo.

echo Testing npm...
npm --version 2>nul
if %errorlevel% equ 0 (
    echo ✅ npm is now working!
    npm --version
) else (
    echo ❌ npm still not working
)
echo.

echo ========================================
echo Solutions Summary
echo ========================================
echo.

echo If Node.js is still not working, try these in order:
echo.

echo Solution 1: Run as Administrator
echo 1. Right-click Command Prompt
echo 2. Select "Run as administrator"
echo 3. Run: nvm use 20.11.0
echo 4. Test: node --version
echo.

echo Solution 2: Manual PATH addition
echo 1. Win + R, type: sysdm.cpl
echo 2. Environment Variables
echo 3. Add to PATH: C:\Program Files\nodejs
echo 4. Restart Command Prompt
echo.

echo Solution 3: Restart computer
echo Sometimes a restart is needed for PATH changes
echo.

echo Solution 4: Reinstall with different settings
echo 1. Uninstall NVM
echo 2. Install to C:\nvm instead of user directory
echo 3. Set symlink to C:\nodejs instead of Program Files
echo.

pause
