import { vi, beforeAll, afterAll, beforeEach, afterEach } from 'vitest'
import { config } from '@vue/test-utils'
import { createPinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import ElementPlus from 'element-plus'

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn(() => ({
  disconnect: vi.fn(),
  observe: vi.fn(),
  unobserve: vi.fn(),
}))

// Mock ResizeObserver
global.ResizeObserver = vi.fn(() => ({
  disconnect: vi.fn(),
  observe: vi.fn(),
  unobserve: vi.fn(),
}))

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn(),
}

Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock
})

// Mock URL.createObjectURL
Object.defineProperty(URL, 'createObjectURL', {
  writable: true,
  value: vi.fn(() => 'mocked-url')
})

Object.defineProperty(URL, 'revokeObjectURL', {
  writable: true,
  value: vi.fn()
})

// Mock fetch
global.fetch = vi.fn()

// Mock XMLHttpRequest
global.XMLHttpRequest = vi.fn(() => ({
  open: vi.fn(),
  send: vi.fn(),
  setRequestHeader: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  abort: vi.fn(),
  readyState: 4,
  status: 200,
  statusText: 'OK',
  responseText: '{}',
  response: {},
}))

// Mock File and FileReader
global.File = vi.fn()
global.FileReader = vi.fn(() => ({
  readAsDataURL: vi.fn(),
  readAsText: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  result: null,
  error: null,
}))

// Mock Image
global.Image = vi.fn(() => ({
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  src: '',
  onload: null,
  onerror: null,
}))

// Mock Canvas
const mockCanvas = {
  getContext: vi.fn(() => ({
    fillRect: vi.fn(),
    clearRect: vi.fn(),
    getImageData: vi.fn(() => ({ data: new Array(4) })),
    putImageData: vi.fn(),
    createImageData: vi.fn(() => ({ data: new Array(4) })),
    setTransform: vi.fn(),
    drawImage: vi.fn(),
    save: vi.fn(),
    fillText: vi.fn(),
    restore: vi.fn(),
    beginPath: vi.fn(),
    moveTo: vi.fn(),
    lineTo: vi.fn(),
    closePath: vi.fn(),
    stroke: vi.fn(),
    translate: vi.fn(),
    scale: vi.fn(),
    rotate: vi.fn(),
    arc: vi.fn(),
    fill: vi.fn(),
    measureText: vi.fn(() => ({ width: 0 })),
    transform: vi.fn(),
    rect: vi.fn(),
    clip: vi.fn(),
  })),
  toDataURL: vi.fn(() => ''),
  width: 0,
  height: 0,
}

global.HTMLCanvasElement = vi.fn(() => mockCanvas)

// Mock console methods for cleaner test output
const originalConsoleError = console.error
const originalConsoleWarn = console.warn

beforeAll(() => {
  // 抑制某些预期的控制台输出
  console.error = vi.fn((message) => {
    // 只显示非预期的错误
    if (!message.includes('Warning') && !message.includes('[Vue warn]')) {
      originalConsoleError(message)
    }
  })
  
  console.warn = vi.fn((message) => {
    // 只显示重要的警告
    if (!message.includes('[Vue warn]') && !message.includes('deprecated')) {
      originalConsoleWarn(message)
    }
  })
})

afterAll(() => {
  // 恢复原始的console方法
  console.error = originalConsoleError
  console.warn = originalConsoleWarn
})

// 全局测试配置
config.global.plugins = [ElementPlus]

// 创建测试用的路由
const createTestRouter = () => {
  return createRouter({
    history: createWebHistory(),
    routes: [
      { path: '/', component: { template: '<div>Home</div>' } },
      { path: '/login', component: { template: '<div>Login</div>' } },
      { path: '/dashboard', component: { template: '<div>Dashboard</div>' } },
      { path: '/cats', component: { template: '<div>Cats</div>' }, meta: { requiresAuth: true } },
      { path: '/cats/:id', component: { template: '<div>Cat Detail</div>' }, meta: { requiresAuth: true } },
      { path: '/health', component: { template: '<div>Health</div>' }, meta: { requiresAuth: true } },
      { path: '/breeding', component: { template: '<div>Breeding</div>' }, meta: { requiresAuth: true } },
      { path: '/customers', component: { template: '<div>Customers</div>' }, meta: { requiresAuth: true } },
      { path: '/inventory', component: { template: '<div>Inventory</div>' }, meta: { requiresAuth: true } },
      { path: '/finance', component: { template: '<div>Finance</div>' }, meta: { requiresAuth: true } },
      { path: '/admin', component: { template: '<div>Admin</div>' }, meta: { requiresAuth: true, roles: ['ADMIN'] } },
      { path: '/unauthorized', component: { template: '<div>Unauthorized</div>' } },
      { path: '/:pathMatch(.*)*', component: { template: '<div>Not Found</div>' } }
    ]
  })
}

// 创建测试用的Pinia实例
const createTestPinia = () => {
  return createPinia()
}

// 导出测试工具
export { createTestRouter, createTestPinia }

// 全局测试钩子
beforeEach(() => {
  // 清理所有mock
  vi.clearAllMocks()
  
  // 重置localStorage和sessionStorage
  localStorageMock.getItem.mockReturnValue(null)
  localStorageMock.setItem.mockClear()
  localStorageMock.removeItem.mockClear()
  localStorageMock.clear.mockClear()
  
  sessionStorageMock.getItem.mockReturnValue(null)
  sessionStorageMock.setItem.mockClear()
  sessionStorageMock.removeItem.mockClear()
  sessionStorageMock.clear.mockClear()
  
  // 重置fetch mock
  vi.mocked(fetch).mockClear()
})

afterEach(() => {
  // 清理DOM
  document.body.innerHTML = ''
  
  // 清理定时器
  vi.clearAllTimers()
})

// 测试工具函数
export const waitFor = (callback: () => boolean, timeout = 5000): Promise<void> => {
  return new Promise((resolve, reject) => {
    const startTime = Date.now()
    
    const check = () => {
      if (callback()) {
        resolve()
      } else if (Date.now() - startTime > timeout) {
        reject(new Error(`Timeout waiting for condition after ${timeout}ms`))
      } else {
        setTimeout(check, 100)
      }
    }
    
    check()
  })
}

export const sleep = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms))
}

// Mock API响应工具
export const mockApiResponse = (data: any, status = 200, delay = 0) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      if (status >= 200 && status < 300) {
        resolve({
          data,
          status,
          statusText: 'OK',
          headers: {},
          config: {}
        })
      } else {
        reject({
          response: {
            data: { message: 'API Error' },
            status,
            statusText: 'Error',
            headers: {},
            config: {}
          }
        })
      }
    }, delay)
  })
}

// 测试数据工厂
export const createMockUser = (overrides = {}) => ({
  id: 1,
  username: '<EMAIL>',
  name: '测试用户',
  roles: ['USER'],
  permissions: ['cat:read'],
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides
})

export const createMockCat = (overrides = {}) => ({
  id: 1,
  name: '测试猫咪',
  breedId: 1,
  breedName: '英国短毛猫',
  gender: 'FEMALE',
  dateOfBirth: '2023-01-01',
  color: '银渐层',
  status: 'PENDING_ADOPTION',
  weight: 3.5,
  photos: [],
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides
})

export const createMockHealthRecord = (overrides = {}) => ({
  id: 1,
  catId: 1,
  recordType: 'CHECKUP',
  recordDate: new Date().toISOString().split('T')[0],
  notes: '健康状况良好',
  veterinarian: '张医生',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides
})

// 断言工具
export const expectToBeVisible = (element: any) => {
  expect(element.exists()).toBe(true)
  expect(element.isVisible()).toBe(true)
}

export const expectToHaveText = (element: any, text: string) => {
  expect(element.exists()).toBe(true)
  expect(element.text()).toContain(text)
}

export const expectToBeDisabled = (element: any) => {
  expect(element.exists()).toBe(true)
  expect(element.attributes('disabled')).toBeDefined()
}

// 环境检查
if (typeof window !== 'undefined') {
  // 浏览器环境特定的设置
  window.scrollTo = vi.fn()
  window.alert = vi.fn()
  window.confirm = vi.fn(() => true)
  window.prompt = vi.fn(() => '')
}

console.log('Integration test setup completed')
