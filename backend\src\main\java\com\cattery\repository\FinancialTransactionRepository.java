package com.cattery.repository;

import com.cattery.entity.FinancialTransaction;
import com.cattery.entity.FinancialCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * 财务交易仓库接口
 */
@Repository
public interface FinancialTransactionRepository extends JpaRepository<FinancialTransaction, Long>, JpaSpecificationExecutor<FinancialTransaction> {

    /**
     * 根据交易类型和日期范围查找交易
     */
    List<FinancialTransaction> findByTransactionTypeAndTransactionDateBetween(
        FinancialTransaction.TransactionType transactionType, 
        LocalDateTime startDate, 
        LocalDateTime endDate);

    /**
     * 根据交易类型查找交易
     */
    List<FinancialTransaction> findByTransactionType(FinancialTransaction.TransactionType transactionType);

    /**
     * 根据日期范围查找交易
     */
    List<FinancialTransaction> findByTransactionDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 根据分类查找交易
     */
    List<FinancialTransaction> findByCategory(FinancialCategory category);

    /**
     * 根据支付方式查找交易
     */
    List<FinancialTransaction> findByPaymentMethod(FinancialTransaction.PaymentMethod paymentMethod);

    /**
     * 计算指定时间范围内的总收入
     */
    @Query("SELECT COALESCE(SUM(ft.amount), 0) FROM FinancialTransaction ft " +
           "WHERE ft.transactionType = 'INCOME' AND ft.transactionDate BETWEEN :startDate AND :endDate")
    BigDecimal calculateTotalIncomeByDateRange(@Param("startDate") LocalDateTime startDate, 
                                              @Param("endDate") LocalDateTime endDate);

    /**
     * 计算指定时间范围内的总支出
     */
    @Query("SELECT COALESCE(SUM(ft.amount), 0) FROM FinancialTransaction ft " +
           "WHERE ft.transactionType = 'EXPENSE' AND ft.transactionDate BETWEEN :startDate AND :endDate")
    BigDecimal calculateTotalExpenseByDateRange(@Param("startDate") LocalDateTime startDate, 
                                               @Param("endDate") LocalDateTime endDate);

    /**
     * 按分类统计收入
     */
    @Query("SELECT ft.category, SUM(ft.amount) FROM FinancialTransaction ft " +
           "WHERE ft.transactionType = 'INCOME' AND ft.transactionDate BETWEEN :startDate AND :endDate " +
           "GROUP BY ft.category")
    List<Object[]> getIncomeByCategory(@Param("startDate") LocalDateTime startDate, 
                                      @Param("endDate") LocalDateTime endDate);

    /**
     * 按分类统计支出
     */
    @Query("SELECT ft.category, SUM(ft.amount) FROM FinancialTransaction ft " +
           "WHERE ft.transactionType = 'EXPENSE' AND ft.transactionDate BETWEEN :startDate AND :endDate " +
           "GROUP BY ft.category")
    List<Object[]> getExpenseByCategory(@Param("startDate") LocalDateTime startDate, 
                                       @Param("endDate") LocalDateTime endDate);

    /**
     * 按支付方式统计
     */
    @Query("SELECT ft.paymentMethod, COUNT(ft) FROM FinancialTransaction ft " +
           "WHERE ft.transactionDate BETWEEN :startDate AND :endDate " +
           "GROUP BY ft.paymentMethod")
    List<Object[]> getTransactionCountByPaymentMethod(@Param("startDate") LocalDateTime startDate,
                                                     @Param("endDate") LocalDateTime endDate);

    /**
     * 分页查询交易类型
     */
    Page<FinancialTransaction> findByTransactionType(FinancialTransaction.TransactionType type, Pageable pageable);

    /**
     * 分页查询日期范围
     */
    Page<FinancialTransaction> findByTransactionDateBetween(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    /**
     * 分页查询分类
     */
    Page<FinancialTransaction> findByCategory(String category, Pageable pageable);

    /**
     * 搜索交易（描述或备注）
     */
    @Query("SELECT ft FROM FinancialTransaction ft WHERE " +
           "LOWER(ft.description) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(ft.notes) LIKE LOWER(CONCAT('%', :keyword, '%'))")
    Page<FinancialTransaction> findByDescriptionContainingIgnoreCaseOrNotesContainingIgnoreCase(
        @Param("keyword") String description, @Param("keyword") String notes, Pageable pageable);

    /**
     * 按类型和日期范围求和
     */
    @Query("SELECT COALESCE(SUM(ft.amount), 0) FROM FinancialTransaction ft " +
           "WHERE ft.transactionType = :type AND ft.transactionDate BETWEEN :startDate AND :endDate")
    BigDecimal sumByTransactionTypeAndDateRange(@Param("type") FinancialTransaction.TransactionType type,
                                               @Param("startDate") LocalDateTime startDate,
                                               @Param("endDate") LocalDateTime endDate);

    /**
     * 按分类、类型和日期范围统计
     */
    @Query("SELECT ft.category, SUM(ft.amount) FROM FinancialTransaction ft " +
           "WHERE ft.transactionType = :type AND ft.transactionDate BETWEEN :startDate AND :endDate " +
           "GROUP BY ft.category")
    List<Object[]> sumByCategoryAndTypeAndDateRange(@Param("type") FinancialTransaction.TransactionType type,
                                                   @Param("startDate") LocalDateTime startDate,
                                                   @Param("endDate") LocalDateTime endDate);

    /**
     * 获取月度统计
     */
    @Query("SELECT MONTH(ft.transactionDate), ft.transactionType, SUM(ft.amount) " +
           "FROM FinancialTransaction ft WHERE YEAR(ft.transactionDate) = :year " +
           "GROUP BY MONTH(ft.transactionDate), ft.transactionType " +
           "ORDER BY MONTH(ft.transactionDate)")
    List<Object[]> getMonthlyStats(@Param("year") int year);

    /**
     * 获取支付方式统计
     */
    @Query("SELECT ft.paymentMethod, SUM(ft.amount), COUNT(ft) FROM FinancialTransaction ft " +
           "WHERE ft.transactionDate BETWEEN :startDate AND :endDate " +
           "GROUP BY ft.paymentMethod")
    List<Object[]> getPaymentMethodStats(@Param("startDate") LocalDateTime startDate,
                                        @Param("endDate") LocalDateTime endDate);

    /**
     * 获取最近的交易
     */
    @Query("SELECT ft FROM FinancialTransaction ft ORDER BY ft.transactionDate DESC")
    List<FinancialTransaction> findRecentTransactions(Pageable pageable);

    /**
     * 获取最近的交易（简化版）
     */
    default List<FinancialTransaction> findRecentTransactions(int limit) {
        return findRecentTransactions(Pageable.ofSize(limit));
    }
}
