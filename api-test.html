<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API连通性深度测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f7fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            background: white;
            margin: 20px 0;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section-header {
            background: #667eea;
            color: white;
            padding: 15px 20px;
            font-weight: bold;
        }
        .section-content {
            padding: 20px;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .test-item:last-child {
            border-bottom: none;
        }
        .test-name {
            font-weight: 500;
        }
        .test-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-pending {
            background: #ffc107;
            color: #856404;
        }
        .status-success {
            background: #28a745;
            color: white;
        }
        .status-error {
            background: #dc3545;
            color: white;
        }
        .status-testing {
            background: #17a2b8;
            color: white;
        }
        .test-details {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            font-size: 12px;
            display: none;
        }
        .test-details.show {
            display: block;
        }
        .start-test-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        }
        .start-test-btn:hover {
            background: #5a6fd8;
        }
        .start-test-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .summary-item {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .summary-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 猫舍管理系统 - API连通性深度测试</h1>
            <p>全面测试后端API的连通性、认证、数据访问等功能</p>
            <button class="start-test-btn" onclick="startFullTest()" id="startBtn">开始全面测试</button>
            
            <div class="progress-bar">
                <div class="progress-fill" id="progressBar"></div>
            </div>
            
            <div class="summary">
                <div class="summary-item">
                    <div class="summary-number" id="totalTests">0</div>
                    <div>总测试数</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number" id="passedTests" style="color: #28a745;">0</div>
                    <div>通过测试</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number" id="failedTests" style="color: #dc3545;">0</div>
                    <div>失败测试</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number" id="successRate" style="color: #667eea;">0%</div>
                    <div>成功率</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="section-header">🌐 基础连通性测试</div>
            <div class="section-content" id="connectivity-tests"></div>
        </div>

        <div class="test-section">
            <div class="section-header">🔐 认证系统测试</div>
            <div class="section-content" id="auth-tests"></div>
        </div>

        <div class="test-section">
            <div class="section-header">🐱 猫咪管理API测试</div>
            <div class="section-content" id="cat-tests"></div>
        </div>

        <div class="test-section">
            <div class="section-header">👥 客户管理API测试</div>
            <div class="section-content" id="customer-tests"></div>
        </div>

        <div class="test-section">
            <div class="section-header">💰 财务管理API测试</div>
            <div class="section-content" id="finance-tests"></div>
        </div>

        <div class="test-section">
            <div class="section-header">🏥 健康记录API测试</div>
            <div class="section-content" id="health-tests"></div>
        </div>

        <div class="test-section">
            <div class="section-header">📊 系统管理API测试</div>
            <div class="section-content" id="system-tests"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080';
        let authToken = null;
        let testResults = {
            total: 0,
            passed: 0,
            failed: 0
        };

        const testSuites = {
            connectivity: [
                { name: '服务器基础连通性', endpoint: '/', method: 'GET', expectAuth: true },
                { name: 'Swagger UI访问', endpoint: '/swagger-ui.html', method: 'GET', expectAuth: false },
                { name: 'API文档配置', endpoint: '/api-docs/swagger-config', method: 'GET', expectAuth: true },
                { name: 'H2数据库控制台', endpoint: '/h2-console', method: 'GET', expectAuth: false }
            ],
            auth: [
                { name: '管理员登录测试', endpoint: '/api/auth/login', method: 'POST', data: {username: 'admin', password: 'admin123'} },
                { name: '普通用户登录测试', endpoint: '/api/auth/login', method: 'POST', data: {username: 'user', password: 'user123'} },
                { name: '错误密码登录测试', endpoint: '/api/auth/login', method: 'POST', data: {username: 'admin', password: 'wrong'}, expectFail: true },
                { name: '用户信息获取', endpoint: '/api/auth/me', method: 'GET', requireAuth: true }
            ],
            cats: [
                { name: '获取猫咪列表', endpoint: '/api/cats', method: 'GET', requireAuth: true },
                { name: '获取猫咪详情', endpoint: '/api/cats/1', method: 'GET', requireAuth: true },
                { name: '搜索猫咪', endpoint: '/api/cats/search?keyword=小', method: 'GET', requireAuth: true }
            ],
            customers: [
                { name: '获取客户列表', endpoint: '/api/customers', method: 'GET', requireAuth: true },
                { name: '搜索客户', endpoint: '/api/customers/search?keyword=test', method: 'GET', requireAuth: true }
            ],
            finance: [
                { name: '获取财务记录', endpoint: '/api/financial/transactions', method: 'GET', requireAuth: true },
                { name: '财务统计', endpoint: '/api/financial/stats', method: 'GET', requireAuth: true }
            ],
            health: [
                { name: '获取健康记录', endpoint: '/api/health', method: 'GET', requireAuth: true },
                { name: '按猫咪查询健康记录', endpoint: '/api/health?catId=1', method: 'GET', requireAuth: true }
            ],
            system: [
                { name: '测试端点', endpoint: '/api/test/hello', method: 'GET', requireAuth: false },
                { name: '繁育记录', endpoint: '/api/breeding', method: 'GET', requireAuth: true },
                { name: '库存管理', endpoint: '/api/inventory', method: 'GET', requireAuth: true }
            ]
        };

        function initializeTests() {
            Object.keys(testSuites).forEach(suiteKey => {
                const container = document.getElementById(`${suiteKey}-tests`);
                testSuites[suiteKey].forEach((test, index) => {
                    const testItem = createTestItem(test, `${suiteKey}-${index}`);
                    container.appendChild(testItem);
                    testResults.total++;
                });
            });
            updateSummary();
        }

        function createTestItem(test, testId) {
            const div = document.createElement('div');
            div.className = 'test-item';
            div.innerHTML = `
                <div>
                    <div class="test-name">${test.name}</div>
                    <div class="test-details" id="details-${testId}">
                        <strong>端点:</strong> ${test.method} ${test.endpoint}<br>
                        <strong>状态:</strong> <span id="status-${testId}">等待测试</span><br>
                        <strong>响应时间:</strong> <span id="time-${testId}">-</span><br>
                        <strong>详情:</strong> <span id="detail-${testId}">-</span>
                    </div>
                </div>
                <div class="test-status status-pending" id="badge-${testId}" onclick="toggleDetails('${testId}')">
                    等待中
                </div>
            `;
            return div;
        }

        function toggleDetails(testId) {
            const details = document.getElementById(`details-${testId}`);
            details.classList.toggle('show');
        }

        async function startFullTest() {
            const startBtn = document.getElementById('startBtn');
            startBtn.disabled = true;
            startBtn.textContent = '测试进行中...';
            
            testResults = { total: testResults.total, passed: 0, failed: 0 };
            
            // 重置所有测试状态
            document.querySelectorAll('.test-status').forEach(badge => {
                badge.className = 'test-status status-pending';
                badge.textContent = '等待中';
            });

            try {
                // 1. 基础连通性测试
                await runTestSuite('connectivity', testSuites.connectivity);
                
                // 2. 认证测试 (获取token)
                await runTestSuite('auth', testSuites.auth);
                
                // 3. 业务API测试
                if (authToken) {
                    await runTestSuite('cats', testSuites.cats);
                    await runTestSuite('customers', testSuites.customers);
                    await runTestSuite('finance', testSuites.finance);
                    await runTestSuite('health', testSuites.health);
                    await runTestSuite('system', testSuites.system);
                } else {
                    console.warn('未获取到认证token，跳过需要认证的测试');
                }
                
            } catch (error) {
                console.error('测试过程中发生错误:', error);
            }
            
            startBtn.disabled = false;
            startBtn.textContent = '重新测试';
            updateSummary();
        }

        async function runTestSuite(suiteKey, tests) {
            for (let i = 0; i < tests.length; i++) {
                const test = tests[i];
                const testId = `${suiteKey}-${i}`;
                await runSingleTest(test, testId);
                updateProgress();
                // 添加小延迟，避免请求过于频繁
                await new Promise(resolve => setTimeout(resolve, 200));
            }
        }

        async function runSingleTest(test, testId) {
            const badge = document.getElementById(`badge-${testId}`);
            const statusSpan = document.getElementById(`status-${testId}`);
            const timeSpan = document.getElementById(`time-${testId}`);
            const detailSpan = document.getElementById(`detail-${testId}`);
            
            badge.className = 'test-status status-testing';
            badge.textContent = '测试中';
            statusSpan.textContent = '正在测试...';
            
            const startTime = Date.now();
            
            try {
                const options = {
                    method: test.method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (test.requireAuth && authToken) {
                    options.headers['Authorization'] = `Bearer ${authToken}`;
                }
                
                if (test.data) {
                    options.body = JSON.stringify(test.data);
                }
                
                const response = await fetch(`${API_BASE_URL}${test.endpoint}`, options);
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                timeSpan.textContent = `${responseTime}ms`;
                
                if (test.expectFail) {
                    // 期望失败的测试
                    if (!response.ok) {
                        badge.className = 'test-status status-success';
                        badge.textContent = '通过';
                        statusSpan.textContent = '按预期失败';
                        detailSpan.textContent = `状态码: ${response.status}`;
                        testResults.passed++;
                    } else {
                        throw new Error('期望失败但实际成功');
                    }
                } else if (test.expectAuth && response.status === 401) {
                    // 期望需要认证的测试
                    badge.className = 'test-status status-success';
                    badge.textContent = '通过';
                    statusSpan.textContent = '正确要求认证';
                    detailSpan.textContent = '安全配置正常';
                    testResults.passed++;
                } else if (response.ok) {
                    const data = await response.json().catch(() => null);
                    
                    // 特殊处理登录接口，保存token
                    if (test.endpoint === '/api/auth/login' && data && data.success && data.data && data.data.token) {
                        authToken = data.data.token;
                        detailSpan.textContent = `登录成功，获取到token`;
                    } else if (data) {
                        detailSpan.textContent = `返回数据: ${JSON.stringify(data).substring(0, 100)}...`;
                    } else {
                        detailSpan.textContent = '响应成功';
                    }
                    
                    badge.className = 'test-status status-success';
                    badge.textContent = '通过';
                    statusSpan.textContent = '测试通过';
                    testResults.passed++;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
            } catch (error) {
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                timeSpan.textContent = `${responseTime}ms`;
                badge.className = 'test-status status-error';
                badge.textContent = '失败';
                statusSpan.textContent = '测试失败';
                detailSpan.textContent = error.message;
                testResults.failed++;
            }
        }

        function updateProgress() {
            const completed = testResults.passed + testResults.failed;
            const progress = (completed / testResults.total) * 100;
            document.getElementById('progressBar').style.width = `${progress}%`;
        }

        function updateSummary() {
            document.getElementById('totalTests').textContent = testResults.total;
            document.getElementById('passedTests').textContent = testResults.passed;
            document.getElementById('failedTests').textContent = testResults.failed;
            
            const successRate = testResults.total > 0 ? 
                Math.round((testResults.passed / testResults.total) * 100) : 0;
            document.getElementById('successRate').textContent = `${successRate}%`;
        }

        // 页面加载时初始化测试
        window.addEventListener('load', () => {
            initializeTests();
        });
    </script>
</body>
</html>
