@echo off
chcp 65001 >nul
echo ========================================
echo Node.js 目录清理脚本
echo ========================================
echo.

echo 🔍 检查和清理Node.js相关目录...
echo.

echo 1. 检查问题根源目录: D:\软件\node_modules\
if exist "D:\软件\node_modules\" (
    echo ⚠️ 找到问题目录！这是导致npm错误的根源
    echo 目录内容:
    dir "D:\软件\node_modules\" /b 2>nul
    echo.
    echo 正在删除问题目录...
    rmdir /s /q "D:\软件\node_modules\" 2>nul
    if exist "D:\软件\node_modules\" (
        echo ❌ 自动删除失败，请手动删除此目录
        echo 路径: D:\软件\node_modules\
    ) else (
        echo ✅ 问题目录删除成功！
    )
) else (
    echo ✅ 问题目录不存在
)
echo.

echo 2. 检查标准安装目录: C:\Program Files\nodejs\
if exist "C:\Program Files\nodejs\" (
    echo ✓ 找到标准安装目录
    echo 正在删除...
    rmdir /s /q "C:\Program Files\nodejs\" 2>nul
    if exist "C:\Program Files\nodejs\" (
        echo ❌ 删除失败，可能需要管理员权限
    ) else (
        echo ✅ 删除成功
    )
) else (
    echo ✅ 标准安装目录不存在
)
echo.

echo 3. 检查32位安装目录: C:\Program Files (x86)\nodejs\
if exist "C:\Program Files (x86)\nodejs\" (
    echo ✓ 找到32位安装目录
    echo 正在删除...
    rmdir /s /q "C:\Program Files (x86)\nodejs\" 2>nul
    if exist "C:\Program Files (x86)\nodejs\" (
        echo ❌ 删除失败，可能需要管理员权限
    ) else (
        echo ✅ 删除成功
    )
) else (
    echo ✅ 32位安装目录不存在
)
echo.

echo 4. 检查用户npm目录: %APPDATA%\npm
if exist "%APPDATA%\npm" (
    echo ✓ 找到用户npm目录
    echo 正在删除...
    rmdir /s /q "%APPDATA%\npm" 2>nul
    if exist "%APPDATA%\npm" (
        echo ❌ 删除失败
    ) else (
        echo ✅ 删除成功
    )
) else (
    echo ✅ 用户npm目录不存在
)
echo.

echo 5. 检查npm缓存目录: %APPDATA%\npm-cache
if exist "%APPDATA%\npm-cache" (
    echo ✓ 找到npm缓存目录
    echo 正在删除...
    rmdir /s /q "%APPDATA%\npm-cache" 2>nul
    if exist "%APPDATA%\npm-cache" (
        echo ❌ 删除失败
    ) else (
        echo ✅ 删除成功
    )
) else (
    echo ✅ npm缓存目录不存在
)
echo.

echo 6. 检查其他可能的Node.js目录: D:\软件\nodejs\
if exist "D:\软件\nodejs\" (
    echo ✓ 找到其他Node.js目录
    echo 正在删除...
    rmdir /s /q "D:\软件\nodejs\" 2>nul
    if exist "D:\软件\nodejs\" (
        echo ❌ 删除失败
    ) else (
        echo ✅ 删除成功
    )
) else (
    echo ✅ 其他Node.js目录不存在
)
echo.

echo ========================================
echo 目录清理完成
echo ========================================
echo.

echo 📋 清理结果总结:
echo.

echo 测试Node.js是否还存在:
node --version 2>nul
if %errorlevel% equ 0 (
    echo ❌ Node.js仍然可以执行
    echo 可能原因:
    echo   1. 程序卸载未完成
    echo   2. 环境变量未清理
    echo   3. 还有其他安装位置
) else (
    echo ✅ Node.js已无法执行 (正常)
)
echo.

echo 测试npm是否还存在:
npm --version 2>nul
if %errorlevel% equ 0 (
    echo ❌ npm仍然可以执行
) else (
    echo ✅ npm已无法执行 (正常)
)
echo.

echo ========================================
echo 下一步操作
echo ========================================
echo.

echo 如果上述测试显示Node.js或npm仍然可以执行:
echo 1. 检查环境变量PATH设置
echo 2. 确保程序卸载完成
echo 3. 重启命令行窗口
echo.

echo 如果测试显示都无法执行:
echo ✅ 卸载成功！可以开始安装新版本
echo.

echo 接下来请:
echo 1. 清理环境变量 (如果需要)
echo 2. 运行: install-new-nodejs.bat
echo 3. 或访问: https://nodejs.org 下载安装
echo.

pause
