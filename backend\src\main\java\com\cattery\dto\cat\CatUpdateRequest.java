package com.cattery.dto.cat;

import jakarta.validation.constraints.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 猫咪更新请求DTO
 */
@Data
public class CatUpdateRequest {

    /**
     * 猫咪姓名
     */
    @Size(max = 100, message = "猫咪名称长度不能超过100个字符")
    private String name;

    /**
     * 品种名称
     */
    @Size(max = 100, message = "品种名称长度不能超过100个字符")
    private String breedName;

    /**
     * 性别
     */
    @Pattern(regexp = "MALE|FEMALE|UNKNOWN", message = "性别必须是MALE、FEMALE或UNKNOWN")
    private String gender;

    /**
     * 出生日期
     */
    @PastOrPresent(message = "出生日期不能是未来时间")
    private LocalDateTime dateOfBirth;

    /**
     * 颜色
     */
    @Size(max = 100, message = "颜色描述长度不能超过100个字符")
    private String color;

    /**
     * 花纹
     */
    @Size(max = 100, message = "花纹描述长度不能超过100个字符")
    private String pattern;

    /**
     * 当前体重（克）
     */
    @DecimalMin(value = "0", message = "体重不能为负数")
    @DecimalMax(value = "50000", message = "体重不能超过50公斤")
    private BigDecimal currentWeight;

    /**
     * 芯片号
     */
    @Size(max = 50, message = "芯片号长度不能超过50个字符")
    private String microchipId;

    /**
     * 注册号
     */
    @Size(max = 50, message = "注册号长度不能超过50个字符")
    private String registrationNumber;

    /**
     * 状态
     */
    @Pattern(regexp = "AVAILABLE|ADOPTED|RESERVED|BREEDING|MEDICAL|QUARANTINE", 
             message = "状态必须是AVAILABLE、ADOPTED、RESERVED、BREEDING、MEDICAL或QUARANTINE")
    private String status;

    /**
     * 描述
     */
    @Size(max = 2000, message = "描述长度不能超过2000个字符")
    private String description;

    /**
     * 备注
     */
    @Size(max = 2000, message = "备注长度不能超过2000个字符")
    private String notes;

    /**
     * 是否绝育
     */
    private Boolean isNeutered;

    /**
     * 父亲ID
     */
    @Min(value = 1, message = "父亲ID必须大于0")
    private Long fatherId;

    /**
     * 母亲ID
     */
    @Min(value = 1, message = "母亲ID必须大于0")
    private Long motherId;
}
