package com.cattery.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * 怀孕记录实体类
 */
@Entity
@Table(name = "pregnancies")
@Data
@EqualsAndHashCode(callSuper = false)
public class Pregnancy {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 母猫
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "mother_id", nullable = false)
    private Cat mother;

    /**
     * 配种记录
     */
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "mating_id")
    private Mating mating;

    /**
     * 怀孕确认日期
     */
    @Column(name = "pregnancy_date", nullable = false)
    private LocalDateTime pregnancyDate;

    /**
     * 预期分娩日期
     */
    @Column(name = "expected_birth_date")
    private LocalDateTime expectedBirthDate;

    /**
     * 实际分娩日期
     */
    @Column(name = "birth_date")
    private LocalDateTime birthDate;

    /**
     * 怀孕状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private Status status = Status.PREGNANT;

    /**
     * 怀孕周期（天）
     */
    @Column(name = "gestation_period")
    private Integer gestationPeriod;

    /**
     * 预期小猫数量
     */
    @Column(name = "expected_kittens")
    private Integer expectedKittens;

    /**
     * 实际小猫数量
     */
    @Column(name = "actual_kittens")
    private Integer actualKittens;

    /**
     * 存活小猫数量
     */
    @Column(name = "surviving_kittens")
    private Integer survivingKittens;

    /**
     * 兽医姓名
     */
    @Column(name = "veterinarian")
    private String veterinarian;

    /**
     * 诊所名称
     */
    @Column(name = "clinic")
    private String clinic;

    /**
     * 备注
     */
    @Column(name = "notes", columnDefinition = "TEXT")
    private String notes;

    /**
     * 并发症
     */
    @Column(name = "complications", columnDefinition = "TEXT")
    private String complications;

    /**
     * 分娩方式
     */
    @Column(name = "delivery_type")
    private String deliveryType;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 怀孕状态枚举
     */
    public enum Status {
        PREGNANT("怀孕中"),
        COMPLETED("已分娩"),
        MISCARRIAGE("流产"),
        ABORTED("人工终止");

        private final String description;

        Status(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 怀孕状态枚举别名（为了兼容ReportService）
     */
    public enum PregnancyStatus {
        PREGNANT("怀孕中"),
        COMPLETED("已分娩"),
        MISCARRIAGE("流产"),
        ABORTED("人工终止");

        private final String description;

        PregnancyStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
