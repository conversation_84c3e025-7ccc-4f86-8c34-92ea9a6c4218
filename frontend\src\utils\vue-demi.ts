// Vue 3 compatibility layer for vue-demi
// This file provides Vue 2/3 compatibility for libraries like VueUse

// Re-export everything from Vue 3
export * from 'vue'

// Vue 2/3 compatibility constants
export const isVue2 = false
export const isVue3 = true
export const Vue2 = undefined
export const version = '3.x'

// Vue 2 compatibility APIs that don't exist in Vue 3
export const set = (target: any, key: string | number, value: any) => {
  if (Array.isArray(target)) {
    target.splice(key as number, 1, value)
  } else {
    target[key] = value
  }
  return value
}

export const del = (target: any, key: string | number) => {
  if (Array.isArray(target)) {
    target.splice(key as number, 1)
  } else {
    delete target[key]
  }
}

// Vue 2 compatibility - install function (not needed in Vue 3)
export const install = () => {
  // No-op in Vue 3
}

// Vue 2 compatibility - Vue constructor (not needed in Vue 3)
export const Vue = undefined

// Additional compatibility exports that some libraries might expect
export const config = {
  productionTip: false,
  devtools: true
}

// Provide a default export for compatibility
export default {
  isVue2,
  isVue3,
  Vue2,
  version,
  set,
  del,
  install,
  Vue,
  config
}
