# Complete Node.js <PERSON>mo<PERSON>
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Complete Node.js Removal Tool" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Step 1: Find current Node.js installation
Write-Host "Step 1: Finding Node.js installation" -ForegroundColor Yellow
Write-Host ""

try {
    $nodePath = Get-Command node -ErrorAction SilentlyContinue
    if ($nodePath) {
        Write-Host "Node.js found at: $($nodePath.Source)" -ForegroundColor Green
    } else {
        Write-Host "Node.js not found in PATH" -ForegroundColor Red
    }
} catch {
    Write-Host "Error checking Node.js: $($_.Exception.Message)" -ForegroundColor Red
}

try {
    $npmPath = Get-Command npm -ErrorAction SilentlyContinue
    if ($npmPath) {
        Write-Host "npm found at: $($npmPath.Source)" -ForegroundColor Green
    } else {
        Write-Host "npm not found in PATH" -ForegroundColor Red
    }
} catch {
    Write-Host "Error checking npm: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Step 2: Check all possible installation directories
Write-Host "Step 2: Checking installation directories" -ForegroundColor Yellow
Write-Host ""

$directories = @(
    "C:\Program Files\nodejs",
    "C:\Program Files (x86)\nodejs",
    "$env:USERPROFILE\nodejs",
    "$env:APPDATA\npm",
    "$env:APPDATA\npm-cache",
    "$env:LOCALAPPDATA\npm",
    "$env:LOCALAPPDATA\npm-cache",
    "D:\nodejs",
    "D:\软件\nodejs",
    "D:\软件\node_modules"
)

$foundDirectories = @()

foreach ($dir in $directories) {
    if (Test-Path $dir) {
        Write-Host "Found: $dir" -ForegroundColor Green
        $foundDirectories += $dir
    } else {
        Write-Host "Not found: $dir" -ForegroundColor Gray
    }
}

Write-Host ""

# Step 3: Remove directories
if ($foundDirectories.Count -gt 0) {
    Write-Host "Step 3: Removing directories" -ForegroundColor Yellow
    Write-Host ""
    
    Write-Host "WARNING: About to delete the following directories:" -ForegroundColor Red
    foreach ($dir in $foundDirectories) {
        Write-Host "  - $dir" -ForegroundColor Red
    }
    Write-Host ""
    
    $confirmation = Read-Host "Continue with deletion? (Y/N)"
    
    if ($confirmation -eq 'Y' -or $confirmation -eq 'y') {
        foreach ($dir in $foundDirectories) {
            try {
                Write-Host "Removing: $dir" -ForegroundColor Cyan
                Remove-Item $dir -Recurse -Force -ErrorAction Stop
                Write-Host "Successfully removed: $dir" -ForegroundColor Green
            } catch {
                Write-Host "Failed to remove: $dir" -ForegroundColor Red
                Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
                Write-Host "You may need to remove this manually with administrator privileges" -ForegroundColor Yellow
            }
        }
    } else {
        Write-Host "Deletion cancelled by user" -ForegroundColor Yellow
    }
} else {
    Write-Host "No Node.js directories found to remove" -ForegroundColor Green
}

Write-Host ""

# Step 4: Environment variables
Write-Host "Step 4: Environment variables cleanup" -ForegroundColor Yellow
Write-Host ""

Write-Host "Opening System Properties for manual PATH cleanup..." -ForegroundColor Cyan
Write-Host "Please remove all Node.js related paths from the PATH variable" -ForegroundColor Yellow
Write-Host "Look for paths containing: nodejs, npm, node_modules" -ForegroundColor Yellow

try {
    Start-Process "sysdm.cpl"
} catch {
    Write-Host "Failed to open System Properties. Please open it manually." -ForegroundColor Red
}

Write-Host ""
Read-Host "Press Enter after cleaning environment variables"

# Step 5: Verification
Write-Host ""
Write-Host "Step 5: Verification" -ForegroundColor Yellow
Write-Host ""

Write-Host "Testing Node.js..." -ForegroundColor Cyan
try {
    $nodeVersion = node --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "ERROR: Node.js still exists!" -ForegroundColor Red
        Write-Host "Version: $nodeVersion" -ForegroundColor Red
        $nodePath = Get-Command node -ErrorAction SilentlyContinue
        if ($nodePath) {
            Write-Host "Path: $($nodePath.Source)" -ForegroundColor Red
        }
    } else {
        Write-Host "SUCCESS: Node.js removed!" -ForegroundColor Green
    }
} catch {
    Write-Host "SUCCESS: Node.js removed!" -ForegroundColor Green
}

Write-Host "Testing npm..." -ForegroundColor Cyan
try {
    $npmVersion = npm --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "ERROR: npm still exists!" -ForegroundColor Red
        Write-Host "Version: $npmVersion" -ForegroundColor Red
    } else {
        Write-Host "SUCCESS: npm removed!" -ForegroundColor Green
    }
} catch {
    Write-Host "SUCCESS: npm removed!" -ForegroundColor Green
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Removal Complete!" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Final check
try {
    node --version 2>$null
    npm --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "SUCCESS: Node.js completely removed!" -ForegroundColor Green
        Write-Host ""
        Write-Host "Next steps:" -ForegroundColor Yellow
        Write-Host "1. Restart computer (recommended)" -ForegroundColor Cyan
        Write-Host "2. Download new Node.js from https://nodejs.org" -ForegroundColor Cyan
        Write-Host "3. Install to C:\Program Files\nodejs\" -ForegroundColor Cyan
        Write-Host "4. Check 'Add to PATH' option during installation" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "Opening Node.js download page..." -ForegroundColor Cyan
        Start-Process "https://nodejs.org"
    } else {
        Write-Host "WARNING: Removal may be incomplete" -ForegroundColor Red
        Write-Host "Please restart computer and run this script again" -ForegroundColor Yellow
    }
} catch {
    Write-Host "SUCCESS: Node.js completely removed!" -ForegroundColor Green
}

Write-Host ""
Read-Host "Press Enter to exit"
