# 中文消息文件

# 通用消息
common.success=操作成功
common.error=操作失败
common.notFound=未找到
common.unauthorized=未授权
common.forbidden=权限不足
common.badRequest=请求参数错误
common.internalError=内部服务器错误
common.serviceUnavailable=服务不可用

# 用户认证
auth.login.success=登录成功
auth.login.failed=登录失败
auth.logout.success=退出成功
auth.token.invalid=令牌无效
auth.token.expired=令牌已过期
auth.password.incorrect=密码错误
auth.user.notFound=用户不存在
auth.user.locked=用户已被锁定
auth.user.disabled=用户已被禁用

# 猫咪管理
cat.created=猫咪创建成功
cat.updated=猫咪信息更新成功
cat.deleted=猫咪删除成功
cat.notFound=猫咪不存在
cat.name.required=猫咪姓名不能为空
cat.breed.required=品种不能为空
cat.gender.required=性别不能为空
cat.dateOfBirth.required=出生日期不能为空
cat.microchip.duplicate=芯片号已存在
cat.registration.duplicate=注册号已存在

# 健康管理
health.record.created=健康记录创建成功
health.record.updated=健康记录更新成功
health.record.deleted=健康记录删除成功
health.record.notFound=健康记录不存在
health.vaccine.due=疫苗即将到期
health.vaccine.overdue=疫苗已过期
health.checkup.due=需要体检
health.checkup.overdue=体检已过期

# 繁育管理
breeding.record.created=繁育记录创建成功
breeding.record.updated=繁育记录更新成功
breeding.record.deleted=繁育记录删除成功
breeding.record.notFound=繁育记录不存在
breeding.mating.scheduled=配种已安排
breeding.pregnancy.confirmed=怀孕已确认
breeding.birth.recorded=分娩记录已创建
breeding.kitten.registered=幼猫已登记

# 客户管理
customer.created=客户创建成功
customer.updated=客户信息更新成功
customer.deleted=客户删除成功
customer.notFound=客户不存在
customer.email.duplicate=邮箱已存在
customer.phone.duplicate=电话号码已存在
customer.inquiry.created=咨询记录创建成功
customer.followup.created=跟进记录创建成功

# 库存管理
inventory.item.created=库存物品创建成功
inventory.item.updated=库存物品更新成功
inventory.item.deleted=库存物品删除成功
inventory.item.notFound=库存物品不存在
inventory.stock.insufficient=库存不足
inventory.stock.updated=库存更新成功
inventory.lowStock.alert=库存不足警告
inventory.outOfStock.alert=缺货警告

# 财务管理
finance.transaction.created=交易记录创建成功
finance.transaction.updated=交易记录更新成功
finance.transaction.deleted=交易记录删除成功
finance.transaction.notFound=交易记录不存在
finance.amount.invalid=金额无效
finance.category.required=分类不能为空
finance.budget.exceeded=预算超支

# AI功能
ai.recognition.success=识别成功
ai.recognition.failed=识别失败
ai.prediction.success=预测成功
ai.prediction.failed=预测失败
ai.service.unavailable=AI服务不可用
ai.image.invalid=图片格式无效
ai.image.tooLarge=图片文件过大
ai.confidence.low=识别置信度较低

# 文件管理
file.upload.success=文件上传成功
file.upload.failed=文件上传失败
file.delete.success=文件删除成功
file.delete.failed=文件删除失败
file.notFound=文件不存在
file.type.invalid=文件类型无效
file.size.exceeded=文件大小超出限制

# 报表
report.generated=报表生成成功
report.generation.failed=报表生成失败
report.exported=报表导出成功
report.export.failed=报表导出失败
report.type.invalid=报表类型无效
report.period.invalid=报表周期无效

# 系统设置
settings.updated=设置更新成功
settings.reset=设置重置成功
settings.backup.created=备份创建成功
settings.backup.restored=备份恢复成功
settings.backup.failed=备份操作失败

# 数据验证
validation.required=此字段为必填项
validation.email.invalid=邮箱格式无效
validation.phone.invalid=电话号码格式无效
validation.date.invalid=日期格式无效
validation.number.invalid=数字格式无效
validation.length.min=长度不能少于{0}个字符
validation.length.max=长度不能超过{0}个字符
validation.value.min=值不能小于{0}
validation.value.max=值不能大于{0}

# 业务规则
business.cat.age.invalid=猫咪年龄无效
business.breeding.age.tooYoung=猫咪年龄太小，不适合繁育
business.breeding.age.tooOld=猫咪年龄太大，不适合繁育
business.breeding.inbreeding.detected=检测到近亲繁殖风险
business.health.vaccine.required=需要接种疫苗
business.health.checkup.required=需要进行体检
business.adoption.age.tooYoung=幼猫年龄太小，不适合领养
business.adoption.health.required=需要健康检查后才能领养

# 状态文本
status.cat.available=可领养
status.cat.adopted=已领养
status.cat.reserved=已预定
status.cat.breeding=繁育中
status.cat.medical=医疗中
status.cat.quarantine=隔离中

status.health.normal=正常
status.health.abnormal=异常
status.health.pending=待检查

status.breeding.planned=计划中
status.breeding.mated=已配种
status.breeding.pregnant=怀孕中
status.breeding.born=已出生
status.breeding.weaned=已断奶

status.customer.potential=潜在客户
status.customer.active=活跃客户
status.customer.adopted=已领养客户
status.customer.breeder=繁育者

status.transaction.income=收入
status.transaction.expense=支出
status.transaction.transfer=转账

# 性别文本
gender.male=公猫
gender.female=母猫
gender.unknown=未知

# 记录类型
recordType.vaccination=疫苗接种
recordType.checkup=体检
recordType.treatment=治疗
recordType.surgery=手术
recordType.dental=牙科
recordType.grooming=美容
recordType.geneticTest=基因检测
recordType.weightCheck=体重检查

# 时间格式
time.format.date=yyyy年MM月dd日
time.format.datetime=yyyy年MM月dd日 HH:mm:ss
time.format.time=HH:mm:ss

# 单位
unit.kg=公斤
unit.g=克
unit.cm=厘米
unit.year=岁
unit.month=个月
unit.day=天

# 货币
currency.cny=人民币
currency.symbol=￥

# 分页
page.first=首页
page.previous=上一页
page.next=下一页
page.last=末页
page.total=共{0}条记录
page.size=每页{0}条

# 操作日志
log.created=创建了{0}
log.updated=更新了{0}
log.deleted=删除了{0}
log.viewed=查看了{0}
log.exported=导出了{0}
log.imported=导入了{0}

# 通知消息
notification.vaccine.due=猫咪{0}的疫苗即将到期
notification.checkup.due=猫咪{0}需要进行体检
notification.birth.due=猫咪{0}即将分娩
notification.stock.low=物品{0}库存不足
notification.customer.inquiry=收到客户{0}的新咨询

# 邮件模板
email.subject.vaccine.reminder=疫苗接种提醒
email.subject.checkup.reminder=体检提醒
email.subject.birth.notification=分娩通知
email.subject.adoption.confirmation=领养确认
email.subject.inquiry.response=咨询回复

# 短信模板
sms.vaccine.reminder=您的猫咪{0}需要接种疫苗，请及时安排。
sms.checkup.reminder=您的猫咪{0}需要进行体检，请及时安排。
sms.birth.notification=您的猫咪{0}已成功分娩，请查看详情。
sms.adoption.confirmation=恭喜您成功领养猫咪{0}！
