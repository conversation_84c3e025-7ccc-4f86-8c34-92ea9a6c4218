<template>
  <div class="cat-recognition-container">
    <el-page-header @back="$router.go(-1)" content="猫咪识别" />
    
    <div class="recognition-content">
      <!-- 上传区域 -->
      <el-card class="upload-card">
        <template #header>
          <div class="card-header">
            <span>上传猫咪照片</span>
            <el-button 
              type="primary" 
              :icon="Camera" 
              @click="openCamera"
              :disabled="loading"
            >
              拍照识别
            </el-button>
          </div>
        </template>
        
        <el-upload
          ref="uploadRef"
          class="upload-demo"
          drag
          :auto-upload="false"
          :on-change="handleFileChange"
          :before-upload="beforeUpload"
          accept="image/*"
          :limit="1"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将图片拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持 JPG/PNG/GIF 格式，文件大小不超过 10MB
            </div>
          </template>
        </el-upload>
        
        <!-- 预览图片 -->
        <div v-if="previewImage" class="image-preview">
          <el-image
            :src="previewImage"
            fit="contain"
            style="width: 300px; height: 200px;"
          />
          <div class="preview-actions">
            <el-button 
              type="primary" 
              @click="recognizeCat"
              :loading="loading"
              :disabled="!selectedFile"
            >
              开始识别
            </el-button>
            <el-button @click="clearImage">重新选择</el-button>
          </div>
        </div>
      </el-card>

      <!-- 识别结果 -->
      <el-card v-if="recognitionResult" class="result-card">
        <template #header>
          <div class="card-header">
            <span>识别结果</span>
            <el-tag 
              :type="getConfidenceType(recognitionResult.confidence)"
              size="large"
            >
              置信度: {{ (recognitionResult.confidence * 100).toFixed(1) }}%
            </el-tag>
          </div>
        </template>
        
        <div class="result-content">
          <!-- 基本信息 -->
          <div class="basic-info">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="品种">
                {{ recognitionResult.breedName || '未知' }}
              </el-descriptions-item>
              <el-descriptions-item label="颜色">
                {{ recognitionResult.color || '未知' }}
              </el-descriptions-item>
              <el-descriptions-item label="性别">
                {{ getGenderText(recognitionResult.gender) }}
              </el-descriptions-item>
              <el-descriptions-item label="估计年龄">
                {{ recognitionResult.estimatedAge || 0 }} 岁
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <!-- 面部特征 -->
          <div v-if="recognitionResult.facialFeatures?.length" class="facial-features">
            <h4>面部特征</h4>
            <div class="features-grid">
              <el-card 
                v-for="feature in recognitionResult.facialFeatures" 
                :key="feature.featureType"
                class="feature-card"
                shadow="hover"
              >
                <div class="feature-content">
                  <div class="feature-icon">
                    <el-icon><View /></el-icon>
                  </div>
                  <div class="feature-details">
                    <h5>{{ getFeatureTypeName(feature.featureType) }}</h5>
                    <p v-if="feature.color">颜色: {{ feature.color }}</p>
                    <p v-if="feature.shape">形状: {{ feature.shape }}</p>
                    <p v-if="feature.size">大小: {{ feature.size }}</p>
                  </div>
                </div>
              </el-card>
            </div>
          </div>

          <!-- 匹配的猫咪 -->
          <div v-if="recognitionResult.matchedCats?.length" class="matched-cats">
            <h4>可能匹配的猫咪</h4>
            <div class="cats-grid">
              <el-card 
                v-for="cat in recognitionResult.matchedCats" 
                :key="cat.id"
                class="cat-card"
                shadow="hover"
                @click="viewCatDetail(cat.id)"
              >
                <div class="cat-content">
                  <el-avatar
                    :size="60"
                    :src="cat.primaryPhoto"
                    :icon="UserFilled"
                  />
                  <div class="cat-info">
                    <h5>{{ cat.name }}</h5>
                    <p>{{ cat.breedName }}</p>
                    <p>{{ cat.color }} · {{ getGenderText(cat.gender) }}</p>
                    <el-tag size="small">
                      {{ calculateAge(cat.dateOfBirth) }}
                    </el-tag>
                  </div>
                </div>
              </el-card>
            </div>
          </div>

          <!-- 相似度匹配 -->
          <div v-if="recognitionResult.similarityMatches?.length" class="similarity-matches">
            <h4>相似度匹配</h4>
            <el-table :data="recognitionResult.similarityMatches" style="width: 100%">
              <el-table-column prop="catId" label="猫咪ID" width="100" />
              <el-table-column label="相似度" width="120">
                <template #default="scope">
                  <el-progress 
                    :percentage="scope.row.similarity * 100" 
                    :color="getSimilarityColor(scope.row.similarity)"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="matchedFeatures" label="匹配特征">
                <template #default="scope">
                  <el-tag 
                    v-for="feature in scope.row.matchedFeatures" 
                    :key="feature"
                    size="small"
                    style="margin-right: 5px;"
                  >
                    {{ feature }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100">
                <template #default="scope">
                  <el-button 
                    type="text" 
                    size="small"
                    @click="viewCatDetail(scope.row.catId)"
                  >
                    查看详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-card>

      <!-- 品种识别结果 -->
      <el-card v-if="breedResult" class="breed-result-card">
        <template #header>
          <span>品种识别结果</span>
        </template>
        
        <div class="breed-content">
          <div class="main-breed">
            <h3>{{ breedResult.breedName }}</h3>
            <el-progress 
              :percentage="breedResult.confidence * 100"
              :color="getConfidenceColor(breedResult.confidence)"
            />
          </div>
          
          <!-- 品种信息 -->
          <div v-if="breedResult.breedInfo" class="breed-info">
            <el-descriptions :column="1" border>
              <el-descriptions-item label="原产地">
                {{ breedResult.breedInfo.origin }}
              </el-descriptions-item>
              <el-descriptions-item label="预期寿命">
                {{ breedResult.breedInfo.lifeExpectancy }}
              </el-descriptions-item>
              <el-descriptions-item label="性格特点">
                <el-tag 
                  v-for="trait in breedResult.breedInfo.characteristics" 
                  :key="trait"
                  size="small"
                  style="margin-right: 5px;"
                >
                  {{ trait }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="常见健康问题">
                <el-tag 
                  v-for="issue in breedResult.breedInfo.commonHealthIssues" 
                  :key="issue"
                  type="warning"
                  size="small"
                  style="margin-right: 5px;"
                >
                  {{ issue }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </div>
          
          <!-- 候选品种 -->
          <div v-if="breedResult.candidates?.length" class="breed-candidates">
            <h4>其他可能的品种</h4>
            <el-table :data="breedResult.candidates" style="width: 100%">
              <el-table-column prop="breedName" label="品种名称" />
              <el-table-column label="置信度" width="150">
                <template #default="scope">
                  <el-progress 
                    :percentage="scope.row.confidence * 100"
                    :color="getConfidenceColor(scope.row.confidence)"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="characteristics" label="特征">
                <template #default="scope">
                  <el-tag 
                    v-for="trait in scope.row.characteristics?.slice(0, 3)" 
                    :key="trait"
                    size="small"
                    style="margin-right: 5px;"
                  >
                    {{ trait }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-card>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button @click="clearResults">清除结果</el-button>
        <el-button type="primary" @click="recognizeBreed" :disabled="!selectedFile">
          品种识别
        </el-button>
        <el-button type="success" @click="saveResults" :disabled="!recognitionResult">
          保存结果
        </el-button>
      </div>
    </div>

    <!-- 相机对话框 -->
    <el-dialog v-model="cameraDialogVisible" title="拍照识别" width="600px">
      <div class="camera-container">
        <video ref="videoRef" autoplay playsinline></video>
        <canvas ref="canvasRef" style="display: none;"></canvas>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeCameraDialog">取消</el-button>
          <el-button type="primary" @click="capturePhoto">拍照</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Camera, UploadFilled, View, UserFilled } from '@element-plus/icons-vue'
import { aiApi } from '@/api/ai'
import type { CatRecognitionResult, BreedRecognitionResult } from '@/types/ai'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const selectedFile = ref<File | null>(null)
const previewImage = ref('')
const recognitionResult = ref<CatRecognitionResult | null>(null)
const breedResult = ref<BreedRecognitionResult | null>(null)
const cameraDialogVisible = ref(false)

// 相机相关
const videoRef = ref<HTMLVideoElement>()
const canvasRef = ref<HTMLCanvasElement>()
const mediaStream = ref<MediaStream | null>(null)

// 文件上传处理
const handleFileChange = (file: any) => {
  selectedFile.value = file.raw
  previewImage.value = URL.createObjectURL(file.raw)
}

const beforeUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('图片大小不能超过 10MB!')
    return false
  }
  return true
}

// 猫咪识别
const recognizeCat = async () => {
  if (!selectedFile.value) {
    ElMessage.warning('请先选择图片')
    return
  }

  loading.value = true
  try {
    const formData = new FormData()
    formData.append('image', selectedFile.value)
    
    const result = await aiApi.recognizeCat(formData)
    recognitionResult.value = result
    
    ElMessage.success('识别完成!')
  } catch (error) {
    console.error('识别失败:', error)
    ElMessage.error('识别失败，请重试')
  } finally {
    loading.value = false
  }
}

// 品种识别
const recognizeBreed = async () => {
  if (!selectedFile.value) {
    ElMessage.warning('请先选择图片')
    return
  }

  loading.value = true
  try {
    const formData = new FormData()
    formData.append('image', selectedFile.value)
    
    const result = await aiApi.recognizeBreed(formData)
    breedResult.value = result
    
    ElMessage.success('品种识别完成!')
  } catch (error) {
    console.error('品种识别失败:', error)
    ElMessage.error('品种识别失败，请重试')
  } finally {
    loading.value = false
  }
}

// 相机功能
const openCamera = async () => {
  try {
    mediaStream.value = await navigator.mediaDevices.getUserMedia({ 
      video: { facingMode: 'environment' } 
    })
    cameraDialogVisible.value = true
    
    setTimeout(() => {
      if (videoRef.value && mediaStream.value) {
        videoRef.value.srcObject = mediaStream.value
      }
    }, 100)
  } catch (error) {
    console.error('无法访问相机:', error)
    ElMessage.error('无法访问相机，请检查权限设置')
  }
}

const capturePhoto = () => {
  if (!videoRef.value || !canvasRef.value) return
  
  const canvas = canvasRef.value
  const video = videoRef.value
  
  canvas.width = video.videoWidth
  canvas.height = video.videoHeight
  
  const ctx = canvas.getContext('2d')
  if (ctx) {
    ctx.drawImage(video, 0, 0)
    
    canvas.toBlob((blob) => {
      if (blob) {
        const file = new File([blob], 'camera-photo.jpg', { type: 'image/jpeg' })
        selectedFile.value = file
        previewImage.value = URL.createObjectURL(file)
        closeCameraDialog()
      }
    }, 'image/jpeg', 0.8)
  }
}

const closeCameraDialog = () => {
  if (mediaStream.value) {
    mediaStream.value.getTracks().forEach(track => track.stop())
    mediaStream.value = null
  }
  cameraDialogVisible.value = false
}

// 工具函数
const getConfidenceType = (confidence: number) => {
  if (confidence >= 0.8) return 'success'
  if (confidence >= 0.6) return 'warning'
  return 'danger'
}

const getConfidenceColor = (confidence: number) => {
  if (confidence >= 0.8) return '#67c23a'
  if (confidence >= 0.6) return '#e6a23c'
  return '#f56c6c'
}

const getSimilarityColor = (similarity: number) => {
  if (similarity >= 0.8) return '#67c23a'
  if (similarity >= 0.6) return '#e6a23c'
  return '#f56c6c'
}

const getGenderText = (gender: string) => {
  return gender === 'MALE' ? '公猫' : gender === 'FEMALE' ? '母猫' : '未知'
}

const getFeatureTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    eyes: '眼睛',
    ears: '耳朵',
    nose: '鼻子',
    mouth: '嘴巴'
  }
  return typeMap[type] || type
}

const calculateAge = (dateOfBirth: string) => {
  const today = new Date()
  const birthDate = new Date(dateOfBirth)
  const ageInMonths = (today.getFullYear() - birthDate.getFullYear()) * 12 + 
                     (today.getMonth() - birthDate.getMonth())
  
  if (ageInMonths < 12) {
    return `${ageInMonths}个月`
  } else {
    const years = Math.floor(ageInMonths / 12)
    const months = ageInMonths % 12
    return months > 0 ? `${years}岁${months}个月` : `${years}岁`
  }
}

// 操作函数
const clearImage = () => {
  selectedFile.value = null
  previewImage.value = ''
  recognitionResult.value = null
  breedResult.value = null
}

const clearResults = () => {
  recognitionResult.value = null
  breedResult.value = null
}

const saveResults = async () => {
  try {
    // 这里可以保存识别结果到数据库
    ElMessage.success('结果已保存')
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

const viewCatDetail = (catId: number) => {
  router.push(`/cats/${catId}`)
}

// 生命周期
onUnmounted(() => {
  if (mediaStream.value) {
    mediaStream.value.getTracks().forEach(track => track.stop())
  }
})
</script>

<style scoped>
.cat-recognition-container {
  padding: 20px;
}

.recognition-content {
  max-width: 1200px;
  margin: 0 auto;
}

.upload-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.upload-demo {
  margin-bottom: 20px;
}

.image-preview {
  text-align: center;
  margin-top: 20px;
}

.preview-actions {
  margin-top: 15px;
}

.result-card, .breed-result-card {
  margin-bottom: 20px;
}

.result-content {
  space-y: 20px;
}

.basic-info {
  margin-bottom: 30px;
}

.facial-features h4,
.matched-cats h4,
.similarity-matches h4,
.breed-candidates h4 {
  margin-bottom: 15px;
  color: #303133;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 30px;
}

.feature-card {
  cursor: pointer;
}

.feature-content {
  display: flex;
  align-items: center;
}

.feature-icon {
  margin-right: 10px;
  color: #409eff;
}

.feature-details h5 {
  margin: 0 0 5px 0;
  color: #303133;
}

.feature-details p {
  margin: 2px 0;
  font-size: 12px;
  color: #606266;
}

.cats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-bottom: 30px;
}

.cat-card {
  cursor: pointer;
  transition: transform 0.2s;
}

.cat-card:hover {
  transform: translateY(-2px);
}

.cat-content {
  display: flex;
  align-items: center;
}

.cat-info {
  margin-left: 15px;
}

.cat-info h5 {
  margin: 0 0 5px 0;
  color: #303133;
}

.cat-info p {
  margin: 2px 0;
  font-size: 12px;
  color: #606266;
}

.breed-content {
  space-y: 20px;
}

.main-breed {
  text-align: center;
  margin-bottom: 30px;
}

.main-breed h3 {
  margin-bottom: 15px;
  color: #303133;
}

.breed-info {
  margin-bottom: 30px;
}

.action-buttons {
  text-align: center;
  margin-top: 30px;
}

.camera-container {
  text-align: center;
}

.camera-container video {
  width: 100%;
  max-width: 500px;
  border-radius: 8px;
}
</style>
