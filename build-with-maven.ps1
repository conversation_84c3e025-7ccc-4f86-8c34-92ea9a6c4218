# Build Project with Maven and Java 17
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Build Project with Maven and Java 17" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Set Java 17 path
$java17Path = "C:\tools\jdk-17.0.2"
if (Test-Path $java17Path) {
    $env:JAVA_HOME = $java17Path
    $env:PATH = "$java17Path\bin;" + $env:PATH
    Write-Host "✅ Java 17 path set: $env:JAVA_HOME" -ForegroundColor Green
} else {
    Write-Host "❌ Java 17 not found at $java17Path" -ForegroundColor Red
    Write-Host "Please install Java 17 first" -ForegroundColor Yellow
    return
}

# Set Maven path
$mavenPath = "C:\tools\apache-maven-3.9.6"
if (Test-Path $mavenPath) {
    $env:PATH = "$mavenPath\bin;" + $env:PATH
    Write-Host "✅ Maven path set: $mavenPath" -ForegroundColor Green
} else {
    Write-Host "❌ Maven not found at $mavenPath" -ForegroundColor Red
    Write-Host "Please install Maven first" -ForegroundColor Yellow
    return
}

# Verify Java and Maven
Write-Host ""
Write-Host "Verifying installations..." -ForegroundColor Cyan

Write-Host "Java version:" -ForegroundColor Yellow
& "$env:JAVA_HOME\bin\java.exe" -version

Write-Host ""
Write-Host "Maven version:" -ForegroundColor Yellow
& "$mavenPath\bin\mvn.cmd" --version

# Set location to backend directory
Write-Host ""
Set-Location "D:\噔噔\backend"
Write-Host "Current directory: $(Get-Location)" -ForegroundColor Green

# Build project
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Building Spring Boot Project" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Write-Host ""
Write-Host "Step 1: Clean project..." -ForegroundColor Yellow
& "$mavenPath\bin\mvn.cmd" clean

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Clean successful!" -ForegroundColor Green
    
    Write-Host ""
    Write-Host "Step 2: Compile project..." -ForegroundColor Yellow
    & "$mavenPath\bin\mvn.cmd" compile
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Compile successful!" -ForegroundColor Green
        
        Write-Host ""
        Write-Host "Step 3: Package project..." -ForegroundColor Yellow
        & "$mavenPath\bin\mvn.cmd" package -DskipTests
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Package successful!" -ForegroundColor Green
            
            # Find the generated jar
            $jarFiles = Get-ChildItem "target" -Filter "*.jar" | Where-Object { $_.Name -notlike "*sources*" -and $_.Name -notlike "*javadoc*" }
            
            if ($jarFiles) {
                $jarFile = $jarFiles[0]
                Write-Host ""
                Write-Host "========================================" -ForegroundColor Green
                Write-Host "BUILD SUCCESS!" -ForegroundColor Green
                Write-Host "========================================" -ForegroundColor Green
                Write-Host ""
                Write-Host "Generated JAR: $($jarFile.Name)" -ForegroundColor Green
                Write-Host "Size: $([math]::Round($jarFile.Length / 1MB, 2)) MB" -ForegroundColor Green
                Write-Host "Location: $(Get-Location)\target\$($jarFile.Name)" -ForegroundColor Green
                
                Write-Host ""
                Write-Host "Starting Spring Boot application..." -ForegroundColor Cyan
                Write-Host "🚀 Look for 'Started CatteryManagementApplication' message" -ForegroundColor Yellow
                Write-Host "🌐 Then access: http://localhost:8080" -ForegroundColor Yellow
                Write-Host ""
                Write-Host "Press Ctrl+C to stop the application" -ForegroundColor Gray
                Write-Host ""
                
                # Run the jar file
                & "$env:JAVA_HOME\bin\java.exe" -jar "target\$($jarFile.Name)"
                
            } else {
                Write-Host "❌ No JAR file found in target directory" -ForegroundColor Red
                Write-Host ""
                Write-Host "Trying alternative: Maven spring-boot:run..." -ForegroundColor Yellow
                & "$mavenPath\bin\mvn.cmd" spring-boot:run
            }
        } else {
            Write-Host "❌ Package failed" -ForegroundColor Red
            Write-Host "Check the error messages above" -ForegroundColor Yellow
        }
    } else {
        Write-Host "❌ Compile failed" -ForegroundColor Red
        Write-Host "Check the error messages above" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ Clean failed" -ForegroundColor Red
    Write-Host "Check the error messages above" -ForegroundColor Yellow
}

Write-Host ""
Read-Host "Press Enter to exit"
