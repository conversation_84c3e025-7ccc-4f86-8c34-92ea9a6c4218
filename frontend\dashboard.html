<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>猫舍管理系统 - 仪表盘</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 24px;
            font-weight: 600;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .welcome-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .welcome-title {
            font-size: 28px;
            margin-bottom: 10px;
            color: #333;
        }

        .welcome-subtitle {
            color: #666;
            font-size: 16px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .stat-title {
            font-size: 14px;
            color: #666;
            font-weight: 500;
        }

        .stat-icon {
            font-size: 24px;
        }

        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .stat-change {
            font-size: 12px;
            color: #28a745;
        }

        .quick-actions {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .section-title {
            font-size: 20px;
            margin-bottom: 1.5rem;
            color: #333;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .action-btn {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 1rem;
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .action-btn:hover {
            background: #667eea;
            color: white;
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .recent-activity {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .activity-list {
            list-style: none;
        }

        .activity-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 1rem 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            background: #f8f9fa;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 500;
            margin-bottom: 5px;
        }

        .activity-time {
            font-size: 12px;
            color: #666;
        }

        .loading {
            text-align: center;
            padding: 2rem;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
            
            .container {
                padding: 1rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <span>🐱</span>
                <span>猫舍管理系统</span>
            </div>
            <div class="user-info">
                <span id="user-name">加载中...</span>
                <button class="logout-btn" onclick="logout()">退出登录</button>
            </div>
        </div>
    </header>

    <div class="container">
        <div class="welcome-section">
            <h1 class="welcome-title">欢迎回来！</h1>
            <p class="welcome-subtitle">这里是您的猫舍管理仪表盘，您可以查看最新的统计数据和快速访问各项功能。</p>
        </div>

        <div id="loading" class="loading">
            <div class="loading-spinner"></div>
            <p>正在加载数据...</p>
        </div>

        <div id="dashboard-content" style="display: none;">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title">总猫咪数量</span>
                        <span class="stat-icon">🐱</span>
                    </div>
                    <div class="stat-value" id="total-cats">0</div>
                    <div class="stat-change">+2 本月新增</div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title">繁育中</span>
                        <span class="stat-icon">💕</span>
                    </div>
                    <div class="stat-value" id="breeding-cats">0</div>
                    <div class="stat-change">活跃繁育</div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title">健康提醒</span>
                        <span class="stat-icon">🏥</span>
                    </div>
                    <div class="stat-value" id="health-alerts">0</div>
                    <div class="stat-change">待处理</div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title">客户咨询</span>
                        <span class="stat-icon">👥</span>
                    </div>
                    <div class="stat-value" id="customer-inquiries">0</div>
                    <div class="stat-change">待回复</div>
                </div>
            </div>

            <div class="quick-actions">
                <h2 class="section-title">快速操作</h2>
                <div class="actions-grid">
                    <div class="action-btn" onclick="navigateTo('cats.html')">
                        <span>🐱</span>
                        <span>猫咪管理</span>
                    </div>
                    <div class="action-btn" onclick="navigateTo('breeding.html')">
                        <span>💕</span>
                        <span>繁育管理</span>
                    </div>
                    <div class="action-btn" onclick="navigateTo('health.html')">
                        <span>🏥</span>
                        <span>健康管理</span>
                    </div>
                    <div class="action-btn" onclick="navigateTo('customers.html')">
                        <span>👥</span>
                        <span>客户管理</span>
                    </div>
                    <div class="action-btn" onclick="navigateTo('finance.html')">
                        <span>💰</span>
                        <span>财务管理</span>
                    </div>
                    <div class="action-btn" onclick="navigateTo('reports.html')">
                        <span>📊</span>
                        <span>报表分析</span>
                    </div>
                </div>
            </div>

            <div class="recent-activity">
                <h2 class="section-title">最近活动</h2>
                <ul class="activity-list" id="activity-list">
                    <li class="activity-item">
                        <div class="activity-icon">🐱</div>
                        <div class="activity-content">
                            <div class="activity-title">新增猫咪：小花</div>
                            <div class="activity-time">2小时前</div>
                        </div>
                    </li>
                    <li class="activity-item">
                        <div class="activity-icon">💉</div>
                        <div class="activity-content">
                            <div class="activity-title">疫苗接种：大橘</div>
                            <div class="activity-time">5小时前</div>
                        </div>
                    </li>
                    <li class="activity-item">
                        <div class="activity-icon">👥</div>
                        <div class="activity-content">
                            <div class="activity-title">新客户咨询</div>
                            <div class="activity-time">1天前</div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080/api';
        let authToken = '';
        let userInfo = {};

        // 检查登录状态
        function checkAuth() {
            authToken = localStorage.getItem('authToken');
            const userInfoStr = localStorage.getItem('userInfo');
            
            if (!authToken) {
                window.location.href = 'login.html';
                return false;
            }
            
            if (userInfoStr) {
                userInfo = JSON.parse(userInfoStr);
                document.getElementById('user-name').textContent = userInfo.realName || userInfo.username || '用户';
            }
            
            return true;
        }

        // 退出登录
        function logout() {
            localStorage.removeItem('authToken');
            localStorage.removeItem('userInfo');
            window.location.href = 'login.html';
        }

        // 导航到其他页面
        function navigateTo(page) {
            // 暂时显示提示，因为其他页面还未创建
            alert(`即将跳转到 ${page}，该功能正在开发中...`);
        }

        // 加载仪表盘数据
        async function loadDashboardData() {
            try {
                // 加载猫咪统计
                const catsResponse = await fetch(`${API_BASE_URL}/cats`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (catsResponse.ok) {
                    const catsData = await catsResponse.json();
                    const cats = catsData.data || catsData || [];
                    
                    document.getElementById('total-cats').textContent = cats.length;
                    document.getElementById('breeding-cats').textContent = 
                        cats.filter(cat => cat.status === 'BREEDING').length;
                } else {
                    console.warn('无法加载猫咪数据');
                    // 使用模拟数据
                    document.getElementById('total-cats').textContent = '3';
                    document.getElementById('breeding-cats').textContent = '1';
                }

                // 模拟其他数据
                document.getElementById('health-alerts').textContent = Math.floor(Math.random() * 5);
                document.getElementById('customer-inquiries').textContent = Math.floor(Math.random() * 10);

            } catch (error) {
                console.error('加载数据失败:', error);
                // 使用模拟数据
                document.getElementById('total-cats').textContent = '3';
                document.getElementById('breeding-cats').textContent = '1';
                document.getElementById('health-alerts').textContent = '2';
                document.getElementById('customer-inquiries').textContent = '5';
            } finally {
                // 隐藏加载状态，显示内容
                document.getElementById('loading').style.display = 'none';
                document.getElementById('dashboard-content').style.display = 'block';
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            if (checkAuth()) {
                loadDashboardData();
            }
        });
    </script>
</body>
</html>
