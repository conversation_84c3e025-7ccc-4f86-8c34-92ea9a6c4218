package com.cattery.dto.report;

import lombok.Data;
import java.util.Map;

/**
 * 健康统计DTO
 */
@Data
public class HealthStatisticsDTO {
    
    /**
     * 健康猫咪数量
     */
    private Long healthyCats;
    
    /**
     * 需要关注的猫咪数量
     */
    private Long catsNeedingAttention;
    
    /**
     * 生病猫咪数量
     */
    private Long sickCats;
    
    /**
     * 按健康状态分布
     */
    private Map<String, Long> healthStatusDistribution;
    
    /**
     * 疫苗接种率
     */
    private Double vaccinationRate;
    
    /**
     * 绝育率
     */
    private Double neuteringRate;
    
    /**
     * 平均健康评分
     */
    private Double averageHealthScore;
    
    /**
     * 常见健康问题
     */
    private Map<String, Long> commonHealthIssues;
    
    /**
     * 按年龄段的健康分布
     */
    private Map<String, Long> healthByAgeGroup;
    
    /**
     * 按品种的健康分布
     */
    private Map<String, Long> healthByBreed;
}
