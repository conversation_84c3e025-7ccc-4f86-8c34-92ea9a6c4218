<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>繁育管理 - 猫舍管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            padding: 0 20px;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 70px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo {
            font-size: 24px;
            font-weight: 700;
            color: white;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo::before {
            content: '🐱';
            font-size: 28px;
        }

        .nav-links {
            display: flex;
            gap: 5px;
        }

        .nav-links a {
            color: rgba(255,255,255,0.9);
            text-decoration: none;
            padding: 10px 18px;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }

        .nav-links a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.2);
            transition: left 0.3s ease;
        }

        .nav-links a:hover::before,
        .nav-links a.active::before {
            left: 0;
        }

        .nav-links a:hover,
        .nav-links a.active {
            color: white;
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
            color: white;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-1px);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .page-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .page-title {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .page-title::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
        }

        .page-title h1 {
            color: #333;
            font-size: 36px;
            margin-bottom: 15px;
            font-weight: 700;
        }

        .page-title p {
            color: #666;
            font-size: 18px;
            max-width: 600px;
            margin: 0 auto;
        }

        .breeding-dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .dashboard-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--card-color, #667eea);
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .card-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            background: var(--card-color, #667eea);
        }

        .card-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }

        .card-content {
            color: #666;
            line-height: 1.6;
        }

        .breeding-pairs {
            --card-color: #10b981;
        }

        .pregnancy-tracking {
            --card-color: #f59e0b;
        }

        .birth-records {
            --card-color: #ef4444;
        }

        .genetic-analysis {
            --card-color: #8b5cf6;
        }

        .quick-actions {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            margin-bottom: 40px;
        }

        .quick-actions h3 {
            color: #333;
            margin-bottom: 25px;
            font-size: 24px;
            font-weight: 600;
            text-align: center;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .action-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 25px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .action-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.1);
            transition: left 0.3s ease;
        }

        .action-card:hover::before {
            left: 0;
        }

        .action-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
        }

        .action-icon {
            font-size: 32px;
            margin-bottom: 10px;
            display: block;
        }

        .breeding-timeline {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        }

        .timeline-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .timeline-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .timeline-subtitle {
            color: #666;
            font-size: 16px;
        }

        .timeline {
            position: relative;
            padding: 20px 0;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, #667eea, #764ba2);
            transform: translateX(-50%);
        }

        .timeline-item {
            position: relative;
            margin-bottom: 30px;
            width: 45%;
        }

        .timeline-item:nth-child(odd) {
            left: 0;
            text-align: right;
            padding-right: 30px;
        }

        .timeline-item:nth-child(even) {
            left: 55%;
            text-align: left;
            padding-left: 30px;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            background: #667eea;
            border: 4px solid white;
            border-radius: 50%;
            top: 0;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.2);
        }

        .timeline-item:nth-child(odd)::before {
            right: -8px;
        }

        .timeline-item:nth-child(even)::before {
            left: -8px;
        }

        .timeline-content {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .timeline-date {
            color: #667eea;
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .timeline-title-item {
            color: #333;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .timeline-description {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }

        .loading {
            text-align: center;
            padding: 60px;
            color: #666;
        }

        .loading::before {
            content: '🐱';
            font-size: 48px;
            display: block;
            margin-bottom: 20px;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                height: auto;
                padding: 15px 0;
                gap: 15px;
            }

            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }

            .page-title {
                padding: 30px 20px;
            }

            .page-title h1 {
                font-size: 28px;
            }

            .breeding-dashboard {
                grid-template-columns: 1fr;
            }

            .actions-grid {
                grid-template-columns: 1fr;
            }

            .timeline::before {
                left: 20px;
            }

            .timeline-item {
                width: calc(100% - 40px);
                left: 40px !important;
                text-align: left !important;
                padding-left: 20px !important;
                padding-right: 0 !important;
            }

            .timeline-item::before {
                left: -28px !important;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">猫舍管理系统</div>
            <div class="nav-links">
                <a href="/dashboard.html">仪表盘</a>
                <a href="/cats-management.html">猫咪管理</a>
                <a href="/customers-management.html">客户管理</a>
                <a href="/health-management.html">健康记录</a>
                <a href="/breeding-management.html" class="active">繁育管理</a>
            </div>
            <div class="user-info">
                <div class="user-avatar" id="userAvatar">A</div>
                <span id="userName">加载中...</span>
                <button class="logout-btn" onclick="logout()">退出</button>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="page-header">
            <div class="page-title">
                <h1>繁育管理</h1>
                <p>专业的猫咪繁育管理系统，追踪配对、怀孕、分娩全过程，确保健康繁育</p>
            </div>
        </div>

        <div id="loadingMessage" class="loading">正在加载繁育数据...</div>

        <div id="breedingContent" style="display: none;">
            <div class="breeding-dashboard">
                <div class="dashboard-card breeding-pairs">
                    <div class="card-header">
                        <div class="card-icon">💕</div>
                        <div class="card-title">繁育配对</div>
                    </div>
                    <div class="card-content">
                        <p>管理繁育配对计划，记录配对时间和结果。当前活跃配对：<strong id="activePairs">0</strong> 对</p>
                        <p style="margin-top: 10px; color: #10b981;">本月新增配对：2对</p>
                    </div>
                </div>

                <div class="dashboard-card pregnancy-tracking">
                    <div class="card-header">
                        <div class="card-icon">🤱</div>
                        <div class="card-title">怀孕追踪</div>
                    </div>
                    <div class="card-content">
                        <p>追踪怀孕母猫的健康状况和预产期。当前怀孕：<strong id="pregnantCats">0</strong> 只</p>
                        <p style="margin-top: 10px; color: #f59e0b;">预计本月分娩：1只</p>
                    </div>
                </div>

                <div class="dashboard-card birth-records">
                    <div class="card-header">
                        <div class="card-icon">👶</div>
                        <div class="card-title">分娩记录</div>
                    </div>
                    <div class="card-content">
                        <p>记录分娩过程和幼猫信息。今年总分娩：<strong id="birthCount">0</strong> 次</p>
                        <p style="margin-top: 10px; color: #ef4444;">健康幼猫：8只</p>
                    </div>
                </div>

                <div class="dashboard-card genetic-analysis">
                    <div class="card-header">
                        <div class="card-icon">🧬</div>
                        <div class="card-title">遗传分析</div>
                    </div>
                    <div class="card-content">
                        <p>分析遗传特征和血统信息，优化繁育计划。已建档血统：<strong id="pedigreeCount">0</strong> 个</p>
                        <p style="margin-top: 10px; color: #8b5cf6;">遗传多样性：良好</p>
                    </div>
                </div>
            </div>

            <div class="quick-actions">
                <h3>快速操作</h3>
                <div class="actions-grid">
                    <button class="action-card" onclick="testAPI('/breeding')">
                        <span class="action-icon">📋</span>
                        查看繁育记录
                    </button>
                    <button class="action-card" onclick="addBreedingPair()">
                        <span class="action-icon">💕</span>
                        添加配对计划
                    </button>
                    <button class="action-card" onclick="trackPregnancy()">
                        <span class="action-icon">🤱</span>
                        怀孕追踪
                    </button>
                    <button class="action-card" onclick="recordBirth()">
                        <span class="action-icon">👶</span>
                        记录分娩
                    </button>
                    <button class="action-card" onclick="viewPedigree()">
                        <span class="action-icon">🧬</span>
                        血统分析
                    </button>
                    <button class="action-card" onclick="generateReport()">
                        <span class="action-icon">📊</span>
                        繁育报告
                    </button>
                </div>
            </div>

            <div class="breeding-timeline">
                <div class="timeline-header">
                    <div class="timeline-title">繁育时间线</div>
                    <div class="timeline-subtitle">最近的繁育活动和重要事件</div>
                </div>
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-content">
                            <div class="timeline-date">2024年1月15日</div>
                            <div class="timeline-title-item">小雪 × 大橘 配对成功</div>
                            <div class="timeline-description">英国短毛猫小雪与大橘成功配对，预计怀孕期65天，预产期3月20日左右。</div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-content">
                            <div class="timeline-date">2024年1月10日</div>
                            <div class="timeline-title-item">美美产下4只幼猫</div>
                            <div class="timeline-description">布偶猫美美顺利分娩，产下4只健康幼猫，母子平安。幼猫已完成初次健康检查。</div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-content">
                            <div class="timeline-date">2024年1月5日</div>
                            <div class="timeline-title-item">花花怀孕确认</div>
                            <div class="timeline-description">波斯猫花花怀孕确认，已进入怀孕第30天，胎儿发育正常，预产期2月10日。</div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-content">
                            <div class="timeline-date">2023年12月28日</div>
                            <div class="timeline-title-item">年度繁育计划制定</div>
                            <div class="timeline-description">完成2024年度繁育计划，预计进行8对配对，目标产出20-25只优质幼猫。</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080/api';
        
        // 检查登录状态
        function checkAuth() {
            const token = localStorage.getItem('token');
            const userInfo = localStorage.getItem('userInfo');
            
            if (!token || !userInfo) {
                window.location.href = '/login-test.html';
                return false;
            }
            
            try {
                const user = JSON.parse(userInfo);
                const userName = user.realName || user.username;
                document.getElementById('userName').textContent = userName;
                document.getElementById('userAvatar').textContent = userName.charAt(0).toUpperCase();
                return true;
            } catch (error) {
                logout();
                return false;
            }
        }
        
        // 退出登录
        function logout() {
            localStorage.removeItem('token');
            localStorage.removeItem('userInfo');
            window.location.href = '/login-test.html';
        }
        
        // 加载繁育数据
        async function loadBreedingData() {
            const token = localStorage.getItem('token');
            
            try {
                const response = await fetch(`${API_BASE_URL}/breeding`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    const breedingData = Array.isArray(data.data) ? data.data : [];
                    updateDashboard(breedingData);
                } else {
                    console.log('繁育数据加载失败:', data.message);
                }
            } catch (error) {
                console.error('加载繁育数据失败:', error);
            } finally {
                document.getElementById('loadingMessage').style.display = 'none';
                document.getElementById('breedingContent').style.display = 'block';
            }
        }
        
        // 更新仪表盘数据
        function updateDashboard(breedingData) {
            // 模拟数据统计
            document.getElementById('activePairs').textContent = breedingData.length || 3;
            document.getElementById('pregnantCats').textContent = Math.floor(breedingData.length / 2) || 2;
            document.getElementById('birthCount').textContent = breedingData.length * 2 || 5;
            document.getElementById('pedigreeCount').textContent = breedingData.length * 3 || 12;
        }
        
        // 测试API
        async function testAPI(endpoint) {
            const token = localStorage.getItem('token');
            
            try {
                const response = await fetch(`${API_BASE_URL}${endpoint}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert(`API测试成功！\n端点: ${endpoint}\n数据条数: ${Array.isArray(data.data) ? data.data.length : '1'}`);
                } else {
                    alert(`API测试失败！\n端点: ${endpoint}\n错误: ${data.message}`);
                }
            } catch (error) {
                console.error('API测试错误:', error);
                alert(`API测试错误！\n端点: ${endpoint}\n错误: ${error.message}`);
            }
        }
        
        // 功能占位函数
        function addBreedingPair() {
            alert('添加配对计划功能开发中...\n\n将包含以下功能：\n• 选择父母猫\n• 设置配对时间\n• 记录配对结果\n• 预测遗传特征');
        }
        
        function trackPregnancy() {
            alert('怀孕追踪功能开发中...\n\n将包含以下功能：\n• 怀孕确认\n• 孕期健康监测\n• 预产期计算\n• 产前准备提醒');
        }
        
        function recordBirth() {
            alert('分娩记录功能开发中...\n\n将包含以下功能：\n• 分娩过程记录\n• 幼猫信息登记\n• 健康状况评估\n• 母猫产后护理');
        }
        
        function viewPedigree() {
            alert('血统分析功能开发中...\n\n将包含以下功能：\n• 血统图谱\n• 遗传特征分析\n• 近亲繁殖检测\n• 优化配对建议');
        }
        
        function generateReport() {
            alert('繁育报告功能开发中...\n\n将包含以下功能：\n• 繁育统计报告\n• 成功率分析\n• 健康状况总结\n• 经济效益分析');
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            if (checkAuth()) {
                loadBreedingData();
            }
        });
    </script>
</body>
</html>
