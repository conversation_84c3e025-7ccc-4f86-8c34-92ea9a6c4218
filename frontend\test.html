<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>猫舍管理系统 - 资源测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { border-color: #28a745; background-color: #d4edda; }
        .error { border-color: #dc3545; background-color: #f8d7da; }
        .warning { border-color: #ffc107; background-color: #fff3cd; }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        #log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🐱 猫舍管理系统 - 资源测试页面</h1>
    
    <div class="test-section">
        <h2>📋 系统状态检查</h2>
        <button onclick="testAll()">运行所有测试</button>
        <button onclick="clearLog()">清空日志</button>
        
        <div id="test-results"></div>
    </div>
    
    <div class="test-section">
        <h2>🔍 详细日志</h2>
        <div id="log"></div>
    </div>
    
    <div class="test-section">
        <h2>🚀 快速启动</h2>
        <p>如果所有测试通过，您可以：</p>
        <ul>
            <li><a href="index.html">打开主系统界面</a></li>
            <li><a href="http://localhost:8080/swagger-ui/index.html" target="_blank">查看API文档</a></li>
            <li><a href="http://localhost:8080/api/test/health" target="_blank">检查API健康状态</a></li>
        </ul>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080/api';
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> <span style="color: ${getLogColor(type)};">[${type.toUpperCase()}]</span> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type}] ${message}`);
        }
        
        function getLogColor(type) {
            switch(type) {
                case 'success': return '#28a745';
                case 'error': return '#dc3545';
                case 'warning': return '#ffc107';
                default: return '#007bff';
            }
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            document.getElementById('test-results').innerHTML = '';
        }
        
        function addTestResult(title, status, message) {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${status}`;
            resultDiv.innerHTML = `
                <strong>${title}</strong><br>
                <span>${message}</span>
            `;
            resultsDiv.appendChild(resultDiv);
        }
        
        async function testResourceLoading() {
            log('开始测试资源加载...');
            
            // 测试CSS文件
            try {
                const response = await fetch('styles/main.css');
                if (response.ok) {
                    log('CSS文件加载成功', 'success');
                    addTestResult('CSS资源', 'success', 'styles/main.css 加载正常');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                log(`CSS文件加载失败: ${error.message}`, 'error');
                addTestResult('CSS资源', 'error', `styles/main.css 加载失败: ${error.message}`);
            }
            
            // 测试JavaScript文件
            try {
                const response = await fetch('scripts/main.js');
                if (response.ok) {
                    log('JavaScript文件加载成功', 'success');
                    addTestResult('JavaScript资源', 'success', 'scripts/main.js 加载正常');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                log(`JavaScript文件加载失败: ${error.message}`, 'error');
                addTestResult('JavaScript资源', 'error', `scripts/main.js 加载失败: ${error.message}`);
            }
            
            // 测试favicon
            try {
                const response = await fetch('favicon.ico');
                if (response.ok) {
                    log('Favicon加载成功', 'success');
                    addTestResult('Favicon', 'success', 'favicon.ico 加载正常');
                } else {
                    log('Favicon不存在，但这不影响系统功能', 'warning');
                    addTestResult('Favicon', 'warning', 'favicon.ico 不存在（可选）');
                }
            } catch (error) {
                log(`Favicon测试失败: ${error.message}`, 'warning');
                addTestResult('Favicon', 'warning', `favicon.ico 测试失败（可选）`);
            }
        }
        
        async function testApiConnection() {
            log('开始测试API连接...');
            
            // 测试健康检查端点
            try {
                const response = await fetch(`${API_BASE_URL}/test/health`);
                if (response.ok) {
                    const data = await response.json();
                    log('API健康检查通过', 'success');
                    log(`API响应: ${JSON.stringify(data)}`, 'info');
                    addTestResult('API连接', 'success', 'API服务正常运行');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                log(`API连接失败: ${error.message}`, 'error');
                addTestResult('API连接', 'error', `无法连接到API服务: ${error.message}`);
                return false;
            }
            
            // 测试其他API端点
            const endpoints = [
                { url: '/test/info', name: '系统信息' },
                { url: '/test/stats', name: '统计数据' },
                { url: '/test/cats', name: '猫咪数据' }
            ];
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(`${API_BASE_URL}${endpoint.url}`);
                    if (response.ok) {
                        log(`${endpoint.name}端点测试通过`, 'success');
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                } catch (error) {
                    log(`${endpoint.name}端点测试失败: ${error.message}`, 'warning');
                }
            }
            
            return true;
        }
        
        async function testBrowserCompatibility() {
            log('开始测试浏览器兼容性...');
            
            const features = [
                { name: 'Fetch API', test: () => typeof fetch !== 'undefined' },
                { name: 'Promise', test: () => typeof Promise !== 'undefined' },
                { name: 'Arrow Functions', test: () => { try { eval('() => {}'); return true; } catch { return false; } } },
                { name: 'Local Storage', test: () => typeof localStorage !== 'undefined' },
                { name: 'CSS Grid', test: () => CSS.supports('display', 'grid') },
                { name: 'CSS Flexbox', test: () => CSS.supports('display', 'flex') }
            ];
            
            let allSupported = true;
            
            features.forEach(feature => {
                if (feature.test()) {
                    log(`${feature.name} 支持`, 'success');
                } else {
                    log(`${feature.name} 不支持`, 'error');
                    allSupported = false;
                }
            });
            
            if (allSupported) {
                addTestResult('浏览器兼容性', 'success', '所有必需功能都受支持');
            } else {
                addTestResult('浏览器兼容性', 'warning', '部分功能可能不受支持');
            }
            
            // 浏览器信息
            log(`浏览器: ${navigator.userAgent}`, 'info');
        }
        
        async function testAll() {
            clearLog();
            log('开始运行所有测试...', 'info');
            
            await testResourceLoading();
            await testBrowserCompatibility();
            await testApiConnection();
            
            log('所有测试完成！', 'success');
        }
        
        // 页面加载时自动运行测试
        window.addEventListener('load', function() {
            log('页面加载完成，开始自动测试...', 'info');
            setTimeout(testAll, 1000);
        });
    </script>
</body>
</html>