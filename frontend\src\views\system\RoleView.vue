<template>
  <div class="view-container">
    <div class="page-header">
      <h1>角色管理</h1>
      <el-button type="primary" @click="showDialog = true">
        <el-icon><Plus /></el-icon>
        新增
      </el-button>
    </div>
    <div class="content-area">
      <el-card>
        <div class="placeholder">
          <el-icon size="64"><Document /></el-icon>
          <h3>角色管理</h3>
          <p>功能开发中，敬请期待...</p>
        </div>
      </el-card>
    </div>
    <el-dialog v-model="showDialog" title="新增角色" width="500px">
      <el-form :model="form" label-width="100px">
        <el-form-item label="名称">
          <el-input v-model="form.name" placeholder="输入名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showDialog = false">取消</el-button>
        <el-button type="primary" @click="save">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Document } from '@element-plus/icons-vue'

const showDialog = ref(false)
const form = reactive({ name: '' })
const save = () => {
  ElMessage.success('保存成功')
  showDialog.value = false
}
</script>

<style scoped>
.view-container { padding: 20px; }
.page-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
.content-area { margin-top: 20px; }
.placeholder { text-align: center; padding: 60px 20px; color: #666; }
.placeholder h3 { margin: 20px 0 10px 0; font-size: 18px; }
.placeholder p { margin: 0; font-size: 14px; }
</style>
