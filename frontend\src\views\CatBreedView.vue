<template>
  <div>
    <el-card>
      <template #header>
        <div class="card-header">
          <span>品种列表</span>
          <el-button type="primary" @click="handleOpenDialog()">添加新品种</el-button>
        </div>
      </template>
      
      <el-table :data="breeds" v-loading="loading" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="品种名称" width="200" />
        <el-table-column prop="description" label="描述" />
        <el-table-column prop="createdAt" label="创建时间" width="180" />
        <el-table-column label="操作" width="180">
          <template #default="scope">
            <el-button size="small" @click="handleOpenDialog(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加/编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="500px">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="品种名称" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { breedApi } from '@/api';
import type { CatBreed } from '@/types';

// --- state
const breeds = ref<CatBreed[]>([]);
const loading = ref(true);
const dialogVisible = ref(false);
const form = ref<Partial<CatBreed>>({
  id: undefined,
  name: '',
  description: ''
});
const formRef = ref<any>(null);

// --- computed
const dialogTitle = computed(() => (form.value.id ? '编辑品种' : '添加新品种'));

// --- validation rules
const rules = {
  name: [{ required: true, message: '请输入品种名称', trigger: 'blur' }]
};

// --- methods
async function fetchBreeds() {
  loading.value = true;
  try {
    breeds.value = await breedApi.getAll();
  } catch (error) {
    ElMessage.error('获取品种列表失败');
    console.error(error);
  } finally {
    loading.value = false;
  }
}

function handleOpenDialog(breed: CatBreed | null = null) {
  dialogVisible.value = true;
  if (breed) {
    form.value = { ...breed };
  } else {
    form.value = { id: undefined, name: '', description: '' };
  }
}

async function handleSubmit() {
  await formRef.value.validate();
  const isEdit = !!form.value.id;

  try {
    if (isEdit && form.value.id) {
      await breedApi.update(form.value.id, form.value);
      ElMessage.success('更新成功');
    } else {
      await breedApi.create(form.value as Omit<CatBreed, 'id'>);
      ElMessage.success('添加成功');
    }
    dialogVisible.value = false;
    fetchBreeds();
  } catch (error) {
    ElMessage.error(error instanceof Error ? error.message : '操作失败');
  }
}

async function handleDelete(id: number) {
    try {
        await ElMessageBox.confirm(
            '你确定要删除这个品种吗?',
            '警告',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }
        );
        await breedApi.delete(id);
        ElMessage.success('删除成功');
        fetchBreeds();
    } catch(error) {
        if(error !== 'cancel') {
            ElMessage.error(error instanceof Error ? error.message : '删除失败');
        }
    }
}

onMounted(fetchBreeds);
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
