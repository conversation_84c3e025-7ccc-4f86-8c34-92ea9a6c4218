package com.cattery.repository;

import com.cattery.entity.Mating;
import com.cattery.entity.Cat;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 配种记录仓库接口
 */
@Repository
public interface MatingRepository extends JpaRepository<Mating, Long>, JpaSpecificationExecutor<Mating> {

    /**
     * 根据雄性猫咪查找配种记录
     */
    List<Mating> findByMaleParent(Cat maleParent);

    /**
     * 根据雌性猫咪查找配种记录
     */
    List<Mating> findByFemaleParent(Cat femaleParent);

    /**
     * 根据配种日期范围查找记录
     */
    List<Mating> findByMatingDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 根据状态查找配种记录
     */
    List<Mating> findByStatus(Mating.Status status);

    /**
     * 查找成功的配种记录
     */
    @Query("SELECT m FROM Mating m WHERE m.status = 'SUCCESS'")
    List<Mating> findSuccessfulMatings();

    /**
     * 统计指定时间范围内的配种数量
     */
    long countByMatingDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 统计成功配种数量
     */
    @Query("SELECT COUNT(m) FROM Mating m WHERE m.status = 'SUCCESS'")
    long countSuccessfulMatings();

    /**
     * 按状态统计数量
     */
    @Query("SELECT m.status, COUNT(m) FROM Mating m GROUP BY m.status")
    List<Object[]> countByStatusGrouped();

    /**
     * 查找特定猫咪的配种历史
     */
    @Query("SELECT m FROM Mating m WHERE m.maleParent = :cat OR m.femaleParent = :cat")
    List<Mating> findMatingHistoryByCat(@Param("cat") Cat cat);

    /**
     * 根据母猫ID查找配种记录（按日期排序）
     */
    List<Mating> findByFemaleParentIdOrderByMatingDateDesc(Long femaleParentId);

    /**
     * 根据公猫ID查找配种记录（按日期排序）
     */
    List<Mating> findByMaleParentIdOrderByMatingDateDesc(Long maleParentId);
}
