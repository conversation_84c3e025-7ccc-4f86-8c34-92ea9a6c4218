@echo off
echo ========================================
echo ULTIMATE Node.js Removal Solution
echo ========================================
echo.

echo Problem: Still getting "A later version of Node.js is already installed"
echo This indicates deep system-level residual information
echo.

echo ========================================
echo Method 1: Professional Uninstaller Tools
echo ========================================
echo.

echo Opening professional uninstaller tools:
echo.

echo 1. Revo Uninstaller (Free)
start https://www.revouninstaller.com/revo-uninstaller-free-download/

echo 2. IObit Uninstaller (Free)
start https://www.iobit.com/en/advanceduninstaller.php

echo 3. Gee<PERSON>tal<PERSON> (Free)
start https://geekuninstaller.com/download

echo.
echo These tools can find and remove hidden installations
echo that Windows built-in uninstaller cannot detect.
echo.

echo ========================================
echo Method 2: Manual MSI Database Cleanup
echo ========================================
echo.

echo Searching Windows Installer database...
echo.

REM Search for Node.js in MSI database
echo Checking MSI database for Node.js entries...
for /f "tokens=1,2 delims=	" %%a in ('wmic product get Name^,IdentifyingNumber /format:table ^| findstr /i node') do (
    if not "%%a"=="" if not "%%a"=="Name" (
        echo Found: %%a - %%b
        echo Attempting forced removal...
        msiexec /x %%b /quiet /norestart /l*v "%TEMP%\nodejs_uninstall.log"
        if %errorlevel% equ 0 (
            echo Successfully removed: %%a
        ) else (
            echo Failed to remove: %%a
        )
    )
)

echo.
echo ========================================
echo Method 3: Registry Deep Scan
echo ========================================
echo.

echo Performing deep registry scan for Node.js entries...
echo.

REM More comprehensive registry cleanup
reg query "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall" /s /f "Node.js" 2>nul | findstr "HKEY_" > "%TEMP%\nodejs_reg_keys.txt"
reg query "HKLM\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall" /s /f "Node.js" 2>nul | findstr "HKEY_" >> "%TEMP%\nodejs_reg_keys.txt"

if exist "%TEMP%\nodejs_reg_keys.txt" (
    echo Found Node.js registry entries:
    type "%TEMP%\nodejs_reg_keys.txt"
    echo.
    echo Removing registry entries...
    for /f "tokens=*" %%i in (%TEMP%\nodejs_reg_keys.txt) do (
        echo Removing: %%i
        reg delete "%%i" /f 2>nul
    )
    del "%TEMP%\nodejs_reg_keys.txt"
) else (
    echo No Node.js registry entries found
)

echo.
echo ========================================
echo Method 4: Windows Installer Cache Cleanup
echo ========================================
echo.

echo Cleaning Windows Installer cache...
cd /d "%WINDIR%\Installer"
if exist "%WINDIR%\Installer" (
    echo Searching for Node.js MSI files in cache...
    for %%f in (*.msi) do (
        REM Check if MSI is related to Node.js
        msiexec /qn /norestart /x "%%f" /l*v "%TEMP%\msi_check.log" 2>nul
        findstr /i "node.js\|nodejs" "%TEMP%\msi_check.log" >nul 2>&1
        if %errorlevel% equ 0 (
            echo Found Node.js MSI: %%f
            echo Removing from cache...
            del "%%f" 2>nul
        )
        del "%TEMP%\msi_check.log" 2>nul
    )
)

echo.
echo ========================================
echo Method 5: Alternative Installation Methods
echo ========================================
echo.

echo If standard installer still fails, try these alternatives:
echo.

echo Option A: Node Version Manager (NVM)
echo 1. Download NVM for Windows
start https://github.com/coreybutler/nvm-windows/releases

echo 2. Install NVM
echo 3. Use: nvm install latest
echo 4. Use: nvm use [version]
echo.

echo Option B: Chocolatey Package Manager
echo 1. Install Chocolatey
start https://chocolatey.org/install

echo 2. Use: choco install nodejs
echo.

echo Option C: Scoop Package Manager
echo 1. Install Scoop
start https://scoop.sh/

echo 2. Use: scoop install nodejs
echo.

echo Option D: Portable Node.js
echo 1. Download Node.js ZIP version
start https://nodejs.org/en/download/

echo 2. Extract to a folder (e.g., C:\nodejs)
echo 3. Add to PATH manually
echo.

echo ========================================
echo Method 6: System File Checker
echo ========================================
echo.

echo Running system file checker to fix potential corruption...
sfc /scannow

echo.
echo ========================================
echo Method 7: Windows Installer Service Reset
echo ========================================
echo.

echo Resetting Windows Installer service...
net stop msiserver
net start msiserver

echo.
echo ========================================
echo Recommendations
echo ========================================
echo.

echo Based on the persistent issue, I recommend:
echo.

echo 1. FIRST: Try Revo Uninstaller (most effective)
echo    - It can find hidden installations
echo    - Removes all traces including registry
echo.

echo 2. SECOND: Use NVM for Windows
echo    - Bypasses system installer issues
echo    - Allows multiple Node.js versions
echo    - More reliable than MSI installer
echo.

echo 3. THIRD: Try Chocolatey
echo    - Package manager approach
echo    - Often works when MSI fails
echo.

echo 4. LAST RESORT: Manual portable installation
echo    - Download ZIP version
echo    - Extract and configure manually
echo.

echo ========================================
echo Next Steps
echo ========================================
echo.

echo Please try the methods in this order:
echo 1. Download and run Revo Uninstaller
echo 2. Use it to completely remove any Node.js traces
echo 3. Restart computer
echo 4. Try installing Node.js again
echo.

echo If that fails:
echo 1. Install NVM for Windows instead
echo 2. Use NVM to install Node.js
echo.

pause
