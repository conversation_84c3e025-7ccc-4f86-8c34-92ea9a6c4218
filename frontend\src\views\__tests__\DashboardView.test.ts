import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { ElCard, ElRow, ElCol, ElButton, ElCalendar } from 'element-plus'
import DashboardView from '../DashboardView.vue'

// Mock API modules
vi.mock('@/api', () => ({
  dashboardApi: {
    getOverview: vi.fn(() => Promise.resolve({
      totalCats: 45,
      totalCustomers: 128,
      monthlyRevenue: 25000,
      totalInventory: 156,
      catsTrend: { value: 5, type: 'up' },
      customersTrend: { value: 2, type: 'up' },
      revenueTrend: { value: 12, type: 'up' },
      inventoryTrend: { value: 3, type: 'down' }
    })),
    getAlerts: vi.fn(() => Promise.resolve({
      healthAlerts: 3,
      vaccineAlerts: 2,
      breedingAlerts: 1,
      inventoryAlerts: 4
    })),
    getRecentActivities: vi.fn(() => Promise.resolve([
      {
        id: 1,
        type: 'cat',
        icon: 'Plus',
        title: '新增猫咪',
        description: '新增猫咪：小花',
        time: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2小时前
      },
      {
        id: 2,
        type: 'health',
        icon: 'Edit',
        title: '健康记录',
        description: '更新健康记录：小黑',
        time: new Date(Date.now() - 4 * 60 * 60 * 1000) // 4小时前
      }
    ])),
    getCalendarEvents: vi.fn(() => Promise.resolve([
      {
        id: 1,
        title: '疫苗接种',
        date: '2024-01-15',
        type: 'health'
      },
      {
        id: 2,
        title: '体检预约',
        date: '2024-01-16',
        type: 'health'
      }
    ]))
  }
}))

// Mock router
const mockPush = vi.fn()
vi.mock('vue-router', () => ({
  useRouter: () => ({
    push: mockPush
  })
}))

// Mock components
vi.mock('@/components/StatCard.vue', () => ({
  default: {
    name: 'StatCard',
    template: '<div class="stat-card-mock">{{ title }}: {{ value }}</div>',
    props: ['title', 'value', 'icon', 'color', 'trend', 'format']
  }
}))

vi.mock('@/components/ChartCard.vue', () => ({
  default: {
    name: 'ChartCard',
    template: '<div class="chart-card-mock">{{ type }} Chart</div>',
    props: ['type', 'data', 'options', 'height']
  }
}))

describe('DashboardView', () => {
  let wrapper: any

  beforeEach(() => {
    wrapper = mount(DashboardView, {
      global: {
        components: {
          ElCard,
          ElRow,
          ElCol,
          ElButton,
          ElCalendar
        },
        stubs: {
          'el-date-picker': true,
          'el-badge': true,
          'el-icon': true,
          'el-skeleton': true,
          'StatCard': true,
          'ChartCard': true
        }
      }
    })
  })

  it('renders properly', () => {
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.dashboard').exists()).toBe(true)
  })

  it('displays dashboard header correctly', () => {
    const header = wrapper.find('.dashboard-header')
    expect(header.exists()).toBe(true)
    expect(header.find('h1').text()).toBe('仪表盘')
    expect(header.find('p').text()).toBe('猫舍管理系统数据概览')
  })

  it('displays alerts section', () => {
    const alertsSection = wrapper.find('.alerts-section')
    expect(alertsSection.exists()).toBe(true)
    
    const alertItems = wrapper.findAll('.alert-item')
    expect(alertItems.length).toBe(4) // 健康、疫苗、繁育、库存提醒
  })

  it('displays statistics cards', () => {
    const statsSection = wrapper.find('.stats-section')
    expect(statsSection.exists()).toBe(true)
    
    // 检查是否有4个统计卡片
    const statCards = wrapper.findAllComponents({ name: 'StatCard' })
    expect(statCards.length).toBe(4)
  })

  it('displays charts section', () => {
    const chartsSection = wrapper.find('.charts-section')
    expect(chartsSection.exists()).toBe(true)
    
    // 检查是否有图表组件
    const chartCards = wrapper.findAllComponents({ name: 'ChartCard' })
    expect(chartCards.length).toBe(2) // 收入趋势图和品种分布图
  })

  it('displays activity section', () => {
    const activitySection = wrapper.find('.activity-section')
    expect(activitySection.exists()).toBe(true)
    
    const activityCard = wrapper.find('.activity-card')
    expect(activityCard.exists()).toBe(true)
    
    const calendarCard = wrapper.find('.calendar-card')
    expect(calendarCard.exists()).toBe(true)
  })

  it('displays quick actions', () => {
    const quickActionsSection = wrapper.find('.quick-actions-section')
    expect(quickActionsSection.exists()).toBe(true)
    
    const quickActions = wrapper.find('.quick-actions')
    expect(quickActions.exists()).toBe(true)
    
    const actionButtons = quickActions.findAll('.el-button')
    expect(actionButtons.length).toBe(5) // 5个快速操作按钮
  })

  it('handles refresh data correctly', async () => {
    const refreshButton = wrapper.find('.header-actions .el-button')
    expect(refreshButton.exists()).toBe(true)
    
    await refreshButton.trigger('click')
    
    // 验证是否调用了刷新方法
    expect(wrapper.vm.loading).toBe(false) // 加载完成后应该为false
  })

  it('navigates to correct routes when clicking alerts', async () => {
    const healthAlert = wrapper.find('.alert-item.health-alert')
    await healthAlert.trigger('click')
    
    expect(mockPush).toHaveBeenCalledWith('/health/reminders')
  })

  it('navigates to correct routes when clicking quick actions', async () => {
    const quickActionButtons = wrapper.findAll('.quick-actions .el-button')
    
    // 点击新增猫咪按钮
    await quickActionButtons[0].trigger('click')
    expect(mockPush).toHaveBeenCalledWith('/cats/create')
  })

  it('calculates total alerts correctly', async () => {
    // 等待组件加载完成
    await wrapper.vm.$nextTick()
    
    // 模拟设置提醒数据
    wrapper.vm.alerts = {
      healthAlerts: 3,
      vaccineAlerts: 2,
      breedingAlerts: 1,
      inventoryAlerts: 4
    }
    
    await wrapper.vm.$nextTick()
    
    expect(wrapper.vm.totalAlerts).toBe(10)
  })

  it('formats time correctly', () => {
    const now = new Date()
    const twoHoursAgo = new Date(now.getTime() - 2 * 60 * 60 * 1000)
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)
    
    expect(wrapper.vm.formatTime(twoHoursAgo)).toBe('2小时前')
    expect(wrapper.vm.formatTime(oneDayAgo)).toBe('1天前')
  })

  it('filters calendar events by date correctly', () => {
    wrapper.vm.calendarEvents = [
      { id: 1, title: '疫苗接种', date: '2024-01-15', type: 'health' },
      { id: 2, title: '体检预约', date: '2024-01-15', type: 'health' },
      { id: 3, title: '配种计划', date: '2024-01-16', type: 'breeding' }
    ]
    
    const eventsFor15th = wrapper.vm.getEventsForDate('2024-01-15')
    expect(eventsFor15th.length).toBe(2)
    
    const eventsFor16th = wrapper.vm.getEventsForDate('2024-01-16')
    expect(eventsFor16th.length).toBe(1)
  })

  it('handles date range change correctly', async () => {
    const startDate = new Date('2024-01-01')
    const endDate = new Date('2024-01-31')
    
    wrapper.vm.handleDateRangeChange([startDate, endDate])
    
    expect(wrapper.vm.dateRange).toEqual([startDate, endDate])
  })

  it('loads dashboard data on mount', async () => {
    // 验证组件挂载时是否调用了数据获取方法
    await wrapper.vm.$nextTick()
    
    expect(wrapper.vm.dashboardData.totalCats).toBeGreaterThanOrEqual(0)
    expect(wrapper.vm.dashboardData.totalCustomers).toBeGreaterThanOrEqual(0)
  })

  it('displays loading state correctly', async () => {
    wrapper.vm.loading = true
    await wrapper.vm.$nextTick()
    
    const refreshButton = wrapper.find('.header-actions .el-button')
    expect(refreshButton.attributes('loading')).toBeDefined()
  })

  it('handles API errors gracefully', async () => {
    // 模拟API错误
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    
    // 这里可以添加错误处理的测试逻辑
    
    consoleSpy.mockRestore()
  })
})
