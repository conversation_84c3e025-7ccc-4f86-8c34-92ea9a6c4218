import { describe, it, expect, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { ElCard, ElTag, ElButton } from 'element-plus'
import type { Cat } from '@/types'

// 创建一个简单的猫咪卡片组件用于测试
const CatCard = {
  name: 'CatCard',
  props: {
    cat: {
      type: Object as () => Cat,
      required: true
    }
  },
  emits: ['edit', 'delete'],
  template: `
    <el-card class="cat-card">
      <template #header>
        <div class="card-header">
          <span>{{ cat.name }}</span>
          <div>
            <el-button size="small" @click="$emit('edit', cat)">编辑</el-button>
            <el-button size="small" type="danger" @click="$emit('delete', cat.id)">删除</el-button>
          </div>
        </div>
      </template>
      <div class="cat-info">
        <p><strong>品种:</strong> {{ cat.breedName || '未知' }}</p>
        <p><strong>性别:</strong> {{ cat.gender === 'MALE' ? '雄性' : '雌性' }}</p>
        <p><strong>颜色:</strong> {{ cat.color || '未知' }}</p>
        <p><strong>状态:</strong> 
          <el-tag :type="getStatusType(cat.status)">
            {{ getStatusText(cat.status) }}
          </el-tag>
        </p>
        <p v-if="cat.description"><strong>描述:</strong> {{ cat.description }}</p>
      </div>
    </el-card>
  `,
  methods: {
    getStatusType(status: string) {
      const statusMap: Record<string, string> = {
        'PENDING_ADOPTION': '',
        'RESERVED': 'warning',
        'ADOPTED': 'success',
        'NOT_FOR_SALE': 'info'
      }
      return statusMap[status] || ''
    },
    getStatusText(status: string) {
      const statusMap: Record<string, string> = {
        'PENDING_ADOPTION': '待领养',
        'RESERVED': '已预定',
        'ADOPTED': '已领养',
        'NOT_FOR_SALE': '非卖品'
      }
      return statusMap[status] || status
    }
  }
}

describe('CatCard', () => {
  let mockCat: Cat

  beforeEach(() => {
    mockCat = {
      id: 1,
      name: '测试猫咪',
      breedId: 1,
      breedName: '波斯猫',
      gender: 'FEMALE',
      dateOfBirth: '2023-01-01',
      color: '白色',
      status: 'PENDING_ADOPTION',
      description: '一只可爱的测试猫咪'
    }
  })

  it('renders cat information correctly', () => {
    const wrapper = mount(CatCard, {
      props: { cat: mockCat },
      global: {
        components: {
          ElCard,
          ElTag,
          ElButton
        }
      }
    })

    expect(wrapper.text()).toContain('测试猫咪')
    expect(wrapper.text()).toContain('波斯猫')
    expect(wrapper.text()).toContain('雌性')
    expect(wrapper.text()).toContain('白色')
    expect(wrapper.text()).toContain('待领养')
    expect(wrapper.text()).toContain('一只可爱的测试猫咪')
  })

  it('displays correct gender text', () => {
    const wrapper = mount(CatCard, {
      props: { cat: mockCat },
      global: {
        components: {
          ElCard,
          ElTag,
          ElButton
        }
      }
    })

    expect(wrapper.text()).toContain('雌性')

    // 测试雄性
    const maleCat = { ...mockCat, gender: 'MALE' as const }
    await wrapper.setProps({ cat: maleCat })
    expect(wrapper.text()).toContain('雄性')
  })

  it('displays correct status tag', () => {
    const wrapper = mount(CatCard, {
      props: { cat: mockCat },
      global: {
        components: {
          ElCard,
          ElTag,
          ElButton
        }
      }
    })

    const tag = wrapper.findComponent(ElTag)
    expect(tag.exists()).toBe(true)
    expect(tag.text()).toBe('待领养')
  })

  it('emits edit event when edit button is clicked', async () => {
    const wrapper = mount(CatCard, {
      props: { cat: mockCat },
      global: {
        components: {
          ElCard,
          ElTag,
          ElButton
        }
      }
    })

    const editButton = wrapper.findAll('button')[0]
    await editButton.trigger('click')

    expect(wrapper.emitted('edit')).toBeTruthy()
    expect(wrapper.emitted('edit')?.[0]).toEqual([mockCat])
  })

  it('emits delete event when delete button is clicked', async () => {
    const wrapper = mount(CatCard, {
      props: { cat: mockCat },
      global: {
        components: {
          ElCard,
          ElTag,
          ElButton
        }
      }
    })

    const deleteButton = wrapper.findAll('button')[1]
    await deleteButton.trigger('click')

    expect(wrapper.emitted('delete')).toBeTruthy()
    expect(wrapper.emitted('delete')?.[0]).toEqual([mockCat.id])
  })

  it('handles missing optional fields gracefully', () => {
    const incompleteCat: Cat = {
      id: 2,
      name: '不完整猫咪',
      gender: 'MALE',
      status: 'ADOPTED'
    }

    const wrapper = mount(CatCard, {
      props: { cat: incompleteCat },
      global: {
        components: {
          ElCard,
          ElTag,
          ElButton
        }
      }
    })

    expect(wrapper.text()).toContain('不完整猫咪')
    expect(wrapper.text()).toContain('未知') // 品种和颜色应该显示为"未知"
    expect(wrapper.text()).toContain('雄性')
    expect(wrapper.text()).toContain('已领养')
    expect(wrapper.text()).not.toContain('描述:') // 没有描述时不应该显示描述字段
  })
})
