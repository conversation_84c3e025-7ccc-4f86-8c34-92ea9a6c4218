<template>
  <div class="pregnancy-tracking">
    <div class="page-header">
      <h1>怀孕跟踪</h1>
      <p>母猫怀孕期管理和跟踪</p>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          记录怀孕
        </el-button>
      </div>
    </div>

    <!-- 怀孕统计 -->
    <el-row :gutter="20" class="stats-section">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon pregnant">
              <el-icon><Female /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ pregnancyStats.totalPregnant }}</div>
              <div class="stat-label">怀孕中</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon due-soon">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ pregnancyStats.dueSoon }}</div>
              <div class="stat-label">即将分娩</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon overdue">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ pregnancyStats.overdue }}</div>
              <div class="stat-label">超期未产</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon avg-gestation">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ pregnancyStats.avgGestationDays }}</div>
              <div class="stat-label">平均孕期(天)</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 怀孕列表 -->
    <el-card class="pregnancy-list-card">
      <template #header>
        <div class="card-header">
          <span>怀孕记录</span>
          <el-button-group>
            <el-button size="small" :type="statusFilter === 'all' ? 'primary' : ''" @click="statusFilter = 'all'">全部</el-button>
            <el-button size="small" :type="statusFilter === 'confirmed' ? 'primary' : ''" @click="statusFilter = 'confirmed'">已确认</el-button>
            <el-button size="small" :type="statusFilter === 'monitoring' ? 'primary' : ''" @click="statusFilter = 'monitoring'">监测中</el-button>
            <el-button size="small" :type="statusFilter === 'due_soon' ? 'primary' : ''" @click="statusFilter = 'due_soon'">即将分娩</el-button>
          </el-button-group>
        </div>
      </template>

      <div class="pregnancy-grid">
        <div
          v-for="pregnancy in filteredPregnancies"
          :key="pregnancy.id"
          class="pregnancy-card"
          @click="viewPregnancyDetail(pregnancy)"
        >
          <div class="pregnancy-header">
            <div class="cat-info">
              <el-avatar :src="pregnancy.motherPhoto" :size="50">
                {{ pregnancy.motherName.charAt(0) }}
              </el-avatar>
              <div class="cat-details">
                <h3>{{ pregnancy.motherName }}</h3>
                <p>{{ pregnancy.breedName }}</p>
                <el-tag :type="getStatusType(pregnancy.status)" size="small">
                  {{ getStatusText(pregnancy.status) }}
                </el-tag>
              </div>
            </div>
            <div class="pregnancy-actions">
              <el-dropdown @command="handleAction">
                <el-button size="small" circle>
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="`edit_${pregnancy.id}`">编辑</el-dropdown-item>
                    <el-dropdown-item :command="`checkup_${pregnancy.id}`">产检记录</el-dropdown-item>
                    <el-dropdown-item :command="`birth_${pregnancy.id}`" v-if="pregnancy.status === 'due_soon'">记录分娩</el-dropdown-item>
                    <el-dropdown-item :command="`delete_${pregnancy.id}`" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>

          <div class="pregnancy-progress">
            <div class="progress-header">
              <span class="gestation-days">第 {{ pregnancy.gestationDays }} 天</span>
              <span class="due-date">
                {{ pregnancy.remainingDays > 0 ? `${pregnancy.remainingDays}天后预产` : '已到预产期' }}
              </span>
            </div>
            <el-progress
              :percentage="pregnancy.progressPercentage"
              :color="getProgressColor(pregnancy.progressPercentage, pregnancy.remainingDays)"
              :stroke-width="12"
              :show-text="false"
            />
            <div class="progress-details">
              <span>配种日期: {{ formatDate(pregnancy.matingDate) }}</span>
              <span>预产期: {{ formatDate(pregnancy.expectedDueDate) }}</span>
            </div>
          </div>

          <div class="pregnancy-info">
            <div class="info-item">
              <span class="label">父猫:</span>
              <span class="value">{{ pregnancy.fatherName }}</span>
            </div>
            <div class="info-item">
              <span class="label">体重变化:</span>
              <span class="value">
                {{ pregnancy.weightGain > 0 ? '+' : '' }}{{ pregnancy.weightGain }}kg
              </span>
            </div>
            <div class="info-item">
              <span class="label">最近检查:</span>
              <span class="value">{{ pregnancy.lastCheckup || '无记录' }}</span>
            </div>
          </div>

          <div class="pregnancy-alerts" v-if="pregnancy.alerts && pregnancy.alerts.length > 0">
            <el-alert
              v-for="alert in pregnancy.alerts"
              :key="alert.id"
              :title="alert.message"
              :type="alert.type"
              size="small"
              :closable="false"
            />
          </div>
        </div>
      </div>

      <el-empty v-if="filteredPregnancies.length === 0" description="暂无怀孕记录" />
    </el-card>

    <!-- 新增/编辑怀孕记录对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingPregnancy ? '编辑怀孕记录' : '记录怀孕'"
      width="600px"
    >
      <PregnancyForm
        :pregnancy="editingPregnancy"
        @submit="handleSubmit"
        @cancel="handleCancel"
      />
    </el-dialog>

    <!-- 怀孕详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="怀孕详情" width="900px">
      <PregnancyDetail
        v-if="selectedPregnancy"
        :pregnancy="selectedPregnancy"
        @edit="editPregnancy"
        @add-checkup="addCheckup"
        @record-birth="recordBirth"
      />
    </el-dialog>

    <!-- 产检记录对话框 -->
    <el-dialog v-model="showCheckupDialog" title="产检记录" width="700px">
      <CheckupForm
        v-if="selectedPregnancy"
        :pregnancy-id="selectedPregnancy.id"
        @submit="handleCheckupSubmit"
        @cancel="showCheckupDialog = false"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Female, Clock, Warning, TrendCharts, MoreFilled
} from '@element-plus/icons-vue'
import { breedingApi } from '@/api'
import PregnancyForm from '@/components/breeding/PregnancyForm.vue'
import PregnancyDetail from '@/components/breeding/PregnancyDetail.vue'
import CheckupForm from '@/components/breeding/CheckupForm.vue'
import type { Pregnancy, PregnancyStats } from '@/types'

const loading = ref(false)
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const showCheckupDialog = ref(false)
const editingPregnancy = ref<Pregnancy | null>(null)
const selectedPregnancy = ref<Pregnancy | null>(null)
const statusFilter = ref('all')

const pregnancyStats = ref<PregnancyStats>({
  totalPregnant: 0,
  dueSoon: 0,
  overdue: 0,
  avgGestationDays: 0
})

const pregnancies = ref<Pregnancy[]>([])

const filteredPregnancies = computed(() => {
  if (statusFilter.value === 'all') {
    return pregnancies.value
  }
  return pregnancies.value.filter(p => p.status === statusFilter.value)
})

async function fetchPregnancies() {
  try {
    loading.value = true
    const [pregnanciesData, statsData] = await Promise.all([
      breedingApi.getPregnancies(),
      breedingApi.getPregnancyStats()
    ])
    
    pregnancies.value = pregnanciesData
    pregnancyStats.value = statsData
  } catch (error) {
    ElMessage.error('获取怀孕记录失败')
  } finally {
    loading.value = false
  }
}

function viewPregnancyDetail(pregnancy: Pregnancy) {
  selectedPregnancy.value = pregnancy
  showDetailDialog.value = true
}

function editPregnancy(pregnancy: Pregnancy) {
  editingPregnancy.value = { ...pregnancy }
  showCreateDialog.value = true
}

function addCheckup(pregnancy: Pregnancy) {
  selectedPregnancy.value = pregnancy
  showCheckupDialog.value = true
}

function recordBirth(pregnancy: Pregnancy) {
  // 跳转到分娩记录页面
  this.$router.push(`/breeding/birth/create?pregnancyId=${pregnancy.id}`)
}

async function handleAction(command: string) {
  const [action, id] = command.split('_')
  const pregnancyId = parseInt(id)
  const pregnancy = pregnancies.value.find(p => p.id === pregnancyId)
  
  if (!pregnancy) return
  
  switch (action) {
    case 'edit':
      editPregnancy(pregnancy)
      break
    case 'checkup':
      addCheckup(pregnancy)
      break
    case 'birth':
      recordBirth(pregnancy)
      break
    case 'delete':
      await deletePregnancy(pregnancyId)
      break
  }
}

async function deletePregnancy(id: number) {
  try {
    await ElMessageBox.confirm('确定要删除这条怀孕记录吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await breedingApi.deletePregnancy(id)
    ElMessage.success('删除成功')
    fetchPregnancies()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

async function handleSubmit(pregnancyData: Partial<Pregnancy>) {
  try {
    if (editingPregnancy.value) {
      await breedingApi.updatePregnancy(editingPregnancy.value.id, pregnancyData)
      ElMessage.success('更新成功')
    } else {
      await breedingApi.createPregnancy(pregnancyData)
      ElMessage.success('创建成功')
    }
    
    showCreateDialog.value = false
    editingPregnancy.value = null
    fetchPregnancies()
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

function handleCancel() {
  showCreateDialog.value = false
  editingPregnancy.value = null
}

async function handleCheckupSubmit(checkupData: any) {
  try {
    await breedingApi.createCheckup(checkupData)
    ElMessage.success('产检记录添加成功')
    showCheckupDialog.value = false
    fetchPregnancies()
  } catch (error) {
    ElMessage.error('添加产检记录失败')
  }
}

function formatDate(date: string | Date) {
  return new Date(date).toLocaleDateString('zh-CN')
}

function getStatusType(status: string) {
  const typeMap: Record<string, string> = {
    'confirmed': 'success',
    'monitoring': 'warning',
    'due_soon': 'danger',
    'suspected': 'info'
  }
  return typeMap[status] || 'info'
}

function getStatusText(status: string) {
  const textMap: Record<string, string> = {
    'confirmed': '已确认',
    'monitoring': '监测中',
    'due_soon': '即将分娩',
    'suspected': '疑似怀孕'
  }
  return textMap[status] || status
}

function getProgressColor(percentage: number, remainingDays: number) {
  if (remainingDays < 0) return '#F56C6C' // 超期
  if (remainingDays <= 7) return '#E6A23C' // 即将分娩
  if (percentage < 50) return '#409EFF' // 早期
  return '#67C23A' // 正常
}

onMounted(() => {
  fetchPregnancies()
})
</script>

<style scoped>
.pregnancy-tracking {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #303133;
}

.page-header p {
  margin: 5px 0 0 0;
  color: #909399;
}

.stats-section {
  margin-bottom: 20px;
}

.stat-card {
  height: 120px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stat-icon.pregnant {
  background: #E6A23C;
}

.stat-icon.due-soon {
  background: #F56C6C;
}

.stat-icon.overdue {
  background: #F56C6C;
}

.stat-icon.avg-gestation {
  background: #409EFF;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  color: #909399;
  margin-top: 5px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pregnancy-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.pregnancy-card {
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pregnancy-card:hover {
  border-color: #409EFF;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.1);
  transform: translateY(-2px);
}

.pregnancy-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.cat-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.cat-details h3 {
  margin: 0 0 5px 0;
  color: #303133;
}

.cat-details p {
  margin: 0 0 8px 0;
  color: #909399;
  font-size: 14px;
}

.pregnancy-progress {
  margin-bottom: 15px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.gestation-days {
  font-weight: bold;
  color: #303133;
}

.due-date {
  color: #606266;
}

.progress-details {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

.pregnancy-info {
  margin-bottom: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 14px;
}

.info-item .label {
  color: #606266;
}

.info-item .value {
  color: #303133;
  font-weight: 500;
}

.pregnancy-alerts {
  margin-top: 10px;
}

.pregnancy-alerts .el-alert {
  margin-bottom: 5px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .pregnancy-grid {
    grid-template-columns: 1fr;
  }
}
</style>
