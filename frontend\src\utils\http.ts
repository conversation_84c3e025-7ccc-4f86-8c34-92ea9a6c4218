import { ElMessage } from 'element-plus'

// API响应接口
export interface ApiResponse<T = any> {
  success: boolean
  message?: string
  data?: T
  timestamp?: string
  path?: string
}

// HTTP请求配置
interface RequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  headers?: Record<string, string>
  body?: any
  timeout?: number
}

// 基础配置
const BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api'
const TIMEOUT = parseInt(import.meta.env.VITE_API_TIMEOUT) || 10000

class HttpClient {
  private baseURL: string
  private timeout: number

  constructor(baseURL: string = BASE_URL, timeout: number = TIMEOUT) {
    this.baseURL = baseURL
    this.timeout = timeout
  }

  private async request<T>(url: string, config: RequestConfig = {}): Promise<T> {
    const {
      method = 'GET',
      headers = {},
      body,
      timeout = this.timeout
    } = config

    // 构建完整URL
    const fullUrl = url.startsWith('http') ? url : `${this.baseURL}${url}`

    // 默认headers
    const defaultHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
      ...headers
    }

    // 添加JWT令牌
    const token = localStorage.getItem('token')
    if (token) {
      defaultHeaders['Authorization'] = `Bearer ${token}`
    }

    // 构建请求配置
    const requestConfig: RequestInit = {
      method,
      headers: defaultHeaders,
      body: body ? JSON.stringify(body) : undefined
    }

    try {
      // 创建超时控制
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), timeout)

      const response = await fetch(fullUrl, {
        ...requestConfig,
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      // 检查响应状态
      if (!response.ok) {
        // 处理401未授权错误
        if (response.status === 401) {
          // 清除本地存储的认证信息
          localStorage.removeItem('token')
          localStorage.removeItem('userInfo')
          localStorage.removeItem('tokenExpiration')

          // 重定向到登录页
          if (window.location.pathname !== '/login') {
            window.location.href = '/login'
          }

          throw new Error('登录已过期，请重新登录')
        }

        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result: ApiResponse<T> = await response.json()

      // 检查业务状态
      if (!result.success) {
        throw new Error(result.message || '请求失败')
      }

      return result.data as T
    } catch (error) {
      // 错误处理
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          ElMessage.error('请求超时，请稍后重试')
          throw new Error('请求超时')
        } else {
          ElMessage.error(error.message)
          throw error
        }
      }
      throw new Error('未知错误')
    }
  }

  // GET请求
  get<T>(url: string, headers?: Record<string, string>): Promise<T> {
    return this.request<T>(url, { method: 'GET', headers })
  }

  // POST请求
  post<T>(url: string, data?: any, headers?: Record<string, string>): Promise<T> {
    return this.request<T>(url, { method: 'POST', body: data, headers })
  }

  // PUT请求
  put<T>(url: string, data?: any, headers?: Record<string, string>): Promise<T> {
    return this.request<T>(url, { method: 'PUT', body: data, headers })
  }

  // DELETE请求
  delete<T>(url: string, headers?: Record<string, string>): Promise<T> {
    return this.request<T>(url, { method: 'DELETE', headers })
  }
}

// 创建默认实例
export const http = new HttpClient()

// 导出类以便创建自定义实例
export { HttpClient }
