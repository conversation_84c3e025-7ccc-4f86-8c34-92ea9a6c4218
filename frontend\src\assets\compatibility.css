/* 现代浏览器兼容性样式 - 使用标准化方法 */

/* 文本大小调整 - 添加现代浏览器支持 */
html {
  -webkit-text-size-adjust: 100%; /* WebKit 内核 */
  text-size-adjust: 100%; /* Chrome 54+, Chrome Android 54+, Edge 79+ */
  /* 移除不支持的 -ms-text-size-adjust */
}

/* 表单元素外观 - 添加标准属性支持现代浏览器 */
input, button, select, textarea {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none; /* 添加标准属性支持现代浏览器 */
}

/* 滚动条样式 - 添加 Safari 兼容方案 */
.scrollable {
  scrollbar-width: thin; /* Firefox 支持 */
  scrollbar-color: #dcdfe6 #ffffff;
}

/* Safari 使用 webkit 前缀方案 */
.scrollable::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.scrollable::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.scrollable::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.scrollable::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* 通用滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #ffffff;
}

::-webkit-scrollbar-thumb {
  background: #dcdfe6;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #e4e7ed;
}

/* 遮罩属性 - 正确顺序：前缀版本在前 */
.mask-element {
  /* 前缀版本在前 */
  -webkit-mask: url('mask.svg') no-repeat center;
  mask: url('mask.svg') no-repeat center;
  -webkit-mask-size: cover;
  mask-size: cover;
}

/* 性能优化 - 避免在动画中使用会触发布局的属性 */
@keyframes slideIn {
  from {
    /* 使用 transform 而不是 left，避免触发布局 */
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    /* 使用 transform 而不是 left，避免触发布局 */
    transform: translateX(100%);
    opacity: 0;
  }
}

/* 高性能动画类 */
.slide-in {
  animation: slideIn 0.3s ease-out;
}

.slide-out {
  animation: slideOut 0.3s ease-in;
}

/* GPU 加速的动画 */
.gpu-accelerated {
  will-change: transform, opacity;
  transform: translateZ(0); /* 强制GPU加速 */
}

/* 现代CSS特性检测 */
@supports (display: grid) {
  .grid-layout {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }
}

@supports (display: flex) {
  .flex-layout {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
  }
}

/* 容器查询支持 */
@supports (container-type: inline-size) {
  .container-query {
    container-type: inline-size;
  }
  
  @container (min-width: 300px) {
    .responsive-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
    }
  }
}

/* 用户偏好设置支持 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

@media (prefers-color-scheme: dark) {
  :root {
    --bg-color: #1a1a1a;
    --text-color: #ffffff;
    --border-color: #333333;
  }
}

@media (prefers-contrast: high) {
  :root {
    --bg-color: #ffffff;
    --text-color: #000000;
    --border-color: #000000;
  }
}

/* 打印样式优化 */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  a,
  a:visited {
    text-decoration: underline;
  }
  
  .no-print {
    display: none !important;
  }
  
  .page-break {
    page-break-after: always;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  /* 触摸设备上增大可点击区域 */
  button,
  .clickable {
    min-height: 44px;
    min-width: 44px;
  }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* 高分辨率图片 - Edge 支持 */
  .high-res-image {
    image-rendering: -webkit-optimize-contrast; /* Edge 79+ 支持 */
    image-rendering: crisp-edges; /* 现代浏览器 */
  }
}

/* 安全区域支持 (iPhone X等) */
@supports (padding: max(0px)) {
  .safe-area {
    padding-left: max(1rem, env(safe-area-inset-left));
    padding-right: max(1rem, env(safe-area-inset-right));
    padding-top: max(1rem, env(safe-area-inset-top));
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }
}

/* 现代字体渲染 */
body {
  font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
  font-variant-ligatures: common-ligatures;
  text-rendering: optimizeLegibility;
}

/* 现代焦点样式 */
:focus-visible {
  outline: 2px solid #409eff;
  outline-offset: 2px;
}

/* 移除旧的焦点样式 */
:focus:not(:focus-visible) {
  outline: none;
}
