# 开发环境配置

# 应用标题
VITE_APP_TITLE=猫舍管理系统

# 应用版本
VITE_APP_VERSION=1.0.0

# API基础URL
VITE_API_BASE_URL=http://localhost:8080

# 上传文件基础URL
VITE_UPLOAD_BASE_URL=http://localhost:8080

# 是否启用Mock数据
VITE_USE_MOCK=false

# 是否启用开发工具
VITE_DEV_TOOLS=true

# 是否启用控制台日志
VITE_ENABLE_CONSOLE=true

# 是否启用错误监控
VITE_ENABLE_ERROR_MONITOR=false

# WebSocket地址
VITE_WS_URL=ws://localhost:8080/ws

# 地图API密钥（如果需要）
VITE_MAP_API_KEY=

# 第三方服务配置
VITE_THIRD_PARTY_SERVICES=true

# 缓存配置
VITE_CACHE_ENABLED=true
VITE_CACHE_DURATION=300000

# 主题配置
VITE_DEFAULT_THEME=light
VITE_DEFAULT_LANGUAGE=zh-CN

# 功能开关
VITE_FEATURE_NOTIFICATIONS=true
VITE_FEATURE_REAL_TIME=true
VITE_FEATURE_ANALYTICS=true
VITE_FEATURE_EXPORT=true
VITE_FEATURE_IMPORT=true

# 调试配置
VITE_DEBUG_MODE=true
VITE_LOG_LEVEL=debug
