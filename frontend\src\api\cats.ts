import request from '@/utils/request'

export interface Cat {
  id?: number
  name: string
  breed: string
  color: string
  gender: 'MALE' | 'FEMALE'
  birthDate: string
  weight?: number
  microchipId?: string
  isVaccinated: boolean
  isSterilized: boolean
  status: 'AVAILABLE' | 'RESERVED' | 'SOLD' | 'BREEDING' | 'MEDICAL' | 'RETIRED'
  price?: number
  description?: string
  notes?: string
  fatherInfo?: string
  motherInfo?: string
  primaryPhoto?: string
  createdTime?: string
  updatedTime?: string
}

export interface CatQuery {
  page?: number
  size?: number
  name?: string
  breed?: string
  status?: string
  gender?: string
  minPrice?: number
  maxPrice?: number
}

export interface CatPhoto {
  id?: number
  catId: number
  photoUrl: string
  createdTime?: string
}

// 获取所有猫咪
export const getCats = (params?: CatQuery) => {
  return request.get('/cats', { params })
}

// 根据ID获取猫咪
export const getCatById = (id: number) => {
  return request.get(`/cats/${id}`)
}

// 创建猫咪
export const createCat = (data: Omit<Cat, 'id' | 'createdTime' | 'updatedTime'>) => {
  return request.post('/cats', data)
}

// 更新猫咪
export const updateCat = (id: number, data: Partial<Cat>) => {
  return request.put(`/cats/${id}`, data)
}

// 删除猫咪
export const deleteCat = (id: number) => {
  return request.delete(`/cats/${id}`)
}

// 获取猫咪照片
export const getCatPhotos = (catId: number) => {
  return request.get(`/cats/${catId}/photos`)
}

// 上传猫咪照片
export const uploadCatPhoto = (catId: number, data: FormData) => {
  return request.post(`/cats/${catId}/photos`, data, {
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

// 删除猫咪照片
export const deleteCatPhoto = (catId: number, photoId: number) => {
  return request.delete(`/cats/${catId}/photos/${photoId}`)
}

// 获取猫咪统计信息
export const getCatStats = () => {
  return request.get('/cats/stats')
}
