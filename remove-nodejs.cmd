@echo off
echo ========================================
echo Complete Node.js Removal Tool
echo ========================================
echo.

echo Step 1: Finding Node.js installation
echo.

echo Current Node.js path:
where node 2>nul
if %errorlevel% equ 0 (
    echo Node.js found
) else (
    echo Node.js not found in PATH
)

echo Current npm path:
where npm 2>nul
if %errorlevel% equ 0 (
    echo npm found
) else (
    echo npm not found in PATH
)

echo.
echo Step 2: Checking installation directories
echo.

if exist "C:\Program Files\nodejs\" (
    echo Found: C:\Program Files\nodejs\
    set FOUND1=1
) else (
    echo Not found: C:\Program Files\nodejs\
)

if exist "C:\Program Files (x86)\nodejs\" (
    echo Found: C:\Program Files (x86)\nodejs\
    set FOUND2=1
) else (
    echo Not found: C:\Program Files (x86)\nodejs\
)

if exist "%USERPROFILE%\nodejs\" (
    echo Found: %USERPROFILE%\nodejs\
    set FOUND3=1
) else (
    echo Not found: %USERPROFILE%\nodejs\
)

if exist "%APPDATA%\npm\" (
    echo Found: %APPDATA%\npm\
    set FOUND4=1
) else (
    echo Not found: %APPDATA%\npm\
)

if exist "%APPDATA%\npm-cache\" (
    echo Found: %APPDATA%\npm-cache\
    set FOUND5=1
) else (
    echo Not found: %APPDATA%\npm-cache\
)

if exist "D:\nodejs\" (
    echo Found: D:\nodejs\
    set FOUND6=1
) else (
    echo Not found: D:\nodejs\
)

if exist "D:\软件\nodejs\" (
    echo Found: D:\软件\nodejs\
    set FOUND7=1
) else (
    echo Not found: D:\软件\nodejs\
)

if exist "D:\软件\node_modules\" (
    echo Found PROBLEM DIR: D:\软件\node_modules\
    set FOUND8=1
) else (
    echo Not found: D:\软件\node_modules\
)

echo.
echo Step 3: Removing directories
echo WARNING: About to delete all Node.js files
echo Press any key to continue or Ctrl+C to cancel...
pause >nul

echo Removing directories...

if defined FOUND1 (
    echo Removing: C:\Program Files\nodejs\
    rmdir /s /q "C:\Program Files\nodejs\" 2>nul
)

if defined FOUND2 (
    echo Removing: C:\Program Files (x86)\nodejs\
    rmdir /s /q "C:\Program Files (x86)\nodejs\" 2>nul
)

if defined FOUND3 (
    echo Removing: %USERPROFILE%\nodejs\
    rmdir /s /q "%USERPROFILE%\nodejs\" 2>nul
)

if defined FOUND4 (
    echo Removing: %APPDATA%\npm\
    rmdir /s /q "%APPDATA%\npm\" 2>nul
)

if defined FOUND5 (
    echo Removing: %APPDATA%\npm-cache\
    rmdir /s /q "%APPDATA%\npm-cache\" 2>nul
)

if defined FOUND6 (
    echo Removing: D:\nodejs\
    rmdir /s /q "D:\nodejs\" 2>nul
)

if defined FOUND7 (
    echo Removing: D:\软件\nodejs\
    rmdir /s /q "D:\软件\nodejs\" 2>nul
)

if defined FOUND8 (
    echo Removing PROBLEM DIR: D:\软件\node_modules\
    rmdir /s /q "D:\软件\node_modules\" 2>nul
)

echo.
echo Step 4: Opening environment variables
echo Please clean PATH variable manually
start sysdm.cpl

echo.
echo Step 5: Verification
echo.

echo Testing Node.js...
node --version 2>nul
if %errorlevel% equ 0 (
    echo ERROR: Node.js still exists!
    node --version
    where node
) else (
    echo SUCCESS: Node.js removed!
)

echo Testing npm...
npm --version 2>nul
if %errorlevel% equ 0 (
    echo ERROR: npm still exists!
) else (
    echo SUCCESS: npm removed!
)

echo.
echo ========================================
echo Removal Complete!
echo ========================================
echo.

node --version 2>nul
npm --version 2>nul
if %errorlevel% neq 0 (
    echo SUCCESS: Node.js completely removed!
    echo.
    echo Next steps:
    echo 1. Restart computer (recommended)
    echo 2. Download new Node.js from https://nodejs.org
    echo 3. Install to C:\Program Files\nodejs\
    echo 4. Check "Add to PATH" option
) else (
    echo WARNING: Removal may be incomplete
    echo Please restart computer and run again
)

echo.
pause
