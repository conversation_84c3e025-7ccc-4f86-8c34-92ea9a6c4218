<template>
  <div class="cat-list-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>🐱 猫咪管理</span>
          <el-button type="primary" @click="loadCats">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>
      
      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索猫咪名称或品种..."
          style="width: 300px"
          @keyup.enter="searchCats"
        >
          <template #append>
            <el-button @click="searchCats">
              <el-icon><Search /></el-icon>
            </el-button>
          </template>
        </el-input>
      </div>
      
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="3" animated />
      </div>
      
      <!-- 猫咪列表 -->
      <div v-else-if="cats.length > 0" class="cat-grid">
        <el-card
          v-for="cat in cats"
          :key="cat.id"
          class="cat-card"
          shadow="hover"
        >
          <div class="cat-info">
            <div class="cat-avatar">
              <el-avatar :size="60" :src="cat.primaryPhoto">
                <el-icon><Avatar /></el-icon>
              </el-avatar>
            </div>
            
            <div class="cat-details">
              <h3>{{ cat.name }}</h3>
              <p><strong>品种:</strong> {{ cat.breed || '未知' }}</p>
              <p><strong>性别:</strong> {{ cat.gender === 'MALE' ? '雄性' : '雌性' }}</p>
              <p><strong>颜色:</strong> {{ cat.color || '未知' }}</p>
              <p><strong>状态:</strong> 
                <el-tag :type="getStatusType(cat.status)">
                  {{ getStatusText(cat.status) }}
                </el-tag>
              </p>
              <p v-if="cat.price"><strong>价格:</strong> ¥{{ cat.price }}</p>
            </div>
          </div>
          
          <div class="cat-actions">
            <el-button size="small" @click="viewCat(cat)">
              <el-icon><View /></el-icon>
              查看详情
            </el-button>
          </div>
        </el-card>
      </div>
      
      <!-- 空状态 -->
      <el-empty v-else description="暂无猫咪数据" />
    </el-card>
    
    <!-- 猫咪详情对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="selectedCat?.name + ' - 详细信息'"
      width="600px"
    >
      <div v-if="selectedCat">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="姓名">{{ selectedCat.name }}</el-descriptions-item>
          <el-descriptions-item label="品种">{{ selectedCat.breed || '未知' }}</el-descriptions-item>
          <el-descriptions-item label="性别">{{ selectedCat.gender === 'MALE' ? '雄性' : '雌性' }}</el-descriptions-item>
          <el-descriptions-item label="颜色">{{ selectedCat.color || '未知' }}</el-descriptions-item>
          <el-descriptions-item label="出生日期">{{ selectedCat.birthDate || '未知' }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedCat.status)">
              {{ getStatusText(selectedCat.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="价格" v-if="selectedCat.price">¥{{ selectedCat.price }}</el-descriptions-item>
          <el-descriptions-item label="芯片ID">{{ selectedCat.microchipId || '未植入' }}</el-descriptions-item>
        </el-descriptions>
        
        <div v-if="selectedCat.description" style="margin-top: 20px;">
          <h4>描述:</h4>
          <p>{{ selectedCat.description }}</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { catApi } from '@/api'

const cats = ref([])
const loading = ref(false)
const searchKeyword = ref('')
const dialogVisible = ref(false)
const selectedCat = ref(null)

const loadCats = async () => {
  loading.value = true
  try {
    const response = await catApi.getAll()
    if (response.data.success) {
      cats.value = response.data.data.content || response.data.data
    } else {
      ElMessage.error('获取猫咪列表失败: ' + response.data.message)
    }
  } catch (error: any) {
    ElMessage.error('获取猫咪列表失败: ' + error.message)
    console.error('加载猫咪列表错误:', error)
  } finally {
    loading.value = false
  }
}

const searchCats = async () => {
  if (!searchKeyword.value.trim()) {
    await loadCats()
    return
  }
  
  loading.value = true
  try {
    const response = await catApi.getAll({ keyword: searchKeyword.value })
    if (response.data.success) {
      cats.value = response.data.data.content || response.data.data
    }
  } catch (error: any) {
    ElMessage.error('搜索失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const viewCat = (cat: any) => {
  selectedCat.value = cat
  dialogVisible.value = true
}

const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'AVAILABLE': 'success',
    'RESERVED': 'warning',
    'SOLD': 'info',
    'BREEDING': 'primary',
    'MEDICAL': 'danger',
    'RETIRED': 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'AVAILABLE': '可售',
    'RESERVED': '预定',
    'SOLD': '已售',
    'BREEDING': '繁育中',
    'MEDICAL': '医疗中',
    'RETIRED': '退休'
  }
  return statusMap[status] || status
}

onMounted(() => {
  loadCats()
})
</script>

<style scoped>
.cat-list-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-bar {
  margin-bottom: 20px;
}

.loading-container {
  padding: 20px;
}

.cat-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.cat-card {
  transition: transform 0.2s;
}

.cat-card:hover {
  transform: translateY(-2px);
}

.cat-info {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.cat-avatar {
  flex-shrink: 0;
}

.cat-details {
  flex: 1;
}

.cat-details h3 {
  margin: 0 0 10px 0;
  color: #409eff;
}

.cat-details p {
  margin: 5px 0;
  font-size: 14px;
  color: #666;
}

.cat-actions {
  text-align: right;
}
</style>


