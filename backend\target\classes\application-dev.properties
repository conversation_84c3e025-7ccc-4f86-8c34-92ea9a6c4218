# ===================================================================
# 开发环境配置
# ===================================================================

# 数据库配置
spring.datasource.url=${DB_URL:***********************************************************************************************************}
spring.datasource.username=${DB_USERNAME:root}
spring.datasource.password=${DB_PASSWORD:password}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA/Hibernate配置
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect

# 日志配置
logging.level.com.cattery=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# 服务器配置
server.port=8080

# JWT配置
jwt.secret=${JWT_SECRET:mySecretKey123456789012345678901234567890}
jwt.expiration=${JWT_EXPIRATION:86400000}

# CORS配置
cors.allowed-origins=${CORS_ORIGINS:http://localhost:5173,http://localhost:5174}

# Swagger配置
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.enabled=true

# 文件存储配置
file.storage.upload-dir=uploads
file.storage.cat-photos-dir=cats/photos
file.storage.cat-videos-dir=cats/videos
file.storage.health-records-dir=health-records
file.storage.breeding-records-dir=breeding-records
file.storage.contracts-dir=contracts
file.storage.temp-dir=temp
file.storage.max-file-size=10485760
file.storage.max-image-size=5242880
file.storage.max-video-size=52428800
file.storage.allowed-image-types=jpg,jpeg,png,gif,webp
file.storage.allowed-video-types=mp4,avi,mov,wmv,flv
file.storage.allowed-document-types=pdf,doc,docx,txt,rtf
file.storage.url-prefix=/api/files
file.storage.enable-compression=true
file.storage.image-compression-quality=0.8
file.storage.thumbnail-width=200
file.storage.thumbnail-height=200
file.storage.keep-original-filename=false
file.storage.max-filename-length=100
