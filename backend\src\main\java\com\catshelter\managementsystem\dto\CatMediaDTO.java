package com.catshelter.managementsystem.dto;

import com.catshelter.managementsystem.model.CatMedia;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 猫咪媒体文件DTO
 */
@Data
public class CatMediaDTO {
    
    private Long id;
    private Long catId;
    private String catName;
    private CatMedia.MediaType mediaType;
    private String filePath;
    private String fileUrl;
    private String originalFileName;
    private String fileName;
    private String fileExtension;
    private String mimeType;
    private Long fileSize;
    private String title;
    private String description;
    private Boolean isPrimary;
    private Integer displayOrder;
    private Integer sortOrder;
    private Integer width;
    private Integer height;
    private Integer duration;
    private String thumbnailPath;
    private String thumbnailUrl;
    private String previewImagePath;
    private String previewImageUrl;
    private String status;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}