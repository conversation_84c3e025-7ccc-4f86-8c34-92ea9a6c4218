<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>猫舍管理系统 - 登录测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 400px;
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-header h2 {
            color: #333;
            margin-bottom: 8px;
            font-size: 24px;
            font-weight: 600;
        }

        .login-header p {
            color: #666;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .login-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .remember-me {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .forgot-password {
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
        }

        .forgot-password:hover {
            text-decoration: underline;
        }

        .login-button {
            width: 100%;
            padding: 12px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .login-button:hover {
            background: #5a6fd8;
        }

        .login-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-size: 14px;
        }

        .result.success {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            color: #0369a1;
        }

        .result.error {
            background: #fef2f2;
            border: 1px solid #ef4444;
            color: #dc2626;
        }

        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h2>猫舍管理系统</h2>
            <p>请登录您的账户</p>
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" value="admin" required>
            </div>

            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" value="admin123" required>
            </div>

            <div class="login-options">
                <label class="remember-me">
                    <input type="checkbox" id="rememberMe">
                    记住我
                </label>
                <a href="#" class="forgot-password">忘记密码？</a>
            </div>

            <button type="submit" class="login-button" id="loginButton">
                登录
            </button>
        </form>

        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080/api';
        
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;
            const button = document.getElementById('loginButton');
            const result = document.getElementById('result');
            
            // 显示加载状态
            button.disabled = true;
            button.innerHTML = '<span class="loading"></span>登录中...';
            result.style.display = 'none';
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // 登录成功
                    localStorage.setItem('token', data.data.token);
                    localStorage.setItem('userInfo', JSON.stringify(data.data.userInfo));
                    
                    result.className = 'result success';
                    result.innerHTML = `
                        <strong>登录成功！</strong><br>
                        欢迎，${data.data.userInfo.realName || data.data.userInfo.username}！<br>
                        Token: ${data.data.token.substring(0, 20)}...
                    `;
                    result.style.display = 'block';
                    
                    // 3秒后跳转到仪表盘
                    setTimeout(() => {
                        window.location.href = '/dashboard.html';
                    }, 3000);
                } else {
                    throw new Error(data.message || '登录失败');
                }
            } catch (error) {
                console.error('登录错误:', error);
                result.className = 'result error';
                result.innerHTML = `<strong>登录失败：</strong>${error.message}`;
                result.style.display = 'block';
            } finally {
                button.disabled = false;
                button.innerHTML = '登录';
            }
        });
    </script>
</body>
</html>
