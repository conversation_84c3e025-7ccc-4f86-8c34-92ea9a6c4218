// 虚拟数据服务
import type { ApiResponse } from './request'

// 模拟延迟
const delay = (ms: number = 500) => new Promise(resolve => setTimeout(resolve, ms))

// 创建成功响应
const createSuccessResponse = <T>(data: T, message: string = '操作成功'): ApiResponse<T> => ({
  code: 200,
  success: true,
  message,
  data,
  timestamp: new Date().toISOString()
})

// 创建错误响应
const createErrorResponse = (message: string = '操作失败', code: number = 500): ApiResponse<null> => ({
  code,
  success: false,
  message,
  data: null,
  timestamp: new Date().toISOString()
})

// 虚拟用户数据
const mockUsers = [
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    realName: '系统管理员',
    phone: '13800138000',
    avatar: '',
    roles: ['ADMIN'],
    permissions: ['*'],
    enabled: true,
    lastLoginTime: new Date().toISOString()
  },
  {
    id: 2,
    username: 'user',
    email: '<EMAIL>',
    realName: '普通用户',
    phone: '13800138001',
    avatar: '',
    roles: ['USER'],
    permissions: ['cat:read', 'customer:read'],
    enabled: true,
    lastLoginTime: new Date().toISOString()
  }
]

// 虚拟猫咪数据
const mockCats = [
  {
    id: 1,
    name: '小白',
    breedName: '英国短毛猫',
    gender: 'FEMALE',
    dateOfBirth: '2023-03-15',
    color: '银渐层',
    pattern: '渐层',
    status: 'AVAILABLE',
    currentWeight: 3.2,
    isNeutered: false,
    microchipId: 'MC001',
    registrationNumber: 'REG001',
    primaryPhoto: '/images/cats/cat1.jpg',
    description: '性格温顺，喜欢和人亲近',
    notes: '健康状况良好',
    createdAt: '2024-01-15T10:00:00Z'
  },
  {
    id: 2,
    name: '小黑',
    breedName: '美国短毛猫',
    gender: 'MALE',
    dateOfBirth: '2023-05-20',
    color: '黑色',
    pattern: '纯色',
    status: 'ADOPTED',
    currentWeight: 4.1,
    isNeutered: true,
    microchipId: 'MC002',
    registrationNumber: 'REG002',
    primaryPhoto: '/images/cats/cat2.jpg',
    description: '活泼好动，喜欢玩耍',
    notes: '已绝育',
    createdAt: '2024-01-20T10:00:00Z'
  },
  {
    id: 3,
    name: '橘子',
    breedName: '橘猫',
    gender: 'MALE',
    dateOfBirth: '2023-07-10',
    color: '橘色',
    pattern: '虎斑',
    status: 'AVAILABLE',
    currentWeight: 5.5,
    isNeutered: false,
    microchipId: 'MC003',
    registrationNumber: 'REG003',
    primaryPhoto: '/images/cats/cat3.jpg',
    description: '贪吃可爱，性格憨厚',
    notes: '需要控制体重',
    createdAt: '2024-02-01T10:00:00Z'
  }
]

// 虚拟客户数据
const mockCustomers = [
  {
    id: 1,
    name: '张三',
    email: '<EMAIL>',
    phone: '13800138001',
    gender: 'MALE',
    age: 28,
    address: '北京市朝阳区xxx街道',
    customerType: 'POTENTIAL',
    status: 'ACTIVE',
    preferredBreed: '英国短毛猫',
    preferredGender: 'FEMALE',
    budgetRange: '3000-5000',
    hasPetExperience: true,
    housingType: 'APARTMENT',
    familySize: 2,
    hasYard: false,
    notes: '首次养猫，希望找一只温顺的猫咪',
    createdAt: '2024-01-10T10:00:00Z'
  },
  {
    id: 2,
    name: '李四',
    email: '<EMAIL>',
    phone: '13800138002',
    gender: 'FEMALE',
    age: 32,
    address: '上海市浦东新区xxx路',
    customerType: 'ADOPTED',
    status: 'ACTIVE',
    preferredBreed: '美国短毛猫',
    preferredGender: 'MALE',
    budgetRange: '5000-8000',
    hasPetExperience: true,
    housingType: 'HOUSE',
    familySize: 3,
    hasYard: true,
    notes: '已经养过猫，希望再添一只',
    createdAt: '2024-01-15T10:00:00Z'
  }
]

// 虚拟统计数据
const mockStats = {
  totalCats: 15,
  availableCats: 8,
  adoptedCats: 5,
  reservedCats: 2,
  totalCustomers: 25,
  potentialCustomers: 15,
  adoptedCustomers: 8,
  vipCustomers: 2,
  monthlyIncome: 25000,
  monthlyExpense: 8000,
  monthlyProfit: 17000,
  pendingInquiries: 3
}

// 虚拟API服务
export const mockApi = {
  // 认证相关
  async login(username: string, password: string) {
    await delay()
    
    const user = mockUsers.find(u => u.username === username)
    if (!user || password !== 'password') {
      return createErrorResponse('用户名或密码错误', 401)
    }
    
    const token = `mock_token_${user.id}_${Date.now()}`
    return createSuccessResponse({
      token,
      user,
      expiresIn: 7200
    }, '登录成功')
  },

  async logout() {
    await delay(200)
    return createSuccessResponse(null, '退出成功')
  },

  async getCurrentUser() {
    await delay()
    const token = localStorage.getItem('token')
    if (!token || !token.startsWith('mock_token_')) {
      return createErrorResponse('未登录', 401)
    }
    
    const userId = parseInt(token.split('_')[2])
    const user = mockUsers.find(u => u.id === userId)
    if (!user) {
      return createErrorResponse('用户不存在', 404)
    }
    
    return createSuccessResponse(user, '获取用户信息成功')
  },

  // 猫咪相关
  async getCats(params?: any) {
    await delay()
    let cats = [...mockCats]
    
    // 简单的筛选逻辑
    if (params?.status) {
      cats = cats.filter(cat => cat.status === params.status)
    }
    if (params?.breedName) {
      cats = cats.filter(cat => cat.breedName.includes(params.breedName))
    }
    
    return createSuccessResponse({
      list: cats,
      total: cats.length,
      page: params?.page || 1,
      size: params?.size || 10
    }, '获取猫咪列表成功')
  },

  async getCatById(id: number) {
    await delay()
    const cat = mockCats.find(c => c.id === id)
    if (!cat) {
      return createErrorResponse('猫咪不存在', 404)
    }
    return createSuccessResponse(cat, '获取猫咪详情成功')
  },

  // 客户相关
  async getCustomers(params?: any) {
    await delay()
    let customers = [...mockCustomers]
    
    if (params?.customerType) {
      customers = customers.filter(c => c.customerType === params.customerType)
    }
    if (params?.name) {
      customers = customers.filter(c => c.name.includes(params.name))
    }
    
    return createSuccessResponse({
      list: customers,
      total: customers.length,
      page: params?.page || 1,
      size: params?.size || 10
    }, '获取客户列表成功')
  },

  async getCustomerById(id: number) {
    await delay()
    const customer = mockCustomers.find(c => c.id === id)
    if (!customer) {
      return createErrorResponse('客户不存在', 404)
    }
    return createSuccessResponse(customer, '获取客户详情成功')
  },

  // 统计相关
  async getStats() {
    await delay()
    return createSuccessResponse(mockStats, '获取统计数据成功')
  },

  // API测试相关
  async testHealth() {
    await delay(300)
    return createSuccessResponse({
      status: 'UP',
      timestamp: new Date().toISOString(),
      application: 'cattery-management-system',
      version: '1.0.0',
      message: '猫舍管理系统API正常运行(虚拟数据模式)'
    }, '系统健康检查通过')
  },

  async testConnection() {
    await delay(300)
    return createSuccessResponse({
      connected: true,
      timestamp: new Date().toISOString(),
      server: 'Mock Server',
      database: 'Mock Database',
      message: '前后端连接正常(虚拟数据模式)'
    }, '连接测试成功')
  },

  async testAuthStatus() {
    await delay(300)
    const token = localStorage.getItem('token')
    return createSuccessResponse({
      authenticated: !!token && token.startsWith('mock_token_'),
      token_valid: !!token,
      timestamp: new Date().toISOString(),
      message: token ? '用户已认证(虚拟数据模式)' : '用户未认证(虚拟数据模式)'
    }, '认证状态测试成功')
  }
}

// 检查是否应该使用虚拟数据
export const shouldUseMockData = (): boolean => {
  // 检查环境变量
  if (import.meta.env.VITE_USE_MOCK_DATA === 'true') {
    return true
  }
  
  // 检查localStorage设置
  if (localStorage.getItem('useMockData') === 'true') {
    return true
  }
  
  return false
}

// 设置虚拟数据模式
export const setMockDataMode = (enabled: boolean) => {
  localStorage.setItem('useMockData', enabled.toString())
  if (enabled) {
    console.log('🎭 已启用虚拟数据模式')
  } else {
    console.log('🔗 已切换到真实API模式')
  }
}
