import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest'
import * as axios from 'axios'
import type { AxiosInstance } from 'axios'

// 获取axios的默认导出
const axiosDefault = axios.default || axios
import { authApi, catApi, healthApi, breedingApi, customerApi, inventoryApi, financeApi } from '@/api'

// 集成测试配置
const TEST_BASE_URL = 'http://localhost:8080/api'
const TEST_USER = {
  username: '<EMAIL>',
  password: 'test123456'
}

describe('API Integration Tests', () => {
  let apiClient: AxiosInstance
  let authToken: string

  beforeAll(async () => {
    // 初始化API客户端
    apiClient = axiosDefault.create({
      baseURL: TEST_BASE_URL,
      timeout: 10000
    })

    // 登录获取认证令牌
    try {
      const loginResponse = await authApi.login(TEST_USER)
      authToken = loginResponse.token
      
      // 设置默认认证头
      apiClient.defaults.headers.common['Authorization'] = `Bearer ${authToken}`
    } catch (error) {
      console.warn('登录失败，某些测试可能会跳过:', error)
    }
  })

  afterAll(async () => {
    // 清理测试数据
    if (authToken) {
      try {
        await authApi.logout()
      } catch (error) {
        console.warn('登出失败:', error)
      }
    }
  })

  describe('Authentication API', () => {
    it('should login successfully with valid credentials', async () => {
      const response = await authApi.login(TEST_USER)
      
      expect(response).toHaveProperty('token')
      expect(response).toHaveProperty('user')
      expect(response.user).toHaveProperty('id')
      expect(response.user).toHaveProperty('username')
      expect(typeof response.token).toBe('string')
    })

    it('should fail login with invalid credentials', async () => {
      const invalidUser = {
        username: '<EMAIL>',
        password: 'wrongpassword'
      }

      await expect(authApi.login(invalidUser)).rejects.toThrow()
    })

    it('should refresh token successfully', async () => {
      if (!authToken) {
        console.warn('跳过令牌刷新测试：未登录')
        return
      }

      const response = await authApi.refreshToken()
      
      expect(response).toHaveProperty('token')
      expect(typeof response.token).toBe('string')
    })

    it('should get current user info', async () => {
      if (!authToken) {
        console.warn('跳过用户信息测试：未登录')
        return
      }

      const user = await authApi.getCurrentUser()
      
      expect(user).toHaveProperty('id')
      expect(user).toHaveProperty('username')
      expect(user).toHaveProperty('roles')
    })
  })

  describe('Cat Management API', () => {
    let testCatId: number

    it('should create a new cat', async () => {
      if (!authToken) {
        console.warn('跳过猫咪创建测试：未登录')
        return
      }

      const catData = {
        name: '测试猫咪',
        breedId: 1,
        gender: 'FEMALE',
        dateOfBirth: '2023-01-01',
        color: '银渐层',
        status: 'PENDING_ADOPTION'
      }

      const response = await catApi.create(catData)
      testCatId = response.id
      
      expect(response).toHaveProperty('id')
      expect(response.name).toBe(catData.name)
      expect(response.gender).toBe(catData.gender)
    })

    it('should get cat list with pagination', async () => {
      const response = await catApi.getAll({ page: 1, size: 10 })
      
      expect(response).toHaveProperty('data')
      expect(response).toHaveProperty('total')
      expect(Array.isArray(response.data)).toBe(true)
      expect(typeof response.total).toBe('number')
    })

    it('should get cat by id', async () => {
      if (!testCatId) {
        console.warn('跳过猫咪详情测试：未创建测试猫咪')
        return
      }

      const cat = await catApi.getById(testCatId)
      
      expect(cat).toHaveProperty('id', testCatId)
      expect(cat).toHaveProperty('name')
      expect(cat).toHaveProperty('breedId')
    })

    it('should update cat information', async () => {
      if (!testCatId) {
        console.warn('跳过猫咪更新测试：未创建测试猫咪')
        return
      }

      const updateData = {
        name: '更新后的猫咪名字',
        color: '蓝色'
      }

      const updatedCat = await catApi.update(testCatId, updateData)
      
      expect(updatedCat.name).toBe(updateData.name)
      expect(updatedCat.color).toBe(updateData.color)
    })

    it('should search cats by criteria', async () => {
      const searchParams = {
        name: '测试',
        gender: 'FEMALE'
      }

      const response = await catApi.search(searchParams)
      
      expect(response).toHaveProperty('data')
      expect(Array.isArray(response.data)).toBe(true)
    })

    it('should delete cat', async () => {
      if (!testCatId) {
        console.warn('跳过猫咪删除测试：未创建测试猫咪')
        return
      }

      await expect(catApi.delete(testCatId)).resolves.not.toThrow()
      
      // 验证猫咪已被删除
      await expect(catApi.getById(testCatId)).rejects.toThrow()
    })
  })

  describe('Health Management API', () => {
    it('should get health statistics', async () => {
      if (!authToken) {
        console.warn('跳过健康统计测试：未登录')
        return
      }

      const stats = await healthApi.getStats()
      
      expect(stats).toHaveProperty('totalHealthRecords')
      expect(stats).toHaveProperty('vaccinesDue')
      expect(stats).toHaveProperty('checkupsDue')
      expect(typeof stats.totalHealthRecords).toBe('number')
    })

    it('should get vaccine records', async () => {
      if (!authToken) {
        console.warn('跳过疫苗记录测试：未登录')
        return
      }

      const response = await healthApi.getVaccineRecords({ page: 1, size: 10 })
      
      expect(response).toHaveProperty('data')
      expect(response).toHaveProperty('total')
      expect(Array.isArray(response.data)).toBe(true)
    })

    it('should create health record', async () => {
      if (!authToken) {
        console.warn('跳过健康记录创建测试：未登录')
        return
      }

      const healthRecord = {
        catId: 1,
        recordType: 'CHECKUP',
        recordDate: '2024-01-15',
        notes: '常规体检，健康状况良好'
      }

      const response = await healthApi.createHealthRecord(healthRecord)
      
      expect(response).toHaveProperty('id')
      expect(response.recordType).toBe(healthRecord.recordType)
    })
  })

  describe('Breeding Management API', () => {
    it('should get breeding statistics', async () => {
      if (!authToken) {
        console.warn('跳过繁育统计测试：未登录')
        return
      }

      const stats = await breedingApi.getStats()
      
      expect(stats).toHaveProperty('breedingFemales')
      expect(stats).toHaveProperty('pregnantCats')
      expect(stats).toHaveProperty('matingSuccessRate')
      expect(typeof stats.breedingFemales).toBe('number')
    })

    it('should get mating records', async () => {
      if (!authToken) {
        console.warn('跳过配种记录测试：未登录')
        return
      }

      const response = await breedingApi.getMatingRecords({ page: 1, size: 10 })
      
      expect(response).toHaveProperty('data')
      expect(response).toHaveProperty('total')
      expect(Array.isArray(response.data)).toBe(true)
    })
  })

  describe('Customer Management API', () => {
    it('should get customer list', async () => {
      if (!authToken) {
        console.warn('跳过客户列表测试：未登录')
        return
      }

      const response = await customerApi.getAll({ page: 1, size: 10 })
      
      expect(response).toHaveProperty('data')
      expect(response).toHaveProperty('total')
      expect(Array.isArray(response.data)).toBe(true)
    })

    it('should create customer', async () => {
      if (!authToken) {
        console.warn('跳过客户创建测试：未登录')
        return
      }

      const customerData = {
        name: '测试客户',
        phone: '13800138000',
        email: '<EMAIL>',
        address: '测试地址'
      }

      const response = await customerApi.create(customerData)
      
      expect(response).toHaveProperty('id')
      expect(response.name).toBe(customerData.name)
      expect(response.phone).toBe(customerData.phone)
    })
  })

  describe('Inventory Management API', () => {
    it('should get inventory statistics', async () => {
      if (!authToken) {
        console.warn('跳过库存统计测试：未登录')
        return
      }

      const stats = await inventoryApi.getStats()
      
      expect(stats).toHaveProperty('totalItems')
      expect(stats).toHaveProperty('lowStockItems')
      expect(stats).toHaveProperty('totalValue')
      expect(typeof stats.totalItems).toBe('number')
    })

    it('should get inventory items', async () => {
      if (!authToken) {
        console.warn('跳过库存物品测试：未登录')
        return
      }

      const response = await inventoryApi.getItems({ page: 1, size: 10 })
      
      expect(response).toHaveProperty('data')
      expect(response).toHaveProperty('total')
      expect(Array.isArray(response.data)).toBe(true)
    })
  })

  describe('Finance Management API', () => {
    it('should get financial statistics', async () => {
      if (!authToken) {
        console.warn('跳过财务统计测试：未登录')
        return
      }

      const stats = await financeApi.getStats()
      
      expect(stats).toHaveProperty('totalIncome')
      expect(stats).toHaveProperty('totalExpense')
      expect(stats).toHaveProperty('netProfit')
      expect(typeof stats.totalIncome).toBe('number')
    })

    it('should get financial transactions', async () => {
      if (!authToken) {
        console.warn('跳过财务交易测试：未登录')
        return
      }

      const response = await financeApi.getTransactions({ page: 1, size: 10 })
      
      expect(response).toHaveProperty('data')
      expect(response).toHaveProperty('total')
      expect(Array.isArray(response.data)).toBe(true)
    })
  })

  describe('Error Handling', () => {
    it('should handle 401 unauthorized error', async () => {
      // 使用无效令牌
      const originalToken = apiClient.defaults.headers.common['Authorization']
      apiClient.defaults.headers.common['Authorization'] = 'Bearer invalid-token'

      await expect(catApi.getAll()).rejects.toThrow()

      // 恢复原始令牌
      apiClient.defaults.headers.common['Authorization'] = originalToken
    })

    it('should handle 404 not found error', async () => {
      await expect(catApi.getById(99999)).rejects.toThrow()
    })

    it('should handle 400 bad request error', async () => {
      if (!authToken) {
        console.warn('跳过错误请求测试：未登录')
        return
      }

      const invalidCatData = {
        name: '', // 空名称应该导致验证错误
        gender: 'INVALID_GENDER'
      }

      await expect(catApi.create(invalidCatData)).rejects.toThrow()
    })

    it('should handle network timeout', async () => {
      // 创建一个超时很短的客户端
      const timeoutClient = axiosDefault.create({
        baseURL: TEST_BASE_URL,
        timeout: 1 // 1ms 超时
      })

      await expect(
        timeoutClient.get('/cats')
      ).rejects.toThrow()
    })
  })

  describe('Data Validation', () => {
    it('should validate required fields', async () => {
      if (!authToken) {
        console.warn('跳过字段验证测试：未登录')
        return
      }

      const incompleteCatData = {
        // 缺少必需字段
        color: '银渐层'
      }

      await expect(catApi.create(incompleteCatData)).rejects.toThrow()
    })

    it('should validate data types', async () => {
      if (!authToken) {
        console.warn('跳过数据类型验证测试：未登录')
        return
      }

      const invalidTypeData = {
        name: '测试猫咪',
        breedId: 'invalid-id', // 应该是数字
        gender: 'FEMALE',
        dateOfBirth: 'invalid-date' // 应该是有效日期
      }

      await expect(catApi.create(invalidTypeData)).rejects.toThrow()
    })

    it('should validate business rules', async () => {
      if (!authToken) {
        console.warn('跳过业务规则验证测试：未登录')
        return
      }

      const futureDateData = {
        name: '测试猫咪',
        breedId: 1,
        gender: 'FEMALE',
        dateOfBirth: '2030-01-01' // 未来日期应该无效
      }

      await expect(catApi.create(futureDateData)).rejects.toThrow()
    })
  })

  describe('Performance Tests', () => {
    it('should handle large data sets efficiently', async () => {
      if (!authToken) {
        console.warn('跳过性能测试：未登录')
        return
      }

      const startTime = Date.now()
      
      // 请求大量数据
      await catApi.getAll({ page: 1, size: 100 })
      
      const endTime = Date.now()
      const responseTime = endTime - startTime
      
      // 响应时间应该在合理范围内（例如5秒）
      expect(responseTime).toBeLessThan(5000)
    })

    it('should handle concurrent requests', async () => {
      if (!authToken) {
        console.warn('跳过并发测试：未登录')
        return
      }

      // 并发发送多个请求
      const promises = Array.from({ length: 5 }, () => catApi.getAll({ page: 1, size: 10 }))
      
      const results = await Promise.all(promises)
      
      // 所有请求都应该成功
      results.forEach(result => {
        expect(result).toHaveProperty('data')
        expect(result).toHaveProperty('total')
      })
    })
  })
})
