package com.cattery.controller;

import com.cattery.dto.ai.*;
import com.cattery.service.ai.AIService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import java.util.List;

/**
 * AI功能控制器 - 提供猫咪识别、健康预测、行为分析等AI功能的REST API
 */
@RestController
@RequestMapping("/api/ai")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "AI功能", description = "猫咪识别、健康预测、行为分析等AI功能")
public class AIController {

    private final AIService aiService;

    @PostMapping(value = "/recognition/cat", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "猫咪个体识别", description = "通过照片识别猫咪个体，返回匹配的猫咪信息")
    @PreAuthorize("hasPermission('cat', 'read')")
    public ResponseEntity<CatRecognitionResultDTO> recognizeCat(
            @Parameter(description = "猫咪照片", required = true)
            @RequestParam("image") MultipartFile image) {
        
        log.info("收到猫咪识别请求，图片大小: {} bytes", image.getSize());
        
        try {
            // 验证文件
            if (image.isEmpty()) {
                throw new IllegalArgumentException("图片文件不能为空");
            }
            
            if (image.getSize() > 10 * 1024 * 1024) { // 10MB限制
                throw new IllegalArgumentException("图片文件大小不能超过10MB");
            }
            
            String contentType = image.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                throw new IllegalArgumentException("文件必须是图片格式");
            }
            
            CatRecognitionResultDTO result = aiService.recognizeCat(image);
            
            log.info("猫咪识别完成，置信度: {}", result.getConfidence());
            return ResponseEntity.ok(result);
            
        } catch (IllegalArgumentException e) {
            log.warn("猫咪识别请求参数错误: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("猫咪识别失败", e);
            throw new RuntimeException("猫咪识别失败: " + e.getMessage());
        }
    }

    @PostMapping(value = "/recognition/breed", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "品种识别", description = "通过照片识别猫咪品种")
    @PreAuthorize("hasPermission('cat', 'read')")
    public ResponseEntity<BreedRecognitionResultDTO> recognizeBreed(
            @Parameter(description = "猫咪照片", required = true)
            @RequestParam("image") MultipartFile image) {
        
        log.info("收到品种识别请求，图片大小: {} bytes", image.getSize());
        
        try {
            // 验证文件
            validateImageFile(image);
            
            BreedRecognitionResultDTO result = aiService.recognizeBreed(image);
            
            log.info("品种识别完成，识别品种: {}", result.getBreedName());
            return ResponseEntity.ok(result);
            
        } catch (IllegalArgumentException e) {
            log.warn("品种识别请求参数错误: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("品种识别失败", e);
            throw new RuntimeException("品种识别失败: " + e.getMessage());
        }
    }

    @PostMapping("/prediction/health/{catId}")
    @Operation(summary = "健康预测", description = "基于猫咪历史数据预测健康状况")
    @PreAuthorize("hasPermission('cat', 'read')")
    public ResponseEntity<HealthPredictionResultDTO> predictHealth(
            @Parameter(description = "猫咪ID", required = true)
            @PathVariable Long catId,
            @Parameter(description = "健康预测请求参数")
            @Valid @RequestBody HealthPredictionRequestDTO request) {
        
        log.info("收到健康预测请求，猫咪ID: {}", catId);
        
        try {
            HealthPredictionResultDTO result = aiService.predictHealth(catId, request);
            
            log.info("健康预测完成，风险等级: {}", result.getRiskLevel());
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("健康预测失败", e);
            throw new RuntimeException("健康预测失败: " + e.getMessage());
        }
    }

    @PostMapping("/analysis/behavior/{catId}")
    @Operation(summary = "行为分析", description = "分析猫咪行为模式和异常行为")
    @PreAuthorize("hasPermission('cat', 'read')")
    public ResponseEntity<BehaviorAnalysisResultDTO> analyzeBehavior(
            @Parameter(description = "猫咪ID", required = true)
            @PathVariable Long catId,
            @Parameter(description = "行为分析请求参数")
            @Valid @RequestBody BehaviorAnalysisRequestDTO request) {
        
        log.info("收到行为分析请求，猫咪ID: {}", catId);
        
        try {
            BehaviorAnalysisResultDTO result = aiService.analyzeBehavior(catId, request);
            
            log.info("行为分析完成，行为评分: {}", result.getBehaviorScore());
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("行为分析失败", e);
            throw new RuntimeException("行为分析失败: " + e.getMessage());
        }
    }

    @PostMapping("/assessment/batch")
    @Operation(summary = "批量健康评估", description = "对多只猫咪进行批量健康评估")
    @PreAuthorize("hasPermission('cat', 'read')")
    public ResponseEntity<List<HealthAssessmentSummaryDTO>> batchHealthAssessment(
            @Parameter(description = "猫咪ID列表", required = true)
            @Valid @RequestBody List<Long> catIds) {
        
        log.info("收到批量健康评估请求，猫咪数量: {}", catIds.size());
        
        try {
            if (catIds.isEmpty()) {
                throw new IllegalArgumentException("猫咪ID列表不能为空");
            }
            
            if (catIds.size() > 100) {
                throw new IllegalArgumentException("批量评估数量不能超过100只");
            }
            
            List<HealthAssessmentSummaryDTO> results = aiService.batchHealthAssessment(catIds);
            
            log.info("批量健康评估完成，成功评估: {} 只猫咪", results.size());
            return ResponseEntity.ok(results);
            
        } catch (IllegalArgumentException e) {
            log.warn("批量健康评估请求参数错误: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("批量健康评估失败", e);
            throw new RuntimeException("批量健康评估失败: " + e.getMessage());
        }
    }

    @GetMapping("/status")
    @Operation(summary = "AI服务状态", description = "获取AI服务的可用性状态")
    @PreAuthorize("hasPermission('system', 'read')")
    public ResponseEntity<AIServiceStatusDTO> getServiceStatus() {
        
        log.info("收到AI服务状态查询请求");
        
        try {
            AIServiceStatusDTO status = aiService.getServiceStatus();
            
            log.info("AI服务状态查询完成，整体可用性: {}", status.getOverallAvailable());
            return ResponseEntity.ok(status);
            
        } catch (Exception e) {
            log.error("AI服务状态查询失败", e);
            throw new RuntimeException("AI服务状态查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/models/info")
    @Operation(summary = "AI模型信息", description = "获取当前使用的AI模型信息")
    @PreAuthorize("hasPermission('system', 'read')")
    public ResponseEntity<AIModelInfoDTO> getModelInfo() {
        
        log.info("收到AI模型信息查询请求");
        
        try {
            AIModelInfoDTO modelInfo = new AIModelInfoDTO();
            modelInfo.setCatRecognitionModel("CatNet-v2.1");
            modelInfo.setBreedClassificationModel("BreedClassifier-v1.8");
            modelInfo.setHealthPredictionModel("HealthPredictor-v1.5");
            modelInfo.setBehaviorAnalysisModel("BehaviorAnalyzer-v1.2");
            modelInfo.setLastUpdated("2024-01-15");
            modelInfo.setAccuracy(0.92);
            
            log.info("AI模型信息查询完成");
            return ResponseEntity.ok(modelInfo);
            
        } catch (Exception e) {
            log.error("AI模型信息查询失败", e);
            throw new RuntimeException("AI模型信息查询失败: " + e.getMessage());
        }
    }

    @PostMapping("/training/feedback")
    @Operation(summary = "提交训练反馈", description = "提交AI识别结果的反馈，用于模型优化")
    @PreAuthorize("hasPermission('cat', 'write')")
    public ResponseEntity<Void> submitTrainingFeedback(
            @Parameter(description = "训练反馈数据")
            @Valid @RequestBody AITrainingFeedbackDTO feedback) {
        
        log.info("收到AI训练反馈，类型: {}", feedback.getFeedbackType());
        
        try {
            // 这里可以将反馈数据发送到AI训练系统
            // 或者存储到数据库中供后续分析
            
            log.info("AI训练反馈提交成功");
            return ResponseEntity.ok().build();
            
        } catch (Exception e) {
            log.error("AI训练反馈提交失败", e);
            throw new RuntimeException("AI训练反馈提交失败: " + e.getMessage());
        }
    }

    @GetMapping("/statistics")
    @Operation(summary = "AI使用统计", description = "获取AI功能的使用统计信息")
    @PreAuthorize("hasPermission('system', 'read')")
    public ResponseEntity<AIUsageStatisticsDTO> getUsageStatistics(
            @Parameter(description = "统计开始日期")
            @RequestParam(required = false) String startDate,
            @Parameter(description = "统计结束日期")
            @RequestParam(required = false) String endDate) {
        
        log.info("收到AI使用统计查询请求");
        
        try {
            AIUsageStatisticsDTO statistics = new AIUsageStatisticsDTO();
            statistics.setTotalRecognitions(1250L);
            statistics.setTotalHealthPredictions(890L);
            statistics.setTotalBehaviorAnalyses(456L);
            statistics.setAverageAccuracy(0.91);
            statistics.setMostUsedFeature("猫咪识别");
            
            log.info("AI使用统计查询完成");
            return ResponseEntity.ok(statistics);
            
        } catch (Exception e) {
            log.error("AI使用统计查询失败", e);
            throw new RuntimeException("AI使用统计查询失败: " + e.getMessage());
        }
    }

    // 私有辅助方法

    private void validateImageFile(MultipartFile image) {
        if (image.isEmpty()) {
            throw new IllegalArgumentException("图片文件不能为空");
        }
        
        if (image.getSize() > 10 * 1024 * 1024) { // 10MB限制
            throw new IllegalArgumentException("图片文件大小不能超过10MB");
        }
        
        String contentType = image.getContentType();
        if (contentType == null || !contentType.startsWith("image/")) {
            throw new IllegalArgumentException("文件必须是图片格式");
        }
        
        // 检查文件扩展名
        String originalFilename = image.getOriginalFilename();
        if (originalFilename != null) {
            String extension = originalFilename.toLowerCase();
            if (!extension.endsWith(".jpg") && !extension.endsWith(".jpeg") && 
                !extension.endsWith(".png") && !extension.endsWith(".gif")) {
                throw new IllegalArgumentException("支持的图片格式: JPG, JPEG, PNG, GIF");
            }
        }
    }
}
