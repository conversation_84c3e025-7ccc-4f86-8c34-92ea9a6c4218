package com.cattery.service.ai;

import com.cattery.dto.ai.*;
import com.cattery.entity.Cat;
import com.cattery.entity.CatHealthRecord;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 健康预测服务 - 提供猫咪健康状况预测和风险评估
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class HealthPredictionService {

    private final RestTemplate restTemplate;

    @Value("${ai.health-prediction.api.url:http://localhost:5001}")
    private String apiUrl;

    @Value("${ai.health-prediction.api.key:}")
    private String apiKey;

    @Value("${ai.health-prediction.timeout:30000}")
    private int timeout;

    /**
     * 健康预测
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    public HealthPredictionResultDTO predictHealth(HealthPredictionInputDTO input) {
        try {
            log.info("开始健康预测，猫咪ID: {}", input.getCatId());
            
            // 构建请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            if (!apiKey.isEmpty()) {
                headers.set("Authorization", "Bearer " + apiKey);
            }
            
            Map<String, Object> requestBody = buildPredictionRequest(input);
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            
            // 调用AI服务
            ResponseEntity<Map> response = restTemplate.postForEntity(
                apiUrl + "/api/v1/predict", request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                return parsePredictionResult(response.getBody());
            } else {
                throw new RuntimeException("健康预测服务调用失败");
            }
            
        } catch (Exception e) {
            log.error("健康预测失败", e);
            return createDefaultPredictionResult();
        }
    }

    /**
     * 疾病风险评估
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    public List<DiseaseRiskDTO> assessDiseaseRisk(HealthPredictionInputDTO input) {
        try {
            log.info("开始疾病风险评估");
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            if (!apiKey.isEmpty()) {
                headers.set("Authorization", "Bearer " + apiKey);
            }
            
            Map<String, Object> requestBody = buildRiskAssessmentRequest(input);
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(
                apiUrl + "/api/v1/risk-assessment", request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                return parseRiskAssessmentResult(response.getBody());
            } else {
                throw new RuntimeException("疾病风险评估服务调用失败");
            }
            
        } catch (Exception e) {
            log.error("疾病风险评估失败", e);
            return createDefaultRiskAssessment();
        }
    }

    /**
     * 健康评估摘要
     */
    public HealthAssessmentSummaryDTO assessHealthSummary(Cat cat, List<CatHealthRecord> healthRecords) {
        try {
            log.info("开始健康评估摘要，猫咪ID: {}", cat.getId());
            
            HealthAssessmentSummaryDTO summary = new HealthAssessmentSummaryDTO();
            summary.setCatId(cat.getId());
            summary.setCatName(cat.getName());
            summary.setAssessmentDate(LocalDateTime.now());
            
            // 计算基础健康指标
            summary.setOverallHealthScore(calculateOverallHealthScore(cat, healthRecords));
            summary.setVaccinationStatus(assessVaccinationStatus(healthRecords));
            summary.setLastCheckupDate(getLastCheckupDate(healthRecords));
            summary.setHealthTrend(calculateHealthTrend(healthRecords));
            
            // 识别健康风险
            summary.setRiskFactors(identifyRiskFactors(cat, healthRecords));
            summary.setRecommendations(generateHealthRecommendations(cat, healthRecords));
            
            // 计算下次检查建议时间
            summary.setNextCheckupRecommended(calculateNextCheckupDate(cat, healthRecords));
            
            return summary;
            
        } catch (Exception e) {
            log.error("健康评估摘要失败", e);
            return createDefaultHealthSummary(cat);
        }
    }

    /**
     * 营养建议
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    public NutritionRecommendationDTO getNutritionRecommendation(HealthPredictionInputDTO input) {
        try {
            log.info("开始营养建议分析");
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            if (!apiKey.isEmpty()) {
                headers.set("Authorization", "Bearer " + apiKey);
            }
            
            Map<String, Object> requestBody = buildNutritionRequest(input);
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(
                apiUrl + "/api/v1/nutrition", request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                return parseNutritionResult(response.getBody());
            } else {
                throw new RuntimeException("营养建议服务调用失败");
            }
            
        } catch (Exception e) {
            log.error("营养建议分析失败", e);
            return createDefaultNutritionRecommendation();
        }
    }

    /**
     * 检查服务可用性
     */
    @SuppressWarnings("rawtypes")
    public boolean isAvailable() {
        try {
            ResponseEntity<Map> response = restTemplate.getForEntity(
                apiUrl + "/api/v1/health", Map.class);
            return response.getStatusCode() == HttpStatus.OK;
        } catch (Exception e) {
            log.warn("健康预测服务不可用: {}", e.getMessage());
            return false;
        }
    }

    // 私有辅助方法

    private Map<String, Object> buildPredictionRequest(HealthPredictionInputDTO input) {
        Map<String, Object> request = new HashMap<>();
        request.put("cat_id", input.getCatId());
        request.put("age", input.getAge());
        request.put("breed", input.getBreed());
        request.put("gender", input.getGender());
        request.put("weight", input.getWeight());
        
        // 健康记录
        List<Map<String, Object>> healthRecords = input.getHealthRecords().stream()
            .map(this::convertHealthRecord)
            .collect(Collectors.toList());
        request.put("health_records", healthRecords);
        
        // 当前症状
        if (input.getCurrentSymptoms() != null) {
            request.put("current_symptoms", input.getCurrentSymptoms());
        }
        
        return request;
    }

    private Map<String, Object> buildRiskAssessmentRequest(HealthPredictionInputDTO input) {
        Map<String, Object> request = buildPredictionRequest(input);
        request.put("assessment_type", "disease_risk");
        request.put("include_genetic_factors", true);
        return request;
    }

    private Map<String, Object> buildNutritionRequest(HealthPredictionInputDTO input) {
        Map<String, Object> request = new HashMap<>();
        request.put("cat_id", input.getCatId());
        request.put("age", input.getAge());
        request.put("breed", input.getBreed());
        request.put("weight", input.getWeight());
        request.put("activity_level", input.getActivityLevel());
        request.put("health_conditions", input.getHealthConditions());
        return request;
    }

    private Map<String, Object> convertHealthRecord(HealthRecordSummaryDTO record) {
        Map<String, Object> recordMap = new HashMap<>();
        recordMap.put("type", record.getRecordType());
        recordMap.put("date", record.getRecordDate().toString());
        recordMap.put("notes", record.getNotes());
        return recordMap;
    }

    @SuppressWarnings("unchecked")
    private HealthPredictionResultDTO parsePredictionResult(Map<String, Object> responseBody) {
        HealthPredictionResultDTO result = new HealthPredictionResultDTO();
        
        result.setOverallHealthScore(getDoubleValue(responseBody, "overall_health_score"));
        result.setRiskLevel(getStringValue(responseBody, "risk_level"));
        result.setPredictionConfidence(getDoubleValue(responseBody, "confidence"));
        
        // 解析疾病风险
        List<Map<String, Object>> diseaseRisks = (List<Map<String, Object>>) responseBody.get("disease_risks");
        if (diseaseRisks != null) {
            List<DiseaseRiskDTO> risks = diseaseRisks.stream()
                .map(this::parseDiseaseRisk)
                .collect(Collectors.toList());
            result.setDiseaseRisks(risks);
        }
        
        // 解析健康趋势
        Map<String, Object> trend = (Map<String, Object>) responseBody.get("health_trend");
        if (trend != null) {
            HealthTrendDTO healthTrend = new HealthTrendDTO();
            healthTrend.setTrendDirection(getStringValue(trend, "direction"));
            healthTrend.setTrendStrength(getDoubleValue(trend, "strength"));
            healthTrend.setPredictionPeriod(getIntValue(trend, "prediction_period"));
            result.setHealthTrend(healthTrend);
        }
        
        return result;
    }

    @SuppressWarnings("unchecked")
    private List<DiseaseRiskDTO> parseRiskAssessmentResult(Map<String, Object> responseBody) {
        List<DiseaseRiskDTO> risks = new ArrayList<>();
        
        List<Map<String, Object>> riskData = (List<Map<String, Object>>) responseBody.get("risks");
        if (riskData != null) {
            risks = riskData.stream()
                .map(this::parseDiseaseRisk)
                .collect(Collectors.toList());
        }
        
        return risks;
    }

    @SuppressWarnings("unchecked")
    private DiseaseRiskDTO parseDiseaseRisk(Map<String, Object> riskData) {
        DiseaseRiskDTO risk = new DiseaseRiskDTO();
        risk.setDiseaseName(getStringValue(riskData, "disease"));
        risk.setRiskScore(getDoubleValue(riskData, "risk_score"));
        risk.setRiskLevel(getStringValue(riskData, "risk_level"));
        risk.setRiskFactors((List<String>) riskData.get("risk_factors"));
        risk.setPreventiveMeasures((List<String>) riskData.get("preventive_measures"));
        return risk;
    }

    @SuppressWarnings("unchecked")
    private NutritionRecommendationDTO parseNutritionResult(Map<String, Object> responseBody) {
        NutritionRecommendationDTO nutrition = new NutritionRecommendationDTO();
        
        nutrition.setDailyCalories(getIntValue(responseBody, "daily_calories"));
        nutrition.setProteinPercentage(getDoubleValue(responseBody, "protein_percentage"));
        nutrition.setFatPercentage(getDoubleValue(responseBody, "fat_percentage"));
        nutrition.setCarbPercentage(getDoubleValue(responseBody, "carb_percentage"));
        
        // 解析营养建议
        List<String> recommendations = (List<String>) responseBody.get("recommendations");
        if (recommendations != null) {
            nutrition.setRecommendations(recommendations);
        }
        
        // 解析食物建议
        List<String> foodSuggestions = (List<String>) responseBody.get("food_suggestions");
        if (foodSuggestions != null) {
            nutrition.setFoodSuggestions(foodSuggestions);
        }
        
        return nutrition;
    }

    // 本地计算方法

    private double calculateOverallHealthScore(Cat cat, List<CatHealthRecord> healthRecords) {
        double baseScore = 85.0; // 基础分数
        
        // 根据年龄调整
        int age = calculateAge(cat.getDateOfBirth());
        if (age > 10) {
            baseScore -= (age - 10) * 2; // 老年猫适当降分
        }
        
        // 根据健康记录调整
        long recentIssues = healthRecords.stream()
            .filter(record -> record.getRecordDate().isAfter(LocalDateTime.now().minusMonths(6)))
            .filter(record -> record.getNotes().toLowerCase().contains("异常") || 
                            record.getNotes().toLowerCase().contains("疾病"))
            .count();
        
        baseScore -= recentIssues * 5; // 每个健康问题扣5分
        
        return Math.max(0, Math.min(100, baseScore));
    }

    private String assessVaccinationStatus(List<CatHealthRecord> healthRecords) {
        // 检查最近的疫苗记录
        Optional<CatHealthRecord> lastVaccine = healthRecords.stream()
            .filter(record -> record.getRecordType().toString().contains("VACCINATION"))
            .max(Comparator.comparing(CatHealthRecord::getRecordDate));
        
        if (lastVaccine.isPresent()) {
            long daysSinceVaccine = ChronoUnit.DAYS.between(
                lastVaccine.get().getRecordDate(), LocalDateTime.now());
            
            if (daysSinceVaccine <= 365) {
                return "UP_TO_DATE";
            } else if (daysSinceVaccine <= 400) {
                return "DUE_SOON";
            } else {
                return "OVERDUE";
            }
        }
        
        return "UNKNOWN";
    }

    private LocalDateTime getLastCheckupDate(List<CatHealthRecord> healthRecords) {
        return healthRecords.stream()
            .filter(record -> record.getRecordType().toString().contains("CHECKUP"))
            .map(CatHealthRecord::getRecordDate)
            .max(LocalDateTime::compareTo)
            .orElse(null);
    }

    private String calculateHealthTrend(List<CatHealthRecord> healthRecords) {
        // 简单的趋势分析
        if (healthRecords.size() < 2) {
            return "STABLE";
        }
        
        // 分析最近6个月的记录
        List<CatHealthRecord> recentRecords = healthRecords.stream()
            .filter(record -> record.getRecordDate().isAfter(LocalDateTime.now().minusMonths(6)))
            .sorted(Comparator.comparing(CatHealthRecord::getRecordDate))
            .collect(Collectors.toList());
        
        if (recentRecords.size() < 2) {
            return "STABLE";
        }
        
        // 简单的趋势判断逻辑
        long issueCount = recentRecords.stream()
            .filter(record -> record.getNotes().toLowerCase().contains("异常"))
            .count();
        
        if (issueCount > recentRecords.size() / 2) {
            return "DECLINING";
        } else if (issueCount == 0) {
            return "IMPROVING";
        } else {
            return "STABLE";
        }
    }

    private List<String> identifyRiskFactors(Cat cat, List<CatHealthRecord> healthRecords) {
        List<String> riskFactors = new ArrayList<>();
        
        // 年龄风险
        int age = calculateAge(cat.getDateOfBirth());
        if (age > 10) {
            riskFactors.add("高龄猫咪，需要更频繁的健康检查");
        }
        
        // 品种风险
        String breed = cat.getBreedName().toLowerCase();
        if (breed.contains("persian")) {
            riskFactors.add("波斯猫易患呼吸道疾病");
        } else if (breed.contains("british")) {
            riskFactors.add("英短易患心脏病和肾病");
        }
        
        // 健康记录风险
        long recentIssues = healthRecords.stream()
            .filter(record -> record.getRecordDate().isAfter(LocalDateTime.now().minusMonths(3)))
            .filter(record -> record.getNotes().toLowerCase().contains("异常"))
            .count();
        
        if (recentIssues > 0) {
            riskFactors.add("近期有健康异常记录");
        }
        
        return riskFactors;
    }

    private List<String> generateHealthRecommendations(Cat cat, List<CatHealthRecord> healthRecords) {
        List<String> recommendations = new ArrayList<>();
        
        // 基于年龄的建议
        int age = calculateAge(cat.getDateOfBirth());
        if (age > 7) {
            recommendations.add("建议每6个月进行一次全面体检");
        } else {
            recommendations.add("建议每年进行一次全面体检");
        }
        
        // 基于疫苗状态的建议
        String vaccinationStatus = assessVaccinationStatus(healthRecords);
        if ("OVERDUE".equals(vaccinationStatus)) {
            recommendations.add("疫苗已过期，请尽快接种");
        } else if ("DUE_SOON".equals(vaccinationStatus)) {
            recommendations.add("疫苗即将到期，请安排接种");
        }
        
        // 基于品种的建议
        String breed = cat.getBreedName().toLowerCase();
        if (breed.contains("persian")) {
            recommendations.add("定期清洁眼部和鼻部，预防呼吸道问题");
        }
        
        return recommendations;
    }

    private LocalDateTime calculateNextCheckupDate(Cat cat, List<CatHealthRecord> healthRecords) {
        LocalDateTime lastCheckup = getLastCheckupDate(healthRecords);
        int age = calculateAge(cat.getDateOfBirth());
        
        if (lastCheckup == null) {
            return LocalDateTime.now().plusMonths(1); // 如果没有体检记录，建议1个月内体检
        }
        
        // 根据年龄确定体检间隔
        int monthsInterval = age > 7 ? 6 : 12;
        return lastCheckup.plusMonths(monthsInterval);
    }

    private int calculateAge(LocalDate dateOfBirth) {
        return (int) ChronoUnit.YEARS.between(dateOfBirth, LocalDate.now());
    }

    // 默认结果创建方法

    private HealthPredictionResultDTO createDefaultPredictionResult() {
        HealthPredictionResultDTO result = new HealthPredictionResultDTO();
        result.setOverallHealthScore(75.0);
        result.setRiskLevel("MEDIUM");
        result.setPredictionConfidence(0.5);
        result.setDiseaseRisks(new ArrayList<>());
        return result;
    }

    private List<DiseaseRiskDTO> createDefaultRiskAssessment() {
        return new ArrayList<>();
    }

    private HealthAssessmentSummaryDTO createDefaultHealthSummary(Cat cat) {
        HealthAssessmentSummaryDTO summary = new HealthAssessmentSummaryDTO();
        summary.setCatId(cat.getId());
        summary.setCatName(cat.getName());
        summary.setAssessmentDate(LocalDateTime.now());
        summary.setOverallHealthScore(75.0);
        summary.setVaccinationStatus("UNKNOWN");
        summary.setHealthTrend("STABLE");
        summary.setRiskFactors(new ArrayList<>());
        summary.setRecommendations(Arrays.asList("建议定期体检"));
        return summary;
    }

    private NutritionRecommendationDTO createDefaultNutritionRecommendation() {
        NutritionRecommendationDTO nutrition = new NutritionRecommendationDTO();
        nutrition.setDailyCalories(300);
        nutrition.setProteinPercentage(30.0);
        nutrition.setFatPercentage(15.0);
        nutrition.setCarbPercentage(5.0);
        nutrition.setRecommendations(Arrays.asList("均衡饮食", "定时定量"));
        nutrition.setFoodSuggestions(Arrays.asList("高质量猫粮", "适量湿粮"));
        return nutrition;
    }

    // 工具方法
    private String getStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : "";
    }

    private double getDoubleValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return 0.0;
    }

    private int getIntValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return 0;
    }
}
