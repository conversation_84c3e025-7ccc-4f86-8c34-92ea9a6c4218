<template>
  <div class="finance-dashboard">
    <div class="page-header">
      <h1>财务管理</h1>
    </div>

    <!-- 财务概览卡片 -->
    <div class="overview-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon income">
                <el-icon><Money /></el-icon>
              </div>
              <div class="card-info">
                <h3>本月收入</h3>
                <p class="amount">¥{{ monthlyIncome.toLocaleString() }}</p>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon expense">
                <el-icon><ShoppingCart /></el-icon>
              </div>
              <div class="card-info">
                <h3>本月支出</h3>
                <p class="amount">¥{{ monthlyExpense.toLocaleString() }}</p>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon profit">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="card-info">
                <h3>本月利润</h3>
                <p class="amount">¥{{ monthlyProfit.toLocaleString() }}</p>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon balance">
                <el-icon><Wallet /></el-icon>
              </div>
              <div class="card-info">
                <h3>账户余额</h3>
                <p class="amount">¥{{ accountBalance.toLocaleString() }}</p>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>收支趋势</span>
            </template>
            <div class="chart-placeholder">
              <p>收支趋势图表</p>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>支出分类</span>
            </template>
            <div class="chart-placeholder">
              <p>支出分类饼图</p>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 最近交易 -->
    <div class="recent-transactions">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>最近交易</span>
            <el-button type="primary" size="small" @click="$router.push('/finance/transactions')">
              查看全部
            </el-button>
          </div>
        </template>
        <el-table :data="recentTransactions" style="width: 100%">
          <el-table-column prop="date" label="日期" width="120" />
          <el-table-column prop="description" label="描述" />
          <el-table-column prop="category" label="分类" width="120" />
          <el-table-column label="类型" width="80">
            <template #default="{ row }">
              <el-tag :type="row.type === 'INCOME' ? 'success' : 'danger'">
                {{ row.type === 'INCOME' ? '收入' : '支出' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="金额" width="120">
            <template #default="{ row }">
              <span :class="row.type === 'INCOME' ? 'income-amount' : 'expense-amount'">
                {{ row.type === 'INCOME' ? '+' : '-' }}¥{{ row.amount.toLocaleString() }}
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Money, ShoppingCart, TrendCharts, Wallet } from '@element-plus/icons-vue'
import { getFinanceStats, getFinanceRecords } from '@/api/finance'

// 响应式数据
const loading = ref(false)
const monthlyIncome = ref(0)
const monthlyExpense = ref(0)
const accountBalance = ref(0)

const recentTransactions = ref([
  {
    id: 1,
    date: '2024-01-15',
    description: '猫咪销售',
    category: '销售收入',
    type: 'INCOME',
    amount: 8000
  },
  {
    id: 2,
    date: '2024-01-14',
    description: '猫粮采购',
    category: '物料采购',
    type: 'EXPENSE',
    amount: 1200
  },
  {
    id: 3,
    date: '2024-01-13',
    description: '疫苗费用',
    category: '医疗费用',
    type: 'EXPENSE',
    amount: 800
  }
])

// 计算属性
const monthlyProfit = computed(() => monthlyIncome.value - monthlyExpense.value)

// 方法
const loadDashboardData = async () => {
  try {
    loading.value = true

    // 获取财务统计数据
    const statsResponse = await getFinanceStats()
    if (statsResponse.success) {
      const stats = statsResponse.data
      monthlyIncome.value = stats.monthlyIncome || 0
      monthlyExpense.value = stats.monthlyExpense || 0
      accountBalance.value = stats.totalIncome - stats.totalExpense || 0
    }

    // 获取最近的财务记录
    const recordsResponse = await getFinanceRecords({ page: 1, size: 5 })
    if (recordsResponse.success) {
      const records = recordsResponse.data
      if (Array.isArray(records)) {
        recentTransactions.value = records.map(record => ({
          id: record.id,
          date: record.transactionDate,
          description: record.description,
          category: record.category,
          type: record.type,
          amount: record.amount
        }))
      }
    }

  } catch (error) {
    console.error('加载财务数据失败:', error)
    ElMessage.error('加载财务数据失败')

    // 使用模拟数据作为后备
    monthlyIncome.value = 45000
    monthlyExpense.value = 32000
    accountBalance.value = 128000
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
.finance-dashboard {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.overview-cards {
  margin-bottom: 20px;
}

.overview-card .card-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.card-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.card-icon.income {
  background: #67c23a;
}

.card-icon.expense {
  background: #f56c6c;
}

.card-icon.profit {
  background: #409eff;
}

.card-icon.balance {
  background: #e6a23c;
}

.card-info h3 {
  margin: 0 0 5px 0;
  font-size: 14px;
  color: #666;
}

.card-info .amount {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.charts-section {
  margin-bottom: 20px;
}

.chart-placeholder {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 4px;
}

.recent-transactions .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.income-amount {
  color: #67c23a;
  font-weight: bold;
}

.expense-amount {
  color: #f56c6c;
  font-weight: bold;
}
</style>
