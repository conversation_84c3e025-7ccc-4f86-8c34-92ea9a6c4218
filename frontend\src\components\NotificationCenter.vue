<template>
  <div class="notification-center">
    <el-popover
      placement="bottom-end"
      :width="400"
      trigger="click"
      popper-class="notification-popover"
    >
      <template #reference>
        <el-badge :value="unreadCount" :hidden="!hasUnreadNotifications" class="notification-badge">
          <el-button :icon="Bell" circle />
        </el-badge>
      </template>

      <div class="notification-content">
        <div class="notification-header">
          <h4>通知中心</h4>
          <div class="header-actions">
            <el-button 
              v-if="hasUnreadNotifications"
              type="text" 
              size="small" 
              @click="markAllAsRead"
            >
              全部已读
            </el-button>
            <el-button type="text" size="small" @click="viewAllNotifications">
              查看全部
            </el-button>
          </div>
        </div>

        <div class="notification-tabs">
          <el-tabs v-model="activeTab" @tab-change="handleTabChange">
            <el-tab-pane label="全部" name="all" />
            <el-tab-pane label="未读" name="unread" />
            <el-tab-pane label="重要" name="important" />
          </el-tabs>
        </div>

        <div class="notification-list">
          <el-scrollbar height="300px">
            <div v-if="loading" class="loading-container">
              <el-skeleton :rows="3" animated />
            </div>
            
            <div v-else-if="filteredNotifications.length === 0" class="empty-container">
              <el-empty description="暂无通知" />
            </div>
            
            <div v-else>
              <div
                v-for="notification in filteredNotifications"
                :key="notification.id"
                class="notification-item"
                :class="{ 'unread': !notification.isRead, 'important': notification.isImportant }"
                @click="handleNotificationClick(notification)"
              >
                <div class="notification-icon">
                  <el-icon :color="getNotificationColor(notification.type)">
                    <component :is="getNotificationIcon(notification.type)" />
                  </el-icon>
                </div>
                
                <div class="notification-body">
                  <div class="notification-title">
                    {{ notification.title }}
                    <el-tag v-if="notification.isImportant" type="danger" size="small">重要</el-tag>
                  </div>
                  <div class="notification-content-text">
                    {{ notification.content }}
                  </div>
                  <div class="notification-meta">
                    <span class="notification-time">{{ formatRelativeTime(notification.createdAt) }}</span>
                    <el-tag :type="getNotificationTagType(notification.type)" size="small">
                      {{ getNotificationTypeText(notification.type) }}
                    </el-tag>
                  </div>
                </div>
                
                <div class="notification-actions">
                  <el-button
                    v-if="!notification.isRead"
                    type="text"
                    size="small"
                    @click.stop="markAsRead(notification.id)"
                  >
                    标记已读
                  </el-button>
                  <el-button
                    type="text"
                    size="small"
                    @click.stop="deleteNotification(notification.id)"
                  >
                    删除
                  </el-button>
                </div>
              </div>
            </div>
          </el-scrollbar>
        </div>

        <div class="notification-footer">
          <el-button type="text" @click="refreshNotifications">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
          <el-button type="text" @click="openNotificationSettings">
            <el-icon><Setting /></el-icon>
            设置
          </el-button>
        </div>
      </div>
    </el-popover>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Bell, Refresh, Setting, InfoFilled, SuccessFilled, 
  WarningFilled, CircleCloseFilled
} from '@element-plus/icons-vue'
import { useNotificationStore } from '@/stores/notifications'
import { formatRelativeTime } from '@/utils'
import type { Notification } from '@/api/notifications'

const router = useRouter()
const notificationStore = useNotificationStore()

// 响应式数据
const activeTab = ref('all')
const loading = ref(false)

// 计算属性
const unreadCount = computed(() => notificationStore.unreadCount)
const hasUnreadNotifications = computed(() => notificationStore.hasUnreadNotifications)
const notifications = computed(() => notificationStore.notifications)

const filteredNotifications = computed(() => {
  switch (activeTab.value) {
    case 'unread':
      return notificationStore.unreadNotifications
    case 'important':
      return notificationStore.importantNotifications
    default:
      return notifications.value
  }
})

// 方法
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName
}

const handleNotificationClick = async (notification: Notification) => {
  // 标记为已读
  if (!notification.isRead) {
    await markAsRead(notification.id)
  }
  
  // 如果有操作链接，跳转到对应页面
  if (notification.actionUrl) {
    router.push(notification.actionUrl)
  }
}

const markAsRead = async (id: number) => {
  try {
    await notificationStore.markNotificationAsRead(id)
    ElMessage.success('已标记为已读')
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const markAllAsRead = async () => {
  try {
    await notificationStore.markAllNotificationsAsRead()
    ElMessage.success('已全部标记为已读')
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const deleteNotification = async (id: number) => {
  try {
    notificationStore.removeNotification(id)
    ElMessage.success('通知已删除')
  } catch (error) {
    ElMessage.error('删除失败')
  }
}

const refreshNotifications = async () => {
  try {
    loading.value = true
    await notificationStore.refreshNotifications()
    ElMessage.success('刷新成功')
  } catch (error) {
    ElMessage.error('刷新失败')
  } finally {
    loading.value = false
  }
}

const viewAllNotifications = () => {
  router.push('/notifications')
}

const openNotificationSettings = () => {
  router.push('/settings/notifications')
}

const getNotificationIcon = (type: string) => {
  const iconMap = {
    INFO: InfoFilled,
    SUCCESS: SuccessFilled,
    WARNING: WarningFilled,
    ERROR: CircleCloseFilled
  }
  return iconMap[type as keyof typeof iconMap] || InfoFilled
}

const getNotificationColor = (type: string) => {
  const colorMap = {
    INFO: '#409EFF',
    SUCCESS: '#67C23A',
    WARNING: '#E6A23C',
    ERROR: '#F56C6C'
  }
  return colorMap[type as keyof typeof colorMap] || '#409EFF'
}

const getNotificationTagType = (type: string) => {
  const typeMap = {
    INFO: 'info',
    SUCCESS: 'success',
    WARNING: 'warning',
    ERROR: 'danger'
  }
  return typeMap[type as keyof typeof typeMap] || 'info'
}

const getNotificationTypeText = (type: string) => {
  const textMap = {
    INFO: '信息',
    SUCCESS: '成功',
    WARNING: '警告',
    ERROR: '错误'
  }
  return textMap[type as keyof typeof textMap] || '信息'
}

// 生命周期
onMounted(async () => {
  await notificationStore.init()
})
</script>

<style scoped lang="scss">
.notification-center {
  .notification-badge {
    :deep(.el-badge__content) {
      border: 2px solid #fff;
    }
  }
}

.notification-content {
  .notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 12px;
    border-bottom: 1px solid #ebeef5;
    margin-bottom: 12px;

    h4 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }

    .header-actions {
      display: flex;
      gap: 8px;
    }
  }

  .notification-tabs {
    margin-bottom: 12px;

    :deep(.el-tabs__header) {
      margin: 0;
    }

    :deep(.el-tabs__nav-wrap::after) {
      display: none;
    }
  }

  .notification-list {
    .loading-container,
    .empty-container {
      padding: 20px;
      text-align: center;
    }

    .notification-item {
      display: flex;
      padding: 12px;
      border-radius: 6px;
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover {
        background-color: #f5f7fa;
      }

      &.unread {
        background-color: #f0f9ff;
        border-left: 3px solid #409eff;
      }

      &.important {
        border-left: 3px solid #f56c6c;
      }

      .notification-icon {
        margin-right: 12px;
        flex-shrink: 0;
      }

      .notification-body {
        flex: 1;
        min-width: 0;

        .notification-title {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 500;
          margin-bottom: 4px;
          color: #303133;
        }

        .notification-content-text {
          font-size: 13px;
          color: #606266;
          margin-bottom: 8px;
          line-height: 1.4;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .notification-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .notification-time {
            font-size: 12px;
            color: #909399;
          }
        }
      }

      .notification-actions {
        display: flex;
        flex-direction: column;
        gap: 4px;
        margin-left: 8px;
        opacity: 0;
        transition: opacity 0.2s;
      }

      &:hover .notification-actions {
        opacity: 1;
      }
    }
  }

  .notification-footer {
    display: flex;
    justify-content: space-between;
    padding-top: 12px;
    border-top: 1px solid #ebeef5;
    margin-top: 12px;
  }
}

:global(.notification-popover) {
  padding: 16px !important;
}
</style>
