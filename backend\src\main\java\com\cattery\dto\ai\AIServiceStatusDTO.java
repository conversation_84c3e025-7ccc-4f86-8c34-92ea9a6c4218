package com.cattery.dto.ai;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * AI服务状态DTO
 */
@Data
public class AIServiceStatusDTO {
    
    /**
     * 服务状态 (ONLINE, OFFLINE, DEGRADED)
     */
    private String status;
    
    /**
     * 检查时间
     */
    private LocalDateTime checkTime;
    
    /**
     * 猫咪识别服务状态
     */
    private Boolean catRecognitionAvailable;
    
    /**
     * 品种识别服务状态
     */
    private Boolean breedRecognitionAvailable;
    
    /**
     * 健康预测服务状态
     */
    private Boolean healthPredictionAvailable;
    
    /**
     * 行为分析服务状态
     */
    private Boolean behaviorAnalysisAvailable;

    /**
     * 图像处理服务状态
     */
    private Boolean imageProcessingAvailable;

    /**
     * 整体可用性
     */
    private Boolean overallAvailable;
    
    /**
     * 服务响应时间（毫秒）
     */
    private Map<String, Long> responseTime;
    
    /**
     * 服务版本信息
     */
    private Map<String, String> serviceVersions;

    /**
     * 服务版本
     */
    private String serviceVersion;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 服务配置信息
     */
    private Map<String, Object> configuration;
    
    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;
}
