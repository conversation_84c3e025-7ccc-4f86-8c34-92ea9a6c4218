package com.cattery.entity;

import jakarta.persistence.*;
import java.time.LocalDate;

@Entity
@Table(name = "breeding_records")
public class BreedingRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne
    @JoinColumn(name = "mother_id")
    private Cat mother;
    
    @ManyToOne
    @JoinColumn(name = "father_id")
    private Cat father;
    
    private LocalDate matingDate;
    private LocalDate expectedBirthDate;
    private LocalDate actualBirthDate;
    private Integer litterSize;
    
    // getters and setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public Cat getMother() { return mother; }
    public void setMother(Cat mother) { this.mother = mother; }
    
    public Cat getFather() { return father; }
    public void setFather(Cat father) { this.father = father; }
    
    public LocalDate getMatingDate() { return matingDate; }
    public void setMatingDate(LocalDate matingDate) { this.matingDate = matingDate; }
    
    public LocalDate getExpectedBirthDate() { return expectedBirthDate; }
    public void setExpectedBirthDate(LocalDate expectedBirthDate) { this.expectedBirthDate = expectedBirthDate; }
    
    public LocalDate getActualBirthDate() { return actualBirthDate; }
    public void setActualBirthDate(LocalDate actualBirthDate) { this.actualBirthDate = actualBirthDate; }
    
    public Integer getLitterSize() { return litterSize; }
    public void setLitterSize(Integer litterSize) { this.litterSize = litterSize; }
}
