import request from '@/utils/request'

export interface BreedingRecord {
  id?: number
  maleCatId: number
  femaleCatId: number
  matingDate: string
  expectedBirthDate?: string
  actualBirthDate?: string
  pregnancyConfirmed: boolean
  pregnancyConfirmDate?: string
  litterSize?: number
  notes?: string
  status: 'PLANNED' | 'MATED' | 'PREGNANT' | 'BORN' | 'FAILED'
  createdAt?: string
  updatedAt?: string
  createdBy?: number
  updatedBy?: number
  maleCat?: {
    id: number
    name: string
    breed: string
  }
  femaleCat?: {
    id: number
    name: string
    breed: string
  }
}

export interface BreedingQuery {
  page?: number
  size?: number
  maleCatId?: number
  femaleCatId?: number
  status?: string
  startDate?: string
  endDate?: string
  pregnancyConfirmed?: boolean
}

// 获取繁育记录列表
export const getBreedingRecords = (params?: BreedingQuery) => {
  return request.get('/breeding-records', { params })
}

// 根据ID获取繁育记录
export const getBreedingRecordById = (id: number) => {
  return request.get(`/breeding-records/${id}`)
}

// 创建繁育记录
export const createBreedingRecord = (data: Omit<BreedingRecord, 'id' | 'createdAt' | 'updatedAt'>) => {
  return request.post('/breeding-records', data)
}

// 更新繁育记录
export const updateBreedingRecord = (id: number, data: Partial<BreedingRecord>) => {
  return request.put(`/breeding-records/${id}`, data)
}

// 删除繁育记录
export const deleteBreedingRecord = (id: number) => {
  return request.delete(`/breeding-records/${id}`)
}

// 确认怀孕
export const confirmPregnancy = (id: number, data: { pregnancyConfirmDate: string; expectedBirthDate?: string }) => {
  return request.post(`/breeding-records/${id}/confirm-pregnancy`, data)
}

// 记录分娩
export const recordBirth = (id: number, data: { actualBirthDate: string; litterSize: number; notes?: string }) => {
  return request.post(`/breeding-records/${id}/record-birth`, data)
}

// 获取繁育统计
export const getBreedingStats = () => {
  return request.get('/breeding-records/stats')
}

// 获取可繁育的猫咪
export const getBreedableCats = (gender?: 'MALE' | 'FEMALE') => {
  return request.get('/cats/breedable', { params: { gender } })
}

// 获取猫咪的繁育历史
export const getCatBreedingHistory = (catId: number) => {
  return request.get(`/cats/${catId}/breeding-history`)
}

// 获取繁育计划
export const getBreedingPlan = (params?: { year?: number; month?: number }) => {
  return request.get('/breeding-records/plan', { params })
}

// 检查近亲繁殖风险
export const checkInbreedingRisk = (maleCatId: number, femaleCatId: number) => {
  return request.get('/breeding-records/inbreeding-check', {
    params: { maleCatId, femaleCatId }
  })
}

// 获取配种建议
export const getBreedingSuggestions = (femaleCatId: number) => {
  return request.get(`/cats/${femaleCatId}/breeding-suggestions`)
}

// 导出繁育记录
export const exportBreedingRecords = (params?: BreedingQuery) => {
  return request.get('/breeding-records/export', {
    params,
    responseType: 'blob'
  })
}
