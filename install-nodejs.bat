@echo off
echo ========================================
echo Node.js 自动安装脚本
echo ========================================
echo.

echo 步骤1: 准备卸载当前Node.js
echo.
echo 请手动执行以下步骤:
echo 1. 打开"控制面板" - "程序和功能"
echo 2. 找到并卸载所有Node.js相关程序
echo 3. 删除目录: D:\软件\node_modules\ (如果存在)
echo.
pause

echo.
echo 步骤2: 下载最新Node.js LTS版本
echo.
echo 正在打开Node.js官网下载页面...
start https://nodejs.org/zh-cn/download/
echo.
echo 请下载: Windows Installer (.msi) - 64位
echo 推荐版本: v20.x LTS 或 v18.x LTS
echo.
pause

echo.
echo 步骤3: 安装指导
echo.
echo 安装时请注意:
echo 1. 选择安装路径: C:\Program Files\nodejs\
echo 2. 勾选 "Add to PATH" 选项
echo 3. 勾选 "Install additional tools" (可选)
echo 4. 完成安装后重启命令行
echo.
pause

echo.
echo 步骤4: 验证安装
echo.
echo 安装完成后，请打开新的命令行窗口执行:
echo   node --version
echo   npm --version
echo.
echo 如果显示版本号，说明安装成功!
echo.

echo ========================================
echo 安装完成后的下一步
echo ========================================
echo.
echo 1. 进入项目目录: cd D:\噔噔\frontend
echo 2. 安装依赖: npm install
echo 3. 启动开发服务器: npm run dev
echo 4. 访问: http://localhost:3000
echo.

echo 如果遇到问题，请运行: npm-troubleshoot.bat
echo.
pause
